# Fleet Fusion - Fleet Management System

Fleet Fusion is a modern fleet management system built with Next.js and NestJS.

## Features

- Vehicle Management
- Driver Assignment
- Trip Tracking
- Maintenance Logging
- Real-time Status Updates
- User Authentication & Authorization

## Tech Stack

### Frontend
- Next.js 14 with App Router
- TypeScript
- Tailwind CSS
- shadcn/ui Components
- React Query
- Zod for Validation

### Backend
- NestJS
- PostgreSQL
- Prisma ORM
- JWT Authentication
- Class Validator

## Getting Started

### Prerequisites
- Node.js 18 or later
- PostgreSQL 12 or later
- npm or pnpm

### Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd fleet-fusion
```

2. Install dependencies:
```bash
# Install root dependencies
npm install

# Install frontend dependencies
cd frontend && npm install

# Install backend dependencies
cd ../backend && npm install
```

3. Set up the database:
```bash
cd backend
# Create and configure .env file
npx prisma migrate dev
```

4. Start the development servers:
```bash
# Start both frontend and backend
npm run dev
```

### Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both frontend and backend
- `npm run start` - Start both frontend and backend in production mode

### Database Migrations

To create a new migration:
```bash
cd backend
npx prisma migrate dev --name <migration-name>
```

## Project Structure

```
fleet-fusion/
??? frontend/               # Next.js frontend application
?   ??? src/
?   ?   ??? app/          # App Router pages
?   ?   ??? components/   # React components
?   ?   ??? lib/         # Utilities and helpers
?   ??? public/          # Static files
?
??? backend/              # NestJS backend application
?   ??? src/
?   ?   ??? modules/     # Feature modules
?   ?   ??? common/     # Shared resources
?   ?   ??? main.ts    # Application entry point
?   ??? prisma/        # Database schema and migrations
?
??? .vscode/           # VS Code configuration
```

## Contributing

1. Create a feature branch
2. Commit your changes
3. Push to the branch
4. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
