{"version": "2.0.0", "tasks": [{"label": "Start Development (Full Stack)", "dependsOn": ["Frontend Dev", "Backend Dev"], "group": {"kind": "build", "isDefault": true}}, {"label": "Frontend Dev", "type": "shell", "command": "npm run dev", "options": {"cwd": "${workspaceFolder}/frontend"}, "problemMatcher": [], "isBackground": true}, {"label": "Backend Dev", "type": "shell", "command": "npm run start:dev", "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": [], "isBackground": true}, {"label": "Database Migration", "type": "shell", "command": "npx prisma migrate dev", "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": []}]}