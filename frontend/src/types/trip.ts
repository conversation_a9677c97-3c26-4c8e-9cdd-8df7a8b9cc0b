import { User } from './user';
import { Vehicle, TruckTrailerAssignment } from './vehicle';
import { Assignment } from './assignment';

export enum TripType {
  DELIVERY = 'DELIVERY',
  TRANSFER = 'TRANSFER',
  TRANSPORT = 'TRANSPORT',
  MAINTENANCE = 'MAINTENANCE',
  OTHER = 'OTHER',
}

export enum TripPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum TripStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum TripStopStatus {
  PENDING = 'PENDING',
  ARRIVED = 'ARRIVED',
  COMPLETED = 'COMPLETED',
  SKIPPED = 'SKIPPED',
  CANCELLED = 'CANCELLED',
}

export enum ExpenseType {
  FUEL = 'FUEL',
  TOLL = 'TOLL',
  PARKING = 'PARKING',
  MAINTENANCE = 'MAINTENANCE',
  FOOD = 'FOOD',
  OTHER = 'OTHER',
}

export interface TripStop {
  id: string;
  tripId: string;
  location: string;
  arrivalTime?: string;
  departureTime?: string;
  purpose?: string;
  status: TripStopStatus;
  notes?: string;
  sequence: number;
  createdAt: string;
  updatedAt: string;
}

export interface TripStopInput {
  location: string;
  purpose?: string;
  sequence: number;
}

export interface Trip {
  id: string;
  type: TripType;
  priority?: TripPriority;
  driver: User;
  driverId: string;
  vehicle: Vehicle;
  vehicleId: string;
  trailer?: Vehicle;
  trailerId?: string;
  truckTrailerAssignmentId?: string;
  assignment?: Assignment;
  assignmentId?: string;
  status: TripStatus;
  startLocation: string;
  endLocation: string;
  startTime: string;
  endTime?: string;
  estimatedDuration?: number;
  actualDuration?: number;
  distance?: number;
  notes?: string;
  purpose?: string;
  cargo?: string;
  cargoWeight?: number;
  stops: TripStop[];
  expenses: TripExpense[];
  createdAt: string;
  updatedAt: string;
  stopData?: TripStopInput[]; // Used only when creating a new trip
}

export interface CreateTripRequest {
  type: TripType;
  priority?: TripPriority;
  driverId: string;
  vehicleId: string;
  trailerId?: string;
  truckTrailerAssignmentId?: string;
  assignmentId?: string;
  startLocation: string;
  endLocation: string;
  startTime: string;
  endTime?: string;
  estimatedDuration?: number;
  distance?: number;
  notes?: string;
  purpose?: string;
  cargo?: string;
  cargoWeight?: number;
  stopData?: TripStopInput[];
}

export interface TruckTrailerPair {
  id: string;
  truck: Vehicle;
  trailer: Vehicle;
  startDate: string;
  endDate?: string;
  status: string;
  assignedBy: string;
  notes?: string;
}

export interface TripExpense {
  id: string;
  tripId: string;
  type: ExpenseType;
  amount: number;
  description?: string;
  receiptUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export const tripTypeVariants: Record<TripType, string> = {
  [TripType.DELIVERY]: 'green',
  [TripType.TRANSFER]: 'purple',
  [TripType.TRANSPORT]: 'cyan',
  [TripType.MAINTENANCE]: 'amber',
  [TripType.OTHER]: 'gray',
};

export const tripPriorityVariants: Record<TripPriority, string> = {
  [TripPriority.LOW]: 'low',
  [TripPriority.NORMAL]: 'normal',
  [TripPriority.HIGH]: 'high',
  [TripPriority.URGENT]: 'urgent',
};

export const tripStatusVariants: Record<TripStatus, string> = {
  [TripStatus.SCHEDULED]: 'pending',
  [TripStatus.IN_PROGRESS]: 'active',
  [TripStatus.COMPLETED]: 'completed',
  [TripStatus.CANCELLED]: 'cancelled',
};

export const tripStopStatusVariants: Record<TripStopStatus, string> = {
  [TripStopStatus.PENDING]: 'pending',
  [TripStopStatus.ARRIVED]: 'active',
  [TripStopStatus.COMPLETED]: 'completed',
  [TripStopStatus.SKIPPED]: 'cancelled',
  [TripStopStatus.CANCELLED]: 'cancelled',
};
