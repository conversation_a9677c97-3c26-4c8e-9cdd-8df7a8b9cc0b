/// <reference types="react" />

declare module 'react' {
  type ReactNode = ReactChild | ReactFragment | ReactPortal | boolean | null | undefined;

  // Hook type declarations
  function useState<T>(initialState: T | (() => T)): [T, (newValue: T | ((prev: T) => T)) => void];
  function useState<T = undefined>(): [T | undefined, (newValue: T | ((prev: T | undefined) => T)) => void];
  
  function useEffect(effect: () => void | (() => void), deps?: ReadonlyArray<any>): void;
  
  function useMemo<T>(factory: () => T, deps: ReadonlyArray<any> | undefined): T;
  
  function useCallback<T extends (...args: any[]) => any>(callback: T, deps: ReadonlyArray<any>): T;

  interface ReactElement<
    P = any,
    T extends string | JSXElementConstructor<any> = string | JSXElementConstructor<any>
  > {
    type: T;
    props: P;
    key: Key | null;
  }

  interface FunctionComponent<P = {}> {
    (props: P, context?: any): ReactElement<any, any> | null;
    displayName?: string;
    defaultProps?: Partial<P>;
  }

  type ElementRef<T> = T extends { current: infer CurrentType }
    ? CurrentType
    : T extends new (...args: any) => { current: infer CurrentType }
    ? CurrentType
    : T extends new (...args: any) => infer InstanceType
    ? InstanceType
    : T extends (...args: any) => infer ReturnType
    ? ReturnType
    : T;

  type ComponentPropsWithoutRef<T> = T extends new (...args: any) => { props: infer P }
    ? P
    : T extends (...args: any) => { props: infer P }
    ? P
    : T extends keyof JSX.IntrinsicElements
    ? JSX.IntrinsicElements[T]
    : {};

  interface ForwardRefExoticComponent<P = {}> {
    (props: P): ReactElement | null;
    readonly $$typeof: symbol;
    defaultProps?: Partial<P>;
    displayName?: string;
  }

  interface ForwardRefRenderFunction<T, P = {}> {
    (props: P, ref: ((instance: T | null) => void) | MutableRefObject<T | null> | null): ReactElement | null;
    displayName?: string;
    defaultProps?: never;
    propTypes?: never;
  }

  function forwardRef<T, P = {}>(
    render: ForwardRefRenderFunction<T, P>,
  ): ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<T>>;

  interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    className?: string;
    style?: CSSProperties;
  }

  interface ButtonHTMLAttributes<T> extends HTMLAttributes<T> {
    autoFocus?: boolean;
    disabled?: boolean;
    form?: string;
    formAction?: string;
    formEncType?: string;
    formMethod?: string;
    formNoValidate?: boolean;
    formTarget?: string;
    name?: string;
    type?: 'submit' | 'reset' | 'button';
    value?: string | ReadonlyArray<string> | number;
  }

  interface ClassAttributes<T> {
    ref?: LegacyRef<T>;
    key?: Key;
  }
}

declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}
