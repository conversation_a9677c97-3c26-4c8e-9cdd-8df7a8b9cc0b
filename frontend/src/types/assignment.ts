export interface Assignment {
  id: string;
  vehicleId: string;
  driverId: string;
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  type: AssignmentType;
  priority: AssignmentPriority;
  notes?: string;
  mileage?: number;
  locationStart?: string;
  locationEnd?: string;
  reason?: string;
  approvedBy?: string;
  approvedAt?: string;
  vehicle: {
    plateNumber: string;
    make: string;
    model: string;
  };
  driver: {
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export enum AssignmentStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD',
  DELAYED = 'DELAYED'
}

export enum AssignmentType {
  REGULAR = 'REGULAR',
  TEMPORARY = 'TEMPORARY',
  EMERGENCY = 'EMERGENCY',
  MAINTENANCE = 'MAINTENANCE',
  TRAINING = 'TRAINING'
}

export enum AssignmentPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline' | 
  'pending' | 'active' | 'completed' | 'cancelled' | 'delayed' | 'on-hold' |
  'regular' | 'temporary' | 'emergency' | 'maintenance' | 'training' |
  'low' | 'normal' | 'high' | 'urgent';

export const assignmentStatusVariants: Record<AssignmentStatus, BadgeVariant> = {
  [AssignmentStatus.PENDING]: 'pending',
  [AssignmentStatus.ACTIVE]: 'active',
  [AssignmentStatus.COMPLETED]: 'completed',
  [AssignmentStatus.CANCELLED]: 'cancelled',
  [AssignmentStatus.ON_HOLD]: 'on-hold',
  [AssignmentStatus.DELAYED]: 'delayed',
};

export const assignmentTypeVariants: Record<AssignmentType, BadgeVariant> = {
  [AssignmentType.REGULAR]: 'regular',
  [AssignmentType.TEMPORARY]: 'temporary',
  [AssignmentType.EMERGENCY]: 'emergency',
  [AssignmentType.MAINTENANCE]: 'maintenance',
  [AssignmentType.TRAINING]: 'training',
};

export const assignmentPriorityVariants: Record<AssignmentPriority, BadgeVariant> = {
  [AssignmentPriority.LOW]: 'low',
  [AssignmentPriority.NORMAL]: 'normal',
  [AssignmentPriority.HIGH]: 'high',
  [AssignmentPriority.URGENT]: 'urgent',
};
