export enum VehicleType {
  TRUCK = 'TRUCK',
  TRAILER = 'TRAILER',
}

export enum MaintenanceCategory {
  ENGINE = 'ENGINE',
  TRANSMISSION = 'TRANSMISSION',
  BRAKES = 'BRAKES',
  ELECTRICAL = 'ELECTRICAL',
  TIRES = 'TIRES',
  COOLING_SYSTEM = 'COOLING_SYSTEM',
  FUEL_SYSTEM = 'FUEL_SYSTEM',
  EXHAUST_SYSTEM = 'EXHAUST_SYSTEM',
  SUSPENSION_AXLES = 'SUSPENSION_AXLES',
  CARGO_AREA = 'CARGO_AREA',
  REFRIGERATION_UNIT = 'REFRIGERATION_UNIT',
  HYDRAULIC_SYSTEMS = 'HYDRAULIC_SYSTEMS',
  LIGHTING_SYSTEM = 'LIGHTING_SYSTEM',
  OTHER = 'OTHER',
}

export interface MaintenanceLog {
  id: string;
  type: 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  category: 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
  description: string;
  date: string;
  scheduledDate?: string;
  mileage?: number;
  cost?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
  vehicle?: {
    id: string;
    make: string;
    model: string;
    plateNumber: string;
    year: number;
    vehicleType?: VehicleType;
  };
  vehicleId: string;
}
