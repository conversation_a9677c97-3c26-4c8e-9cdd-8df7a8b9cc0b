export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'ADMIN' | 'MANAGER' | 'DRIVER';
  status?: string;
}

export interface Driver extends User {
  role: 'DRIVER';
  phone?: string;
  status?: string;
  licenseNumber?: string;
  licenseType?: string;
  licenseExpiry?: string;
  licenseRestrictions?: string;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  hireDate?: string;
  notes?: string;
  vehicleId?: string; // For convenience, even though it's derived from assignments
}
