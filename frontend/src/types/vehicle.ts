export enum VehicleStatus {
  AVAILABLE = 'AVAILABLE',
  ASSIGNED = 'ASSIGNED',
  MAINTENANCE = 'MAINTENANCE',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE',
}

export enum VehicleType {
  TRUCK = 'TRUCK',
  TRAILER = 'TRAILER',
}

export enum TrailerType {
  DRY_VAN = 'DRY_VAN',
  REFRIGERATED = 'REFRIGERATED',
  FLATBED = 'FLATBED',
  TANKER = 'TANKER',
  LOWBOY = 'LOWBOY',
  STEP_DECK = 'STEP_DECK',
  CONTAINER_CHASSIS = 'CONTAINER_CHASSIS',
}

export enum FuelType {
  GASOLINE = 'GASOLINE',
  DIESEL = 'DIESEL',
  ELECTRIC = 'ELECTRIC',
  HYBRID = 'HYBRID',
  OTHER = 'OTHER',
}

export interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  status: VehicleStatus;
  vehicleType: VehicleType;
  lastMaintenance: string | null;
  createdAt: string;
  updatedAt: string;

  // Basic properties
  vin?: string;
  color?: string;
  mileage?: number;
  fuelType?: FuelType;
  purchaseDate?: string;

  // Truck-specific fields
  engineType?: string;
  transmission?: string;
  fuelCapacity?: number;
  axleConfiguration?: string;
  cabConfiguration?: string;

  // Trailer-specific fields
  trailerType?: TrailerType;
  cargoCapacity?: number;
  maxWeight?: number;
  length?: number;
  width?: number;
  height?: number;
  hasRefrigeration?: boolean;

  // Relations
  assignments?: any[];
  truckAssignments?: TruckTrailerAssignment[];
  trailerAssignments?: TruckTrailerAssignment[];
}

export enum AssignmentStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  PENDING = 'PENDING',
  ON_HOLD = 'ON_HOLD',
  DELAYED = 'DELAYED',
}

export interface TruckTrailerAssignment {
  id: string;
  truckId: string;
  trailerId: string;
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  notes?: string;
  assignedBy: string;
  createdAt: string;
  updatedAt: string;

  // Relations
  truck?: Vehicle;
  trailer?: Vehicle;
}

export interface CreateTruckTrailerAssignmentRequest {
  truckId: string;
  trailerId: string;
  startDate: string;
  endDate?: string;
  notes?: string;
  assignedBy?: string;
}

export interface UpdateTruckTrailerAssignmentRequest {
  truckId?: string;
  trailerId?: string;
  startDate?: string;
  endDate?: string;
  status?: AssignmentStatus;
  notes?: string;
  assignedBy?: string;
}

export interface CreateVehicleRequest {
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  vehicleType: VehicleType;
  vin?: string;
  color?: string;
  mileage?: number;
  fuelType?: FuelType;
  purchaseDate?: string;

  // Truck-specific fields
  engineType?: string;
  transmission?: string;
  fuelCapacity?: number;
  axleConfiguration?: string;
  cabConfiguration?: string;

  // Trailer-specific fields
  trailerType?: TrailerType;
  cargoCapacity?: number;
  maxWeight?: number;
  length?: number;
  width?: number;
  height?: number;
  hasRefrigeration?: boolean;
}

export interface UpdateVehicleRequest {
  plateNumber?: string;
  make?: string;
  model?: string;
  year?: number;
  vehicleType?: VehicleType;
  status?: VehicleStatus;
  vin?: string;
  color?: string;
  mileage?: number;
  fuelType?: FuelType;
  purchaseDate?: string;

  // Truck-specific fields
  engineType?: string;
  transmission?: string;
  fuelCapacity?: number;
  axleConfiguration?: string;
  cabConfiguration?: string;

  // Trailer-specific fields
  trailerType?: TrailerType;
  cargoCapacity?: number;
  maxWeight?: number;
  length?: number;
  width?: number;
  height?: number;
  hasRefrigeration?: boolean;
}
