export interface VehicleReview {
  id: string;
  vehicleId: string;
  reviewType: ReviewType;
  reviewBy?: string; // Made optional to suit Polish law tracking requirements
  scheduledDate: string;
  completedDate?: string;
  location?: string;
  status: ReviewStatus;
  findings?: string;
  recommendations?: string;
  nextReviewDate?: string;
  documents?: string[];
  // Additional fields used in the UI components
  date?: string;
  type?: 'SCHEDULED' | 'UNSCHEDULED' | 'INCIDENT';
  reviewer?: string;
  mileage?: number;
  rating?: number;
  comments?: string;
  actionItems?: string[];
  vehicleInfo?: {
    id: string;
    make: string;
    model: string;
    year: number;
    vin?: string;
  };
}

export type ReviewType = 'ANNUAL_INSPECTION' | 'TECHNICAL_INSPECTION' | 'SAFETY_CHECK' | 'EMISSIONS_TEST' | 'QUALITY_CONTROL';
export type ReviewStatus = 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
