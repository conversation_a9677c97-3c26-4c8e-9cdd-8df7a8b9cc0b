export interface InsurancePolicy {
  id: string;
  vehicleId: string;
  policyNumber: string;
  provider: string;
  type: InsuranceType;
  startDate: string;
  endDate: string;
  premium: number;
  coverage: number;
  deductible: number;
  status: PolicyStatus;
  description?: string;
  notes?: string;
  documents?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export type InsuranceType = 'COMPREHENSIVE' | 'THIRD_PARTY' | 'FIRE_THEFT' | 'LIABILITY';
export type PolicyStatus = 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'RENEWAL_DUE';
