'use client';

import React from 'react';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import { setGlobalAuthErrorHandler } from '../lib/auth-handler';
import { isTokenExpired } from '../lib/jwt-utils';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'MANAGER' | 'DRIVER';
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  isLoading: boolean;
  // For testing role-based access
  setUserRole: (role: User['role']) => void;
  // Handle authentication errors (401s)
  handleAuthError: () => void;
}

const AuthContext = (React as any).createContext(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = (React as any).useState(null);
  const [isLoading, setIsLoading] = (React as any).useState(true);
  const [isCheckingToken, setIsCheckingToken] = (React as any).useState(false);
  const router = useRouter();

  const logout = (React as any).useCallback(() => {
    console.log('🔐 Logging out user');
    Cookies.remove('token');
    setUser(null);
    // Redirect to login page if not already there
    if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
      router.push('/login');
    }
  }, [router]);

  // Handle authentication errors (like 401)
  const handleAuthError = (React as any).useCallback(() => {
    console.log('🔐 Authentication error detected, logging out');
    logout();
  }, [logout]);

  // Register global auth error handler when the component mounts
  (React as any).useEffect(() => {
    setGlobalAuthErrorHandler(handleAuthError);
  }, [handleAuthError]);

  (React as any).useEffect(() => {
    // Prevent multiple simultaneous token checks
    if (isCheckingToken) return;
    
    // Check if user is already logged in
    const token = Cookies.get('token');
    if (token) {
      // Check if token is expired before making API call
      if (isTokenExpired(token)) {
        console.log('🔐 Token is expired, removing it');
        Cookies.remove('token');
        setUser(null);
        setIsLoading(false);
        return;
      }
      
      setIsCheckingToken(true);
      
      // Validate token and get user info
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      fetch(`${backendUrl}/api/auth/profile`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
        .then((res) => {
          if (!res.ok) {
            throw new Error('Token validation failed');
          }
          return res.json();
        })
        .then((data) => {
          setUser(data);
          // Only redirect to dashboard if not already on a specific page
          if (window.location.pathname === '/' || window.location.pathname === '/login') {
            router.push('/dashboard');
          }
        })
        .catch((error) => {
          console.log('🔐 Token validation failed:', error.message);
          Cookies.remove('token');
          setUser(null);
        })
        .finally(() => {
          setIsLoading(false);
          setIsCheckingToken(false);
        });
    } else {
      setIsLoading(false);
    }
  }, []); // Remove all dependencies to run only once

  const login = async (email: string, password: string) => {
    console.log('🔐 Login attempt started');
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    console.log('🔐 Backend URL:', backendUrl);
    console.log('🔐 Full login URL:', `${backendUrl}/api/auth/login`);

    try {
      const response = await fetch(`${backendUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      console.log('🔐 Response status:', response.status);
      console.log('🔐 Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('🔐 Error response:', errorText);
        throw new Error('Invalid credentials');
      }

      const data = await response.json();
      console.log('🔐 Response data:', data);
      console.log('🔐 Login successful, token received:', data.accessToken ? '✅' : '❌');

      Cookies.set('token', data.accessToken, {
        expires: 1, // expires in 1 day (matches JWT expiration)
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      });

      console.log('🔐 Token stored in cookies:', Cookies.get('token') ? '✅' : '❌');

      setUser(data.user);
      router.push('/dashboard');
    } catch (error) {
      console.error('🔐 Fetch error:', error);
      throw error;
    }
  };

  const register = async (email: string, password: string, firstName: string, lastName: string) => {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    const response = await fetch(`${backendUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password, firstName, lastName }),
    });

    if (!response.ok) {
      throw new Error('Registration failed');
    }

    const data = await response.json();
    Cookies.set('token', data.accessToken, {
      expires: 1, // expires in 1 day (matches JWT expiration)
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
    setUser(data.user);
  };

  // For development/testing purposes only - switch between roles
  const setUserRole = (role: User['role']) => {
    if (!user) return;
    setUser({ ...user, role });
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      login, 
      logout, 
      register, 
      isLoading, 
      setUserRole,
      handleAuthError
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = (React as any).useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
