'use client';

import { useAuth } from '@/context/auth-context';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { hasModuleAccess, hasSubmoduleAccess, ModuleName, SubmoduleName } from '@/lib/rbac/permissions';

export default function DashboardPage() {
  const { user } = useAuth();
  const { t } = useTranslation(['navigation', 'common']);

  const stats = [
    { name: t('navigation:dashboard.stats.totalVehicles', 'Total Vehicles'), value: '12' },
    { name: t('navigation:dashboard.stats.activeTrips', 'Active Trips'), value: '3' },
    { name: t('navigation:dashboard.stats.drivers', 'Drivers'), value: '8' },
    { name: t('navigation:dashboard.stats.documents', 'Documents'), value: '156' },
  ];

  const modules = [
    {
      name: t('navigation:dashboard.modules.fleetManagement', 'Fleet Management'),
      description: t('navigation:dashboard.modules.fleetDescription', 'Manage vehicles, service history, insurance and reviews'),
      icon: (
        <svg
          className="h-6 w-6 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
      href: '/fleet',
      module: 'fleet' as ModuleName,
      links: [
        { name: t('fleet:vehicles.title'), href: '/fleet/vehicles', submodule: 'fleet.vehicles' as SubmoduleName },
        { name: t('fleet:service.title'), href: '/fleet?tab=service', submodule: 'fleet.service' as SubmoduleName },
        { name: t('fleet:insurance.title'), href: '/fleet/insurance', submodule: 'fleet.insurance' as SubmoduleName },
      ]
    },
    {
      name: t('navigation:dashboard.modules.driverManagement', 'Driver Management'),
      description: t('navigation:dashboard.modules.driverDescription', 'Manage drivers, assignments and contact information'),
      icon: (
        <svg
          className="h-6 w-6 text-green-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      ),
      href: '/drivers',
      module: 'drivers' as ModuleName,
      links: [
        { name: t('drivers:title'), href: '/drivers/list', submodule: 'drivers.list' as SubmoduleName },
        { name: t('navigation:assignments'), href: '/drivers/assignments', submodule: 'drivers.assignments' as SubmoduleName },
        { name: t('navigation:contacts'), href: '/drivers/contacts', submodule: 'drivers.contacts' as SubmoduleName },
      ]
    },
    {
      name: t('navigation:dashboard.modules.tripManagement', 'Trip Management'),
      description: t('navigation:dashboard.modules.tripDescription', 'Create and manage trips, view trip history and reports'),
      icon: (
        <svg
          className="h-6 w-6 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
          />
        </svg>
      ),
      href: '/trips',
      module: 'trips' as ModuleName,
      links: [
        { name: 'All Trips', href: '/trips/list', submodule: 'trips.list' as SubmoduleName },
        { name: 'Create Trip', href: '/trips/create', submodule: 'trips.create' as SubmoduleName },
        { name: 'Reports', href: '/trips/reports', submodule: 'trips.reports' as SubmoduleName },
      ]
    },
    {
      name: 'Document Management',
      description: 'Store, view and manage documents with Paperless-NGX integration',
      icon: (
        <svg
          className="h-6 w-6 text-amber-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
      href: '/documents',
      module: 'documents' as ModuleName,
      links: [
        { name: 'Browse', href: '/documents/browse', submodule: 'documents.browse' as SubmoduleName },
        { name: 'Upload', href: '/documents/upload', submodule: 'documents.upload' as SubmoduleName },
        { name: 'Search', href: '/documents/search', submodule: 'documents.search' as SubmoduleName },
      ]
    },
  ];

  // Filter quick actions based on user permissions
  const quickActions = [
    { 
      name: 'Add New Vehicle',
      description: 'Register a new vehicle to your fleet',
      href: '/fleet/vehicles/create',
      module: 'fleet' as ModuleName,
      submodule: 'fleet.vehicles' as SubmoduleName
    },
    { 
      name: 'Start New Trip',
      description: 'Create and assign a new trip',
      href: '/trips/create',
      module: 'trips' as ModuleName,
      submodule: 'trips.create' as SubmoduleName
    },
    { 
      name: 'Upload Document',
      description: 'Upload and categorize a new document',
      href: '/documents/upload',
      module: 'documents' as ModuleName,
      submodule: 'documents.upload' as SubmoduleName
    }
  ].filter(action => 
    !action.module || hasModuleAccess(user?.role, action.module)
  ).filter(action => 
    !action.submodule || hasSubmoduleAccess(user?.role, action.submodule)
  );

  return (
    <div>
      <div className="mb-8">
        <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          {t('navigation:dashboard.welcome', 'Welcome back')}, {user?.firstName}!
        </h2>
        <p className="mt-1 text-sm leading-6 text-gray-600">
          {t('navigation:dashboard.subtitle', "Here's what's happening with your fleet today.")}
        </p>
      </div>

      {/* Stats Section */}
      <dl className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item) => (
          <div
            key={item.name}
            className="relative overflow-hidden rounded-lg bg-white px-4 pb-12 pt-5 shadow sm:px-6 sm:pt-6"
          >
            <dt>
              <div className="absolute rounded-md bg-blue-500 p-3">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                  />
                </svg>
              </div>
              <p className="ml-16 truncate text-sm font-medium text-gray-500">
                {item.name}
              </p>
            </dt>
            <dd className="ml-16 flex items-baseline pb-6 sm:pb-7">
              <p className="text-2xl font-semibold text-gray-900">{item.value}</p>
            </dd>
          </div>
        ))}
      </dl>

      {/* Module Cards */}
      <div className="mt-10">
        <h3 className="text-lg font-medium leading-6 text-gray-900 mb-5">{t('navigation:dashboard.mainModules', 'Main Modules')}</h3>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          {modules
            .filter(module => !module.module || hasModuleAccess(user?.role, module.module))
            .map((module) => {
              // Filter links based on submodule permissions
              const accessibleLinks = module.links.filter(link => 
                !link.submodule || hasSubmoduleAccess(user?.role, link.submodule)
              );
              
              // Skip rendering the module if no links are accessible
              if (accessibleLinks.length === 0) return null;
              
              return (
                <div 
                  key={module.name} 
                  className="rounded-lg bg-white overflow-hidden shadow transition-all hover:shadow-md"
                >
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        {module.icon}
                      </div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900">{module.name}</h4>
                        <p className="mt-1 text-sm text-gray-500">
                          {module.description}
                        </p>
                      </div>
                    </div>
                    <div className="mt-6">
                      <div className="flex flex-wrap gap-2">
                        {accessibleLinks.map((link) => (
                          <Link
                            key={link.name}
                            href={link.href}
                            className="inline-flex items-center rounded-md bg-blue-50 px-2.5 py-1.5 text-sm font-medium text-blue-700 hover:bg-blue-100"
                          >
                            {link.name}
                          </Link>
                        ))}
                        <Link
                          href={module.href}
                          className="inline-flex items-center rounded-md px-2.5 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100"
                        >
                          View All
                          <svg 
                            className="ml-1 h-4 w-4" 
                            fill="none" 
                            viewBox="0 0 24 24" 
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>

      {/* Quick Actions */}
      {quickActions.length > 0 && (
        <div className="mt-10">
          <h3 className="text-lg font-medium leading-6 text-gray-900">Quick Actions</h3>
          <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {quickActions.map(action => (
              <Link 
                key={action.name}
                href={action.href} 
                className="rounded-lg bg-white p-4 text-left shadow hover:bg-gray-50"
              >
                <h4 className="text-base font-medium text-gray-900">{action.name}</h4>
                <p className="mt-1 text-sm text-gray-500">{action.description}</p>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
