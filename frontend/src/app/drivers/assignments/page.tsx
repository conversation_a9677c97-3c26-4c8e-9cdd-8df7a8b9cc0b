'use client';

import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { RouteGuard } from '@/lib/rbac';
import { Driver } from '@/types/user';
import { Vehicle } from '@/types/vehicle';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DriverService } from '@/lib/api/driver-service';
import { FleetService } from '@/lib/api/fleet-service';
import { VehicleAssignmentService, Assignment } from '@/lib/api/vehicle-assignment-service';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';

export default function DriverAssignmentsPage() {
  return (
    <RouteGuard submodule="drivers.assignments">
      <DriverAssignmentsContent />
    </RouteGuard>
  );
}

function DriverAssignmentsContent() {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableDrivers, setAvailableDrivers] = useState<Driver[]>([]);
  const [availableVehicles, setAvailableVehicles] = useState<Vehicle[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newAssignment, setNewAssignment] = useState({
    driverId: '',
    vehicleId: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: '',
    notes: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [sortField, setSortField] = useState<string>('startDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const { toast } = useToast();

  // Fetch assignments, drivers, and vehicles
  useEffect(() => {
    const fetchData = async () => {
      console.log('🔄 Starting data fetch in assignments page...');
      setLoading(true);
      try {
        console.log('📢 Fetching drivers...');
        const drivers = await DriverService.getDrivers();
        console.log('👨‍💼 Drivers loaded:', drivers.length);
        
        console.log('📢 Fetching vehicles...');
        const vehicles = await FleetService.getVehicles();
        console.log('🚗 Vehicles loaded:', vehicles.length);
        
        console.log('📢 Fetching assignments...');
        const assignmentsData = await VehicleAssignmentService.getAllAssignments();
        console.log('📋 Assignments loaded:', assignmentsData);
        console.log('📋 Assignments type:', typeof assignmentsData);
        console.log('📋 Assignments is array:', Array.isArray(assignmentsData));
        
        // Ensure we set an array
        const validAssignments = Array.isArray(assignmentsData) ? assignmentsData : [];
        setAssignments(validAssignments);
        setAvailableDrivers(drivers || []);
        setAvailableVehicles((vehicles || []).filter(v => v.status === 'AVAILABLE'));
        console.log('✅ State updated with fetched data, assignments count:', validAssignments.length);
      } catch (error) {
        console.error('❌ Error in data fetching:', error);
        toast({
          title: 'Error',
          description: 'Failed to load data. Please try refreshing the page.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
        console.log('🏁 Data fetching complete');
      }
    };

    fetchData();
  }, [toast]);

  // Filter and sort assignments
  const filteredAndSortedAssignments = useMemo(() => {
    // Ensure assignments is an array before filtering
    if (!Array.isArray(assignments)) {
      console.warn('⚠️ Assignments is not an array:', assignments);
      return [];
    }
    
    // First, filter assignments by status
    let filtered = [...assignments];
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(a => a.status === statusFilter);
    }
    
    // Then sort the filtered assignments
    return filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'startDate':
          comparison = new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
          break;
        case 'endDate':
          // Handle null/undefined endDate values
          if (!a.endDate && !b.endDate) comparison = 0;
          else if (!a.endDate) comparison = 1;
          else if (!b.endDate) comparison = -1;
          else comparison = new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
          break;
        case 'driverName':
          const driverA = a.driver ? `${a.driver.lastName}, ${a.driver.firstName}` : '';
          const driverB = b.driver ? `${b.driver.lastName}, ${b.driver.firstName}` : '';
          comparison = driverA.localeCompare(driverB);
          break;
        case 'vehicleName':
          const vehicleA = a.vehicle ? `${a.vehicle.make} ${a.vehicle.model}` : '';
          const vehicleB = b.vehicle ? `${b.vehicle.make} ${b.vehicle.model}` : '';
          comparison = vehicleA.localeCompare(vehicleB);
          break;
      }
      
      // Apply sort direction
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [assignments, statusFilter, sortField, sortDirection]);

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setNewAssignment({
      ...newAssignment,
      [field]: e.target.value,
    });
  };

  const handleCreateAssignment = async () => {
    if (!newAssignment.driverId || !newAssignment.vehicleId || !newAssignment.startDate) {
      toast({
        title: 'Missing information',
        description: 'Please fill out all required fields',
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    try {
      await VehicleAssignmentService.createAssignment({
        ...newAssignment,
        type: 'REGULAR',
        priority: 'NORMAL',
      });

      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      // Close dialog and reset form
      setIsDialogOpen(false);
      setNewAssignment({
        driverId: '',
        vehicleId: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: '',
        notes: '',
      });

      toast({
        title: 'Success',
        description: 'Vehicle assigned successfully',
      });
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign vehicle',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEndAssignment = async (assignmentId: string) => {
    if (!confirm('Are you sure you want to end this assignment?')) {
      return;
    }

    try {
      await VehicleAssignmentService.completeAssignment(assignmentId);
      
      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      toast({
        title: 'Success',
        description: 'Assignment ended successfully',
      });
    } catch (error) {
      console.error('Error ending assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to end assignment',
        variant: 'destructive',
      });
    }
  };

  const handleCancelAssignment = async (assignmentId: string) => {
    if (!confirm('Are you sure you want to cancel this assignment?')) {
      return;
    }

    try {
      await VehicleAssignmentService.cancelAssignment(assignmentId);
      
      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      toast({
        title: 'Success',
        description: 'Assignment cancelled successfully',
      });
    } catch (error) {
      console.error('Error cancelling assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel assignment',
        variant: 'destructive',
      });
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Vehicle Assignments
        </h1>
        <p className="mt-1 text-sm leading-6 text-gray-600">
          Manage driver and vehicle assignments
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center flex-wrap gap-4">
              <CardTitle>Vehicle Assignments</CardTitle>
              <div className="flex items-center gap-4 flex-wrap">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="statusFilter">Status:</Label>
                  <select
                    id="statusFilter"
                    className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="ALL">All</option>
                    <option value="ACTIVE">Active</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="CANCELLED">Cancelled</option>
                  </select>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="sortField">Sort By:</Label>
                  <select
                    id="sortField"
                    className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm"
                    value={sortField}
                    onChange={(e) => setSortField(e.target.value)}
                  >
                    <option value="startDate">Start Date</option>
                    <option value="endDate">End Date</option>
                    <option value="driverName">Driver Name</option>
                    <option value="vehicleName">Vehicle</option>
                  </select>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                    className="h-8 w-8"
                  >
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </Button>
                </div>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>Create Assignment</Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[525px]">
                    <div className="space-y-2 mb-4">
                      <DialogTitle>Assign Vehicle to Driver</DialogTitle>
                      <DialogDescription>
                        Create a new vehicle assignment. All fields with an asterisk are required.
                      </DialogDescription>
                    </div>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="driver" className="text-right">
                          Driver *
                        </Label>
                        <select
                          id="driver"
                          className="col-span-3 flex h-9 w-full rounded-md border border-input bg-background px-3 py-1"
                          value={newAssignment.driverId}
                          onChange={handleInputChange('driverId')}
                          required
                        >
                          <option value="">Select a driver</option>
                          {availableDrivers.map(driver => (
                            <option key={driver.id} value={driver.id}>
                              {driver.firstName} {driver.lastName}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="vehicle" className="text-right">
                          Vehicle *
                        </Label>
                        <select
                          id="vehicle"
                          className="col-span-3 flex h-9 w-full rounded-md border border-input bg-background px-3 py-1"
                          value={newAssignment.vehicleId}
                          onChange={handleInputChange('vehicleId')}
                          required
                        >
                          <option value="">Select a vehicle</option>
                          {availableVehicles.map(vehicle => (
                            <option key={vehicle.id} value={vehicle.id}>
                              {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="startDate" className="text-right">
                          Start Date *
                        </Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={newAssignment.startDate}
                          onChange={handleInputChange('startDate')}
                          className="col-span-3"
                          required
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="endDate" className="text-right">
                          End Date
                        </Label>
                        <Input
                          id="endDate"
                          type="date"
                          value={newAssignment.endDate}
                          onChange={handleInputChange('endDate')}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="notes" className="text-right">
                          Notes
                        </Label>
                        <Input
                          id="notes"
                          value={newAssignment.notes}
                          onChange={handleInputChange('notes')}
                          className="col-span-3"
                        />
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleCreateAssignment} disabled={submitting}>
                        {submitting ? 'Creating...' : 'Create Assignment'}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : filteredAndSortedAssignments.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-gray-500">
                  {statusFilter === 'ALL' 
                    ? 'No vehicle assignments found' 
                    : `No ${statusFilter.toLowerCase()} assignments found`}
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Driver</TableHead>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedAssignments.map((assignment) => (
                    <TableRow key={assignment.id} className={assignment.status !== 'ACTIVE' ? 'bg-gray-50' : ''}>
                      <TableCell>
                        {assignment.driver ? (
                          <Link href={`/drivers/details/${assignment.driverId}`} className="text-blue-600 hover:underline">
                            {assignment.driver.firstName} {assignment.driver.lastName}
                          </Link>
                        ) : (
                          'Unknown Driver'
                        )}
                      </TableCell>
                      <TableCell>
                        {assignment.vehicle ? (
                          <Link href={`/fleet/vehicles/${assignment.vehicleId}`} className="text-blue-600 hover:underline">
                            {assignment.vehicle.make} {assignment.vehicle.model} ({assignment.vehicle.plateNumber})
                          </Link>
                        ) : (
                          'Unknown Vehicle'
                        )}
                      </TableCell>
                      <TableCell>{new Date(assignment.startDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {assignment.endDate 
                          ? new Date(assignment.endDate).toLocaleDateString()
                          : 'Current'
                        }
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${
                          assignment.status === 'ACTIVE' 
                            ? 'bg-green-100 text-green-800' 
                            : assignment.status === 'COMPLETED' 
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                        }`}>
                          {assignment.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {assignment.status === 'ACTIVE' && (
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleEndAssignment(assignment.id)}
                            >
                              End Assignment
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleCancelAssignment(assignment.id)}
                              className="text-red-500 hover:text-red-700 border-red-200 hover:bg-red-50"
                            >
                              Cancel
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
