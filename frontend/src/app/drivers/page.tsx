'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RouteGuard } from '@/lib/rbac';
import { DriversDashboard } from '@/components/drivers/DriversDashboard';
import { DriversListContent } from '@/components/drivers/DriversListContent';
import { DriversAssignmentsContent } from '@/components/drivers/DriversAssignmentsContent';
import { DriversContactsContent } from '@/components/drivers/DriversContactsContent';
import { DriverDetailContent } from '@/components/drivers/DriverDetailContent';

export default function DriversManagementPage() {
  return (
    <RouteGuard module="drivers">
      <DriversContent />
    </RouteGuard>
  );
}

function DriversContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab and driver ID from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';
  const driverId = searchParams.get('driverId');

  const handleDriverUpdated = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleAddDriver = () => {
    // Navigate to drivers list tab where add functionality would be
    router.push('/drivers?tab=list');
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <DriversDashboard />}
        {activeTab === 'list' && !driverId && (
          <DriversListContent
            onDriverUpdated={handleDriverUpdated}
          />
        )}
        {activeTab === 'list' && driverId && (
          <DriverDetailContent
            driverId={driverId}
            onBack={() => router.push('/drivers?tab=list')}
            onDriverUpdated={handleDriverUpdated}
          />
        )}
        {activeTab === 'assignments' && (
          <DriversAssignmentsContent
            onAssignmentUpdated={handleDriverUpdated}
          />
        )}
        {activeTab === 'contacts' && (
          <DriversContactsContent
            onContactUpdated={handleDriverUpdated}
          />
        )}
      </div>
    </div>
  );
}
