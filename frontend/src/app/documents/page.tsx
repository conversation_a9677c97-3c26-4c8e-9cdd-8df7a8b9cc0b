'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { RouteGuard } from '@/lib/rbac';
import { DocumentsDashboard } from '@/components/documents/DocumentsDashboard';
import { DocumentsBrowseContent } from '@/components/documents/DocumentsBrowseContent';
import { DocumentsUploadContent } from '@/components/documents/DocumentsUploadContent';
import { DocumentsSearchContent } from '@/components/documents/DocumentsSearchContent';

export default function DocumentsManagementPage() {
  return (
    <RouteGuard module="documents">
      <DocumentsContent />
    </RouteGuard>
  );
}

function DocumentsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';

  const handleDocumentUpdated = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <DocumentsDashboard />}
        {activeTab === 'browse' && (
          <DocumentsBrowseContent
            onDocumentUpdated={handleDocumentUpdated}
            refreshTrigger={refreshTrigger}
          />
        )}
        {activeTab === 'upload' && (
          <DocumentsUploadContent
            onDocumentUploaded={handleDocumentUpdated}
          />
        )}
        {activeTab === 'search' && (
          <DocumentsSearchContent
            refreshTrigger={refreshTrigger}
          />
        )}
      </div>
    </div>
  );
}
