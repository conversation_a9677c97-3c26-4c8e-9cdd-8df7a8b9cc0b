'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RouteGuard } from '@/lib/rbac';
import { TripsDashboard } from '@/components/trips/TripsDashboard';
import { TripsListContent } from '@/components/trips/TripsListContent';
import { TripsCreateContent } from '@/components/trips/TripsCreateContent';
import { TripsReportsContent } from '@/components/trips/TripsReportsContent';

export default function TripsManagementPage() {
  return (
    <RouteGuard module="trips">
      <TripsContent />
    </RouteGuard>
  );
}

function TripsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';

  const handleTripCreated = () => {
    setRefreshTrigger(prev => prev + 1);
    // Switch to trips list tab to show the new trip
    router.push('/trips?tab=list');
  };

  const handleCreateTrip = () => {
    router.push('/trips?tab=create');
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <TripsDashboard />}
        {activeTab === 'list' && (
          <TripsListContent
            onTripUpdated={() => setRefreshTrigger(prev => prev + 1)}
          />
        )}
        {activeTab === 'create' && (
          <TripsCreateContent
            onTripCreated={handleTripCreated}
          />
        )}
        {activeTab === 'reports' && <TripsReportsContent />}
      </div>
    </div>
  );
}
