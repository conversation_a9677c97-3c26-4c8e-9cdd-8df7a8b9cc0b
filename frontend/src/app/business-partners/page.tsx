'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RouteGuard } from '@/lib/rbac';
import { BusinessPartnersDashboard } from '@/components/business-partners/BusinessPartnersDashboard';
import { BusinessPartnersManagement } from '@/components/business-partners/business-partners-management';
import { BusinessPartnersShippers } from '@/components/business-partners/BusinessPartnersShippers';
import { BusinessPartnersLogistics } from '@/components/business-partners/BusinessPartnersLogistics';
import { AddBusinessPartnerDialog } from '@/components/business-partners/add-business-partner-dialog';

export default function BusinessPartnersPage() {
  return (
    <RouteGuard module="business-partners">
      <BusinessPartnersContent />
    </RouteGuard>
  );
}

function BusinessPartnersContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [showAddForm, setShowAddForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';

  const handlePartnerAdded = () => {
    setShowAddForm(false);
    setRefreshTrigger(prev => prev + 1);
    // Switch to all partners tab to show the new partner
    router.push('/business-partners?tab=all');
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <BusinessPartnersDashboard />}
        {activeTab === 'all' && (
          <BusinessPartnersManagement
            onPartnerUpdated={() => setRefreshTrigger(prev => prev + 1)}
          />
        )}
        {activeTab === 'shippers' && (
          <BusinessPartnersShippers
            onPartnerUpdated={() => setRefreshTrigger(prev => prev + 1)}
          />
        )}
        {activeTab === 'logistics' && (
          <BusinessPartnersLogistics
            onPartnerUpdated={() => setRefreshTrigger(prev => prev + 1)}
          />
        )}
      </div>

      {/* Add Partner Form Dialog */}
      {showAddForm && (
        <AddBusinessPartnerDialog
          open={showAddForm}
          onOpenChange={setShowAddForm}
          onPartnerAdded={handlePartnerAdded}
        />
      )}
    </div>
  );
}
