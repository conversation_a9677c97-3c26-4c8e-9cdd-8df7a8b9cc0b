'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { RouteGuard } from '@/lib/rbac';
import { InsuranceService } from '@/lib/api/insurance-service';
import { FleetService } from '@/lib/api/fleet-service';
import { InsuranceType } from '@/types/insurance';
import { useToast } from '@/components/ui/use-toast';

export default function AddInsurancePolicy() {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    vehicleId: '',
    policyNumber: '',
    provider: '',
    type: 'COMPREHENSIVE' as InsuranceType,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
    premium: '',
    coverage: '',
    deductible: ''
  });

  useEffect(() => {
    async function fetchVehicles() {
      try {
        const vehiclesData = await FleetService.getVehicles();
        setVehicles(vehiclesData);
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicles. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    fetchVehicles();
  }, [toast]);

  const handleChange = (e: any) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };
  
  const handleSubmit = async (e: any) => {
    e.preventDefault();
    
    if (!formData.vehicleId) {
      toast({
        title: 'Error',
        description: 'Please select a vehicle.',
        variant: 'destructive',
      });
      return;
    }
    
    if (!formData.policyNumber || !formData.provider) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const policyData = {
        vehicleId: formData.vehicleId,
        policyNumber: formData.policyNumber,
        provider: formData.provider,
        type: formData.type,
        startDate: formData.startDate,
        endDate: formData.endDate,
        premium: parseFloat(formData.premium) || 0,
        coverage: parseFloat(formData.coverage) || 0,
        deductible: parseFloat(formData.deductible) || 0,
      };
      
      await InsuranceService.createPolicy(policyData);
      
      toast({
        title: t('common:success'),
        description: t('fleet:messages.policyCreatedSuccess'),
      });

      router.push('/fleet/insurance');
    } catch (error) {
      console.error('Error creating policy:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToCreatePolicy'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <RouteGuard submodule="fleet.insurance">
      <div>
        <div className="mb-6">
          <Link href="/fleet/insurance" className="text-sm text-blue-600 hover:text-blue-800">
            Back to Insurance Policies
          </Link>
          <h1 className="mt-2 text-2xl font-bold">Add Insurance Policy</h1>
          <p className="text-sm text-gray-600">
            Create a new insurance policy for a vehicle
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          {loading ? (
            <div className="text-center py-4">Loading vehicles...</div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <label htmlFor="vehicleId" className="block text-sm font-medium text-gray-700">Vehicle *</label>
                  <select 
                    id="vehicleId"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value={formData.vehicleId}
                    onChange={handleChange}
                    required
                  >
                    <option value="">Select a vehicle</option>
                    {vehicles.map((vehicle) => (
                      <option key={vehicle.id} value={vehicle.id}>
                        {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="policyNumber" className="block text-sm font-medium text-gray-700">Policy Number *</label>
                    <input 
                      id="policyNumber"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Enter policy number" 
                      value={formData.policyNumber}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="provider" className="block text-sm font-medium text-gray-700">Provider *</label>
                    <input 
                      id="provider"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Insurance provider" 
                      value={formData.provider}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">Insurance Type</label>
                  <select 
                    id="type"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value={formData.type}
                    onChange={handleChange}
                  >
                    <option value="COMPREHENSIVE">Comprehensive</option>
                    <option value="THIRD_PARTY">Third Party</option>
                    <option value="FIRE_THEFT">Fire & Theft</option>
                    <option value="LIABILITY">Liability</option>
                  </select>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">Start Date</label>
                    <input 
                      id="startDate"
                      type="date"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value={formData.startDate}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">End Date</label>
                    <input 
                      id="endDate"
                      type="date"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value={formData.endDate}
                      onChange={handleChange}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="premium" className="block text-sm font-medium text-gray-700">Premium</label>
                    <input 
                      id="premium"
                      type="number"
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="0.00" 
                      value={formData.premium}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="coverage" className="block text-sm font-medium text-gray-700">Coverage Amount</label>
                    <input 
                      id="coverage"
                      type="number"
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="0.00" 
                      value={formData.coverage}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="deductible" className="block text-sm font-medium text-gray-700">Deductible Amount</label>
                    <input 
                      id="deductible"
                      type="number"
                      step="0.01"
                      min="0"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="0.00" 
                      value={formData.deductible}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end gap-4 mt-4">
                <button 
                  type="button"
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onClick={() => router.push('/fleet/insurance')}
                >
                  Cancel
                </button>
                <button 
                  type="submit"
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save Policy'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </RouteGuard>
  );
}