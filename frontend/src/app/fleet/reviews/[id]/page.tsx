'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ReviewService } from '@/lib/api/review-service';
import { RouteGuard } from '@/lib/rbac';
import { formatDate } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Dialog } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { VehicleReview } from '@/types/review';

export default function ReviewDetailsPage() {
  const params = useParams();
  const reviewId = params.id as string;
  const router = useRouter();
  const { toast } = useToast();
  const [review, setReview] = useState<VehicleReview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmDelete, setConfirmDelete] = useState(false);

  useEffect(() => {
    const fetchReviewDetails = async () => {
      try {
        const reviewService = new ReviewService();
        const reviewData = await reviewService.getReviewById(reviewId);
        setReview(reviewData);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load review details',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchReviewDetails();
  }, [reviewId, toast]);

  const handleDelete = async () => {
    try {
      const reviewService = new ReviewService();
      await reviewService.deleteReview(reviewId);
      toast({
        title: 'Success',
        description: 'Review deleted successfully',
      });
      router.push('/fleet/reviews');
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete review',
      });
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }

  if (!review) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Review Not Found</h2>
              <p className="mb-4">The review you're looking for does not exist or has been deleted.</p>
              <Button onClick={() => router.push('/fleet/reviews')}>
                Back to Reviews
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Rating to display as stars
  const renderRating = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <span key={i} className={`text-xl ${i < rating ? 'text-yellow-500' : 'text-gray-300'}`}>★</span>
    ));
  };

  return (
    <RouteGuard submodule="fleet.reviews">
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex justify-between">
              <div>Vehicle Review Details</div>
              <Badge className={
                review.type === 'SCHEDULED' ? 'bg-blue-500' :
                review.type === 'INCIDENT' ? 'bg-red-500' : 
                'bg-yellow-500'
              }>
                {review.type}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">Vehicle Information</h3>
                <p className="text-sm text-gray-500">
                  {review.vehicleInfo?.make} {review.vehicleInfo?.model} ({review.vehicleInfo?.year})
                </p>
                <p className="text-sm text-gray-500">VIN: {review.vehicleInfo?.vin}</p>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium">Review Information</h3>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <p className="text-sm font-medium">Review Date</p>
                    <p className="text-sm text-gray-500">{review.date ? formatDate(review.date) : 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Reviewer</p>
                    <p className="text-sm text-gray-500">{review.reviewer}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Overall Rating</p>
                    <div className="text-sm">{review.rating ? renderRating(review.rating) : 'N/A'}</div>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Mileage</p>
                    <p className="text-sm text-gray-500">{review.mileage} miles</p>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium">Comments</h3>
                <p className="text-sm text-gray-500 mt-2 whitespace-pre-line">{review.comments}</p>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium">Action Items</h3>
                {review.actionItems && review.actionItems.length > 0 ? (
                  <ul className="list-disc pl-5 mt-2">
                    {review.actionItems.map((item: string, index: number) => (
                      <li key={index} className="text-sm text-gray-500">{item}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500">No action items</p>
                )}
              </div>
              
              <div className="flex justify-between mt-8">
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/fleet/reviews')}
                >
                  Back
                </Button>
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => router.push(`/fleet/reviews/${reviewId}/edit`)}
                  >
                    Edit
                  </Button>
                  <Button 
                    variant="destructive" 
                    onClick={() => setConfirmDelete(true)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
            <h2 className="text-xl font-semibold mb-4">Confirm Delete</h2>
            <p className="mb-6">Are you sure you want to delete this review? This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setConfirmDelete(false)}>Cancel</Button>
              <Button variant="destructive" onClick={handleDelete}>Delete</Button>
            </div>
          </div>
        </Dialog>
      </div>
    </RouteGuard>
  );
}
