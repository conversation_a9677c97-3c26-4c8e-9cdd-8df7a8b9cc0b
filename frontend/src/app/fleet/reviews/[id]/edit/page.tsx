'use client';

import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ReviewService } from '@/lib/api/review-service';
import { RouteGuard } from '@/lib/rbac';
import { VehicleReview, ReviewStatus } from '@/types/review';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';

const reviewFormSchema = z.object({
  vehicleId: z.string().min(1, 'Vehicle is required'),
  date: z.date(),
  type: z.enum(['SCHEDULED', 'UNSCHEDULED', 'INCIDENT']),
  reviewer: z.string().min(3, 'Reviewer name is required'),
  mileage: z.coerce.number().min(0, 'Mileage must be a non-negative number'),
  rating: z.coerce.number().min(1).max(5),
  comments: z.string().min(10, 'Comments should be at least 10 characters long'),
  actionItems: z.string().optional(),
});

export default function EditReviewPage() {
  const params = useParams();
  const reviewId = params.id as string;
  const router = useRouter();
  const { toast } = useToast();
  const [review, setReview] = useState<VehicleReview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  const form = useForm<z.infer<typeof reviewFormSchema>>({
    resolver: zodResolver(reviewFormSchema),
    defaultValues: {
      vehicleId: '',
      date: new Date(),
      type: 'SCHEDULED',
      reviewer: '',
      mileage: 0,
      rating: 3,
      comments: '',
      actionItems: '',
    },
  });

  useEffect(() => {
    const fetchReviewDetails = async () => {
      try {
        const reviewService = new ReviewService();
        const reviewData = await reviewService.getReviewById(reviewId);
        setReview(reviewData);
        
        // Set form values
        if (reviewData) {
          form.reset({
            vehicleId: reviewData.vehicleId,
            date: reviewData.date ? new Date(reviewData.date) : new Date(),
            type: reviewData.type as any,
            reviewer: reviewData.reviewer,
            mileage: reviewData.mileage,
            rating: reviewData.rating,
            comments: reviewData.comments,
            actionItems: reviewData.actionItems ? reviewData.actionItems.join('\n') : '',
          });
        }
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load review details',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchReviewDetails();
  }, [reviewId, toast, form]);

  const onSubmit = async (values: z.infer<typeof reviewFormSchema>) => {
    setIsSaving(true);
    try {
      const reviewService = new ReviewService();
      
      // Process action items from text to array
      const actionItemsArray = values.actionItems 
        ? values.actionItems.split('\n').filter(item => item.trim() !== '') 
        : [];
      
      await reviewService.updateReview(reviewId, {
        findings: values.comments,
        recommendations: values.actionItems,
        reviewBy: values.reviewer,
        status: 'COMPLETED' as ReviewStatus,
        // Filter out properties that don't match the API
        // actionItems is not part of the API contract
      });
      
      toast({
        title: 'Success',
        description: 'Review updated successfully',
      });
      
      router.push(`/fleet/reviews/${reviewId}`);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update review',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }

  if (!review) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Review Not Found</h2>
              <p className="mb-4">The review you're looking for does not exist or has been deleted.</p>
              <Button onClick={() => router.push('/fleet/reviews')}>
                Back to Reviews
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <RouteGuard submodule="fleet.reviews">
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Edit Vehicle Review</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Review Date</FormLabel>
                        <FormControl>
                          <DatePicker 
                            date={field.value} 
                            setDate={field.onChange} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Review Type</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select review type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                            <SelectItem value="UNSCHEDULED">Unscheduled</SelectItem>
                            <SelectItem value="INCIDENT">Incident</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="reviewer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reviewer</FormLabel>
                        <FormControl>
                          <Input placeholder="Reviewer name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="mileage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mileage</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="Current mileage" 
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="rating"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rating (1-5)</FormLabel>
                        <Select 
                          onValueChange={(value: string) => field.onChange(parseInt(value))} 
                          defaultValue={field.value.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select rating" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="1">1 - Poor</SelectItem>
                            <SelectItem value="2">2 - Fair</SelectItem>
                            <SelectItem value="3">3 - Average</SelectItem>
                            <SelectItem value="4">4 - Good</SelectItem>
                            <SelectItem value="5">5 - Excellent</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="comments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comments</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter review comments" 
                          rows={5}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="actionItems"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Action Items (one per line)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter action items (one per line)" 
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-between mt-8">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => router.push(`/fleet/reviews/${reviewId}`)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </RouteGuard>
  );
}
