'use client';

import { RouteGuard } from '@/lib/rbac';
import { EnhancedMaintenanceDashboard } from '@/components/maintenance/enhanced-maintenance-dashboard';

export default function MaintenanceDashboardPage() {
  return (
    <RouteGuard submodule="fleet.service">
      <MaintenanceDashboardContent />
    </RouteGuard>
  );
}

function MaintenanceDashboardContent() {
  return (
    <div className="space-y-6">
      <EnhancedMaintenanceDashboard />
    </div>
  );
}
