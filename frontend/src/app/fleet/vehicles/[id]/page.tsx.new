'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { RouteGuard } from '@/lib/rbac';
import { Vehicle } from '@/types/vehicle';
import { InsurancePolicy } from '@/types/insurance';
import { VehicleReview } from '@/types/review';
import { MaintenanceLog } from '@/types/maintenance';
import { FleetService } from '@/lib/api/fleet-service';
import { InsuranceService } from '@/lib/api/insurance-service';
import { ReviewService } from '@/lib/api/review-service';
import { VehicleAssignmentService } from '@/lib/api/vehicle-assignment-service';
const reviewService = new ReviewService();
import { formatDate, formatCurrency } from '@/lib/utils';

export default function VehicleDetailPage() {
  return (
    <RouteGuard submodule="fleet.vehicles">
      <VehicleDetailContent />
    </RouteGuard>
  );
}

function VehicleDetailContent() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const vehicleId = Array.isArray(params.id) ? params.id[0] : params.id;
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [policies, setPolicies] = useState<InsurancePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingPolicies, setLoadingPolicies] = useState(true);
  const [reviews, setReviews] = useState<VehicleReview[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [maintenanceLogs, setMaintenanceLogs] = useState<MaintenanceLog[]>([]);
  const [loadingMaintenance, setLoadingMaintenance] = useState(true);
  const [assignments, setAssignments] = useState<any[]>([]);
  const [loadingAssignments, setLoadingAssignments] = useState(true);

  useEffect(() => {
    async function fetchVehicleDetails() {
      try {
        const data = await FleetService.getVehicleById(vehicleId);
        setVehicle(data);
      } catch (error) {
        console.error('Error fetching vehicle details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicle details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    async function fetchInsurancePolicies() {
      try {
        setLoadingPolicies(true);
        const policiesData = await InsuranceService.getPolicies({vehicleId});
        setPolicies(policiesData);
      } catch (error) {
        console.error('Error fetching insurance policies:', error);
        toast({
          title: 'Error',
          description: 'Failed to load insurance policies for this vehicle.',
          variant: 'destructive',
        });
      } finally {
        setLoadingPolicies(false);
      }
    }
    
    async function fetchVehicleReviews() {
      try {
        setLoadingReviews(true);
        const reviewsData = await reviewService.getVehicleReviews(vehicleId);
        setReviews(reviewsData);
      } catch (error) {
        console.error('Error fetching vehicle reviews:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicle reviews.',
          variant: 'destructive',
        });
      } finally {
        setLoadingReviews(false);
      }
    }

    async function fetchMaintenanceLogs() {
      try {
        setLoadingMaintenance(true);
        const logsData = await FleetService.getVehicleMaintenanceLogs(vehicleId);
        setMaintenanceLogs(logsData);
      } catch (error) {
        console.error('Error fetching maintenance logs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load maintenance logs.',
          variant: 'destructive',
        });
      } finally {
        setLoadingMaintenance(false);
      }
    }

    async function fetchVehicleAssignments() {
      try {
        setLoadingAssignments(true);
        const assignmentsData = await VehicleAssignmentService.getVehicleAssignments(vehicleId);
        setAssignments(assignmentsData);
      } catch (error) {
        console.error('Error fetching vehicle assignments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicle assignments.',
          variant: 'destructive',
        });
      } finally {
        setLoadingAssignments(false);
      }
    }

    if (vehicleId) {
      fetchVehicleDetails();
      fetchInsurancePolicies();
      fetchVehicleReviews();
      fetchMaintenanceLogs();
      fetchVehicleAssignments();
    }
  }, [vehicleId, toast]);

  const getReviewStatusBadge = (status: string) => {
    switch(status) {
      case 'SCHEDULED':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Scheduled</Badge>;
      case 'IN_PROGRESS':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">In Progress</Badge>;
      case 'COMPLETED':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Completed</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getMaintenanceStatusBadge = (status: string) => {
    switch(status) {
      case 'SCHEDULED':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Scheduled</Badge>;
      case 'IN_PROGRESS':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">In Progress</Badge>;
      case 'COMPLETED':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Completed</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getAssignmentStatusBadge = (status: string) => {
    switch(status) {
      case 'ACTIVE':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Active</Badge>;
      case 'COMPLETED':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Completed</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return <div className="text-center py-10">Loading vehicle details...</div>;
  }

  if (!vehicle) {
    return (
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <h3 className="text-lg font-medium text-gray-900">Vehicle not found</h3>
        <p className="mt-2 text-sm text-gray-500">
          The vehicle you're looking for doesn't exist or has been removed.
        </p>
        <div className="mt-6">
          <Button onClick={() => router.push('/fleet/vehicles')}>
            Back to Vehicles
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link href="/fleet/vehicles" className="text-sm text-blue-600 hover:text-blue-800">
            ← Back to Vehicles
          </Link>
          <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:tracking-tight">
            {vehicle.make} {vehicle.model} ({vehicle.year})
          </h1>
          <p className="text-sm text-gray-600">
            Plate Number: <span className="font-medium">{vehicle.plateNumber}</span>
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline"
            onClick={() => router.push(`/fleet/vehicles/${vehicleId}/edit`)}
          >
            Edit
          </Button>
          <Button 
            variant="outline"
            onClick={() => router.push(`/fleet/vehicles/${vehicleId}/add`)}
          >
            Add Related Vehicle
          </Button>
          <Button variant="destructive">Decommission</Button>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="service">Service History</TabsTrigger>
          <TabsTrigger value="insurance">Insurance</TabsTrigger>
          <TabsTrigger value="reviews">Reviews & Inspections</TabsTrigger>
          <TabsTrigger value="assignments">Driver Assignments</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Vehicle Information</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">Complete details and specifications.</p>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Make</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.make}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Model</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.model}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Year</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.year}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">VIN</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.vin || 'Not available'}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 sm:mt-0 sm:col-span-2">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      {vehicle.status || 'Available'}
                    </span>
                  </dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Mileage</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {vehicle.mileage ? `${vehicle.mileage.toLocaleString()} km` : 'Not available'}
                  </dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Fuel Type</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.fuelType || 'Not specified'}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Color</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.color || 'Not specified'}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Purchase Date</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {vehicle.purchaseDate ? formatDate(vehicle.purchaseDate) : 'Not available'}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="service">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium leading-6 text-gray-900">Service History</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Maintenance records and service logs.</p>
              </div>
              <Button onClick={() => router.push(`/fleet/service/add?vehicleId=${vehicleId}`)}>
                Add Service Record
              </Button>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              {loadingMaintenance ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-4">
                      <Skeleton className="h-4 w-48 mb-2" />
                      <Skeleton className="h-3 w-32 mb-1" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                  ))}
                </div>
              ) : maintenanceLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="mb-4">No service records found for this vehicle.</p>
                  <Button onClick={() => router.push(`/fleet/service/add?vehicleId=${vehicleId}`)}>
                    Add First Service Record
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {maintenanceLogs.map((log: any) => (
                    <div key={log.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-900">{log.type}</h4>
                          <p className="text-sm text-gray-600">{log.description}</p>
                          <p className="text-sm text-gray-500">
                            Scheduled: {formatDate(log.scheduledDate)}
                          </p>
                          {log.date && (
                            <p className="text-sm text-gray-500">
                              Completed: {formatDate(log.date)}
                            </p>
                          )}
                          {log.cost && (
                            <p className="text-sm text-gray-500">
                              Cost: {formatCurrency(log.cost)}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {getMaintenanceStatusBadge(log.status)}
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.push(`/fleet/service/${log.id}`)}
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="insurance">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Insurance Information</h3>
              <Button onClick={() => router.push(`/fleet/insurance/add?vehicleId=${vehicleId}`)}>
                Add Insurance Policy
              </Button>
            </div>
            {loadingPolicies ? (
              <div className="space-y-4">
                {[...Array(2)].map((_, i) => (
                  <div key={i} className="border rounded-lg p-4">
                    <Skeleton className="h-4 w-48 mb-2" />
                    <Skeleton className="h-3 w-32 mb-1" />
                    <Skeleton className="h-3 w-40" />
                  </div>
                ))}
              </div>
            ) : policies.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="mb-4">No insurance policies found for this vehicle.</p>
                <Button onClick={() => router.push(`/fleet/insurance/add?vehicleId=${vehicleId}`)}>
                  Add Insurance Policy
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {policies.map((policy) => (
                  <div key={policy.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900">{policy.provider}</h4>
                        <p className="text-sm text-gray-500">Policy: {policy.policyNumber}</p>
                        <p className="text-sm text-gray-500">
                          Coverage: {formatCurrency(policy.coverage)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Expires: {formatDate(policy.endDate)}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/fleet/insurance/${policy.id}`)}
                        >
                          View
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/fleet/insurance/edit/${policy.id}`)}
                        >
                          Edit
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="reviews">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium leading-6 text-gray-900">Technical Inspections & Reviews</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Scheduled and completed vehicle inspections
                </p>
              </div>
              <Button onClick={() => router.push(`/fleet/reviews/add?vehicleId=${vehicleId}`)}>
                Schedule Review
              </Button>
            </div>
            
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              {loadingReviews ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-4">
                      <Skeleton className="h-4 w-48 mb-2" />
                      <Skeleton className="h-3 w-32 mb-1" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                  ))}
                </div>
              ) : reviews.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="mb-4">No reviews scheduled or completed for this vehicle.</p>
                  <Button onClick={() => router.push(`/fleet/reviews/add?vehicleId=${vehicleId}`)}>
                    Schedule First Review
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review: any) => (
                    <div key={review.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-900">{review.type || review.reviewType}</h4>
                          <p className="text-sm text-gray-500">
                            Scheduled: {formatDate(review.scheduledDate)}
                          </p>
                          {review.completedDate && (
                            <p className="text-sm text-gray-500">
                              Completed: {formatDate(review.completedDate)}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {getReviewStatusBadge(review.status)}
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.push(`/fleet/reviews/${review.id}`)}
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="assignments">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Driver Assignments</h3>
              <Button onClick={() => router.push(`/drivers/assignments?vehicleId=${vehicleId}`)}>
                Assign Driver
              </Button>
            </div>
            
            {loadingAssignments ? (
              <div className="space-y-4">
                {[...Array(2)].map((_, i) => (
                  <div key={i} className="border rounded-lg p-4">
                    <Skeleton className="h-4 w-48 mb-2" />
                    <Skeleton className="h-3 w-32 mb-1" />
                    <Skeleton className="h-3 w-40" />
                  </div>
                ))}
              </div>
            ) : assignments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p className="mb-4">No driver assignments found for this vehicle.</p>
                <Button onClick={() => router.push(`/drivers/assignments?vehicleId=${vehicleId}`)}>
                  Assign First Driver
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {assignments.map((assignment: any) => (
                  <div key={assignment.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {assignment.driver?.firstName} {assignment.driver?.lastName}
                        </h4>
                        <p className="text-sm text-gray-500">
                          Email: {assignment.driver?.email}
                        </p>
                        <p className="text-sm text-gray-500">
                          Start Date: {formatDate(assignment.startDate)}
                        </p>
                        {assignment.endDate && (
                          <p className="text-sm text-gray-500">
                            End Date: {formatDate(assignment.endDate)}
                          </p>
                        )}
                        {assignment.notes && (
                          <p className="text-sm text-gray-600 mt-2">
                            Notes: {assignment.notes}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {getAssignmentStatusBadge(assignment.status)}
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/drivers/details/${assignment.driverId}`)}
                        >
                          View Driver
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
