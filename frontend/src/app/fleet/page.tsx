'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RouteGuard } from '@/lib/rbac';
import { FleetDashboard } from '@/components/fleet/FleetDashboard';
import { FleetVehiclesContent } from '@/components/fleet/FleetVehiclesContent';
import { FleetVehicleDetail } from '@/components/fleet/FleetVehicleDetail';
import { FleetServiceContent } from '@/components/fleet/FleetServiceContent';
import { FleetServiceDetail } from '@/components/fleet/FleetServiceDetail';
import { FleetInsuranceContent } from '@/components/fleet/FleetInsuranceContent';
import { FleetInsuranceDetail } from '@/components/fleet/FleetInsuranceDetail';
import { FleetReviewsContent } from '@/components/fleet/FleetReviewsContent';
import { FleetReviewDetail } from '@/components/fleet/FleetReviewDetail';

export default function FleetManagementPage() {
  return (
    <RouteGuard module="fleet">
      <FleetContent />
    </RouteGuard>
  );
}

function FleetContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab, vehicle ID, service ID, policy ID, and review ID from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';
  const vehicleId = searchParams.get('vehicleId');
  const serviceId = searchParams.get('serviceId');
  const policyId = searchParams.get('policyId');
  const reviewId = searchParams.get('reviewId');

  const handleFleetUpdated = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleAddServiceRecord = () => {
    console.log('Add service record within fleet module');
    // This will be handled by the FleetServiceContent component
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <FleetDashboard />}
        {activeTab === 'vehicles' && !vehicleId && (
          <FleetVehiclesContent
            onVehicleUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'vehicles' && vehicleId && (
          <FleetVehicleDetail
            vehicleId={vehicleId}
            onBack={() => router.push('/fleet?tab=vehicles')}
            onVehicleUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'service' && !serviceId && (
          <FleetServiceContent
            onServiceUpdated={handleFleetUpdated}
            onAddRecord={handleAddServiceRecord}
          />
        )}
        {activeTab === 'service' && serviceId && (
          <FleetServiceDetail
            serviceId={serviceId}
            onBack={() => router.push('/fleet?tab=service')}
            onServiceUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'insurance' && !policyId && (
          <FleetInsuranceContent
            onInsuranceUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'insurance' && policyId && (
          <FleetInsuranceDetail
            policyId={policyId}
            onBack={() => router.push('/fleet?tab=insurance')}
            onInsuranceUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'reviews' && !reviewId && (
          <FleetReviewsContent
            onReviewUpdated={handleFleetUpdated}
          />
        )}
        {activeTab === 'reviews' && reviewId && (
          <FleetReviewDetail
            reviewId={reviewId}
            onBack={() => router.push('/fleet?tab=reviews')}
            onReviewUpdated={handleFleetUpdated}
          />
        )}
      </div>
    </div>
  );
}
