'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import Cookies from 'js-cookie';

interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  status: 'AVAILABLE' | 'ASSIGNED' | 'MAINTENANCE' | 'OUT_OF_SERVICE';
  lastMaintenance: string | null;
  assignments: Array<{
    driver: {
      firstName: string;
      lastName: string;
    };
  }>;
}

export default function VehiclesPage() {
  const { user } = useAuth();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const token = Cookies.get('token');
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
        const response = await fetch(`${backendUrl}/api/vehicles`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch vehicles');
        const data = await response.json();
        setVehicles(data);
      } catch (err) {
        setError('Failed to load vehicles');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchVehicles();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Vehicles</h1>
        {(user?.role === 'ADMIN' || user?.role === 'MANAGER') && (
          <Button>Add Vehicle</Button>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {vehicles.map((vehicle) => (
          <div key={vehicle.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">
                  {vehicle.make} {vehicle.model}
                </h3>
                <p className="text-sm text-gray-500">Year: {vehicle.year}</p>
                <p className="text-sm text-gray-500">
                  Plate: {vehicle.plateNumber}
                </p>
              </div>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  vehicle.status === 'AVAILABLE'
                    ? 'bg-green-100 text-green-800'
                    : vehicle.status === 'ASSIGNED'
                    ? 'bg-blue-100 text-blue-800'
                    : vehicle.status === 'MAINTENANCE'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {vehicle.status}
              </span>
            </div>
            {vehicle.assignments?.[0]?.driver && (
              <div className="mt-4 pt-4 border-t">
                <p className="text-sm text-gray-500">
                  Assigned to:{' '}
                  {`${vehicle.assignments[0].driver.firstName} ${vehicle.assignments[0].driver.lastName}`}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
