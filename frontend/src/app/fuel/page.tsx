'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { RouteGuard } from '@/lib/rbac';
import { FuelDashboard } from '@/components/fuel/FuelDashboard';
import { FuelRecordsList } from '@/components/fuel/FuelRecordsList';
import { FuelPriceMonitor } from '@/components/fuel/FuelPriceMonitor';
import { FuelAnalytics } from '@/components/fuel/FuelAnalytics';
import { FuelRecordForm } from '@/components/fuel/FuelRecordForm';

export default function FuelManagementPage() {
  return (
    <RouteGuard module="fuel">
      <FuelContent />
    </RouteGuard>
  );
}

function FuelContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [showAddForm, setShowAddForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Get active tab from URL params
  const activeTab = searchParams.get('tab') || 'dashboard';

  const handleRecordAdded = () => {
    setShowAddForm(false);
    setRefreshTrigger(prev => prev + 1);
    // Switch to records tab to show the new record
    router.push('/fuel?tab=records');
  };

  return (
    <div className="space-y-6">
      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'dashboard' && <FuelDashboard />}
        {activeTab === 'records' && (
          <FuelRecordsList
            onRecordUpdated={() => setRefreshTrigger(prev => prev + 1)}
            onAddRecord={() => setShowAddForm(true)}
          />
        )}
        {activeTab === 'prices' && <FuelPriceMonitor />}
        {activeTab === 'analytics' && <FuelAnalytics />}
      </div>

      {/* Add Record Form Dialog */}
      {showAddForm && (
        <FuelRecordForm
          onClose={() => setShowAddForm(false)}
          onRecordAdded={handleRecordAdded}
          showAsDialog={true}
        />
      )}
    </div>
  );
}
