'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

export default function TestNavigation() {
  const router = useRouter();
  const [vehicleId, setVehicleId] = useState('123');
  const [navigationResult, setNavigationResult] = useState<string | null>(null);

  const handleNavigate = (path: string) => {
    console.log(`Navigating to: ${path}`);
    try {
      router.push(path);
      setNavigationResult(`Successfully pushed to route: ${path}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setNavigationResult(`Error navigating to: ${path} - ${error}`);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Test Navigation</CardTitle>
          <CardDescription>Test various navigation paths to debug routing issues</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Vehicle ID for testing:</h3>
            <div className="flex items-center gap-2">
              <Input
                value={vehicleId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setVehicleId(e.target.value)}
                placeholder="Vehicle ID"
                className="w-full max-w-sm"
              />
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Regular Routes:</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => handleNavigate('/fleet')}>
                Fleet Dashboard
              </Button>
              <Button onClick={() => handleNavigate('/fleet/vehicles')}>
                Vehicles List
              </Button>
              <Button onClick={() => handleNavigate('/fleet/vehicles/add')}>
                Add Vehicle
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Dynamic Routes:</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => handleNavigate(`/fleet/vehicles/${vehicleId}`)}>
                View Vehicle
              </Button>
              <Button onClick={() => handleNavigate(`/fleet/vehicles/${vehicleId}/edit`)}>
                Edit Vehicle
              </Button>
              <Button onClick={() => handleNavigate(`/fleet/vehicles/${vehicleId}/add`)}>
                Add Related Vehicle
              </Button>
            </div>
          </div>

          <div className="mt-8 p-4 border border-gray-200 rounded-md">
            <h3 className="text-lg font-medium mb-2">Debug Info:</h3>
            <p className="text-sm text-gray-500">
              Current route paths to test: <br />
              • <code>/fleet/vehicles/{vehicleId}</code><br />
              • <code>/fleet/vehicles/{vehicleId}/edit</code><br />
              • <code>/fleet/vehicles/{vehicleId}/add</code><br />
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
