import { useEffect, useCallback, useRef } from 'react';
import webSocketService from '@/lib/websocket';
import { useAuth } from '@/context/auth-context';

interface UseWebSocketOptions {
  onTripUpdate?: (data: any) => void;
  onVehicleUpdate?: (data: any) => void;
  onDriverUpdate?: (data: any) => void;
  onNotification?: (data: any) => void;
  onSystemAlert?: (data: any) => void;
  autoConnect?: boolean;
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const { user } = useAuth();
  const {
    onTripUpdate,
    onVehicleUpdate,
    onDriverUpdate,
    onNotification,
    onSystemAlert,
    autoConnect = true,
  } = options;

  // Use refs to store callbacks to avoid re-registering listeners
  const callbacksRef = useRef({
    onTripUpdate,
    onVehicleUpdate,
    onDriverUpdate,
    onNotification,
    onSystemAlert,
  });

  // Update refs when callbacks change
  useEffect(() => {
    callbacksRef.current = {
      onTripUpdate,
      onVehicleUpdate,
      onDriverUpdate,
      onNotification,
      onSystemAlert,
    };
  }, [onTripUpdate, onVehicleUpdate, onDriverUpdate, onNotification, onSystemAlert]);

  // Stable callback functions
  const handleTripUpdate = useCallback((data: any) => {
    callbacksRef.current.onTripUpdate?.(data);
  }, []);

  const handleVehicleUpdate = useCallback((data: any) => {
    callbacksRef.current.onVehicleUpdate?.(data);
  }, []);

  const handleDriverUpdate = useCallback((data: any) => {
    callbacksRef.current.onDriverUpdate?.(data);
  }, []);

  const handleNotification = useCallback((data: any) => {
    callbacksRef.current.onNotification?.(data);
  }, []);

  const handleSystemAlert = useCallback((data: any) => {
    callbacksRef.current.onSystemAlert?.(data);
  }, []);

  useEffect(() => {
    if (!autoConnect) return;

    // Authenticate if user is logged in
    if (user) {
      const token = localStorage.getItem('token');
      if (token) {
        webSocketService.authenticate(token);
      }
    }

    // Register event listeners
    if (onTripUpdate) {
      webSocketService.onTripUpdate(handleTripUpdate);
    }
    if (onVehicleUpdate) {
      webSocketService.onVehicleUpdate(handleVehicleUpdate);
    }
    if (onDriverUpdate) {
      webSocketService.onDriverUpdate(handleDriverUpdate);
    }
    if (onNotification) {
      webSocketService.onNotification(handleNotification);
    }
    if (onSystemAlert) {
      webSocketService.onSystemAlert(handleSystemAlert);
    }

    // Cleanup function
    return () => {
      if (onTripUpdate) {
        webSocketService.offTripUpdate(handleTripUpdate);
      }
      if (onVehicleUpdate) {
        webSocketService.offVehicleUpdate(handleVehicleUpdate);
      }
      if (onDriverUpdate) {
        webSocketService.offDriverUpdate(handleDriverUpdate);
      }
      if (onNotification) {
        webSocketService.offNotification(handleNotification);
      }
      if (onSystemAlert) {
        webSocketService.offSystemAlert(handleSystemAlert);
      }
    };
  }, [
    user,
    autoConnect,
    handleTripUpdate,
    handleVehicleUpdate,
    handleDriverUpdate,
    handleNotification,
    handleSystemAlert,
    onTripUpdate,
    onVehicleUpdate,
    onDriverUpdate,
    onNotification,
    onSystemAlert,
  ]);

  // Return utility functions
  return {
    isConnected: webSocketService.isSocketConnected(),
    joinRoom: webSocketService.joinRoom.bind(webSocketService),
    leaveRoom: webSocketService.leaveRoom.bind(webSocketService),
    authenticate: webSocketService.authenticate.bind(webSocketService),
    disconnect: webSocketService.disconnect.bind(webSocketService),
  };
}

// Specialized hooks for specific use cases
export function useTripUpdates(onUpdate: (data: any) => void) {
  return useWebSocket({
    onTripUpdate: onUpdate,
  });
}

export function useVehicleUpdates(onUpdate: (data: any) => void) {
  return useWebSocket({
    onVehicleUpdate: onUpdate,
  });
}

export function useDriverUpdates(onUpdate: (data: any) => void) {
  return useWebSocket({
    onDriverUpdate: onUpdate,
  });
}

export function useNotifications(onNotification: (data: any) => void) {
  return useWebSocket({
    onNotification,
  });
}

export function useSystemAlerts(onAlert: (data: any) => void) {
  return useWebSocket({
    onSystemAlert: onAlert,
  });
}
