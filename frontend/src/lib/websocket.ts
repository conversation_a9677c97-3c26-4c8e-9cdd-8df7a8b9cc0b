import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.connect();
  }

  private connect() {
    try {
      const serverUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      
      this.socket = io(`${serverUrl}/realtime`, {
        transports: ['websocket', 'polling'],
        timeout: 5000,
        forceNew: true,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Authenticate if we have a token
      const token = localStorage.getItem('token');
      if (token) {
        this.authenticate(token);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from WebSocket server:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnected = false;
      this.handleReconnect();
    });

    this.socket.on('authenticated', (data) => {
      if (data.success) {
        console.log('WebSocket authenticated successfully');
      } else {
        console.error('WebSocket authentication failed:', data.error);
      }
    });

    // Real-time event listeners
    this.socket.on('trip-updated', (data) => {
      this.handleTripUpdate(data);
    });

    this.socket.on('vehicle-updated', (data) => {
      this.handleVehicleUpdate(data);
    });

    this.socket.on('driver-updated', (data) => {
      this.handleDriverUpdate(data);
    });

    this.socket.on('notification', (data) => {
      this.handleNotification(data);
    });

    this.socket.on('system-alert', (data) => {
      this.handleSystemAlert(data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
      
      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  public authenticate(token: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('authenticate', { token });
    }
  }

  public joinRoom(room: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-room', { room });
    }
  }

  public leaveRoom(room: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave-room', { room });
    }
  }

  // Event handlers that can be overridden
  private handleTripUpdate(data: any) {
    // Dispatch custom event for trip updates
    window.dispatchEvent(new CustomEvent('trip-updated', { detail: data }));
  }

  private handleVehicleUpdate(data: any) {
    // Dispatch custom event for vehicle updates
    window.dispatchEvent(new CustomEvent('vehicle-updated', { detail: data }));
  }

  private handleDriverUpdate(data: any) {
    // Dispatch custom event for driver updates
    window.dispatchEvent(new CustomEvent('driver-updated', { detail: data }));
  }

  private handleNotification(data: any) {
    // Dispatch custom event for notifications
    window.dispatchEvent(new CustomEvent('notification', { detail: data }));
  }

  private handleSystemAlert(data: any) {
    // Dispatch custom event for system alerts
    window.dispatchEvent(new CustomEvent('system-alert', { detail: data }));
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  public isSocketConnected(): boolean {
    return this.isConnected;
  }

  // Subscribe to real-time updates
  public onTripUpdate(callback: (data: any) => void) {
    window.addEventListener('trip-updated', (event: any) => {
      callback(event.detail);
    });
  }

  public onVehicleUpdate(callback: (data: any) => void) {
    window.addEventListener('vehicle-updated', (event: any) => {
      callback(event.detail);
    });
  }

  public onDriverUpdate(callback: (data: any) => void) {
    window.addEventListener('driver-updated', (event: any) => {
      callback(event.detail);
    });
  }

  public onNotification(callback: (data: any) => void) {
    window.addEventListener('notification', (event: any) => {
      callback(event.detail);
    });
  }

  public onSystemAlert(callback: (data: any) => void) {
    window.addEventListener('system-alert', (event: any) => {
      callback(event.detail);
    });
  }

  // Remove event listeners
  public offTripUpdate(callback: (data: any) => void) {
    window.removeEventListener('trip-updated', callback as any);
  }

  public offVehicleUpdate(callback: (data: any) => void) {
    window.removeEventListener('vehicle-updated', callback as any);
  }

  public offDriverUpdate(callback: (data: any) => void) {
    window.removeEventListener('driver-updated', callback as any);
  }

  public offNotification(callback: (data: any) => void) {
    window.removeEventListener('notification', callback as any);
  }

  public offSystemAlert(callback: (data: any) => void) {
    window.removeEventListener('system-alert', callback as any);
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
