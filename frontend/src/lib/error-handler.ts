/**
 * Centralized error handling utility for the Fleet Fusion frontend application
 * Provides consistent error handling patterns and user-friendly error messages
 */

// We'll create our own toast function since we can't use hooks in a static class
interface ToastOptions {
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
}

// Simple toast function for non-component contexts
const showToast = (options: ToastOptions) => {
  // In production, this would use a global event system
  console.error(`[Toast] ${options.title}: ${options.description}`);
  
  // If window exists (client-side), we could dispatch a custom event
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('show-toast', { detail: options });
    window.dispatchEvent(event);
  }
};

export interface ApiError {
  message: string;
  statusCode: number;
  details?: any;
  timestamp?: string;
  path?: string;
  method?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export enum ErrorCategory {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  SERVER_ERROR = 'SERVER_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  UNKNOWN = 'UNKNOWN'
}

export class ErrorHandler {
  /**
   * Handle API errors and show appropriate user notifications
   */
  static handleApiError(error: unknown, context?: string): ApiError {
    let apiError: ApiError;

    if (error instanceof Response) {
      // Handle Response objects
      apiError = {
        message: `HTTP ${error.status}: ${error.statusText}`,
        statusCode: error.status,
      };
    } else if (error instanceof Error) {
      // Handle standard Error objects
      try {
        // Try to parse as API error response
        const parsed = JSON.parse(error.message);
        apiError = {
          message: parsed.message || error.message,
          statusCode: parsed.statusCode || 500,
          details: parsed.details,
          timestamp: parsed.timestamp,
          path: parsed.path,
          method: parsed.method,
        };
      } catch {
        // Fallback to simple error
        apiError = {
          message: error.message,
          statusCode: 500,
        };
      }
    } else if (typeof error === 'object' && error !== null) {
      // Handle error objects
      const errorObj = error as any;
      apiError = {
        message: errorObj.message || 'An unexpected error occurred',
        statusCode: errorObj.statusCode || errorObj.status || 500,
        details: errorObj.details,
      };
    } else {
      // Handle unknown error types
      apiError = {
        message: String(error) || 'An unexpected error occurred',
        statusCode: 500,
      };
    }

    // Enhanced logging for debugging
    this.logError(apiError, context, error);

    return apiError;
  }

  /**
   * Show error toast notification to user
   */
  static showErrorToast(error: unknown, context?: string, customTitle?: string): void {
    const apiError = this.handleApiError(error, context);
    
    showToast({
      title: customTitle || this.getErrorTitle(apiError.statusCode),
      description: this.getUserFriendlyMessage(apiError),
      variant: 'destructive',
    });
  }

  /**
   * Get user-friendly error title based on status code
   */
  private static getErrorTitle(statusCode: number): string {
    switch (statusCode) {
      case 400:
        return 'Invalid Request';
      case 401:
        return 'Authentication Required';
      case 403:
        return 'Access Denied';
      case 404:
        return 'Not Found';
      case 409:
        return 'Conflict';
      case 422:
        return 'Validation Error';
      case 429:
        return 'Too Many Requests';
      case 500:
        return 'Server Error';
      case 502:
      case 503:
      case 504:
        return 'Service Unavailable';
      default:
        return 'Error';
    }
  }

  /**
   * Convert API error to user-friendly message
   */
  private static getUserFriendlyMessage(apiError: ApiError): string {
    // Handle specific business logic errors first
    if (apiError.message.includes('Driver already has an active assignment')) {
      return 'This driver is already assigned to another vehicle. Please select a different driver or complete their current assignment.';
    }

    if (apiError.message.includes('Vehicle already has an active assignment')) {
      return 'This vehicle is already assigned to another driver. Please select a different vehicle or complete the current assignment.';
    }

    if (apiError.message.includes('already has an active assignment')) {
      return 'This resource is currently assigned. Please complete or cancel the existing assignment first.';
    }

    if (apiError.message.includes('User is not a driver')) {
      return 'The selected user is not registered as a driver. Please select a valid driver from the list.';
    }

    if (apiError.message.includes('currently') && apiError.message.includes('cannot be assigned')) {
      return 'This resource is not available for assignment due to its current status. Please check its availability.';
    }

    if (apiError.message.includes('Database operation failed')) {
      return 'A database error occurred. Please try again or contact support if the problem persists.';
    }

    // Handle specific error patterns
    if (apiError.message.toLowerCase().includes('network')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }

    if (apiError.message.toLowerCase().includes('timeout')) {
      return 'The request timed out. Please check your connection and try again.';
    }

    if (apiError.message.includes('not found')) {
      const resourceMatch = apiError.message.match(/(\w+) with ID/);
      const resource = resourceMatch ? resourceMatch[1].toLowerCase() : 'item';
      return `The requested ${resource} could not be found. It may have been deleted or moved.`;
    }

    if (apiError.message.includes('validation') || apiError.message.includes('invalid')) {
      return 'Please check your input and try again. Some required fields may be missing or contain invalid data.';
    }

    // Handle HTTP status codes
    if (apiError.statusCode === 401) {
      return 'Your session has expired. Please log in again to continue.';
    }

    if (apiError.statusCode === 403) {
      return 'You don\'t have permission to perform this action. Contact your administrator if you believe this is an error.';
    }

    if (apiError.statusCode === 404) {
      return 'The requested resource was not found. It may have been moved or deleted.';
    }

    if (apiError.statusCode === 409) {
      return apiError.message || 'This action conflicts with existing data. Please refresh the page and try again.';
    }

    if (apiError.statusCode === 422) {
      return apiError.message || 'The data provided is invalid or incomplete. Please review your input and try again.';
    }

    if (apiError.statusCode === 429) {
      return 'Too many requests sent. Please wait a moment before trying again.';
    }

    if (apiError.statusCode >= 500) {
      return 'An internal server error occurred. Please try again later or contact support if the problem persists.';
    }

    // Return the original message if it's user-friendly, otherwise provide a generic message
    return apiError.message || 'An unexpected error occurred. Please try again or contact support.';
  }

  /**
   * Extract validation errors from API response
   */
  static extractValidationErrors(error: unknown): ValidationError[] {
    const apiError = this.handleApiError(error);
    
    if (apiError.details && Array.isArray(apiError.details)) {
      return apiError.details.map((detail: any) => ({
        field: detail.field || detail.property || 'unknown',
        message: detail.message || detail.constraints?.join(', ') || 'Invalid value',
      }));
    }

    if (apiError.details && typeof apiError.details === 'object') {
      return Object.entries(apiError.details).map(([field, message]) => ({
        field,
        message: String(message),
      }));
    }

    return [];
  }

  /**
   * Handle form validation errors
   */
  static handleFormErrors(
    error: unknown, 
    setError: (field: string, error: { type: string; message: string }) => void
  ): void {
    const validationErrors = this.extractValidationErrors(error);
    
    if (validationErrors.length > 0) {
      validationErrors.forEach(({ field, message }) => {
        setError(field, { type: 'manual', message });
      });
    } else {
      // Show general error if no specific field errors
      this.showErrorToast(error, 'Form Validation');
    }
  }

  /**
   * Categorize error for better handling and monitoring
   */
  static categorizeError(apiError: ApiError): ErrorCategory {
    // Check message patterns first
    if (apiError.message.toLowerCase().includes('network') ||
        apiError.message.toLowerCase().includes('fetch')) {
      return ErrorCategory.NETWORK;
    }

    if (apiError.message.includes('already has an active assignment') ||
        apiError.message.includes('cannot be assigned') ||
        apiError.message.includes('User is not a driver')) {
      return ErrorCategory.BUSINESS_LOGIC;
    }

    if (apiError.message.includes('validation') ||
        apiError.message.includes('invalid')) {
      return ErrorCategory.VALIDATION;
    }

    // Check status codes
    switch (apiError.statusCode) {
      case 401:
        return ErrorCategory.AUTHENTICATION;
      case 403:
        return ErrorCategory.AUTHORIZATION;
      case 404:
        return ErrorCategory.NOT_FOUND;
      case 409:
        return ErrorCategory.CONFLICT;
      case 422:
        return ErrorCategory.VALIDATION;
      case 429:
        return ErrorCategory.RATE_LIMIT;
      case 500:
      case 502:
      case 503:
      case 504:
        return ErrorCategory.SERVER_ERROR;
      default:
        return ErrorCategory.UNKNOWN;
    }
  }

  /**
   * Enhanced error logging for debugging and monitoring
   */
  private static logError(apiError: ApiError, context?: string, originalError?: unknown): void {
    const category = this.categorizeError(apiError);
    const logData = {
      timestamp: new Date().toISOString(),
      context: context || 'Unknown',
      category,
      statusCode: apiError.statusCode,
      message: apiError.message,
      details: apiError.details,
      path: apiError.path,
      method: apiError.method,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'Unknown',
    };

    // Log different levels based on error severity
    if (apiError.statusCode >= 500) {
      console.error(`[ErrorHandler] Server Error:`, logData);
    } else if (apiError.statusCode >= 400) {
      console.warn(`[ErrorHandler] Client Error:`, logData);
    } else {
      console.info(`[ErrorHandler] General Error:`, logData);
    }

    // In development, also log the original error for debugging
    if (process.env.NODE_ENV === 'development' && originalError) {
      console.debug('[ErrorHandler] Original Error:', originalError);
    }

    // In production, you might want to send errors to a monitoring service
    if (process.env.NODE_ENV === 'production' && apiError.statusCode >= 500) {
      // Example: Send to error monitoring service
      // errorMonitoringService.captureError(logData);
    }
  }

  /**
   * Check if error is recoverable (user can retry)
   */
  static isRecoverableError(error: unknown): boolean {
    const apiError = this.handleApiError(error);

    // Network errors, timeouts, and server errors are typically recoverable
    return (
      apiError.statusCode >= 500 ||
      apiError.statusCode === 429 ||
      apiError.message.toLowerCase().includes('network') ||
      apiError.message.toLowerCase().includes('timeout')
    );
  }

  /**
   * Create retry handler for recoverable errors
   */
  static createRetryHandler<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): () => Promise<T> {
    return async () => {
      let lastError: unknown;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = error;
          
          if (attempt === maxRetries || !this.isRecoverableError(error)) {
            throw error;
          }
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
      
      throw lastError;
    };
  }
}

/**
 * Wrapper for async operations with error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context?: string,
  showToast: boolean = true
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    if (showToast) {
      ErrorHandler.showErrorToast(error, context);
    } else {
      ErrorHandler.handleApiError(error, context);
    }
    return null;
  }
}

/**
 * Hook-like wrapper for React components
 */
export function useErrorHandler() {
  return {
    handleError: (error: unknown, context?: string) => 
      ErrorHandler.showErrorToast(error, context),
    handleApiError: (error: unknown, context?: string) => 
      ErrorHandler.handleApiError(error, context),
    handleFormErrors: (
      error: unknown,
      setError: (field: string, error: { type: string; message: string }) => void
    ) => ErrorHandler.handleFormErrors(error, setError),
    withErrorHandling: <T>(operation: () => Promise<T>, context?: string) =>
      withErrorHandling(operation, context),
  };
}
