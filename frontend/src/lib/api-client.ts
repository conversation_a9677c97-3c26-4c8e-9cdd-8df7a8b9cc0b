/**
 * Enhanced API client with comprehensive error handling, retry logic, and type safety
 * Centralizes all API communication for the Fleet Fusion frontend
 */

import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from './error-handler';
import { getAuthHeaders } from './auth-headers';

export interface ApiRequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  skipAuth?: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export class ApiClient {
  private readonly baseUrl: string;
  private readonly defaultTimeout: number = 30000; // 30 seconds
  private readonly defaultRetries: number = 3;

  constructor(baseUrl?: string) {
    // Use direct backend URL for all requests (simpler and more reliable)
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
  }

  /**
   * Determine if an endpoint should go to the backend API
   */
  private isBackendApiEndpoint(endpoint: string): boolean {
    const backendEndpoints = [
      '/vehicles',
      '/users',
      '/auth',
      '/trips',
      '/maintenance',
      '/insurance',
      '/reviews',
      '/business-partners',
      '/documents',
      '/distance',
      '/enhanced-maintenance',
      '/vehicle-assignments',
      '/truck-trailer-assignments',
      '/health'
    ];

    return backendEndpoints.some(prefix =>
      endpoint.startsWith(prefix) || endpoint === prefix.slice(1)
    );
  }

  /**
   * Make an HTTP request with enhanced error handling
   */
  async request<T = any>(
    endpoint: string,
    config: ApiRequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = 1000,
      skipAuth = false,
      ...requestConfig
    } = config;

    // Construct URL for direct backend communication
    let url: string;
    if (endpoint.startsWith('http')) {
      // Full URL provided
      url = endpoint;
    } else {
      // Add /api prefix if not already present and construct full URL
      const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
      url = `${this.baseUrl}${apiEndpoint}`;
    }
    
    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...requestConfig.headers,
    };

    // Add authentication headers if not skipped
    if (!skipAuth) {
      Object.assign(headers, getAuthHeaders());
    }

    const requestOptions: RequestInit = {
      ...requestConfig,
      headers,
    };

    // Create retry handler
    const retryHandler = ErrorHandler.createRetryHandler(
      () => this.performRequest<T>(url, requestOptions, timeout),
      retries,
      retryDelay
    );

    try {
      return await retryHandler();
    } catch (error) {
      // Enhanced error context
      const context = `${requestOptions.method || 'GET'} ${endpoint}`;
      throw ErrorHandler.handleApiError(error, context);
    }
  }

  /**
   * Perform the actual HTTP request with timeout
   */
  private async performRequest<T>(
    url: string,
    options: RequestInit,
    timeout: number
  ): Promise<ApiResponse<T>> {
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse response
      const data = await this.parseResponse<T>(response);

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Parse response based on content type
   */
  private async parseResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage: string;
      let errorDetails: any = null;

      try {
        const contentType = response.headers.get('content-type');
        
        if (contentType?.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`;
          errorDetails = errorData;
        } else {
          errorMessage = await response.text() || `HTTP ${response.status}: ${response.statusText}`;
        }
      } catch {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }

      const error = new Error(errorMessage);
      (error as any).statusCode = response.status;
      (error as any).details = errorDetails;
      throw error;
    }

    // Handle empty responses
    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return null as unknown as T;
    }

    const contentType = response.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      return await response.json();
    } else if (contentType?.includes('text/')) {
      return (await response.text()) as unknown as T;
    } else {
      return (await response.blob()) as unknown as T;
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, config?: ApiRequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      method: 'GET',
      ...config,
    });
    return response.data;
  }

  /**
   * POST request
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
    return response.data;
  }

  /**
   * PUT request
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
    return response.data;
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<T> {
    const response = await this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
    return response.data;
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, config?: ApiRequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      method: 'DELETE',
      ...config,
    });
    return response.data;
  }

  /**
   * Upload file with progress tracking
   */
  async upload<T = any>(
    endpoint: string,
    formData: FormData,
    config?: ApiRequestConfig & {
      onProgress?: (progress: number) => void;
    }
  ): Promise<T> {
    const { onProgress, ...requestConfig } = config || {};

    // Remove Content-Type to let browser set it for FormData
    const headers = { ...requestConfig.headers };
    delete (headers as any)['Content-Type'];

    const response = await this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      ...requestConfig,
      headers,
    });

    return response.data;
  }

  /**
   * Download file
   */
  async download(
    endpoint: string,
    filename?: string,
    config?: ApiRequestConfig
  ): Promise<void> {
    const response = await this.request<Blob>(endpoint, {
      ...config,
      headers: {
        ...config?.headers,
        Accept: '*/*',
      },
    });

    // Create download link
    const url = window.URL.createObjectURL(response.data);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Get paginated results
   */
  async getPaginated<T = any>(
    endpoint: string,
    params?: {
      page?: number;
      pageSize?: number;
      sort?: string;
      order?: 'asc' | 'desc';
      [key: string]: any;
    },
    config?: ApiRequestConfig
  ): Promise<PaginatedResponse<T>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const url = `${endpoint}?${searchParams.toString()}`;
    return this.get<PaginatedResponse<T>>(url, config);
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.get('/api/health', { skipAuth: true, timeout: 5000, retries: 1 });
  }
}

// Create default API client instance
export const apiClient = new ApiClient();

// Convenience methods using the default client
export const api = {
  get: <T = any>(endpoint: string, config?: ApiRequestConfig) => 
    apiClient.get<T>(endpoint, config),
  
  post: <T = any>(endpoint: string, data?: any, config?: ApiRequestConfig) =>
    apiClient.post<T>(endpoint, data, config),
  
  put: <T = any>(endpoint: string, data?: any, config?: ApiRequestConfig) =>
    apiClient.put<T>(endpoint, data, config),
  
  patch: <T = any>(endpoint: string, data?: any, config?: ApiRequestConfig) =>
    apiClient.patch<T>(endpoint, data, config),
  
  delete: <T = any>(endpoint: string, config?: ApiRequestConfig) =>
    apiClient.delete<T>(endpoint, config),
  
  upload: <T = any>(endpoint: string, formData: FormData, config?: ApiRequestConfig & { onProgress?: (progress: number) => void }) =>
    apiClient.upload<T>(endpoint, formData, config),
  
  download: (endpoint: string, filename?: string, config?: ApiRequestConfig) =>
    apiClient.download(endpoint, filename, config),
  
  getPaginated: <T = any>(endpoint: string, params?: any, config?: ApiRequestConfig) =>
    apiClient.getPaginated<T>(endpoint, params, config),
  
  healthCheck: () => apiClient.healthCheck(),
};

export default apiClient;
