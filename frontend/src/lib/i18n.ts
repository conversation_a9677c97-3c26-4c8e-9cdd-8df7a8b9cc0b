import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import commonEn from '../../public/locales/en/common.json';
import commonPl from '../../public/locales/pl/common.json';
import navigationEn from '../../public/locales/en/navigation.json';
import navigationPl from '../../public/locales/pl/navigation.json';
import fleetEn from '../../public/locales/en/fleet.json';
import fleetPl from '../../public/locales/pl/fleet.json';
import tripsEn from '../../public/locales/en/trips.json';
import tripsPl from '../../public/locales/pl/trips.json';
import driversEn from '../../public/locales/en/drivers.json';
import driversPl from '../../public/locales/pl/drivers.json';
import fuelEn from '../../public/locales/en/fuel.json';
import fuelPl from '../../public/locales/pl/fuel.json';
import partnersEn from '../../public/locales/en/partners.json';
import partnersPl from '../../public/locales/pl/partners.json';
import businessPartnersEn from '../../public/locales/en/businessPartners.json';
import businessPartnersPl from '../../public/locales/pl/businessPartners.json';
import formsEn from '../../public/locales/en/forms.json';
import formsPl from '../../public/locales/pl/forms.json';

const resources = {
  en: {
    common: commonEn,
    navigation: navigationEn,
    fleet: fleetEn,
    trips: tripsEn,
    drivers: driversEn,
    fuel: fuelEn,
    partners: partnersEn,
    businessPartners: businessPartnersEn,
    forms: formsEn,
  },
  pl: {
    common: commonPl,
    navigation: navigationPl,
    fleet: fleetPl,
    trips: tripsPl,
    drivers: driversPl,
    fuel: fuelPl,
    partners: partnersPl,
    businessPartners: businessPartnersPl,
    forms: formsPl,
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'pl', // Default language
    fallbackLng: 'pl',
    debug: process.env.NODE_ENV === 'development',

    // Namespace configuration
    ns: ['common', 'navigation', 'fleet', 'trips', 'drivers', 'fuel', 'partners', 'businessPartners', 'forms'],
    defaultNS: 'common',

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    react: {
      useSuspense: false,
    },
  });

export default i18n;
