/**
 * Timezone utilities for handling date/time conversions
 * Server timezone: Europe/Warsaw (UTC+1/UTC+2 with DST)
 */

// Default timezone for the application
export const DEFAULT_TIMEZONE = 'Europe/Warsaw';

/**
 * Convert a datetime-local input value to UTC ISO string for backend
 * @param localDateTime - Value from datetime-local input (YYYY-MM-DDTHH:mm)
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @returns UTC ISO string for backend storage
 */
export function localDateTimeToUTC(localDateTime: string, timezone: string = DEFAULT_TIMEZONE): string {
  if (!localDateTime) return '';
  
  // Create a date object assuming the input is in the specified timezone
  const date = new Date(localDateTime);
  
  // Get the timezone offset for the specified timezone at this date
  const tempDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
  const offset = tempDate.getTime() - utcDate.getTime();
  
  // Adjust for timezone offset
  const utcTime = new Date(date.getTime() - offset);
  
  return utcTime.toISOString();
}

/**
 * Convert UTC ISO string from backend to local datetime-local format
 * @param utcISOString - UTC ISO string from backend
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @returns Local datetime string for datetime-local input (YYYY-MM-DDTHH:mm)
 */
export function utcToLocalDateTime(utcISOString: string, timezone: string = DEFAULT_TIMEZONE): string {
  if (!utcISOString) return '';
  
  const date = new Date(utcISOString);
  
  // Convert to target timezone
  const localDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  
  // Format for datetime-local input
  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');
  const hours = String(localDate.getHours()).padStart(2, '0');
  const minutes = String(localDate.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Format UTC date for display in local timezone
 * @param utcDate - UTC date string or Date object
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string in local timezone
 */
export function formatDateTimeInTimezone(
  utcDate: string | Date,
  timezone: string = DEFAULT_TIMEZONE,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  }
): string {
  if (!utcDate) return 'N/A';
  
  const date = new Date(utcDate);
  return date.toLocaleString('en-US', {
    ...options,
    timeZone: timezone
  });
}

/**
 * Format date only (no time) in local timezone
 * @param utcDate - UTC date string or Date object
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @returns Formatted date string
 */
export function formatDateInTimezone(
  utcDate: string | Date,
  timezone: string = DEFAULT_TIMEZONE
): string {
  if (!utcDate) return 'N/A';
  
  const date = new Date(utcDate);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: timezone
  });
}

/**
 * Get current date/time in local timezone formatted for datetime-local input
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @returns Current datetime in YYYY-MM-DDTHH:mm format
 */
export function getCurrentLocalDateTime(timezone: string = DEFAULT_TIMEZONE): string {
  const now = new Date();
  return utcToLocalDateTime(now.toISOString(), timezone);
}

/**
 * Check if a date string is in the past (in local timezone)
 * @param utcDate - UTC date string
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @returns true if date is in the past
 */
export function isDateInPast(utcDate: string, timezone: string = DEFAULT_TIMEZONE): boolean {
  if (!utcDate) return false;
  
  const date = new Date(utcDate);
  const now = new Date();
  
  // Convert both to the same timezone for comparison
  const dateInTimezone = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  const nowInTimezone = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
  
  return dateInTimezone < nowInTimezone;
}

/**
 * Get timezone offset string (e.g., "+01:00", "+02:00")
 * @param timezone - Target timezone (default: Europe/Warsaw)
 * @param date - Date to check offset for (default: now)
 * @returns Timezone offset string
 */
export function getTimezoneOffset(timezone: string = DEFAULT_TIMEZONE, date: Date = new Date()): string {
  const utc = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
  const local = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  
  const offsetMs = local.getTime() - utc.getTime();
  const offsetHours = Math.floor(Math.abs(offsetMs) / (1000 * 60 * 60));
  const offsetMinutes = Math.floor((Math.abs(offsetMs) % (1000 * 60 * 60)) / (1000 * 60));
  
  const sign = offsetMs >= 0 ? '+' : '-';
  const hours = String(offsetHours).padStart(2, '0');
  const minutes = String(offsetMinutes).padStart(2, '0');
  
  return `${sign}${hours}:${minutes}`;
}
