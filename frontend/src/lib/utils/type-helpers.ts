// Utility function to safely handle form state
export function safeFormHandler<T>(handler: (data: T) => void | Promise<void>) {
  return async (data: T): Promise<void> => {
    const result = handler(data);
    if (result instanceof Promise) {
      await result;
    }
  };
}

// Type guard for checking if something is a Driver
export function isDriver(user: any): user is { role: 'DRIVER' } {
  return user && user.role === 'DRIVER';
}

// Filter users to get only drivers
export function filterDrivers(users: any[]): any[] {
  return users.filter(isDriver);
}
