interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

interface LocationData {
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
}

interface DistanceResult {
  distance: number; // in kilometers
  duration: number; // in hours
  durationText: string; // human-readable duration
  success: boolean;
  error?: string;
}

interface GeocodeResult {
  latitude: number;
  longitude: number;
  success: boolean;
  error?: string;
}

class DistanceService {
  private static instance: DistanceService;
  private cache: Map<string, DistanceResult> = new Map();
  private geocodeCache: Map<string, GeocodeResult> = new Map();
  private lastRequestTime: number = 0;
  private readonly MIN_REQUEST_INTERVAL = 1000; // 1 second between requests

  // API configuration
  private readonly ORS_API_KEY = process.env.NEXT_PUBLIC_OPENROUTESERVICE_API_KEY || '';
  private readonly GOOGLE_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
  private readonly ORS_BASE_URL = 'https://api.openrouteservice.org/v2';
  private readonly USE_ROUTING = true; // Set to false to use direct distance
  private readonly PREFERRED_API = 'openroute'; // 'openroute' or 'google'
  private readonly ROUTING_PROFILE = 'driving-car'; // 'driving-hgv' for trucks, 'driving-car' for cars

  private constructor() {}

  static getInstance(): DistanceService {
    if (!DistanceService.instance) {
      DistanceService.instance = new DistanceService();
    }
    return DistanceService.instance;
  }

  /**
   * Calculate distance using location objects with postal codes
   */
  async calculateDistanceFromLocations(
    startLocation: LocationData,
    endLocation: LocationData
  ): Promise<DistanceResult> {
    // Build address strings prioritizing postal codes
    const startAddress = this.buildAddressString(startLocation);
    const endAddress = this.buildAddressString(endLocation);

    console.log('🏢 Calculating distance from location objects:', {
      start: startLocation,
      end: endLocation,
      startAddress,
      endAddress
    });

    return this.calculateDistance(startAddress, endAddress);
  }

  /**
   * Build address string from location object, prioritizing postal code
   */
  private buildAddressString(location: LocationData): string {
    // If we have postal code, use it for most accurate geocoding
    if (location.postalCode) {
      const country = location.country || 'Poland';
      return `${location.postalCode}, ${country}`;
    }

    // Build from available components
    const parts: string[] = [];

    if (location.address) parts.push(location.address);
    if (location.city) parts.push(location.city);

    const country = location.country || 'Poland';
    parts.push(country);

    return parts.join(', ');
  }

  /**
   * Calculate distance between two locations using OpenRouteService API (free alternative to Google Maps)
   */
  async calculateDistance(
    startLocation: string,
    endLocation: string,
    startCoords?: LocationCoordinates,
    endCoords?: LocationCoordinates
  ): Promise<DistanceResult> {
    // Create cache key
    const cacheKey = `${startLocation}->${endLocation}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      let startLatLng: LocationCoordinates;
      let endLatLng: LocationCoordinates;

      // Use provided coordinates or geocode the addresses
      if (startCoords) {
        startLatLng = startCoords;
      } else {
        const geocoded = await this.geocodeAddress(startLocation);
        if (!geocoded.success) {
          return { distance: 0, duration: 0, durationText: '0 minutes', success: false, error: geocoded.error };
        }
        startLatLng = { latitude: geocoded.latitude, longitude: geocoded.longitude };
      }

      if (endCoords) {
        endLatLng = endCoords;
      } else {
        const geocoded = await this.geocodeAddress(endLocation);
        if (!geocoded.success) {
          return { distance: 0, duration: 0, durationText: '0 minutes', success: false, error: geocoded.error };
        }
        endLatLng = { latitude: geocoded.latitude, longitude: geocoded.longitude };
      }

      // Calculate distance using OpenRouteService
      const result = await this.calculateDistanceFromCoordinates(startLatLng, endLatLng);
      
      // Cache the result
      this.cache.set(cacheKey, result);
      
      return result;
    } catch (error) {
      console.error('Error calculating distance:', error);
      return {
        distance: 0,
        duration: 0,
        success: false,
        error: 'Failed to calculate distance'
      };
    }
  }

  /**
   * Extract postal code from address string
   */
  private extractPostalCode(address: string): string | null {
    // Polish postal code pattern: XX-XXX (e.g., 00-001, 31-007)
    const postalCodeRegex = /\b\d{2}-\d{3}\b/;
    const match = address.match(postalCodeRegex);
    return match ? match[0] : null;
  }

  /**
   * Format duration in hours to human-readable text
   */
  private formatDuration(durationHours: number): string {
    const totalMinutes = Math.round(durationHours * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours === 0) {
      return `${minutes} minutes`;
    } else if (minutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minutes`;
    }
  }

  /**
   * Clean and format address for better geocoding results
   */
  private cleanAddressForGeocoding(address: string): string {
    // Extract postal code if present
    const postalCode = this.extractPostalCode(address);

    // If we have a postal code, try full address first, then postal code as fallback
    if (postalCode) {
      console.log(`🏷️ Found postal code: ${postalCode} in address: ${address}`);

      // Try to extract street address and city for more precise geocoding
      const parts = address.split(',').map(part => part.trim());
      if (parts.length >= 3) {
        // Format: "Business Name, Street Address, Postal Code City"
        const streetAddress = parts[1];
        const cityPart = parts[2];

        // Extract city from "05-870 Błonie" format
        const cityMatch = cityPart.match(/\d{2}-\d{3}\s+(.+)/);
        const city = cityMatch ? cityMatch[1] : cityPart.replace(/\d{2}-\d{3}\s*/, '');

        if (streetAddress && city) {
          // Try with postal code + street address for maximum precision
          const preciseAddress = `${streetAddress}, ${postalCode} ${city}, Poland`;
          console.log(`🏠 Using precise address: ${preciseAddress}`);
          return preciseAddress;
        }
      }

      // Fallback to postal code only
      console.log(`🏷️ Fallback to postal code: ${postalCode}, Poland`);
      return `${postalCode}, Poland`;
    }

    // Remove business names and clean up the address
    // Format: "Business Name, Street Address, City, State" -> "Street Address, City, Poland"
    const parts = address.split(',').map(part => part.trim());

    // Check if address already contains "Poland" to avoid duplication
    const hasPoland = address.toLowerCase().includes('poland');

    if (parts.length >= 3) {
      // Skip the first part (business name) and use street address + city
      const streetAddress = parts[1];
      const city = parts[2];
      const state = parts.length > 3 ? parts[3] : '';

      // Create a clean address for geocoding
      let cleanAddress = `${streetAddress}, ${city}`;
      if (state && state !== '' && state.toLowerCase() !== 'poland') {
        cleanAddress += `, ${state}`;
      }

      // Only add Poland if not already present
      if (!hasPoland) {
        cleanAddress += ', Poland';
      }

      return cleanAddress;
    }

    // For simple addresses like "Warszawa, Poland" - don't modify if already has Poland
    if (hasPoland) {
      return address;
    }

    // Fallback: use the original address with Poland appended
    return `${address}, Poland`;
  }

  /**
   * Geocode an address to get coordinates using OpenStreetMap Nominatim (free)
   */
  private async geocodeAddress(address: string): Promise<GeocodeResult> {
    // Check cache first
    if (this.geocodeCache.has(address)) {
      return this.geocodeCache.get(address)!;
    }

    try {
      // Rate limiting: ensure minimum interval between requests
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
        const delay = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
        console.log(`🗺️ Rate limiting: waiting ${delay}ms before geocoding request`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      this.lastRequestTime = Date.now();

      // Clean the address for better geocoding results
      const cleanAddress = this.cleanAddressForGeocoding(address);
      console.log(`🗺️ Geocoding: "${address}" -> "${cleanAddress}"`);

      const encodedAddress = encodeURIComponent(cleanAddress);
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=10&countrycodes=pl&addressdetails=1&bounded=1&viewbox=14.0,49.0,24.5,55.0&extratags=1`,
        {
          headers: {
            'User-Agent': 'FleetFusion-App/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }

      const data = await response.json();
      console.log(`🗺️ Geocoding results for "${cleanAddress}":`, data);

      if (data.length === 0) {
        // Try with just the city name as fallback
        const cityMatch = address.match(/,\s*([^,]+)(?:,|$)/);
        if (cityMatch) {
          const cityName = cityMatch[1].trim();
          console.log(`🗺️ Trying fallback with city: "${cityName}"`);

          const fallbackResponse = await fetch(
            `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(cityName + ', Poland')}&limit=1&countrycodes=pl`,
            {
              headers: {
                'User-Agent': 'FleetFusion-App/1.0'
              }
            }
          );

          if (fallbackResponse.ok) {
            const fallbackData = await fallbackResponse.json();
            if (fallbackData.length > 0) {
              const result = {
                latitude: parseFloat(fallbackData[0].lat),
                longitude: parseFloat(fallbackData[0].lon),
                success: true
              };
              this.geocodeCache.set(address, result);
              return result;
            }
          }
        }

        const result = { latitude: 0, longitude: 0, success: false, error: 'Address not found' };
        this.geocodeCache.set(address, result);
        return result;
      }

      // Select the best result - prefer exact address matches
      let bestResult = data[0];

      // Extract house number from original address for matching
      const houseNumberMatch = address.match(/\b(\d+[A-Za-z]?)\b/);
      const searchHouseNumber = houseNumberMatch ? houseNumberMatch[1] : null;

      console.log(`🏠 Looking for house number: ${searchHouseNumber} in results:`, data.map(r => ({
        display_name: r.display_name,
        house_number: r.address?.house_number,
        importance: r.importance,
        type: r.type
      })));

      for (const result of data) {
        let score = 0;

        // Prefer exact house number matches
        if (searchHouseNumber && result.address?.house_number === searchHouseNumber) {
          score += 100;
          console.log(`🎯 Exact house number match found: ${result.address.house_number}`);
        }

        // Prefer higher importance
        score += (result.importance || 0) * 10;

        // Prefer specific address types over general places
        if (result.type === 'house' || result.type === 'building') {
          score += 50;
        } else if (result.class === 'highway' && result.type === 'residential') {
          score += 30;
        } else if (result.class === 'place') {
          score += 5;
        }

        // Calculate current best score
        let bestScore = 0;
        if (searchHouseNumber && bestResult.address?.house_number === searchHouseNumber) {
          bestScore += 100;
        }
        bestScore += (bestResult.importance || 0) * 10;
        if (bestResult.type === 'house' || bestResult.type === 'building') {
          bestScore += 50;
        } else if (bestResult.class === 'highway' && bestResult.type === 'residential') {
          bestScore += 30;
        } else if (bestResult.class === 'place') {
          bestScore += 5;
        }

        if (score > bestScore) {
          bestResult = result;
          console.log(`🔄 New best result selected with score ${score}:`, result.display_name);
        }
      }

      console.log(`🗺️ Selected best result:`, bestResult);

      const lat = parseFloat(bestResult.lat);
      const lon = parseFloat(bestResult.lon);

      // Validate parsed coordinates
      if (isNaN(lat) || isNaN(lon)) {
        console.error('🗺️ Invalid coordinates from geocoding:', { lat, lon, raw: data[0] });
        const result = { latitude: 0, longitude: 0, success: false, error: 'Invalid coordinates from geocoding' };
        this.geocodeCache.set(address, result);
        return result;
      }

      const result = {
        latitude: lat,
        longitude: lon,
        success: true
      };

      console.log('🗺️ Geocoding successful:', result);

      this.geocodeCache.set(address, result);
      return result;
    } catch (error) {
      console.error('Geocoding error:', error);
      const result = { latitude: 0, longitude: 0, success: false, error: 'Geocoding failed' };
      this.geocodeCache.set(address, result);
      return result;
    }
  }

  /**
   * Calculate distance using routing API or fallback to direct distance
   */
  private async calculateDistanceFromCoordinates(
    start: LocationCoordinates,
    end: LocationCoordinates
  ): Promise<DistanceResult> {
    try {
      // Try routing API first if enabled and API key is available
      if (this.USE_ROUTING) {
        if (this.PREFERRED_API === 'google' && this.GOOGLE_API_KEY) {
          console.log('🛣️ Using Google Maps for routing calculation');
          const routingResult = await this.calculateGoogleRoutedDistance(start, end);
          if (routingResult.success) {
            return routingResult;
          }
          console.log('🛣️ Google routing failed, trying OpenRouteService');
        }

        if (this.ORS_API_KEY) {
          console.log('🛣️ Using OpenRouteService for routing calculation');
          const routingResult = await this.calculateRoutedDistance(start, end);
          if (routingResult.success) {
            return routingResult;
          }
          console.log('🛣️ OpenRouteService failed, falling back to direct distance');
        }
      }

      // Fallback to direct distance calculation
      console.log('📏 Using direct distance calculation (Haversine)');
      const distance = this.haversineDistance(start, end);

      // Estimate duration based on average truck speed (60 km/h)
      const duration = distance / 60;

      return {
        distance: Math.round(distance * 10) / 10, // Round to 1 decimal place
        duration: Math.round(duration * 10) / 10, // Round to 1 decimal place
        durationText: this.formatDuration(duration),
        success: true
      };
    } catch (error) {
      console.error('Distance calculation error:', error);
      return {
        distance: 0,
        duration: 0,
        durationText: '0 minutes',
        success: false,
        error: 'Distance calculation failed'
      };
    }
  }

  /**
   * Calculate routed distance using OpenRouteService API
   */
  private async calculateRoutedDistance(
    start: LocationCoordinates,
    end: LocationCoordinates
  ): Promise<DistanceResult> {
    try {
      console.log('🛣️ Using backend proxy for routing calculation');
      console.log('🛣️ Profile:', this.ROUTING_PROFILE);
      console.log('📍 Start coords:', start);
      console.log('📍 End coords:', end);

      const requestBody = {
        startLatitude: start.latitude,
        startLongitude: start.longitude,
        endLatitude: end.latitude,
        endLongitude: end.longitude,
        profile: this.ROUTING_PROFILE
      };

      console.log('📦 Request body:', requestBody);

      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/distance/calculate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🛣️ Backend API error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Backend API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('🛣️ Backend response:', data);

      if (!data.success) {
        throw new Error(data.error || 'Backend distance calculation failed');
      }

      return {
        distance: data.distance,
        duration: data.duration,
        durationText: data.durationText || this.formatDuration(data.duration),
        success: true
      };

    } catch (error) {
      console.error('🛣️ Backend routing error:', error);
      return {
        distance: 0,
        duration: 0,
        durationText: '0 minutes',
        success: false,
        error: `Backend routing failed: ${error.message}`
      };
    }
  }

  /**
   * Calculate routed distance using Google Maps Directions API
   */
  private async calculateGoogleRoutedDistance(
    start: LocationCoordinates,
    end: LocationCoordinates
  ): Promise<DistanceResult> {
    try {
      // Rate limiting for Google API
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
        const delay = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      this.lastRequestTime = Date.now();

      const origin = `${start.latitude},${start.longitude}`;
      const destination = `${end.latitude},${end.longitude}`;

      const url = `https://maps.googleapis.com/maps/api/directions/json?` +
        `origin=${origin}&destination=${destination}` +
        `&mode=driving&avoid=tolls&units=metric` +
        `&key=${this.GOOGLE_API_KEY}`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Google Maps API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== 'OK' || !data.routes || data.routes.length === 0) {
        throw new Error(`Google Maps API: ${data.status} - ${data.error_message || 'No routes found'}`);
      }

      const route = data.routes[0];
      const leg = route.legs[0];

      const distanceKm = leg.distance.value / 1000; // Convert meters to kilometers
      const durationHours = leg.duration.value / 3600; // Convert seconds to hours

      console.log('🛣️ Google Maps result:', {
        distance: distanceKm,
        duration: durationHours,
        route: leg
      });

      return {
        distance: Math.round(distanceKm * 10) / 10,
        duration: Math.round(durationHours * 10) / 10,
        durationText: this.formatDuration(durationHours),
        success: true
      };

    } catch (error) {
      console.error('🛣️ Google Maps error:', error);
      return {
        distance: 0,
        duration: 0,
        durationText: '0 minutes',
        success: false,
        error: `Google Maps failed: ${error.message}`
      };
    }
  }

  /**
   * Calculate distance using Haversine formula (as fallback)
   */
  private haversineDistance(start: LocationCoordinates, end: LocationCoordinates): number {
    console.log('📏 Haversine calculation:', {
      start: { lat: start.latitude, lng: start.longitude },
      end: { lat: end.latitude, lng: end.longitude }
    });

    // Validate coordinates
    if (!start.latitude || !start.longitude || !end.latitude || !end.longitude) {
      console.error('❌ Invalid coordinates for Haversine calculation:', { start, end });
      return 0;
    }

    if (Math.abs(start.latitude) > 90 || Math.abs(end.latitude) > 90) {
      console.error('❌ Invalid latitude values:', { start, end });
      return 0;
    }

    if (Math.abs(start.longitude) > 180 || Math.abs(end.longitude) > 180) {
      console.error('❌ Invalid longitude values:', { start, end });
      return 0;
    }

    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(end.latitude - start.latitude);
    const dLon = this.toRadians(end.longitude - start.longitude);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(start.latitude)) * Math.cos(this.toRadians(end.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    console.log('📏 Haversine result:', {
      deltaLat: dLat,
      deltaLon: dLon,
      a: a,
      c: c,
      distance: distance
    });

    return distance;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Clear cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.cache.clear();
    this.geocodeCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { distanceCache: number; geocodeCache: number } {
    return {
      distanceCache: this.cache.size,
      geocodeCache: this.geocodeCache.size
    };
  }



}

export const distanceService = DistanceService.getInstance();
export type { LocationCoordinates, DistanceResult, GeocodeResult, LocationData };
