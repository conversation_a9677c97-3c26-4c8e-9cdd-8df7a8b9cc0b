// Simple test file for distance service - can be run in browser console
import { distanceService } from './distance-service';

export async function testDistanceService() {
  console.log('🧪 Testing Distance Service...');
  
  // Test 1: Simple city to city
  console.log('Test 1: Warsaw to Krakow');
  const result1 = await distanceService.calculateDistance(
    'Warszawa, Poland',
    'Kraków, Poland'
  );
  console.log('Result 1:', result1);
  
  // Test 2: Business location format
  console.log('Test 2: Business location format');
  const result2 = await distanceService.calculateDistance(
    'GFL Błonie, Sochaczewska 96A, Błonie',
    'FM Logistics Mszczonów, Ługowa 30, Mszczonów'
  );
  console.log('Result 2:', result2);
  
  // Test 3: Simple addresses
  console.log('Test 3: Simple addresses');
  const result3 = await distanceService.calculateDistance(
    'Sochaczewska 96A, Błonie, Poland',
    'Ługowa 30, Mszczonów, Poland'
  );
  console.log('Result 3:', result3);
  
  console.log('🧪 Distance Service Tests Complete');
  
  return { result1, result2, result3 };
}

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testDistanceService = testDistanceService;
}
