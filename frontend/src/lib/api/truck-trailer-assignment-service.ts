import { apiClient } from '../api-client';
import { ErrorHandler } from './error-handler';
import { 
  TruckTrailerAssignment, 
  CreateTruckTrailerAssignmentRequest, 
  UpdateTruckTrailerAssignmentRequest 
} from '@/types/vehicle';

export class TruckTrailerAssignmentService {
  private static readonly BASE_URL = '/truck-trailer-assignments';

  static async getAll(): Promise<TruckTrailerAssignment[]> {
    try {
      return await apiClient.get<TruckTrailerAssignment[]>(this.BASE_URL);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'TruckTrailerAssignmentService.getAll');
      throw error;
    }
  }

  static async getActive(): Promise<TruckTrailerAssignment[]> {
    try {
      return await apiClient.get<TruckTrailerAssignment[]>(`${this.BASE_URL}/active`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'TruckTrailerAssignmentService.getActive');
      throw error;
    }
  }

  static async getById(id: string): Promise<TruckTrailerAssignment> {
    try {
      return await apiClient.get<TruckTrailerAssignment>(`${this.BASE_URL}/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.getById(${id})`);
      throw error;
    }
  }

  static async getByTruck(truckId: string): Promise<TruckTrailerAssignment[]> {
    try {
      return await apiClient.get<TruckTrailerAssignment[]>(`${this.BASE_URL}/truck/${truckId}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.getByTruck(${truckId})`);
      throw error;
    }
  }

  static async getByTrailer(trailerId: string): Promise<TruckTrailerAssignment[]> {
    try {
      return await apiClient.get<TruckTrailerAssignment[]>(`${this.BASE_URL}/trailer/${trailerId}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.getByTrailer(${trailerId})`);
      throw error;
    }
  }

  static async create(data: CreateTruckTrailerAssignmentRequest): Promise<TruckTrailerAssignment> {
    try {
      return await apiClient.post<TruckTrailerAssignment>(this.BASE_URL, data);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'TruckTrailerAssignmentService.create');
      throw error;
    }
  }

  static async update(id: string, data: UpdateTruckTrailerAssignmentRequest): Promise<TruckTrailerAssignment> {
    try {
      return await apiClient.patch<TruckTrailerAssignment>(`${this.BASE_URL}/${id}`, data);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.update(${id})`);
      throw error;
    }
  }

  static async complete(id: string): Promise<TruckTrailerAssignment> {
    try {
      return await apiClient.patch<TruckTrailerAssignment>(`${this.BASE_URL}/${id}/complete`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.complete(${id})`);
      throw error;
    }
  }

  static async cancel(id: string): Promise<TruckTrailerAssignment> {
    try {
      return await apiClient.patch<TruckTrailerAssignment>(`${this.BASE_URL}/${id}/cancel`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.cancel(${id})`);
      throw error;
    }
  }

  static async delete(id: string): Promise<void> {
    try {
      await apiClient.delete(`${this.BASE_URL}/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `TruckTrailerAssignmentService.delete(${id})`);
      throw error;
    }
  }
}
