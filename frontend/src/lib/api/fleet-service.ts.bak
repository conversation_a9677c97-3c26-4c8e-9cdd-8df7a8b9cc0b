import { Vehicle, VehicleStatus, FuelType } from '@/types/vehicle';
import { MaintenanceLog } from '@/types/maintenance';
import { apiClient } from './client';
import { ErrorHand<PERSON> } from './error-handler';

// Type definitions for API requests
export interface CreateVehicleRequest {
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  vin?: string;
  color?: string;
  mileage?: number;
  fuelType?: FuelType;
  purchaseDate?: string;
  status?: VehicleStatus;
}

export interface UpdateVehicleRequest extends Partial<CreateVehicleRequest> {}

export interface CreateMaintenanceRequest {
  vehicleId: string;
  type: 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  category: 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
  description: string;
  scheduledDate: string;
  mileage?: number;
  cost?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
}

export interface UpdateMaintenanceRequest extends Partial<CreateMaintenanceRequest> {}

/**
 * Modernized Fleet Service using ApiClient and ErrorHandler
 * Provides comprehensive fleet management functionality with proper error handling,
 * retry logic, loading states, and consistent API responses.
 */
export class FleetService {
  // Utility method to build query parameters
  private static buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  }

  /**
   * Get all vehicles with optional filtering
   * @param filters - Optional filters for vehicles
   * @returns Array of vehicles
   */
  static async getVehicles(filters?: {
    status?: VehicleStatus;
    make?: string;
    year?: number;
    page?: number;
    limit?: number;
  }): Promise<Vehicle[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const endpoint = `/vehicles${queryString ? `?${queryString}` : ''}`;
      return await apiClient.get(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getVehicles');
      throw error;
    }
  }

  /**
   * Get a specific vehicle by ID
   * @param id - Vehicle ID
   * @returns Vehicle details
   */
  static async getVehicleById(id: string): Promise<Vehicle> {
    try {
      return await apiClient.get(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new vehicle
   * @param vehicleData - Vehicle data to create
   * @returns Created vehicle
   */
  static async createVehicle(vehicleData: CreateVehicleRequest & { fuelType?: FuelType }): Promise<Vehicle> {
    try {
      return await apiClient.post('/vehicles', vehicleData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createVehicle');
      throw error;
    }
  }

  /**
   * Update a vehicle
   * @param id - Vehicle ID
   * @param vehicleData - Data to update
   * @returns Updated vehicle
   */
  static async updateVehicle(id: string, vehicleData: UpdateVehicleRequest & { fuelType?: FuelType }): Promise<Vehicle> {
    try {
      return await apiClient.put(`/vehicles/${id}`, vehicleData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateVehicle(${id})`);
      throw error;
    }
  }

  /**
   * Delete a vehicle
   * @param id - Vehicle ID
   * @returns Success message
   */
  static async deleteVehicle(id: string): Promise<void> {
    try {
      await apiClient.delete(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteVehicle(${id})`);
      throw error;
    }
  }

  /**
   * Get maintenance logs for all vehicles or with filters
   * @param filters - Optional filters
   * @returns Array of maintenance logs
   */
  static async getMaintenanceLogs(filters?: {
    vehicleId?: string;
    type?: string;
    category?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<MaintenanceLog[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const endpoint = `/maintenance${queryString ? `?${queryString}` : ''}`;
      return await apiClient.get(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getMaintenanceLogs');
      throw error;
    }
  }

  /**
   * Get maintenance logs for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Array of maintenance logs for the vehicle
   */
  static async getVehicleMaintenanceLogs(vehicleId: string): Promise<MaintenanceLog[]> {
    try {
      return await apiClient.get(`/vehicles/${vehicleId}/maintenance`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleMaintenanceLogs(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get a specific maintenance log by ID
   * @param id - Maintenance log ID
   * @returns Maintenance log details
   */
  static async getMaintenanceLogById(id: string): Promise<MaintenanceLog> {
    try {
      return await apiClient.get(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getMaintenanceLogById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new maintenance log
   * @param maintenanceData - Maintenance data to create
   * @returns Created maintenance log
   */
  static async createMaintenanceLog(maintenanceData: CreateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      return await apiClient.post('/maintenance', maintenanceData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createMaintenanceLog');
      throw error;
    }
  }

  /**
   * Update a maintenance log
   * @param id - Maintenance log ID
   * @param maintenanceData - Data to update
   * @returns Updated maintenance log
   */
  static async updateMaintenanceLog(id: string, maintenanceData: UpdateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      return await apiClient.put(`/maintenance/${id}`, maintenanceData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Delete a maintenance log
   * @param id - Maintenance log ID
   * @returns Success message
   */
  static async deleteMaintenanceLog(id: string): Promise<void> {
    try {
      await apiClient.delete(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Get upcoming maintenance for all vehicles
   * @param days - Number of days ahead to look (default: 30)
   * @returns Array of upcoming maintenance items
   */
  static async getUpcomingMaintenance(days: number = 30): Promise<MaintenanceLog[]> {
    try {
      return await apiClient.get(`/maintenance/upcoming?days=${days}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getUpcomingMaintenance');
      throw error;
    }
  }

  /**
   * Get vehicle statistics
   * @returns Vehicle statistics
   */
  static async getVehicleStats(): Promise<{
    total: number;
    available: number;
    assigned: number;
    maintenance: number;
    outOfService: number;
  }> {
    try {
      return await apiClient.get('/vehicles/stats');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getVehicleStats');
      throw error;
    }
  }

  /**
   * Get maintenance statistics
   * @returns Maintenance statistics
   */
  static async getMaintenanceStats(): Promise<{
    total: number;
    scheduled: number;
    inProgress: number;
    completed: number;
    overdue: number;
  }> {
    try {
      return await apiClient.get('/maintenance/stats');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getMaintenanceStats');
      throw error;
    }
  }
}

// Type definitions for API requests
export interface CreateVehicleRequest {
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  vin?: string;
  color?: string;
  mileage?: number;
  fuelType?: FuelType;
  purchaseDate?: string;
  status?: VehicleStatus;
}

export interface UpdateVehicleRequest extends Partial<CreateVehicleRequest> {}

export interface CreateMaintenanceRequest {
  vehicleId: string;
  type: 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  category: 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
  description: string;
  scheduledDate: string;
  mileage?: number;
  cost?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
}

export interface UpdateMaintenanceRequest extends Partial<CreateMaintenanceRequest> {}

/**
 * Modernized Fleet Service using ApiClient and ErrorHandler
 * Provides comprehensive fleet management functionality with proper error handling,
 * retry logic, loading states, and consistent API responses.
 */
export class FleetService {

  // Utility method to build query parameters
  private static buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  }

  // Vehicle Management Methods

  /**
   * Get all vehicles with optional filtering
   * @param params - Optional query parameters for filtering
   * @returns Promise<Vehicle[]> - Array of vehicles
   */
  static async getVehicles(params?: {
    status?: VehicleStatus;
    make?: string;
    model?: string;
    year?: number;
  }): Promise<Vehicle[]> {
    try {
      const queryString = params ? this.buildQueryString(params) : '';
      const endpoint = queryString ? `/vehicles?${queryString}` : '/vehicles';
      
      return await apiClient.get<Vehicle[]>(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getVehicles');
      throw error;
    }
  }

  /**
   * Get a specific vehicle by ID
   * @param id - Vehicle ID
   * @returns Promise<Vehicle> - Vehicle details
   */
  static async getVehicleById(id: string): Promise<Vehicle> {
    if (!id) {
      throw new Error('Vehicle ID is required');
    }

    try {
      return await apiClient.get<Vehicle>(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new vehicle
   * @param vehicleData - Vehicle creation data
   * @returns Promise<Vehicle> - Created vehicle
   */
  static async createVehicle(vehicleData: CreateVehicleRequest): Promise<Vehicle> {
    try {
      const vehicle = await apiClient.post<Vehicle>('/vehicles', vehicleData);
      return vehicle;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createVehicle');
      throw error;
    }
  }

  /**
   * Update an existing vehicle
   * @param id - Vehicle ID
   * @param vehicleData - Vehicle update data
   * @returns Promise<Vehicle> - Updated vehicle
   */
  static async updateVehicle(id: string, vehicleData: UpdateVehicleRequest): Promise<Vehicle> {
    if (!id) {
      throw new Error('Vehicle ID is required');
    }

    try {
      const vehicle = await apiClient.patch<Vehicle>(`/vehicles/${id}`, vehicleData);
      return vehicle;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateVehicle(${id})`);
      throw error;
    }
  }

  /**
   * Delete a vehicle
   * @param id - Vehicle ID
   * @returns Promise<void>
   */
  static async deleteVehicle(id: string): Promise<void> {
    if (!id) {
      throw new Error('Vehicle ID is required');
    }

    try {
      await apiClient.delete(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteVehicle(${id})`);
      throw error;
    }
  }

  // Maintenance Management Methods

  /**
   * Get maintenance logs with optional filtering
   * @param params - Optional query parameters
   * @returns Promise<MaintenanceLog[]> - Array of maintenance logs
   */
  static async getMaintenanceLogs(params?: {
    vehicleId?: string;
    status?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<MaintenanceLog[]> {
    try {
      const queryString = params ? this.buildQueryString(params) : '';
      const endpoint = queryString ? `/maintenance?${queryString}` : '/maintenance';
      
      return await apiClient.get<MaintenanceLog[]>(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getMaintenanceLogs');
      throw error;
    }
  }

  /**
   * Get maintenance logs for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Promise<MaintenanceLog[]> - Array of maintenance logs
   */
  static async getVehicleMaintenanceLogs(vehicleId: string): Promise<MaintenanceLog[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      return await apiClient.get<MaintenanceLog[]>(`/vehicles/${vehicleId}/maintenance`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleMaintenanceLogs(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get a specific maintenance log by ID
   * @param id - Maintenance log ID
   * @returns Promise<MaintenanceLog> - Maintenance log details
   */
  static async getMaintenanceLogById(id: string): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      return await apiClient.get<MaintenanceLog>(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getMaintenanceLogById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new maintenance log
   * @param maintenanceData - Maintenance creation data
   * @returns Promise<MaintenanceLog> - Created maintenance log
   */
  static async createMaintenanceLog(maintenanceData: CreateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      const maintenanceLog = await apiClient.post<MaintenanceLog>('/maintenance', maintenanceData);
      return maintenanceLog;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createMaintenanceLog');
      throw error;
    }
  }

  /**
   * Update an existing maintenance log
   * @param id - Maintenance log ID
   * @param maintenanceData - Maintenance update data
   * @returns Promise<MaintenanceLog> - Updated maintenance log
   */
  static async updateMaintenanceLog(id: string, maintenanceData: UpdateMaintenanceRequest): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      const maintenanceLog = await apiClient.patch<MaintenanceLog>(`/maintenance/${id}`, maintenanceData);
      return maintenanceLog;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Delete a maintenance log
   * @param id - Maintenance log ID
   * @returns Promise<void>
   */
  static async deleteMaintenanceLog(id: string): Promise<void> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      await apiClient.delete(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteMaintenanceLog(${id})`);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get vehicle statistics
   * @returns Promise<any> - Vehicle statistics
   */
  static async getVehicleStats(): Promise<{
    total: number;
    available: number;
    assigned: number;
    maintenance: number;
    outOfService: number;
  }> {
    try {
      return await apiClient.get('/vehicles/stats');
    } catch (error) {
      // Don't show toast for stats errors, just log them
      ErrorHandler.handleApiError(error, 'FleetService.getVehicleStats');
      throw error;
    }
  }

  /**
   * Get upcoming maintenance schedule
   * @param days - Number of days to look ahead (default: 30)
   * @returns Promise<MaintenanceLog[]> - Upcoming maintenance items
   */
  static async getUpcomingMaintenance(days: number = 30): Promise<MaintenanceLog[]> {
    try {
      const queryString = this.buildQueryString({ days });
      return await apiClient.get<MaintenanceLog[]>(`/maintenance/upcoming?${queryString}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getUpcomingMaintenance');
      throw error;
    }
  }
}
