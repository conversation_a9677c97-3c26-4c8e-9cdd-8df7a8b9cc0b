import { ApiClient } from '@/lib/api-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';
import { Trip, TripType, TripPriority, TripStatus, TripStop, TripExpense, ExpenseType, TripStopInput, CreateTripRequest, TruckTrailerPair } from '@/types/trip';

// Request type definitions - using the one from types/trip.ts

export interface UpdateTripRequest {
  type?: TripType;
  priority?: TripPriority;
  driverId?: string;
  vehicleId?: string;
  trailerId?: string;
  truckTrailerAssignmentId?: string;
  assignmentId?: string;
  status?: TripStatus;
  startLocation?: string;
  endLocation?: string;
  startTime?: string;
  endTime?: string;
  estimatedDuration?: number;
  actualDuration?: number;
  distance?: number;
  notes?: string;
  purpose?: string;
  cargo?: string;
  cargoWeight?: number;
  // Business Partners Integration
  pickupPartnerId?: string;
  deliveryPartnerId?: string;
  pickupLocationId?: string;
  deliveryLocationId?: string;
}

export interface CreateTripExpenseRequest {
  tripId: string;
  type: ExpenseType;
  amount: number;
  description?: string;
  receiptUrl?: string;
}

export interface UpdateTripExpenseRequest {
  type?: ExpenseType;
  amount?: number;
  description?: string;
  receiptUrl?: string;
}

export interface TripFilters {
  status?: TripStatus;
  type?: TripType;
  priority?: TripPriority;
  driverId?: string;
  vehicleId?: string;
  assignmentId?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export class TripService {
  private static apiClient = new ApiClient();

  /**
   * Build query string from filters
   */
  private static buildQueryString(filters: Record<string, any>): string {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });
    
    return params.toString();
  }

  // Core CRUD Operations

  /**
   * Get all trips with optional filters
   * @param filters - Optional filters for trips
   * @returns Promise<Trip[]> - Array of trips
   */
  static async getTrips(filters?: TripFilters): Promise<Trip[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/trips?${queryString}` : '/trips';
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getTrips');
      throw error;
    }
  }

  /**
   * Get trips for the current driver
   * @param filters - Optional filters for driver trips
   * @returns Promise<Trip[]> - Array of driver trips
   */
  static async getMyTrips(filters?: TripFilters): Promise<Trip[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/trips/my-trips?${queryString}` : '/trips/my-trips';
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getMyTrips');
      throw error;
    }
  }

  /**
   * Get a specific trip by ID
   * @param id - Trip ID
   * @returns Promise<Trip> - Trip details
   */
  static async getTripById(id: string): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      return await this.apiClient.get<Trip>(`/trips/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getTripById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new trip
   * @param tripData - Trip creation data
   * @returns Promise<Trip & {conflictWarnings?: string[]}> - Created trip with optional conflict warnings
   */
  static async createTrip(tripData: CreateTripRequest): Promise<Trip & {conflictWarnings?: string[]}> {
    try {
      return await this.apiClient.post<Trip & {conflictWarnings?: string[]}>('/trips', tripData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.createTrip');
      throw error;
    }
  }

  /**
   * Update an existing trip
   * @param id - Trip ID
   * @param tripData - Trip update data
   * @returns Promise<Trip> - Updated trip
   */
  static async updateTrip(id: string, tripData: UpdateTripRequest): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      return await this.apiClient.patch<Trip>(`/trips/${id}`, tripData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.updateTrip(${id})`);
      throw error;
    }
  }

  /**
   * Delete a trip
   * @param id - Trip ID
   */
  static async deleteTrip(id: string): Promise<void> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      await this.apiClient.delete(`/trips/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.deleteTrip(${id})`);
      throw error;
    }
  }

  // Trip Status Management

  /**
   * Update trip status
   * @param id - Trip ID
   * @param status - New status
   * @returns Promise<Trip> - Updated trip
   */
  static async updateTripStatus(id: string, status: TripStatus): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      // Use the generic update method for all status changes
      // since specialized endpoints don't exist on the backend
      return await this.updateTrip(id, { status });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.updateTripStatus(${id}, ${status})`);
      throw error;
    }
  }

  /**
   * Start a trip
   * @param id - Trip ID
   * @returns Promise<Trip> - Updated trip
   */
  static async startTrip(id: string): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      // Use generic update since /start endpoint doesn't exist
      return await this.updateTrip(id, {
        status: 'IN_PROGRESS',
        startTime: new Date().toISOString()
      });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.startTrip(${id})`);
      throw error;
    }
  }

  /**
   * Complete a trip
   * @param id - Trip ID
   * @param completionData - Optional completion data
   * @returns Promise<Trip> - Updated trip
   */
  static async completeTrip(id: string, completionData?: {
    endTime?: string;
    actualDuration?: number;
    distance?: number;
    notes?: string;
  }): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      // Use generic update since /complete endpoint doesn't exist
      return await this.updateTrip(id, {
        status: 'COMPLETED',
        endTime: completionData?.endTime || new Date().toISOString(),
        actualDuration: completionData?.actualDuration,
        distance: completionData?.distance,
        notes: completionData?.notes
      });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.completeTrip(${id})`);
      throw error;
    }
  }

  /**
   * Cancel a trip
   * @param id - Trip ID
   * @param reason - Cancellation reason
   * @returns Promise<Trip> - Updated trip
   */
  static async cancelTrip(id: string, reason?: string): Promise<Trip> {
    if (!id) {
      throw new Error('Trip ID is required');
    }

    try {
      // Use generic update since /cancel endpoint doesn't exist
      return await this.updateTrip(id, {
        status: 'CANCELLED',
        notes: reason ? `Cancelled: ${reason}` : 'Cancelled'
      });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.cancelTrip(${id})`);
      throw error;
    }
  }

  // Trip Stops Management

  /**
   * Get trip stops
   * @param tripId - Trip ID
   * @returns Promise<TripStop[]> - Array of trip stops
   */
  static async getTripStops(tripId: string): Promise<TripStop[]> {
    if (!tripId) {
      throw new Error('Trip ID is required');
    }

    try {
      return await this.apiClient.get<TripStop[]>(`/trips/${tripId}/stops`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getTripStops(${tripId})`);
      throw error;
    }
  }

  /**
   * Update trip stop status
   * @param tripId - Trip ID
   * @param stopId - Stop ID
   * @param status - New status
   * @param notes - Optional notes
   * @returns Promise<TripStop> - Updated stop
   */
  static async updateTripStop(tripId: string, stopId: string, status: string, notes?: string): Promise<TripStop> {
    if (!tripId || !stopId) {
      throw new Error('Trip ID and Stop ID are required');
    }

    try {
      return await this.apiClient.patch<TripStop>(`/trips/${tripId}/stops/${stopId}`, { 
        status, 
        notes: notes || '' 
      });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.updateTripStop(${tripId}, ${stopId})`);
      throw error;
    }
  }

  // Trip Expenses Management

  /**
   * Get trip expenses
   * @param tripId - Trip ID
   * @returns Promise<TripExpense[]> - Array of trip expenses
   */
  static async getTripExpenses(tripId: string): Promise<TripExpense[]> {
    if (!tripId) {
      throw new Error('Trip ID is required');
    }

    try {
      return await this.apiClient.get<TripExpense[]>(`/trips/${tripId}/expenses`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getTripExpenses(${tripId})`);
      throw error;
    }
  }

  /**
   * Create a trip expense
   * @param expenseData - Expense creation data
   * @returns Promise<TripExpense> - Created expense
   */
  static async createTripExpense(expenseData: CreateTripExpenseRequest): Promise<TripExpense> {
    try {
      return await this.apiClient.post<TripExpense>('/trip-expenses', expenseData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.createTripExpense');
      throw error;
    }
  }

  /**
   * Update a trip expense
   * @param id - Expense ID
   * @param expenseData - Expense update data
   * @returns Promise<TripExpense> - Updated expense
   */
  static async updateTripExpense(id: string, expenseData: UpdateTripExpenseRequest): Promise<TripExpense> {
    if (!id) {
      throw new Error('Expense ID is required');
    }

    try {
      return await this.apiClient.patch<TripExpense>(`/trip-expenses/${id}`, expenseData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.updateTripExpense(${id})`);
      throw error;
    }
  }

  /**
   * Delete a trip expense
   * @param id - Expense ID
   */
  static async deleteTripExpense(id: string): Promise<void> {
    if (!id) {
      throw new Error('Expense ID is required');
    }

    try {
      await this.apiClient.delete(`/trip-expenses/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.deleteTripExpense(${id})`);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get trip statistics
   * @param filters - Optional filters for statistics
   * @returns Promise<any> - Trip statistics
   */
  static async getTripStats(filters?: { 
    startDate?: string; 
    endDate?: string; 
    driverId?: string; 
    vehicleId?: string; 
  }): Promise<any> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/trips/stats?${queryString}` : '/trips/stats';
      return await this.apiClient.get<any>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getTripStats');
      throw error;
    }
  }

  /**
   * Get active trips
   * @returns Promise<Trip[]> - Array of active trips
   */
  static async getActiveTrips(): Promise<Trip[]> {
    try {
      return await this.apiClient.get<Trip[]>('/trips/active');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getActiveTrips');
      throw error;
    }
  }

  /**
   * Get upcoming trips
   * @param limit - Optional limit for number of trips
   * @returns Promise<Trip[]> - Array of upcoming trips
   */
  static async getUpcomingTrips(limit?: number): Promise<Trip[]> {
    try {
      const queryString = limit ? `limit=${limit}` : '';
      const url = queryString ? `/trips/upcoming?${queryString}` : '/trips/upcoming';
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getUpcomingTrips');
      throw error;
    }
  }

  /**
   * Get trip history for a specific driver
   * @param driverId - Driver ID
   * @param limit - Optional limit for number of trips
   * @returns Promise<Trip[]> - Array of driver trips
   */
  static async getDriverTripHistory(driverId: string, limit?: number): Promise<Trip[]> {
    if (!driverId) {
      throw new Error('Driver ID is required');
    }

    try {
      const queryString = limit ? `limit=${limit}` : '';
      const url = queryString ? `/drivers/${driverId}/trips?${queryString}` : `/drivers/${driverId}/trips`;
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getDriverTripHistory(${driverId})`);
      throw error;
    }
  }

  /**
   * Get trip history for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @param limit - Optional limit for number of trips
   * @returns Promise<Trip[]> - Array of vehicle trips
   */
  static async getVehicleTripHistory(vehicleId: string, limit?: number): Promise<Trip[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      const queryString = limit ? `limit=${limit}` : '';
      const url = queryString ? `/trips/vehicle/${vehicleId}?${queryString}` : `/trips/vehicle/${vehicleId}`;
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getVehicleTripHistory(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get trip history for a specific trailer
   * @param trailerId - Trailer ID
   * @param limit - Optional limit for number of trips
   * @returns Promise<Trip[]> - Array of trailer trips
   */
  static async getTrailerTripHistory(trailerId: string, limit?: number): Promise<Trip[]> {
    if (!trailerId) {
      throw new Error('Trailer ID is required');
    }

    try {
      const queryString = limit ? `limit=${limit}` : '';
      const url = queryString ? `/trips/trailer/${trailerId}?${queryString}` : `/trips/trailer/${trailerId}`;
      return await this.apiClient.get<Trip[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `TripService.getTrailerTripHistory(${trailerId})`);
      throw error;
    }
  }

  /**
   * Get active truck-trailer pairs
   * @returns Promise<TruckTrailerPair[]> - Array of active truck-trailer pairs
   */
  static async getActiveTruckTrailerPairs(): Promise<TruckTrailerPair[]> {
    try {
      return await this.apiClient.get<TruckTrailerPair[]>('/trips/truck-trailer-pairs/active');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getActiveTruckTrailerPairs');
      throw error;
    }
  }

  /**
   * Get available truck-trailer pairs
   * @returns Promise<TruckTrailerPair[]> - Array of available truck-trailer pairs
   */
  static async getAvailableTruckTrailerPairs(): Promise<TruckTrailerPair[]> {
    try {
      return await this.apiClient.get<TruckTrailerPair[]>('/trips/truck-trailer-pairs/available');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'TripService.getAvailableTruckTrailerPairs');
      throw error;
    }
  }
}
