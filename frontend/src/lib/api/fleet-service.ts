import {
  Vehicle,
  VehicleStatus,
  VehicleType,
  FuelType,
  CreateVehicleRequest,
  UpdateVehicleRequest
} from '@/types/vehicle';

// Re-export types for convenience
export type { CreateVehicleRequest, UpdateVehicleRequest };
import { MaintenanceLog } from '@/types/maintenance';
import { apiClient } from '../api-client';
import { <PERSON>rror<PERSON>and<PERSON> } from './error-handler';

export interface CreateMaintenanceRequest {
  vehicleId: string;
  type: 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  category: 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
  description: string;
  scheduledDate: string;
  mileage?: number;
  cost?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
}

export interface UpdateMaintenanceRequest extends Partial<CreateMaintenanceRequest> {}

/**
 * Modernized Fleet Service using ApiClient and ErrorHandler
 * Provides comprehensive fleet management functionality with proper error handling,
 * retry logic, loading states, and consistent API responses.
 */
export class FleetService {
  // Utility method to build query parameters
  private static buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  }

  /**
   * Get all vehicles with optional filtering
   * @param filters - Optional filters for vehicles
   * @returns Array of vehicles
   */
  static async getVehicles(filters?: {
    status?: VehicleStatus;
    type?: VehicleType;
    make?: string;
    model?: string;
    year?: number;
    page?: number;
    limit?: number;
  }): Promise<Vehicle[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const endpoint = `/vehicles${queryString ? `?${queryString}` : ''}`;
      return await apiClient.get(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getVehicles');
      throw error;
    }
  }

  /**
   * Get all trucks
   * @returns Array of truck vehicles
   */
  static async getTrucks(): Promise<Vehicle[]> {
    try {
      return await apiClient.get('/vehicles/trucks');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getTrucks');
      throw error;
    }
  }

  /**
   * Get all trailers
   * @returns Array of trailer vehicles
   */
  static async getTrailers(): Promise<Vehicle[]> {
    try {
      return await apiClient.get('/vehicles/trailers');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getTrailers');
      throw error;
    }
  }

  /**
   * Get available trucks (not assigned to trailers)
   * @returns Array of available truck vehicles
   */
  static async getAvailableTrucks(): Promise<Vehicle[]> {
    try {
      return await apiClient.get('/vehicles/trucks/available');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getAvailableTrucks');
      throw error;
    }
  }

  /**
   * Get available trailers (not assigned to trucks)
   * @returns Array of available trailer vehicles
   */
  static async getAvailableTrailers(): Promise<Vehicle[]> {
    try {
      return await apiClient.get('/vehicles/trailers/available');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getAvailableTrailers');
      throw error;
    }
  }

  /**
   * Get a specific vehicle by ID
   * @param id - Vehicle ID
   * @returns Vehicle details
   */
  static async getVehicleById(id: string): Promise<Vehicle> {
    try {
      return await apiClient.get(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new vehicle
   * @param vehicleData - Vehicle data to create
   * @returns Created vehicle
   */
  static async createVehicle(vehicleData: CreateVehicleRequest): Promise<Vehicle> {
    try {
      return await apiClient.post('/vehicles', vehicleData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createVehicle');
      throw error;
    }
  }

  /**
   * Update a vehicle
   * @param id - Vehicle ID
   * @param vehicleData - Data to update
   * @returns Updated vehicle
   */
  static async updateVehicle(id: string, vehicleData: UpdateVehicleRequest): Promise<Vehicle> {
    try {
      return await apiClient.put(`/vehicles/${id}`, vehicleData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateVehicle(${id})`);
      throw error;
    }
  }

  /**
   * Delete a vehicle
   * @param id - Vehicle ID
   * @returns Success message
   */
  static async deleteVehicle(id: string): Promise<void> {
    try {
      await apiClient.delete(`/vehicles/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteVehicle(${id})`);
      throw error;
    }
  }

  /**
   * Get maintenance logs for all vehicles or with filters
   * @param filters - Optional filters
   * @returns Array of maintenance logs
   */
  static async getMaintenanceLogs(filters?: {
    vehicleId?: string;
    type?: string;
    category?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<MaintenanceLog[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const endpoint = `/maintenance${queryString ? `?${queryString}` : ''}`;
      return await apiClient.get(endpoint);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getMaintenanceLogs');
      throw error;
    }
  }

  /**
   * Get maintenance logs for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Array of maintenance logs for the vehicle
   */
  static async getVehicleMaintenanceLogs(vehicleId: string): Promise<MaintenanceLog[]> {
    try {
      return await apiClient.get(`/vehicles/${vehicleId}/maintenance-logs`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getVehicleMaintenanceLogs(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get a specific maintenance log by ID
   * @param id - Maintenance log ID
   * @returns Maintenance log details
   */
  static async getMaintenanceLogById(id: string): Promise<MaintenanceLog> {
    try {
      return await apiClient.get(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.getMaintenanceLogById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new maintenance log
   * @param maintenanceData - Maintenance data to create
   * @returns Created maintenance log
   */
  static async createMaintenanceLog(maintenanceData: CreateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      return await apiClient.post('/maintenance', maintenanceData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.createMaintenanceLog');
      throw error;
    }
  }

  /**
   * Update a maintenance log
   * @param id - Maintenance log ID
   * @param maintenanceData - Data to update
   * @returns Updated maintenance log
   */
  static async updateMaintenanceLog(id: string, maintenanceData: UpdateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      return await apiClient.patch(`/maintenance/${id}`, maintenanceData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.updateMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Delete a maintenance log
   * @param id - Maintenance log ID
   * @returns Success message
   */
  static async deleteMaintenanceLog(id: string): Promise<void> {
    try {
      await apiClient.delete(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `FleetService.deleteMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Get upcoming maintenance for all vehicles
   * @param days - Number of days ahead to look (default: 30)
   * @returns Array of upcoming maintenance items
   */
  static async getUpcomingMaintenance(days: number = 30): Promise<MaintenanceLog[]> {
    try {
      return await apiClient.get(`/maintenance/upcoming?days=${days}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getUpcomingMaintenance');
      throw error;
    }
  }

  /**
   * Get vehicle statistics
   * @returns Vehicle statistics
   */
  static async getVehicleStats(): Promise<{
    total: number;
    available: number;
    assigned: number;
    maintenance: number;
    outOfService: number;
  }> {
    try {
      return await apiClient.get('/vehicles/stats');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getVehicleStats');
      throw error;
    }
  }

  /**
   * Get maintenance statistics
   * @returns Maintenance statistics
   */
  static async getMaintenanceStats(): Promise<{
    total: number;
    scheduled: number;
    inProgress: number;
    completed: number;
    overdue: number;
  }> {
    try {
      return await apiClient.get('/maintenance/stats');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'FleetService.getMaintenanceStats');
      throw error;
    }
  }
}
