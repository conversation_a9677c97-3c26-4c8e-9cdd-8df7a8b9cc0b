// API Client
export { apiClient, api, type ApiRequestConfig, type ApiResponse, type PaginatedResponse } from '../api-client';

// Error Handling
export {
  <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  FleetFusionError,
  NetworkError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ServerError,
  TimeoutError,
  type ApiError
} from './error-handler';

// Services
export { FleetService } from './fleet-service';
export { InsuranceService } from './insurance-service';
export { ReviewService } from './review-service';
export { DriverService } from './driver-service';
export { TripService } from './trip-service';
export { DocumentService } from './document-service';
export { MaintenanceService } from './maintenance-service';
export {
  EnhancedMaintenanceService,
  type MaintenanceStats,
  type MaintenanceRecommendation
} from './enhanced-maintenance-service';
export { VehicleAssignmentService } from './vehicle-assignment-service';
export { TruckTrailerAssignmentService } from './truck-trailer-assignment-service';
export { BusinessPartnersService } from './business-partners-service';
