import { apiClient } from '../api-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler';

const PAPERLESS_API_URL = process.env.NEXT_PUBLIC_PAPERLESS_URL || 'http://localhost:8000/api';

export class DocumentService {
  /**
   * Get all documents
   * @param page - Page number (pagination)
   * @param limit - Number of items per page
   * @returns Array of documents
   */
  static async getDocuments(page = 1, limit = 20): Promise<any> {
    try {
      return await apiClient.get(`/documents?page=${page}&limit=${limit}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'DocumentService.getDocuments');
      throw error;
    }
  }

  /**
   * Get a specific document by ID
   * @param id - Document ID
   * @returns Document details
   */
  static async getDocument(id: string): Promise<any> {
    try {
      return await apiClient.get(`/documents/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `DocumentService.getDocument(${id})`);
      throw error;
    }
  }

  /**
   * Upload a new document
   * @param formData - Document form data including file
   * @returns Uploaded document metadata
   */
  static async uploadDocument(formData: FormData): Promise<any> {
    try {
      return await apiClient.upload('/documents', formData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'DocumentService.uploadDocument');
      throw error;
    }
  }

  /**
   * Search for documents
   * @param query - Search query
   * @param filters - Search filters
   * @returns Search results
   */
  static async searchDocuments(query: string, filters: object = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('query', query);

      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, String(value));
      });

      return await apiClient.get(`/documents/search?${queryParams}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'DocumentService.searchDocuments');
      throw error;
    }
  }

  /**
   * Delete a document
   * @param id - Document ID
   */
  static async deleteDocument(id: string): Promise<void> {
    try {
      await apiClient.delete(`/documents/${id}`);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `DocumentService.deleteDocument(${id})`);
      throw error;
    }
  }

  /**
   * Get document categories
   * @returns List of document categories
   */
  static async getDocumentCategories(): Promise<any[]> {
    try {
      return await apiClient.get('/documents/categories');
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), 'DocumentService.getDocumentCategories');
      throw error;
    }
  }

  /**
   * Share a document with a user or group
   * @param documentId - Document ID
   * @param shareData - Share details (user IDs, permissions)
   * @returns Share result
   */
  static async shareDocument(documentId: string, shareData: any): Promise<any> {
    try {
      return await apiClient.post(`/documents/${documentId}/share`, shareData);
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.handleApiError(error), `DocumentService.shareDocument(${documentId})`);
      throw error;
    }
  }
}
