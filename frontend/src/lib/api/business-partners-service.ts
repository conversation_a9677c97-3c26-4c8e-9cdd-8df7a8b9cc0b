import { api } from '../api-client';

export interface BusinessPartner {
  id: string;
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  locations: PartnerLocation[];
  _count: {
    tripPickups: number;
    tripDeliveries: number;
  };
}

export interface PartnerLocation {
  id: string;
  name: string;
  type: 'PICKUP_POINT' | 'DELIVERY_POINT' | 'WAREHOUSE' | 'DISTRIBUTION_CENTER';
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  contactPerson?: string;
  phone?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
}

export interface CreateBusinessPartnerDto {
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
}

export interface UpdateBusinessPartnerDto {
  name?: string;
  type?: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
}

export class BusinessPartnersService {
  /**
   * Get all business partners
   * @param filters - Optional filters for partners
   * @returns Array of business partners
   */
  static async getBusinessPartners(filters?: {
    type?: 'SHIPPER' | 'LOGISTICS_PARTNER';
    status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
    search?: string;
  }): Promise<BusinessPartner[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.type) params.append('type', filters.type);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.search) params.append('search', filters.search);
      
      const queryString = params.toString();
      const url = queryString ? `/business-partners?${queryString}` : '/business-partners';
      
      return await api.get<BusinessPartner[]>(url);
    } catch (error) {
      console.error('Error fetching business partners:', error);
      throw error;
    }
  }

  /**
   * Get a specific business partner by ID
   * @param id - Partner ID
   * @returns Business partner details
   */
  static async getBusinessPartner(id: string): Promise<BusinessPartner> {
    try {
      return await api.get<BusinessPartner>(`/business-partners/${id}`);
    } catch (error) {
      console.error(`Error fetching business partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all shippers
   * @returns Array of shipper partners
   */
  static async getShippers(): Promise<BusinessPartner[]> {
    try {
      return await api.get<BusinessPartner[]>('/business-partners/shippers');
    } catch (error) {
      console.error('Error fetching shippers:', error);
      throw error;
    }
  }

  /**
   * Get all logistics partners
   * @returns Array of logistics partners
   */
  static async getLogisticsPartners(): Promise<BusinessPartner[]> {
    try {
      return await api.get<BusinessPartner[]>('/business-partners/logistics-partners');
    } catch (error) {
      console.error('Error fetching logistics partners:', error);
      throw error;
    }
  }

  /**
   * Create a new business partner
   * @param partnerData - Partner data to create
   * @returns Created business partner
   */
  static async createBusinessPartner(partnerData: CreateBusinessPartnerDto): Promise<BusinessPartner> {
    try {
      return await api.post<BusinessPartner>('/business-partners', partnerData);
    } catch (error) {
      console.error('Error creating business partner:', error);
      throw error;
    }
  }

  /**
   * Update a business partner
   * @param id - Partner ID
   * @param partnerData - Data to update
   * @returns Updated business partner
   */
  static async updateBusinessPartner(id: string, partnerData: UpdateBusinessPartnerDto): Promise<BusinessPartner> {
    try {
      return await api.put<BusinessPartner>(`/business-partners/${id}`, partnerData);
    } catch (error) {
      console.error(`Error updating business partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a business partner
   * @param id - Partner ID
   * @returns Success message
   */
  static async deleteBusinessPartner(id: string): Promise<void> {
    try {
      await api.delete(`/business-partners/${id}`);
    } catch (error) {
      console.error(`Error deleting business partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get locations for a specific partner
   * @param partnerId - Partner ID
   * @returns Array of partner locations
   */
  static async getPartnerLocations(partnerId: string): Promise<PartnerLocation[]> {
    try {
      return await api.get<PartnerLocation[]>(`/business-partners/${partnerId}/locations`);
    } catch (error) {
      console.error(`Error fetching locations for partner ${partnerId}:`, error);
      throw error;
    }
  }

  /**
   * Get all pickup locations
   * @param partnerId - Optional partner ID to filter by
   * @returns Array of pickup locations
   */
  static async getPickupLocations(partnerId?: string): Promise<PartnerLocation[]> {
    try {
      const params = partnerId ? `?partnerId=${partnerId}` : '';
      return await api.get<PartnerLocation[]>(`/business-partners/locations/pickup/all${params}`);
    } catch (error) {
      console.error('Error fetching pickup locations:', error);
      throw error;
    }
  }

  /**
   * Get all delivery locations
   * @param partnerId - Optional partner ID to filter by
   * @returns Array of delivery locations
   */
  static async getDeliveryLocations(partnerId?: string): Promise<PartnerLocation[]> {
    try {
      const params = partnerId ? `?partnerId=${partnerId}` : '';
      return await api.get<PartnerLocation[]>(`/business-partners/locations/delivery/all${params}`);
    } catch (error) {
      console.error('Error fetching delivery locations:', error);
      throw error;
    }
  }
}
