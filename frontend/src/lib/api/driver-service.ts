import { api } from '../api-client';
import { User, Driver } from '@/types/user';

export class DriverService {
  /**
   * Get all drivers
   * @returns Array of drivers
   */
  static async getDrivers(): Promise<Driver[]> {
    try {
      const users: User[] = await api.get('/users/drivers');
      // Convert User[] to Driver[] to satisfy the typings
      return users.filter(user => user.role === 'DRIVER') as Driver[];
    } catch (error) {
      console.error('Error fetching drivers:', error);
      throw error;
    }
  }

  /**
   * Get a specific driver by ID
   * @param id - Driver ID
   * @returns Driver details
   */
  static async getDriver(id: string): Promise<Driver> {
    try {
      const user: User = await api.get(`/users/drivers/${id}`);
      // Convert User to Driver to satisfy typings
      if (user.role !== 'DRIVER') {
        throw new Error(`User with id ${id} is not a driver`);
      }
      return user as Driver;
    } catch (error) {
      console.error(`Error fetching driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new driver
   * @param driverData - Driver data to create
   * @returns Created driver
   */
  static async createDriver(driverData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    licenseNumber?: string;
    role: 'DRIVER';
    status: 'AVAILABLE';
  }): Promise<Driver> {
    try {
      const user: User = await api.post('/users/driver', driverData);
      return user as Driver;
    } catch (error) {
      console.error('Error creating driver:', error);
      throw error;
    }
  }

  /**
   * Update a driver
   * @param id - Driver ID
   * @param driverData - Data to update
   * @returns Updated driver
   */
  static async updateDriver(id: string, driverData: Partial<Driver>): Promise<Driver> {
    try {
      const user: User = await api.patch(`/users/drivers/${id}`, driverData);
      return user as Driver;
    } catch (error) {
      console.error(`Error updating driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a driver
   * @param id - Driver ID
   * @returns Success message
   */
  static async deleteDriver(id: string): Promise<void> {
    try {
      await api.delete(`/users/drivers/${id}`);
    } catch (error) {
      console.error(`Error deleting driver ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get available drivers (not assigned to any vehicle)
   * @returns Array of available drivers
   */
  static async getAvailableDrivers(): Promise<Driver[]> {
    try {
      const users: User[] = await api.get('/users/drivers/available');
      return users.filter(user => user.role === 'DRIVER') as Driver[];
    } catch (error) {
      console.error('Error fetching available drivers:', error);
      throw error;
    }
  }

  /**
   * Get assigned drivers
   * @returns Array of assigned drivers
   */
  static async getAssignedDrivers(): Promise<Driver[]> {
    try {
      const users: User[] = await api.get('/users/drivers/assigned');
      return users.filter(user => user.role === 'DRIVER') as Driver[];
    } catch (error) {
      console.error('Error fetching assigned drivers:', error);
      throw error;
    }
  }

  /**
   * Update driver status
   * @param id - Driver ID
   * @param status - New status
   * @returns Updated driver
   */
  static async updateDriverStatus(id: string, status: string): Promise<Driver> {
    try {
      const user: User = await api.patch(`/users/drivers/${id}/status`, { status });
      return user as Driver;
    } catch (error) {
      console.error(`Error updating driver status for ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get driver assignments
   * @param id - Driver ID
   * @returns Array of assignments
   */
  static async getDriverAssignments(id: string): Promise<any[]> {
    try {
      return await api.get(`/users/drivers/${id}/assignments`);
    } catch (error) {
      console.error(`Error fetching driver assignments for ${id}:`, error);
      throw error;
    }
  }
}
