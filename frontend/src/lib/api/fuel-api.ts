import { apiClient } from './client';

// Types
export interface FuelRecord {
  id: string;
  vehicleId: string;
  driverId: string;
  quantity: number;
  totalCost: number;
  pricePerLiter: number;
  location: string;
  fuelingDate: string;
  odometerReading: number;
  receiptNumber?: string;
  receiptUrl?: string;
  notes?: string;
  enteredBy: string;
  createdAt: string;
  vehicle: {
    plateNumber: string;
    make: string;
    model: string;
  };
  driver: {
    firstName: string;
    lastName: string;
  };
}

export interface CreateFuelRecordRequest {
  vehicleId: string;
  driverId: string;
  quantity: number;
  totalCost: number;
  location: string;
  fuelingDate: string;
  odometerReading: number;
  receiptNumber?: string;
  notes?: string;
  enteredBy: string;
}

export interface UpdateFuelRecordRequest {
  quantity?: number;
  totalCost?: number;
  location?: string;
  fuelingDate?: string;
  odometerReading?: number;
  receiptNumber?: string;
  notes?: string;
}

export interface FuelRecordFilters {
  search?: string;
  vehicleId?: string;
  driverId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

export interface FuelPrice {
  id: string;
  effectiveDate: string;
  dieselPriceNet: number;
  dieselPriceGross: number;
  vatRate: number;
  source: string;
  scrapedAt: string;
}

export interface FuelDashboardStats {
  totalFuelCost: number;
  totalLiters: number;
  averageEfficiency: number;
  totalVehicles: number;
  activeDrivers: number;
  monthlyBudget: number;
  monthlySpent: number;
  topPerformers: {
    vehicles: Array<{
      id: string;
      plateNumber: string;
      efficiency: number;
      totalCost: number;
    }>;
    drivers: Array<{
      id: string;
      name: string;
      efficiency: number;
      totalCost: number;
    }>;
  };
  recentTrends: {
    costTrend: number;
    efficiencyTrend: number;
    volumeTrend: number;
  };
}

export interface SystemAlert {
  id: string;
  type: string;
  message: string;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  resolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  createdAt: string;
}

export interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  vehicleType: string;
}

export interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

// Fuel Records API
export const fuelRecordsApi = {
  // Get fuel records with filters and pagination
  async getRecords(filters: FuelRecordFilters = {}) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const response = await apiClient.get<{
      success: boolean;
      data: FuelRecord[];
      pagination: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
      };
    }>(`/fuel/records?${params.toString()}`);

    return response;
  },

  // Create new fuel record
  async createRecord(data: CreateFuelRecordRequest, receiptFile?: File) {
    const formData = new FormData();
    
    // Add all the data fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });

    // Add receipt file if provided
    if (receiptFile) {
      formData.append('receiptFile', receiptFile);
    }

    const response = await apiClient.request<{
      success: boolean;
      data: FuelRecord;
    }>('/fuel/records', {
      method: 'POST',
      body: formData,
      headers: {}, // Let browser set Content-Type for FormData
    });

    return response.data;
  },

  // Update fuel record
  async updateRecord(id: string, data: UpdateFuelRecordRequest, receiptFile?: File) {
    const formData = new FormData();
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });

    if (receiptFile) {
      formData.append('receiptFile', receiptFile);
    }

    const response = await apiClient.request<{
      success: boolean;
      data: FuelRecord;
    }>(`/fuel/records/${id}`, {
      method: 'PUT',
      body: formData,
      headers: {},
    });

    return response.data;
  },

  // Delete fuel record
  async deleteRecord(id: string) {
    const response = await apiClient.delete<{
      success: boolean;
    }>(`/fuel/records/${id}`);

    return response;
  },

  // Get vehicle fuel summary
  async getVehicleSummary(vehicleId: string, startDate?: string, endDate?: string) {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const response = await apiClient.get<{
      success: boolean;
      data: any;
    }>(`/fuel/vehicles/${vehicleId}/summary?${params.toString()}`);

    return response.data;
  },

  // Get vehicle fuel efficiency
  async getVehicleEfficiency(vehicleId: string, startDate?: string, endDate?: string) {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const response = await apiClient.get<{
      success: boolean;
      data: any;
    }>(`/fuel/vehicles/${vehicleId}/efficiency?${params.toString()}`);

    return response.data;
  },
};

// Fuel Prices API
export const fuelPricesApi = {
  // Get current fuel price
  async getCurrentPrice() {
    const response = await apiClient.get<{
      success: boolean;
      data: FuelPrice;
    }>('/fuel-prices/current');

    return response.data;
  },

  // Get price history
  async getPriceHistory(days: number = 30) {
    const response = await apiClient.get<{
      success: boolean;
      data: FuelPrice[];
    }>(`/fuel-prices/history?days=${days}`);

    return response.data;
  },

  // Manual price fetch
  async manualFetch() {
    const response = await apiClient.post<{
      success: boolean;
      data: any;
    }>('/fuel-prices/manual-fetch');

    return response.data;
  },

  // Get scraping status
  async getScrapingStatus() {
    const response = await apiClient.get<{
      success: boolean;
      data: any;
    }>('/fuel-prices/status');

    return response.data;
  },
};

// Vehicles API (for fuel form)
export const vehiclesApi = {
  // Get trucks for fuel records
  async getTrucks() {
    const response = await apiClient.get<Vehicle[]>('/vehicles/trucks');
    return response;
  },
};

// Drivers API (for fuel form)
export const driversApi = {
  // Get active drivers
  async getActiveDrivers() {
    const response = await apiClient.get<Driver[]>('/users/drivers');
    return response;
  },
};

// Dashboard API
export const dashboardApi = {
  // Get dashboard stats
  async getDashboardStats() {
    const response = await apiClient.get<{
      success: boolean;
      data: FuelDashboardStats;
    }>('/fuel/dashboard/stats');

    return response.data;
  },

  // Get basic stats for main page
  async getBasicStats() {
    const response = await apiClient.get<{
      success: boolean;
      data: {
        totalRecords: number;
        totalCost: number;
        totalLiters: number;
        averageEfficiency: number;
        currentMonthCost: number;
        lastMonthCost: number;
      };
    }>('/fuel/stats');

    return response.data;
  },
};

// System Alerts API
export const systemAlertsApi = {
  // Get all alerts with filters
  async getAlerts(filters: { resolved?: boolean; severity?: string; type?: string } = {}) {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, String(value));
      }
    });

    const response = await apiClient.get<{
      success: boolean;
      data: SystemAlert[];
    }>(`/fuel-prices/alerts?${params.toString()}`);

    return response.data;
  },

  // Get unresolved alerts
  async getUnresolvedAlerts() {
    const response = await apiClient.get<{
      success: boolean;
      data: SystemAlert[];
    }>('/fuel-prices/alerts/unresolved');

    return response.data;
  },

  // Get alert statistics
  async getAlertStats() {
    const response = await apiClient.get<{
      success: boolean;
      data: {
        total: number;
        unresolved: number;
        byType: Record<string, number>;
        bySeverity: Record<string, number>;
      };
    }>('/fuel-prices/alerts/stats');

    return response.data;
  },

  // Resolve an alert
  async resolveAlert(id: string, resolvedBy: string) {
    const response = await apiClient.put<{
      success: boolean;
      data: SystemAlert;
    }>(`/fuel-prices/alerts/${id}/resolve`, { resolvedBy });

    return response.data;
  },
};

export default {
  fuelRecordsApi,
  fuelPricesApi,
  vehiclesApi,
  driversApi,
  dashboardApi,
  systemAlertsApi,
};
