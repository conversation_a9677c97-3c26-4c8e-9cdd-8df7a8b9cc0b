/**
 * Enhanced error handler with comprehensive error types, retry logic, and user-friendly messages
 * Provides centralized error handling for the Fleet Fusion frontend
 */

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
  timestamp: string;
  context?: string;
}

export class FleetFusionError extends Error {
  public readonly status?: number;
  public readonly code?: string;
  public readonly details?: any;
  public readonly timestamp: string;
  public readonly context?: string;
  public readonly isRetryable: boolean;

  constructor(
    message: string,
    status?: number,
    code?: string,
    details?: any,
    context?: string
  ) {
    super(message);
    this.name = 'FleetFusionError';
    this.status = status;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.context = context;
    this.isRetryable = this.determineRetryability();
  }

  private determineRetryability(): boolean {
    if (!this.status) return true;
    
    // Retry on server errors and rate limiting
    if (this.status >= 500) return true;
    if (this.status === 429) return true; // Rate limited
    if (this.status === 408) return true; // Request timeout
    
    // Don't retry on client errors
    if (this.status >= 400 && this.status < 500) return false;
    
    return true;
  }

  toJSON(): ApiError {
    return {
      message: this.message,
      status: this.status,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp,
      context: this.context,
    };
  }
}

export class NetworkError extends FleetFusionError {
  constructor(message: string = 'Network connection failed', context?: string) {
    super(message, undefined, 'NETWORK_ERROR', undefined, context);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends FleetFusionError {
  constructor(message: string, details?: any, context?: string) {
    super(message, 400, 'VALIDATION_ERROR', details, context);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends FleetFusionError {
  constructor(message: string = 'Authentication failed', context?: string) {
    super(message, 401, 'AUTH_ERROR', undefined, context);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends FleetFusionError {
  constructor(message: string = 'Insufficient permissions', context?: string) {
    super(message, 403, 'AUTHORIZATION_ERROR', undefined, context);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends FleetFusionError {
  constructor(resource: string, context?: string) {
    super(`${resource} not found`, 404, 'NOT_FOUND', { resource }, context);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends FleetFusionError {
  constructor(message: string, details?: any, context?: string) {
    super(message, 409, 'CONFLICT_ERROR', details, context);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends FleetFusionError {
  constructor(retryAfter?: number, context?: string) {
    super('Rate limit exceeded', 429, 'RATE_LIMIT', { retryAfter }, context);
    this.name = 'RateLimitError';
  }
}

export class ServerError extends FleetFusionError {
  constructor(message: string = 'Internal server error', status: number = 500, context?: string) {
    super(message, status, 'SERVER_ERROR', undefined, context);
    this.name = 'ServerError';
  }
}

export class TimeoutError extends FleetFusionError {
  constructor(timeout: number, context?: string) {
    super(`Request timed out after ${timeout}ms`, 408, 'TIMEOUT', { timeout }, context);
    this.name = 'TimeoutError';
  }
}

export class ErrorHandler {
  /**
   * Handle API errors and convert them to appropriate error types
   */
  static handleApiError(error: any, context?: string): FleetFusionError {
    // Network errors (fetch failures, etc.)
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return new NetworkError('Unable to connect to server', context);
    }

    // Timeout errors
    if (error.name === 'AbortError') {
      return new TimeoutError(30000, context);
    }

    // If it's already a FleetFusionError, just add context if missing
    if (error instanceof FleetFusionError) {
      if (!error.context && context) {
        return new FleetFusionError(
          error.message,
          error.status,
          error.code,
          error.details,
          context
        );
      }
      return error;
    }

    // Handle HTTP Response errors
    if (error instanceof Response) {
      return this.handleHttpError(error, context);
    }

    // Handle Error objects with response property (axios-style)
    if (error.response) {
      return this.handleHttpError(error.response, context);
    }

    // Handle fetch Response-like objects
    if (error.status && typeof error.status === 'number') {
      return this.createErrorFromStatus(error.status, error.statusText || error.message, context);
    }

    // Generic JavaScript errors
    if (error instanceof Error) {
      return new FleetFusionError(
        error.message || 'An unexpected error occurred',
        undefined,
        'GENERIC_ERROR',
        { originalError: error.name },
        context
      );
    }

    // Unknown error types
    return new FleetFusionError(
      'An unexpected error occurred',
      undefined,
      'UNKNOWN_ERROR',
      { originalError: error },
      context
    );
  }

  /**
   * Handle HTTP response errors
   */
  private static handleHttpError(response: Response, context?: string): FleetFusionError {
    let errorData: any = {};
    
    try {
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        // For sync handling, we'll just use the status text for now
        errorData = { message: response.statusText };
      } else {
        errorData = { message: response.statusText };
      }
    } catch {
      // If we can't parse the response, use status text
      errorData = { message: response.statusText };
    }

    const message = errorData.message || errorData.error || response.statusText || 'Request failed';
    
    return this.createErrorFromStatus(response.status, message, context, errorData);
  }

  /**
   * Create appropriate error type based on HTTP status code
   */
  private static createErrorFromStatus(
    status: number,
    message: string,
    context?: string,
    details?: any
  ): FleetFusionError {
    switch (status) {
      case 400:
        return new ValidationError(message, details, context);
      case 401:
        return new AuthenticationError(message, context);
      case 403:
        return new AuthorizationError(message, context);
      case 404:
        return new NotFoundError(message, context);
      case 409:
        return new ConflictError(message, details, context);
      case 429:
        const retryAfter = details?.retryAfter || details?.['retry-after'];
        return new RateLimitError(retryAfter, context);
      case 408:
        return new TimeoutError(30000, context);
      default:
        if (status >= 500) {
          return new ServerError(message, status, context);
        }
        return new FleetFusionError(message, status, 'HTTP_ERROR', details, context);
    }
  }

  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: FleetFusionError): string {
    switch (error.code) {
      case 'NETWORK_ERROR':
        return 'Unable to connect to the server. Please check your internet connection.';
      case 'TIMEOUT':
        return 'The request took too long to complete. Please try again.';
      case 'AUTH_ERROR':
        return 'Please log in to continue.';
      case 'AUTHORIZATION_ERROR':
        return 'You do not have permission to perform this action.';
      case 'VALIDATION_ERROR':
        return error.message || 'Please check your input and try again.';
      case 'NOT_FOUND':
        return error.message || 'The requested resource was not found.';
      case 'CONFLICT_ERROR':
        return error.message || 'This action conflicts with existing data.';
      case 'RATE_LIMIT':
        return 'Too many requests. Please wait a moment before trying again.';
      case 'SERVER_ERROR':
        return 'A server error occurred. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Create a retry handler with exponential backoff
   */
  static createRetryHandler<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): () => Promise<T> {
    return async (): Promise<T> => {
      let lastError: FleetFusionError;

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = this.handleApiError(error);
          
          // Don't retry if it's not retryable or on the last attempt
          if (!lastError.isRetryable || attempt === maxRetries) {
            throw lastError;
          }

          // Exponential backoff with jitter
          const delay = Math.min(baseDelay * Math.pow(2, attempt), 10000);
          const jitter = Math.random() * 0.1 * delay;
          await new Promise(resolve => setTimeout(resolve, delay + jitter));
        }
      }

      throw lastError!;
    };
  }

  /**
   * Log error for debugging purposes
   */
  static logError(error: FleetFusionError, additionalContext?: any): void {
    const logData = {
      ...error.toJSON(),
      ...additionalContext,
    };

    if (process.env.NODE_ENV === 'development') {
      console.error('FleetFusion Error:', logData);
    } else {
      // In production, you might want to send to an error tracking service
      console.error('Application Error:', {
        message: error.message,
        code: error.code,
        context: error.context,
        timestamp: error.timestamp,
      });
    }
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(error: FleetFusionError): boolean {
    return error.isRetryable;
  }

  /**
   * Get retry delay for rate limited requests
   */
  static getRetryDelay(error: FleetFusionError): number {
    if (error instanceof RateLimitError && error.details?.retryAfter) {
      return error.details.retryAfter * 1000; // Convert seconds to milliseconds
    }
    return 1000; // Default 1 second
  }
}

export default ErrorHandler;
