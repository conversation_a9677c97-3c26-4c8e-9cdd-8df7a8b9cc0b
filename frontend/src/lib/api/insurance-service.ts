import { InsurancePolicy, InsuranceType, PolicyStatus } from '@/types/insurance';
import { ApiClient } from '../api-client';
import { <PERSON>rror<PERSON>and<PERSON> } from '../error-handler';

// Type definitions for API requests
export interface CreateInsurancePolicyRequest {
  vehicleId: string;
  policyNumber: string;
  provider: string;
  type: InsuranceType;
  startDate: string;
  endDate: string;
  premium: number;
  coverage: number;
  deductible: number;
  notes?: string;
}

export interface UpdateInsurancePolicyRequest extends Partial<CreateInsurancePolicyRequest> {
  status?: PolicyStatus;
}

/**
 * Modernized Insurance Service using ApiClient and ErrorHandler
 * Provides comprehensive insurance policy management functionality with proper error handling,
 * retry logic, loading states, and consistent API responses.
 */
export class InsuranceService {
  private static apiClient = new ApiClient();

  // Utility method to build query parameters
  private static buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  }

  // Insurance Policy Management Methods

  /**
   * Get all insurance policies with optional filtering
   * @param params - Optional query parameters for filtering
   * @returns Promise<InsurancePolicy[]> - Array of insurance policies
   */
  static async getPolicies(params?: {
    vehicleId?: string;
    status?: PolicyStatus;
    type?: InsuranceType;
    provider?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<InsurancePolicy[]> {
    try {
      const queryString = params ? this.buildQueryString(params) : '';
      const endpoint = queryString ? `/insurance?${queryString}` : '/insurance';
      
      return await this.apiClient.get<InsurancePolicy[]>(endpoint);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'InsuranceService.getPolicies');
      throw error;
    }
  }

  /**
   * Get insurance policies for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Promise<InsurancePolicy[]> - Array of insurance policies
   */
  static async getVehiclePolicies(vehicleId: string): Promise<InsurancePolicy[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      return await this.apiClient.get<InsurancePolicy[]>(`/vehicles/${vehicleId}/insurance`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.getVehiclePolicies(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get a specific insurance policy by ID
   * @param id - Policy ID
   * @returns Promise<InsurancePolicy> - Policy details
   */
  static async getPolicyById(id: string): Promise<InsurancePolicy> {
    if (!id) {
      throw new Error('Policy ID is required');
    }

    try {
      return await this.apiClient.get<InsurancePolicy>(`/insurance/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.getPolicyById(${id})`);
      throw error;
    }
  }

  /**
   * Create a new insurance policy
   * @param policyData - Policy creation data
   * @returns Promise<InsurancePolicy> - Created policy
   */
  static async createPolicy(policyData: CreateInsurancePolicyRequest): Promise<InsurancePolicy> {
    try {
      const policy = await this.apiClient.post<InsurancePolicy>('/insurance', policyData);
      return policy;
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'InsuranceService.createPolicy');
      throw error;
    }
  }

  /**
   * Update an existing insurance policy
   * @param id - Policy ID
   * @param policyData - Policy update data
   * @returns Promise<InsurancePolicy> - Updated policy
   */
  static async updatePolicy(id: string, policyData: UpdateInsurancePolicyRequest): Promise<InsurancePolicy> {
    if (!id) {
      throw new Error('Policy ID is required');
    }

    try {
      const policy = await this.apiClient.patch<InsurancePolicy>(`/insurance/${id}`, policyData);
      return policy;
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.updatePolicy(${id})`);
      throw error;
    }
  }

  /**
   * Delete an insurance policy
   * @param id - Policy ID
   * @returns Promise<void>
   */
  static async deletePolicy(id: string): Promise<void> {
    if (!id) {
      throw new Error('Policy ID is required');
    }

    try {
      await this.apiClient.delete(`/insurance/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.deletePolicy(${id})`);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get upcoming insurance policy renewals
   * @param days - Number of days to look ahead (default: 30)
   * @returns Promise<InsurancePolicy[]> - Policies requiring renewal
   */
  static async getUpcomingRenewals(days: number = 30): Promise<InsurancePolicy[]> {
    try {
      const queryString = this.buildQueryString({ days });
      return await this.apiClient.get<InsurancePolicy[]>(`/insurance/upcoming-renewals?${queryString}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'InsuranceService.getUpcomingRenewals');
      throw error;
    }
  }

  /**
   * Get expired insurance policies
   * @returns Promise<InsurancePolicy[]> - Expired policies
   */
  static async getExpiredPolicies(): Promise<InsurancePolicy[]> {
    try {
      return await this.apiClient.get<InsurancePolicy[]>('/insurance/expired');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'InsuranceService.getExpiredPolicies');
      throw error;
    }
  }

  /**
   * Get insurance statistics
   * @returns Promise<any> - Insurance statistics
   */
  static async getInsuranceStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    renewalDue: number;
    cancelled: number;
    totalPremium: number;
    totalCoverage: number;
  }> {
    try {
      return await this.apiClient.get('/insurance/stats');
    } catch (error) {
      // Don't show toast for stats errors, just log them
      ErrorHandler.handleApiError(error, 'InsuranceService.getInsuranceStats');
      throw error;
    }
  }

  /**
   * Renew an insurance policy
   * @param id - Policy ID
   * @param renewalData - Renewal data
   * @returns Promise<InsurancePolicy> - Renewed policy
   */
  static async renewPolicy(id: string, renewalData: {
    endDate: string;
    premium?: number;
    coverage?: number;
    deductible?: number;
  }): Promise<InsurancePolicy> {
    if (!id) {
      throw new Error('Policy ID is required');
    }

    try {
      const policy = await this.apiClient.post<InsurancePolicy>(`/insurance/${id}/renew`, renewalData);
      return policy;
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.renewPolicy(${id})`);
      throw error;
    }
  }

  /**
   * Cancel an insurance policy
   * @param id - Policy ID
   * @param reason - Cancellation reason
   * @returns Promise<InsurancePolicy> - Cancelled policy
   */
  static async cancelPolicy(id: string, reason?: string): Promise<InsurancePolicy> {
    if (!id) {
      throw new Error('Policy ID is required');
    }

    try {
      const policy = await this.apiClient.patch<InsurancePolicy>(`/insurance/${id}/cancel`, { reason });
      return policy;
    } catch (error) {
      ErrorHandler.showErrorToast(error, `InsuranceService.cancelPolicy(${id})`);
      throw error;
    }
  }
}
