import { api } from '../api-client';

export interface Assignment {
  id: string;
  vehicleId: string;
  driverId: string;
  startDate: string;
  endDate?: string;
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  type?: string;
  priority?: string;
  notes?: string;
  vehicle?: any;
  driver?: any;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAssignmentDto {
  driverId: string;
  vehicleId: string;
  startDate: string;
  endDate?: string;
  notes?: string;
  type?: string;
  priority?: string;
}

export class VehicleAssignmentService {
  /**
   * Get all assignments
   * @returns Array of vehicle assignments
   */
  static async getAllAssignments(): Promise<Assignment[]> {
    try {
      console.log('🔍 Fetching assignments from API...');
      const data = await api.get<Assignment[]>('/vehicles/assignments');
      console.log(`✅ Assignment API returned ${data?.length || 0} assignments`);
      return data || [];
    } catch (error) {
      console.error('❌ Error fetching assignments:', error);
      throw error;
    }
  }

  /**
   * Get assignments for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Array of vehicle assignments
   */
  static async getVehicleAssignments(vehicleId: string): Promise<Assignment[]> {
    try {
      const data = await api.get<Assignment[]>(`/vehicles/${vehicleId}/assignments`);
      return data || [];
    } catch (error) {
      console.error(`Error fetching assignments for vehicle ${vehicleId}:`, error);
      throw error;
    }
  }

  /**
   * Get assignments for a specific driver
   * @param driverId - Driver ID
   * @returns Array of vehicle assignments
   */
  static async getDriverAssignments(driverId: string): Promise<Assignment[]> {
    try {
      const data = await api.get<Assignment[]>(`/users/drivers/${driverId}/assignments`);
      return data || [];
    } catch (error) {
      console.error(`Error fetching assignments for driver ${driverId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new vehicle assignment
   * @param assignment - Assignment data
   * @returns Created assignment
   */
  static async createAssignment(assignment: CreateAssignmentDto): Promise<Assignment> {
    try {
      const data = await api.post<Assignment>(`/vehicles/${assignment.vehicleId}/assignments`, {
        driverId: assignment.driverId,
        startDate: assignment.startDate,
        endDate: assignment.endDate,
        notes: assignment.notes,
        type: assignment.type,
        priority: assignment.priority
      });
      return data;
    } catch (error) {
      console.error('Error creating assignment:', error);
      throw error;
    }
  }

  /**
   * Complete a vehicle assignment (end the assignment)
   * @param assignmentId - Assignment ID
   * @returns Updated assignment
   */
  static async completeAssignment(assignmentId: string): Promise<Assignment> {
    try {
      const data = await api.patch<Assignment>(`/vehicles/assignments/${assignmentId}/complete`);
      return data;
    } catch (error) {
      console.error(`Error completing assignment ${assignmentId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel a vehicle assignment
   * @param assignmentId - Assignment ID
   * @returns Updated assignment
   */
  static async cancelAssignment(assignmentId: string): Promise<Assignment> {
    try {
      const data = await api.patch<Assignment>(`/vehicles/assignments/${assignmentId}/cancel`);
      return data;
    } catch (error) {
      console.error(`Error cancelling assignment ${assignmentId}:`, error);
      throw error;
    }
  }
}
