import { ApiClient } from '../api-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../error-handler';
import { VehicleReview, ReviewType, ReviewStatus } from '@/types/review';

// Request interfaces for type safety
export interface CreateReviewRequest {
  vehicleId: string;
  reviewType: ReviewType;
  reviewBy?: string;
  scheduledDate: string;
  location?: string;
  findings?: string;
  recommendations?: string;
  nextReviewDate?: string;
  documents?: string[];
}

export interface UpdateReviewRequest {
  reviewType?: ReviewType;
  reviewBy?: string;
  scheduledDate?: string;
  completedDate?: string;
  location?: string;
  status?: ReviewStatus;
  findings?: string;
  recommendations?: string;
  nextReviewDate?: string;
  documents?: string[];
}

export interface ReviewFilters {
  vehicleId?: string;
  reviewType?: ReviewType;
  status?: ReviewStatus;
  fromDate?: string;
  toDate?: string;
  location?: string;
  reviewBy?: string;
  limit?: number;
  offset?: number;
}

export interface ReviewStats {
  totalReviews: number;
  completedReviews: number;
  pendingReviews: number;
  failedReviews: number;
  upcomingReviews: number;
  overdueReviews: number;
  reviewsByType: Record<ReviewType, number>;
  reviewsByStatus: Record<ReviewStatus, number>;
  averageCompletionTime?: number;
  complianceRate: number;
}

/**
 * Service for vehicle reviews API calls
 */
export class ReviewService {
  private apiClient: ApiClient;

  constructor() {
    this.apiClient = new ApiClient();
  }

  /**
   * Build query string from filters
   */
  private buildQueryString(filters: ReviewFilters): string {
    const params = new URLSearchParams();
    
    if (filters.vehicleId) params.append('vehicleId', filters.vehicleId);
    if (filters.reviewType) params.append('reviewType', filters.reviewType);
    if (filters.status) params.append('status', filters.status);
    if (filters.fromDate) params.append('fromDate', filters.fromDate);
    if (filters.toDate) params.append('toDate', filters.toDate);
    if (filters.location) params.append('location', filters.location);
    if (filters.reviewBy) params.append('reviewBy', filters.reviewBy);
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());
    
    return params.toString();
  }

  /**
   * Get all vehicle reviews with optional filtering
   */
  async getReviews(filters: ReviewFilters = {}): Promise<VehicleReview[]> {
    try {
      const queryString = this.buildQueryString(filters);
      const url = queryString ? `/reviews?${queryString}` : '/reviews';
      
      return await this.apiClient.get<VehicleReview[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch vehicle reviews');
      throw error;
    }
  }

  /**
   * Get reviews for a specific vehicle
   */
  async getVehicleReviews(vehicleId: string): Promise<VehicleReview[]> {
    try {
      return await this.apiClient.get<VehicleReview[]>(`/reviews?vehicleId=${vehicleId}`);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch vehicle reviews');
      throw error;
    }
  }

  /**
   * Get a review by ID
   */
  async getReviewById(id: string): Promise<VehicleReview> {
    try {
      return await this.apiClient.get<VehicleReview>(`/reviews/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch review details');
      throw error;
    }
  }

  /**
   * Create a new vehicle review
   */
  async createReview(reviewData: CreateReviewRequest): Promise<VehicleReview> {
    try {
      return await this.apiClient.post<VehicleReview>('/reviews', reviewData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to create vehicle review');
      throw error;
    }
  }

  /**
   * Update a vehicle review
   */
  async updateReview(id: string, reviewData: UpdateReviewRequest): Promise<VehicleReview> {
    try {
      return await this.apiClient.patch<VehicleReview>(`/reviews/${id}`, reviewData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to update vehicle review');
      throw error;
    }
  }

  /**
   * Delete a vehicle review
   */
  async deleteReview(id: string): Promise<void> {
    try {
      await this.apiClient.delete(`/reviews/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to delete vehicle review');
      throw error;
    }
  }

  /**
   * Update review status
   */
  async updateReviewStatus(id: string, status: ReviewStatus, completedDate?: string): Promise<VehicleReview> {
    try {
      const updateData: UpdateReviewRequest = { status };
      if (completedDate) {
        updateData.completedDate = completedDate;
      }
      
      return await this.apiClient.patch<VehicleReview>(`/reviews/${id}`, updateData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to update review status');
      throw error;
    }
  }

  /**
   * Complete a review
   */
  async completeReview(id: string, findings?: string, recommendations?: string, nextReviewDate?: string): Promise<VehicleReview> {
    try {
      const updateData: UpdateReviewRequest = {
        status: 'COMPLETED',
        completedDate: new Date().toISOString(),
        ...(findings && { findings }),
        ...(recommendations && { recommendations }),
        ...(nextReviewDate && { nextReviewDate })
      };
      
      return await this.apiClient.patch<VehicleReview>(`/reviews/${id}`, updateData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to complete review');
      throw error;
    }
  }

  /**
   * Cancel a review
   */
  async cancelReview(id: string): Promise<VehicleReview> {
    try {
      return await this.apiClient.patch<VehicleReview>(`/reviews/${id}`, { 
        status: 'CANCELLED' 
      });
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to cancel review');
      throw error;
    }
  }

  /**
   * Get upcoming reviews (next 30 days)
   */
  async getUpcomingReviews(daysAhead: number = 30): Promise<VehicleReview[]> {
    try {
      const toDate = new Date();
      toDate.setDate(toDate.getDate() + daysAhead);
      
      return await this.apiClient.get<VehicleReview[]>(
        `/reviews/upcoming?toDate=${toDate.toISOString().split('T')[0]}`
      );
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch upcoming reviews');
      throw error;
    }
  }

  /**
   * Get overdue reviews
   */
  async getOverdueReviews(): Promise<VehicleReview[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      return await this.apiClient.get<VehicleReview[]>(
        `/reviews/overdue?toDate=${today}`
      );
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch overdue reviews');
      throw error;
    }
  }

  /**
   * Get reviews by type
   */
  async getReviewsByType(reviewType: ReviewType): Promise<VehicleReview[]> {
    try {
      return await this.apiClient.get<VehicleReview[]>(`/reviews?reviewType=${reviewType}`);
    } catch (error) {
      ErrorHandler.showErrorToast(`Failed to fetch ${reviewType} reviews`);
      throw error;
    }
  }

  /**
   * Get review statistics
   */
  async getReviewStats(vehicleId?: string): Promise<ReviewStats> {
    try {
      const url = vehicleId ? `/reviews/stats?vehicleId=${vehicleId}` : '/reviews/stats';
      return await this.apiClient.get<ReviewStats>(url);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch review statistics');
      throw error;
    }
  }

  /**
   * Get review compliance report
   */
  async getComplianceReport(fromDate?: string, toDate?: string): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (fromDate) params.append('fromDate', fromDate);
      if (toDate) params.append('toDate', toDate);
      
      const queryString = params.toString();
      const url = queryString ? `/reviews/compliance?${queryString}` : '/reviews/compliance';
      
      return await this.apiClient.get(url);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to fetch compliance report');
      throw error;
    }
  }

  /**
   * Schedule next review for a vehicle
   */
  async scheduleNextReview(vehicleId: string, reviewType: ReviewType, scheduledDate: string, location?: string): Promise<VehicleReview> {
    try {
      const reviewData: CreateReviewRequest = {
        vehicleId,
        reviewType,
        scheduledDate,
        ...(location && { location })
      };
      
      return await this.apiClient.post<VehicleReview>('/reviews', reviewData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to schedule next review');
      throw error;
    }
  }

  /**
   * Reschedule a review
   */
  async rescheduleReview(id: string, newScheduledDate: string, location?: string): Promise<VehicleReview> {
    try {
      const updateData: UpdateReviewRequest = {
        scheduledDate: newScheduledDate,
        ...(location && { location })
      };
      
      return await this.apiClient.patch<VehicleReview>(`/reviews/${id}`, updateData);
    } catch (error) {
      ErrorHandler.showErrorToast('Failed to reschedule review');
      throw error;
    }
  }
}
