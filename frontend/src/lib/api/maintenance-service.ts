import { ApiClient } from '@/lib/api-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';
import { MaintenanceLog } from '@/types/maintenance';

// Type definitions for maintenance
export type MaintenanceType = 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
export type MaintenanceCategory = 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
export type MaintenanceStatus = 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';

// Request type definitions
export interface CreateMaintenanceLogRequest {
  type: MaintenanceType;
  category: MaintenanceCategory;
  description: string;
  date?: string;
  scheduledDate?: string;
  vehicleId: string;
  mileage?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  status?: MaintenanceStatus;
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
}

export interface UpdateMaintenanceLogRequest {
  type?: MaintenanceType;
  category?: MaintenanceCategory;
  description?: string;
  date?: string;
  scheduledDate?: string;
  vehicleId?: string;
  mileage?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  status?: MaintenanceStatus;
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
}

export interface MaintenanceFilters {
  vehicleId?: string;
  type?: MaintenanceType;
  category?: MaintenanceCategory;
  status?: MaintenanceStatus;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export class MaintenanceService {
  private static apiClient = new ApiClient();

  /**
   * Build query string from filters
   */
  private static buildQueryString(filters: Record<string, any>): string {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });
    
    return params.toString();
  }

  // Core CRUD Operations

  /**
   * Get all maintenance logs with optional filters
   * @param filters - Optional filters for maintenance logs
   * @returns Promise<MaintenanceLog[]> - Array of maintenance logs
   */
  static async getMaintenanceLogs(filters?: MaintenanceFilters): Promise<MaintenanceLog[]> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/maintenance?${queryString}` : '/maintenance';
      return await this.apiClient.get<MaintenanceLog[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.getMaintenanceLogs');
      throw error;
    }
  }

  /**
   * Get maintenance logs for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @param filters - Optional additional filters
   * @returns Promise<MaintenanceLog[]> - Array of vehicle maintenance logs
   */
  static async getVehicleMaintenanceLogs(vehicleId: string, filters?: Omit<MaintenanceFilters, 'vehicleId'>): Promise<MaintenanceLog[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/vehicles/${vehicleId}/maintenance?${queryString}` : `/vehicles/${vehicleId}/maintenance`;
      return await this.apiClient.get<MaintenanceLog[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.getVehicleMaintenanceLogs(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get a specific maintenance log by ID
   * @param id - Maintenance log ID
   * @returns Promise<MaintenanceLog> - Maintenance log details
   */
  static async getMaintenanceLog(id: string): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      return await this.apiClient.get<MaintenanceLog>(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.getMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Create a new maintenance log
   * @param maintenanceData - Maintenance log creation data
   * @returns Promise<MaintenanceLog> - Created maintenance log
   */
  static async createMaintenanceLog(maintenanceData: CreateMaintenanceLogRequest): Promise<MaintenanceLog> {
    try {
      return await this.apiClient.post<MaintenanceLog>('/maintenance', maintenanceData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.createMaintenanceLog');
      throw error;
    }
  }

  /**
   * Update an existing maintenance log
   * @param id - Maintenance log ID
   * @param maintenanceData - Maintenance log update data
   * @returns Promise<MaintenanceLog> - Updated maintenance log
   */
  static async updateMaintenanceLog(id: string, maintenanceData: UpdateMaintenanceLogRequest): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      return await this.apiClient.patch<MaintenanceLog>(`/maintenance/${id}`, maintenanceData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.updateMaintenanceLog(${id})`);
      throw error;
    }
  }

  /**
   * Delete a maintenance log
   * @param id - Maintenance log ID
   */
  static async deleteMaintenanceLog(id: string): Promise<void> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      await this.apiClient.delete(`/maintenance/${id}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.deleteMaintenanceLog(${id})`);
      throw error;
    }
  }

  // Status Management

  /**
   * Update maintenance log status
   * @param id - Maintenance log ID
   * @param status - New status
   * @returns Promise<MaintenanceLog> - Updated maintenance log
   */
  static async updateMaintenanceStatus(id: string, status: MaintenanceStatus): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      return await this.apiClient.patch<MaintenanceLog>(`/maintenance/${id}/status`, { status });
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.updateMaintenanceStatus(${id})`);
      throw error;
    }
  }

  /**
   * Mark maintenance as completed
   * @param id - Maintenance log ID
   * @param completionData - Completion data
   * @returns Promise<MaintenanceLog> - Updated maintenance log
   */
  static async completeMaintenanceLog(id: string, completionData?: {
    actualCost?: number;
    partsCost?: number;
    laborCost?: number;
    notes?: string;
    mileage?: number;
  }): Promise<MaintenanceLog> {
    if (!id) {
      throw new Error('Maintenance log ID is required');
    }

    try {
      return await this.apiClient.patch<MaintenanceLog>(`/maintenance/${id}/complete`, completionData || {});
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.completeMaintenanceLog(${id})`);
      throw error;
    }
  }

  // Utility Methods

  /**
   * Get upcoming maintenance for a vehicle
   * @param vehicleId - Vehicle ID
   * @param days - Number of days to look ahead (default: 30)
   * @returns Promise<MaintenanceLog[]> - Array of upcoming maintenance
   */
  static async getUpcomingMaintenance(vehicleId?: string, days: number = 30): Promise<MaintenanceLog[]> {
    try {
      const queryString = vehicleId ? `vehicleId=${vehicleId}&days=${days}` : `days=${days}`;
      return await this.apiClient.get<MaintenanceLog[]>(`/maintenance/upcoming?${queryString}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.getUpcomingMaintenance');
      throw error;
    }
  }

  /**
   * Get overdue maintenance
   * @param vehicleId - Optional vehicle ID to filter
   * @returns Promise<MaintenanceLog[]> - Array of overdue maintenance
   */
  static async getOverdueMaintenance(vehicleId?: string): Promise<MaintenanceLog[]> {
    try {
      const queryString = vehicleId ? `vehicleId=${vehicleId}` : '';
      const url = queryString ? `/maintenance/overdue?${queryString}` : '/maintenance/overdue';
      return await this.apiClient.get<MaintenanceLog[]>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.getOverdueMaintenance');
      throw error;
    }
  }

  /**
   * Get maintenance statistics
   * @param filters - Optional filters for statistics
   * @returns Promise<any> - Maintenance statistics
   */
  static async getMaintenanceStats(filters?: {
    vehicleId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    try {
      const queryString = filters ? this.buildQueryString(filters) : '';
      const url = queryString ? `/maintenance/stats?${queryString}` : '/maintenance/stats';
      return await this.apiClient.get<any>(url);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.getMaintenanceStats');
      throw error;
    }
  }

  /**
   * Get maintenance cost analysis
   * @param vehicleId - Optional vehicle ID
   * @param period - Period for analysis (default: 'year')
   * @returns Promise<any> - Cost analysis data
   */
  static async getMaintenanceCostAnalysis(vehicleId?: string, period: 'month' | 'quarter' | 'year' = 'year'): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (vehicleId) params.append('vehicleId', vehicleId);
      params.append('period', period);
      
      return await this.apiClient.get<any>(`/maintenance/cost-analysis?${params.toString()}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'MaintenanceService.getMaintenanceCostAnalysis');
      throw error;
    }
  }

  /**
   * Get maintenance schedule for a vehicle
   * @param vehicleId - Vehicle ID
   * @returns Promise<MaintenanceLog[]> - Scheduled maintenance
   */
  static async getMaintenanceSchedule(vehicleId: string): Promise<MaintenanceLog[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      return await this.apiClient.get<MaintenanceLog[]>(`/vehicles/${vehicleId}/maintenance/schedule`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `MaintenanceService.getMaintenanceSchedule(${vehicleId})`);
      throw error;
    }
  }
}
