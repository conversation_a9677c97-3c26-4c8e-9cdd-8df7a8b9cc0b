import { ApiClient } from '../api-client';
import { ErrorHandler } from '../error-handler';
import { MaintenanceLog, MaintenanceCategory, VehicleType } from '@/types/maintenance';

export interface MaintenanceRecommendation {
  type: string;
  category: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  estimatedCost: number;
  dueAtMileage?: number;
  note?: string;
}

export interface MaintenanceStats {
  trucks: {
    totalMaintenance: number;
    averageCost: number;
    totalCost: number;
    averagePartsCost: number;
    averageLaborCost: number;
  };
  trailers: {
    totalMaintenance: number;
    averageCost: number;
    totalCost: number;
    averagePartsCost: number;
    averageLaborCost: number;
  };
}

export interface CreateMaintenanceRequest {
  vehicleId: string;
  type: string;
  category: MaintenanceCategory;
  description: string;
  status?: string;
  date?: string;
  scheduledDate: string;
  mileage?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
}

export class EnhancedMaintenanceService {
  private static apiClient = new ApiClient();

  /**
   * Get maintenance categories for a specific vehicle type
   * @param vehicleType - Vehicle type (TRUCK or TRAILER)
   * @returns Promise<MaintenanceCategory[]> - Array of valid categories
   */
  static async getMaintenanceCategoriesForVehicleType(vehicleType: VehicleType): Promise<MaintenanceCategory[]> {
    try {
      return await this.apiClient.get<MaintenanceCategory[]>(`/enhanced-maintenance/categories/${vehicleType}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.getMaintenanceCategoriesForVehicleType');
      throw error;
    }
  }

  /**
   * Get maintenance recommendations for a specific vehicle
   * @param vehicleId - Vehicle ID
   * @returns Promise<MaintenanceRecommendation[]> - Array of maintenance recommendations
   */
  static async getMaintenanceRecommendations(vehicleId: string): Promise<MaintenanceRecommendation[]> {
    if (!vehicleId) {
      throw new Error('Vehicle ID is required');
    }

    try {
      return await this.apiClient.get<MaintenanceRecommendation[]>(`/enhanced-maintenance/recommendations/${vehicleId}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `EnhancedMaintenanceService.getMaintenanceRecommendations(${vehicleId})`);
      throw error;
    }
  }

  /**
   * Get maintenance records by vehicle type
   * @param vehicleType - Vehicle type (TRUCK or TRAILER)
   * @returns Promise<MaintenanceLog[]> - Array of maintenance records
   */
  static async getMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]> {
    try {
      return await this.apiClient.get<MaintenanceLog[]>(`/enhanced-maintenance/by-vehicle-type?type=${vehicleType}`);
    } catch (error) {
      ErrorHandler.showErrorToast(error, `EnhancedMaintenanceService.getMaintenanceByVehicleType(${vehicleType})`);
      throw error;
    }
  }

  /**
   * Get maintenance statistics by vehicle type
   * @returns Promise<MaintenanceStats> - Maintenance statistics
   */
  static async getMaintenanceStatsByVehicleType(): Promise<MaintenanceStats> {
    try {
      return await this.apiClient.get<MaintenanceStats>('/enhanced-maintenance/stats/by-vehicle-type');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.getMaintenanceStatsByVehicleType');
      throw error;
    }
  }

  /**
   * Get upcoming truck maintenance
   * @returns Promise<MaintenanceLog[]> - Array of upcoming truck maintenance
   */
  static async getUpcomingTruckMaintenance(): Promise<MaintenanceLog[]> {
    try {
      return await this.apiClient.get<MaintenanceLog[]>('/enhanced-maintenance/upcoming/trucks');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.getUpcomingTruckMaintenance');
      throw error;
    }
  }

  /**
   * Get upcoming trailer maintenance
   * @returns Promise<MaintenanceLog[]> - Array of upcoming trailer maintenance
   */
  static async getUpcomingTrailerMaintenance(): Promise<MaintenanceLog[]> {
    try {
      return await this.apiClient.get<MaintenanceLog[]>('/enhanced-maintenance/upcoming/trailers');
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.getUpcomingTrailerMaintenance');
      throw error;
    }
  }

  /**
   * Create maintenance record with vehicle type validation
   * @param maintenanceData - Maintenance creation data
   * @returns Promise<MaintenanceLog> - Created maintenance record
   */
  static async createMaintenance(maintenanceData: CreateMaintenanceRequest): Promise<MaintenanceLog> {
    try {
      return await this.apiClient.post<MaintenanceLog>('/enhanced-maintenance', maintenanceData);
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.createMaintenance');
      throw error;
    }
  }

  /**
   * Get maintenance dashboard data
   * @returns Promise<any> - Dashboard data with stats and upcoming maintenance
   */
  static async getMaintenanceDashboard(): Promise<{
    stats: MaintenanceStats;
    upcomingTrucks: MaintenanceLog[];
    upcomingTrailers: MaintenanceLog[];
    recommendations: { [vehicleId: string]: MaintenanceRecommendation[] };
  }> {
    try {
      const [stats, upcomingTrucks, upcomingTrailers] = await Promise.all([
        this.getMaintenanceStatsByVehicleType(),
        this.getUpcomingTruckMaintenance(),
        this.getUpcomingTrailerMaintenance(),
      ]);

      return {
        stats,
        upcomingTrucks,
        upcomingTrailers,
        recommendations: {}, // Can be populated with specific vehicle recommendations
      };
    } catch (error) {
      ErrorHandler.showErrorToast(error, 'EnhancedMaintenanceService.getMaintenanceDashboard');
      throw error;
    }
  }

  /**
   * Get maintenance overview for a specific vehicle type
   * @param vehicleType - Vehicle type (TRUCK or TRAILER)
   * @returns Promise<any> - Maintenance overview data
   */
  static async getMaintenanceOverview(vehicleType: VehicleType): Promise<{
    maintenance: MaintenanceLog[];
    upcoming: MaintenanceLog[];
    categories: MaintenanceCategory[];
  }> {
    try {
      const [maintenance, upcoming, categories] = await Promise.all([
        this.getMaintenanceByVehicleType(vehicleType),
        vehicleType === VehicleType.TRUCK 
          ? this.getUpcomingTruckMaintenance() 
          : this.getUpcomingTrailerMaintenance(),
        this.getMaintenanceCategoriesForVehicleType(vehicleType),
      ]);

      return {
        maintenance,
        upcoming,
        categories,
      };
    } catch (error) {
      ErrorHandler.showErrorToast(error, `EnhancedMaintenanceService.getMaintenanceOverview(${vehicleType})`);
      throw error;
    }
  }
}
