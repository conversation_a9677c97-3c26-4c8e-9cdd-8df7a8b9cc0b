import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

import { formatDateInTimezone, formatDateTimeInTimezone } from './utils/timezone';

export function formatDate(date: string | Date | null): string {
  if (!date) return "N/A";
  return formatDateInTimezone(date);
}

export function formatDateTime(date: string | Date): string {
  return formatDateTimeInTimezone(date);
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
}

export function formatDistance(distance: number): string {
  return `${distance.toFixed(1)} km`;
}

export function getInitials(name: string): string {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();
}

export function classNames(
  ...classes: (string | boolean | undefined | null)[]
): string {
  return classes.filter(Boolean).join(" ");
}
