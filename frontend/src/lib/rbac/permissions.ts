// Define module permissions based on user roles
export type ModuleName =
  | 'fleet'
  | 'drivers'
  | 'trips'
  | 'fuel'
  | 'documents'
  | 'business-partners';

export type SubmoduleName =
  | 'fleet.vehicles'
  | 'fleet.service'
  | 'fleet.insurance'
  | 'fleet.reviews'
  | 'drivers.list'
  | 'drivers.assignments'
  | 'drivers.contacts'
  | 'trips.list'
  | 'trips.create'
  | 'trips.delete'
  | 'trips.reports'
  | 'fuel.dashboard'
  | 'fuel.records'
  | 'fuel.prices'
  | 'fuel.reports'
  | 'documents.browse'
  | 'documents.upload'
  | 'documents.search'
  | 'business-partners.list'
  | 'business-partners.shippers'
  | 'business-partners.logistics';

export type UserRole = 'ADMIN' | 'MANAGER' | 'DRIVER';

// Define which roles have access to which modules
export const modulePermissions: Record<ModuleName, UserRole[]> = {
  fleet: ['ADMIN', 'MANAGER'],
  drivers: ['ADMIN', 'MANAGER'],
  trips: ['ADMIN', 'MANAGER', 'DRIVER'],
  fuel: ['ADMIN', 'MANAGER'],
  documents: ['ADMIN', 'MANAGER', 'DRIVER'],
  'business-partners': ['ADMIN', 'MANAGER'],
};

// Define which roles have access to which submodules
export const submodulePermissions: Record<SubmoduleName, UserRole[]> = {
  'fleet.vehicles': ['ADMIN', 'MANAGER'],
  'fleet.service': ['ADMIN', 'MANAGER'],
  'fleet.insurance': ['ADMIN'], // Only admin can access insurance data
  'fleet.reviews': ['ADMIN', 'MANAGER'],
  
  'drivers.list': ['ADMIN', 'MANAGER'],
  'drivers.assignments': ['ADMIN', 'MANAGER'],
  'drivers.contacts': ['ADMIN', 'MANAGER'],
  
  'trips.list': ['ADMIN', 'MANAGER', 'DRIVER'],
  'trips.create': ['ADMIN', 'MANAGER'],
  'trips.delete': ['ADMIN', 'MANAGER'], // Only ADMIN and MANAGER can delete trips
  'trips.reports': ['ADMIN', 'MANAGER'],

  'fuel.dashboard': ['ADMIN', 'MANAGER'],
  'fuel.records': ['ADMIN', 'MANAGER'],
  'fuel.prices': ['ADMIN', 'MANAGER'],
  'fuel.reports': ['ADMIN', 'MANAGER'],

  'documents.browse': ['ADMIN', 'MANAGER', 'DRIVER'],
  'documents.upload': ['ADMIN', 'MANAGER'],
  'documents.search': ['ADMIN', 'MANAGER', 'DRIVER'],

  'business-partners.list': ['ADMIN', 'MANAGER'],
  'business-partners.shippers': ['ADMIN', 'MANAGER'],
  'business-partners.logistics': ['ADMIN', 'MANAGER'],
};

// Helper functions to check permissions
export function hasModuleAccess(role: UserRole | undefined | null, module: ModuleName): boolean {
  if (!role) return false;
  return modulePermissions[module].includes(role);
}

export function hasSubmoduleAccess(role: UserRole | undefined | null, submodule: SubmoduleName): boolean {
  if (!role) return false;
  return submodulePermissions[submodule].includes(role);
}

// Get accessible modules for a specific role
export function getAccessibleModules(role: UserRole | undefined | null): ModuleName[] {
  if (!role) return [];
  return Object.entries(modulePermissions)
    .filter(([_, roles]) => roles.includes(role))
    .map(([module]) => module as ModuleName);
}

// Get accessible submodules for a specific role and module
export function getAccessibleSubmodules(role: UserRole | undefined | null, module: ModuleName): SubmoduleName[] {
  if (!role) return [];
  return Object.entries(submodulePermissions)
    .filter(([key, roles]) => key.startsWith(`${module}.`) && roles.includes(role))
    .map(([submodule]) => submodule as SubmoduleName);
}
