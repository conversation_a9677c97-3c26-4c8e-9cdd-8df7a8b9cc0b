'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { ModuleName, SubmoduleName, hasModuleAccess, hasSubmoduleAccess } from './permissions';

interface RouteGuardProps {
  children: ReactNode;
  module?: ModuleName;
  submodule?: SubmoduleName;
  fallbackPath?: string;
}

export default function RouteGuard({
  children,
  module,
  submodule,
  fallbackPath = '/unauthorized',
}: RouteGuardProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('🔒 RouteGuard useEffect:', {
      isLoading,
      user: user ? { role: user.role, email: user.email } : null,
      module,
      submodule
    });

    // Wait until authentication is checked
    if (isLoading) {
      console.log('🔒 RouteGuard: Still loading...');
      return;
    }

    // If no user is logged in, redirect to login
    if (!user) {
      console.log('🔒 RouteGuard: No user, redirecting to login');
      router.push('/login');
      return;
    }

    // Check module access
    if (module && !hasModuleAccess(user.role, module as ModuleName)) {
      console.log('🔒 RouteGuard: Module access denied for', module, 'role:', user.role);
      router.push(fallbackPath);
      return;
    }

    // Check submodule access
    if (submodule && !hasSubmoduleAccess(user.role, submodule as SubmoduleName)) {
      console.log('🔒 RouteGuard: Submodule access denied for', submodule, 'role:', user.role);
      router.push(fallbackPath);
      return;
    }

    console.log('🔒 RouteGuard: Access granted');
  }, [user, isLoading, module, submodule, router, fallbackPath]);

  // Show nothing while checking authentication
  if (isLoading) {
    console.log('🔒 RouteGuard render: Loading...');
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  // If user not logged in, show nothing (will redirect)
  if (!user) {
    console.log('🔒 RouteGuard render: No user');
    return null;
  }

  // If access denied, show nothing (will redirect)
  if ((module && !hasModuleAccess(user.role, module)) ||
      (submodule && !hasSubmoduleAccess(user.role, submodule))) {
    console.log('🔒 RouteGuard render: Access denied');
    return null;
  }

  // User has access, render children
  console.log('🔒 RouteGuard render: Rendering children');
  return <>{children}</>;
}
