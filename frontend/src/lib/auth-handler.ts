/**
 * Global authentication error handler
 * Allows API client to trigger logout without direct dependency on auth context
 */

type AuthErrorHandler = () => void;

let globalAuthErrorHandler: AuthErrorHandler | null = null;

export const setGlobalAuthErrorHandler = (handler: AuthErrorHandler) => {
  globalAuthErrorHandler = handler;
};

export const triggerGlobalAuthError = () => {
  if (globalAuthErrorHandler) {
    console.log('🔐 Triggering global auth error handler');
    globalAuthErrorHandler();
  } else {
    console.warn('🔐 No global auth error handler registered');
  }
};
