import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Add paths that don't require authentication
const publicPaths = ['/login', '/register', '/test-navigation'];
const apiPaths = ['/api']; // Add this if we add API routes in the future  

// Define route redirects from old to new module routes - simplified version for debugging
const redirectMaps = [
  // Fleet routes - keeping only essential redirects
  { from: '/dashboard/fleet', to: '/fleet' },
  { from: '/dashboard/fleet/vehicles', to: '/fleet/vehicles' },
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for API routes to avoid interference with rewrites
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Handle redirects from old routes to new module routes
  for (const redirect of redirectMaps) {
    if (pathname.startsWith(redirect.from)) {
      // Keep any additional path segments and query parameters
      const newPath = pathname.replace(redirect.from, redirect.to);
      return NextResponse.redirect(new URL(newPath, request.url));
    }
  }

  // Allow access to public paths without any checks
  if (publicPaths.includes(pathname)) {
    return NextResponse.next();
  }

  // For debugging purposes, we'll temporarily allow all routes
  // This is only for testing - in production you would want proper authentication
  return NextResponse.next();
}

// Configure middleware to match specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
