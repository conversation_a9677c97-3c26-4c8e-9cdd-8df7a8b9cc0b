'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ConsistentContainer } from '@/components/ui/consistent-layout';
import { 
  BarChart3, 
  Fuel, 
  TrendingUp, 
  Car, 
  Wrench, 
  Shield, 
  Star,
  Users,
  UserCheck,
  Phone,
  Route,
  Plus,
  FileText,
  Building2,
  Truck,
  Package,
  FolderOpen,
  Upload,
  Search
} from 'lucide-react';

interface ModuleTab {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
}

interface ModuleNavigationConfig {
  [key: string]: {
    tabs: ModuleTab[];
    getActiveTab: (pathname: string, searchParams?: URLSearchParams) => string;
  };
}

const getModuleConfigs = (t: any): ModuleNavigationConfig => ({
  fuel: {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3 },
      { id: 'records', label: t('fuel:tabs.records'), icon: Fuel },
      { id: 'prices', label: t('fuel:tabs.prices'), icon: TrendingUp },
      { id: 'analytics', label: t('fuel:tabs.analytics'), icon: BarChart3 },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },
  fleet: {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3 },
      { id: 'vehicles', label: t('fleet:tabs.vehicles'), icon: Car },
      { id: 'service', label: t('fleet:tabs.service'), icon: Wrench },
      { id: 'insurance', label: t('fleet:tabs.insurance'), icon: Shield },
      { id: 'reviews', label: t('fleet:tabs.reviews'), icon: Star },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },
  drivers: {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3, href: '/drivers' },
      { id: 'list', label: t('drivers:title'), icon: Users, href: '/drivers?tab=list' },
      { id: 'assignments', label: t('navigation:assignments'), icon: UserCheck, href: '/drivers?tab=assignments' },
      { id: 'contacts', label: t('navigation:contacts'), icon: Phone, href: '/drivers?tab=contacts' },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },
  trips: {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3, href: '/trips' },
      { id: 'list', label: t('trips:title'), icon: Route, href: '/trips?tab=list' },
      { id: 'create', label: t('trips:createTrip'), icon: Plus, href: '/trips?tab=create' },
      { id: 'reports', label: t('navigation:reports'), icon: FileText, href: '/trips?tab=reports' },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },
  'business-partners': {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3, href: '/business-partners' },
      { id: 'all', label: t('partners:title'), icon: Building2, href: '/business-partners?tab=all' },
      { id: 'shippers', label: t('businessPartners:types.shipper'), icon: Truck, href: '/business-partners?tab=shippers' },
      { id: 'logistics', label: t('partners:types.logisticsPartner'), icon: Package, href: '/business-partners?tab=logistics' },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },
  documents: {
    tabs: [
      { id: 'dashboard', label: t('navigation:dashboard.title'), icon: BarChart3 },
      { id: 'browse', label: t('navigation:browse'), icon: FolderOpen },
      { id: 'upload', label: t('navigation:upload'), icon: Upload },
      { id: 'search', label: t('navigation:search'), icon: Search },
    ],
    getActiveTab: (pathname, searchParams) => {
      const tab = searchParams?.get('tab');
      return tab || 'dashboard';
    },
  },

});

interface ModuleNavigationProps {
  module: string;
  onTabChange?: (tabId: string) => void;
}

export function ModuleNavigation({ module, onTabChange }: ModuleNavigationProps) {
  const { t } = useTranslation(['navigation', 'fuel', 'fleet', 'drivers', 'trips', 'partners']);
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const moduleConfigs = getModuleConfigs(t);
  const config = moduleConfigs[module];

  if (!config) {
    return null;
  }

  const activeTab = config.getActiveTab(pathname, searchParams);

  const handleTabChange = (tabId: string) => {
    const tab = config.tabs.find(t => t.id === tabId);
    
    if (tab?.href) {
      // Navigate to different page
      router.push(tab.href);
    } else if (tab?.onClick) {
      // Custom click handler
      tab.onClick();
    } else if (onTabChange) {
      // Use provided tab change handler (for single-page modules like fuel)
      onTabChange(tabId);
    } else {
      // Default behavior - update URL params
      const url = new URL(window.location.href);
      if (tabId === 'dashboard') {
        url.searchParams.delete('tab');
      } else {
        url.searchParams.set('tab', tabId);
      }
      router.push(url.pathname + url.search);
    }
  };

  return (
    <div className="bg-white border-b border-gray-200">
      <ConsistentContainer>
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="h-12 w-full justify-start bg-transparent border-0 p-0">
            {config.tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="h-12 px-4 py-2 data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:text-blue-600 rounded-none border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700 gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>
      </ConsistentContainer>
    </div>
  );
}

// Helper function to determine current module from pathname
export function getCurrentModule(pathname: string): string | null {
  const segments = pathname.split('/').filter(Boolean);
  if (segments.length === 0) return null;

  const moduleMap: { [key: string]: string } = {
    'fleet': 'fleet',
    'drivers': 'drivers',
    'trips': 'trips',
    'business-partners': 'business-partners',
    'fuel': 'fuel',
    'documents': 'documents',
  };

  return moduleMap[segments[0]] || null;
}
