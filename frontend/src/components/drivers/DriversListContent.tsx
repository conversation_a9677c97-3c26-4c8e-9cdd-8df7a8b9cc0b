import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Driver } from '@/types/user';
import { DriverService } from '@/lib/api/driver-service';
import { Plus, UserPlus, Loader2, AlertCircle, Users, Edit } from 'lucide-react';

const driverSchema = z.object({
  email: z.string().email('Nieprawidłowy adres email'),
  password: z.string().min(6, 'Hasło musi mieć co najmniej 6 znaków'),
  firstName: z.string().min(1, 'Imię jest wymagane').max(50, 'Imię jest za długie'),
  lastName: z.string().min(1, 'Nazwisko jest wymagane').max(50, 'Nazwisko jest za długie'),
  phoneNumber: z.string().optional(),
  licenseNumber: z.string().optional(),
});

const editDriverSchema = z.object({
  email: z.string().email('Nieprawidłowy adres email'),
  password: z.string().optional().refine((val) => !val || val.length >= 6, {
    message: 'Hasło musi mieć co najmniej 6 znaków jeśli zostanie podane',
  }),
  firstName: z.string().min(1, 'Imię jest wymagane').max(50, 'Imię jest za długie'),
  lastName: z.string().min(1, 'Nazwisko jest wymagane').max(50, 'Nazwisko jest za długie'),
  phoneNumber: z.string().optional(),
  licenseNumber: z.string().optional(),
});

type DriverFormData = z.infer<typeof driverSchema>;

interface CreateDriverFormData extends DriverFormData {
  role: 'DRIVER';
  status: 'AVAILABLE';
}

interface DriversListContentProps {
  onDriverUpdated?: () => void;
}

export const DriversListContent: React.FC<DriversListContentProps> = ({
  onDriverUpdated
}) => {
  const { t } = useTranslation(['drivers', 'forms', 'common']);
  const router = useRouter();
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<DriverFormData>({
    resolver: zodResolver(driverSchema),
    defaultValues: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      licenseNumber: '',
    },
  });

  const editForm = useForm<DriverFormData>({
    resolver: zodResolver(editDriverSchema),
    defaultValues: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      licenseNumber: '',
    },
  });

  const fetchDrivers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DriverService.getDrivers();
      setDrivers(data);
    } catch (error) {
      console.error('Error fetching drivers:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:messages.loadError', 'Failed to load drivers. Please try again.'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchDrivers();
  }, [fetchDrivers]);

  const handleSubmit = async (data: DriverFormData) => {
    setIsSubmitting(true);
    try {
      const driverData: CreateDriverFormData = {
        ...data,
        role: 'DRIVER',
        status: 'AVAILABLE',
      };

      await DriverService.createDriver(driverData);

      toast({
        title: t('common:success'),
        description: t('drivers:messages.driverCreated'),
      });

      setOpen(false);
      form.reset();
      await fetchDrivers();

      if (onDriverUpdated) {
        onDriverUpdated();
      }
    } catch (error) {
      console.error('Error creating driver:', error);
      toast({
        title: t('common:error'),
        description: error instanceof Error ? error.message : t('drivers:messages.failedToCreateDriver'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = async (data: DriverFormData) => {
    if (!editingDriver) return;

    setIsSubmitting(true);
    try {
      const updateData = {
        ...data,
        // Only include password if it's provided
        ...(data.password ? { password: data.password } : {}),
      };

      await DriverService.updateDriver(editingDriver.id, updateData);

      toast({
        title: t('common:success'),
        description: t('drivers:messages.driverUpdated'),
      });

      setEditOpen(false);
      setEditingDriver(null);
      editForm.reset();
      await fetchDrivers();

      if (onDriverUpdated) {
        onDriverUpdated();
      }
    } catch (error) {
      console.error('Error updating driver:', error);
      toast({
        title: t('common:error'),
        description: error instanceof Error ? error.message : t('drivers:messages.failedToUpdateDriver'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (driver: Driver) => {
    setEditingDriver(driver);
    editForm.reset({
      email: driver.email,
      password: '', // Don't populate password for security
      firstName: driver.firstName,
      lastName: driver.lastName,
      phoneNumber: driver.phone || '',
      licenseNumber: driver.licenseNumber || '',
    });
    setEditOpen(true);
  };

  const handleView = (driverId: string) => {
    router.push(`/drivers?tab=list&driverId=${driverId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'available':
        return t('drivers:status.available');
      case 'assigned':
        return t('drivers:status.assigned');
      case 'inactive':
        return t('drivers:status.inactive');
      case 'active':
        return t('drivers:status.active');
      default:
        return status || t('common:common.notAvailable');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{t('drivers:loadingDrivers')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('drivers:title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('drivers:description', 'Manage your fleet drivers and their information')}
          </p>
        </div>
        
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              {t('drivers:addDriver')}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md" aria-describedby="driver-dialog-description">
            <DialogHeader>
              <DialogTitle>
                <UserPlus className="h-5 w-5 mr-2 inline" />
                {t('drivers:createDriver')}
              </DialogTitle>
              <DialogDescription id="driver-dialog-description">
                {t('drivers:createDriverDescription', 'Fill out the form below to create a new driver account.')}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">{t('drivers:fields.firstName')}</Label>
                  <Input
                    id="firstName"
                    {...form.register('firstName')}
                    placeholder={t('drivers:placeholders.firstName')}
                    aria-describedby="firstName-error"
                  />
                  {form.formState.errors.firstName && (
                    <p id="firstName-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.firstName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">{t('drivers:fields.lastName')}</Label>
                  <Input
                    id="lastName"
                    {...form.register('lastName')}
                    placeholder={t('drivers:placeholders.lastName')}
                    aria-describedby="lastName-error"
                  />
                  {form.formState.errors.lastName && (
                    <p id="lastName-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">{t('drivers:fields.email')}</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  placeholder={t('drivers:placeholders.email')}
                  aria-describedby="email-error"
                />
                {form.formState.errors.email && (
                  <p id="email-error" className="text-sm text-destructive" role="alert">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">{t('forms:password')}</Label>
                <Input
                  id="password"
                  type="password"
                  {...form.register('password')}
                  placeholder={t('drivers:placeholders.password')}
                  aria-describedby="password-error"
                />
                {form.formState.errors.password && (
                  <p id="password-error" className="text-sm text-destructive" role="alert">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">{t('drivers:fields.phone')} ({t('common:common.optional')})</Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  {...form.register('phoneNumber')}
                  placeholder={t('drivers:placeholders.phone')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="licenseNumber">{t('drivers:fields.licenseNumber')} ({t('common:common.optional')})</Label>
                <Input
                  id="licenseNumber"
                  {...form.register('licenseNumber')}
                  placeholder={t('drivers:placeholders.licenseNumber')}
                />
              </div>
              
              <div className="flex justify-end gap-3 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  {t('common:actions.cancel')}
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {t('drivers:creating', 'Creating...')}
                    </>
                  ) : (
                    t('drivers:createDriver')
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Drivers List */}
      {drivers.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('drivers:noDriversFound')}</h3>
              <p className="text-muted-foreground mb-4">
                {t('drivers:getStartedMessage', 'Get started by creating your first driver account.')}
              </p>
              <Button onClick={() => setOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('drivers:addFirstDriver', 'Add First Driver')}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t('drivers:allDrivers')} ({drivers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('common:common.name')}</TableHead>
                  <TableHead>{t('drivers:fields.email')}</TableHead>
                  <TableHead>{t('drivers:fields.phone')}</TableHead>
                  <TableHead>{t('drivers:fields.licenseNumber')}</TableHead>
                  <TableHead>{t('drivers:fields.status')}</TableHead>
                  <TableHead>{t('common:actions.title')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {drivers.map((driver) => (
                  <TableRow key={driver.id}>
                    <TableCell className="font-medium">
                      {driver.firstName} {driver.lastName}
                    </TableCell>
                    <TableCell>{driver.email}</TableCell>
                    <TableCell>{driver.phone || t('common:common.notAvailable')}</TableCell>
                    <TableCell>{driver.licenseNumber || t('common:common.notAvailable')}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(driver.status || '')}>
                        {getStatusLabel(driver.status || '')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleView(driver.id)}
                        >
                          {t('drivers:actions.viewDetails')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(driver)}
                        >
                          {t('common:actions.edit')}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Edit Driver Dialog */}
      <Dialog open={editOpen} onOpenChange={setEditOpen}>
        <DialogContent className="max-w-md" aria-describedby="edit-driver-dialog-description">
          <DialogHeader>
            <DialogTitle>
              <Edit className="h-5 w-5 mr-2 inline" />
              {t('drivers:editDriver')}
            </DialogTitle>
            <DialogDescription id="edit-driver-dialog-description">
              {t('drivers:editDriverDescription')}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-firstName">{t('drivers:fields.firstName')}</Label>
                <Input
                  id="edit-firstName"
                  {...editForm.register('firstName')}
                  placeholder={t('drivers:placeholders.firstName')}
                  aria-describedby="edit-firstName-error"
                />
                {editForm.formState.errors.firstName && (
                  <p id="edit-firstName-error" className="text-sm text-destructive" role="alert">
                    {editForm.formState.errors.firstName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-lastName">{t('drivers:fields.lastName')}</Label>
                <Input
                  id="edit-lastName"
                  {...editForm.register('lastName')}
                  placeholder={t('drivers:placeholders.lastName')}
                  aria-describedby="edit-lastName-error"
                />
                {editForm.formState.errors.lastName && (
                  <p id="edit-lastName-error" className="text-sm text-destructive" role="alert">
                    {editForm.formState.errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-email">{t('drivers:fields.email')}</Label>
              <Input
                id="edit-email"
                type="email"
                {...editForm.register('email')}
                placeholder={t('drivers:placeholders.email')}
                aria-describedby="edit-email-error"
              />
              {editForm.formState.errors.email && (
                <p id="edit-email-error" className="text-sm text-destructive" role="alert">
                  {editForm.formState.errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-password">{t('drivers:fields.passwordOptional')}</Label>
              <Input
                id="edit-password"
                type="password"
                {...editForm.register('password')}
                placeholder={t('drivers:placeholders.password')}
                aria-describedby="edit-password-error"
              />
              {editForm.formState.errors.password && (
                <p id="edit-password-error" className="text-sm text-destructive" role="alert">
                  {editForm.formState.errors.password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-phoneNumber">{t('drivers:fields.phone')} ({t('common:common.optional')})</Label>
              <Input
                id="edit-phoneNumber"
                type="tel"
                {...editForm.register('phoneNumber')}
                placeholder={t('drivers:placeholders.phone')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-licenseNumber">{t('drivers:fields.licenseNumber')} ({t('common:common.optional')})</Label>
              <Input
                id="edit-licenseNumber"
                {...editForm.register('licenseNumber')}
                placeholder={t('drivers:placeholders.licenseNumber')}
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setEditOpen(false);
                  setEditingDriver(null);
                  editForm.reset();
                }}
                disabled={isSubmitting}
              >
                {t('common:actions.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t('drivers:updating')}
                  </>
                ) : (
                  t('drivers:updateDriver')
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
