import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Driver } from '@/types/user';
import { Vehicle } from '@/types/vehicle';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DriverService } from '@/lib/api/driver-service';
import { FleetService } from '@/lib/api/fleet-service';
import { VehicleAssignmentService, Assignment } from '@/lib/api/vehicle-assignment-service';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import { UserCheck, Plus } from 'lucide-react';

interface DriversAssignmentsContentProps {
  onAssignmentUpdated?: () => void;
}

export const DriversAssignmentsContent: React.FC<DriversAssignmentsContentProps> = ({
  onAssignmentUpdated
}) => {
  const { t } = useTranslation(['drivers', 'common']);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [availableDrivers, setAvailableDrivers] = useState<Driver[]>([]);
  const [availableVehicles, setAvailableVehicles] = useState<Vehicle[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newAssignment, setNewAssignment] = useState({
    driverId: '',
    vehicleId: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: '',
    notes: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [sortField, setSortField] = useState<string>('startDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const { toast } = useToast();

  // Fetch assignments, drivers, and vehicles
  useEffect(() => {
    const fetchData = async () => {
      console.log('🔄 Starting data fetch in assignments page...');
      setLoading(true);
      try {
        console.log('📢 Fetching drivers...');
        const drivers = await DriverService.getDrivers();
        console.log('👨‍💼 Drivers loaded:', drivers.length);
        
        console.log('📢 Fetching vehicles...');
        const vehicles = await FleetService.getVehicles();
        console.log('🚗 Vehicles loaded:', vehicles.length);
        
        console.log('📢 Fetching assignments...');
        const assignmentsData = await VehicleAssignmentService.getAllAssignments();
        console.log('📋 Assignments loaded:', assignmentsData);
        
        // Ensure we set an array
        const validAssignments = Array.isArray(assignmentsData) ? assignmentsData : [];
        setAssignments(validAssignments);
        setAvailableDrivers(drivers || []);
        setAvailableVehicles((vehicles || []).filter(v => v.status === 'AVAILABLE'));
        console.log('✅ State updated with fetched data, assignments count:', validAssignments.length);
      } catch (error) {
        console.error('❌ Error in data fetching:', error);
        toast({
          title: t('common:error'),
          description: t('drivers:messages.failedToLoadData'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
        console.log('🏁 Data fetching complete');
      }
    };

    fetchData();
  }, [toast]);

  // Filter and sort assignments
  const filteredAndSortedAssignments = useMemo(() => {
    // Ensure assignments is an array before filtering
    if (!Array.isArray(assignments)) {
      console.warn('⚠️ Assignments is not an array:', assignments);
      return [];
    }
    
    // First, filter assignments by status
    let filtered = [...assignments];
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(a => a.status === statusFilter);
    }
    
    // Then sort the filtered assignments
    return filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'startDate':
          comparison = new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
          break;
        case 'endDate':
          // Handle null/undefined endDate values
          if (!a.endDate && !b.endDate) comparison = 0;
          else if (!a.endDate) comparison = 1;
          else if (!b.endDate) comparison = -1;
          else comparison = new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
          break;
        case 'driverName':
          const driverA = a.driver ? `${a.driver.lastName}, ${a.driver.firstName}` : '';
          const driverB = b.driver ? `${b.driver.lastName}, ${b.driver.firstName}` : '';
          comparison = driverA.localeCompare(driverB);
          break;
        case 'vehicleName':
          const vehicleA = a.vehicle ? `${a.vehicle.make} ${a.vehicle.model}` : '';
          const vehicleB = b.vehicle ? `${b.vehicle.make} ${b.vehicle.model}` : '';
          comparison = vehicleA.localeCompare(vehicleB);
          break;
      }
      
      // Apply sort direction
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [assignments, statusFilter, sortField, sortDirection]);

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return t('drivers:assignments.active');
      case 'COMPLETED':
        return t('drivers:assignments.completed');
      case 'CANCELLED':
        return t('drivers:assignments.cancelled');
      default:
        return status;
    }
  };

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setNewAssignment({
      ...newAssignment,
      [field]: e.target.value,
    });
  };

  const handleCreateAssignment = async () => {
    if (!newAssignment.driverId || !newAssignment.vehicleId || !newAssignment.startDate) {
      toast({
        title: t('drivers:assignments.missingInformation'),
        description: t('drivers:assignments.fillRequiredFields'),
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    try {
      await VehicleAssignmentService.createAssignment({
        ...newAssignment,
        type: 'REGULAR',
        priority: 'NORMAL',
      });

      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      // Close dialog and reset form
      setIsDialogOpen(false);
      setNewAssignment({
        driverId: '',
        vehicleId: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: '',
        notes: '',
      });

      toast({
        title: t('common:success'),
        description: t('drivers:assignments.vehicleAssignedSuccess'),
      });

      if (onAssignmentUpdated) {
        onAssignmentUpdated();
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:assignments.failedToAssignVehicle'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEndAssignment = async (assignmentId: string) => {
    if (!confirm(t('drivers:assignments.confirmEndAssignment'))) {
      return;
    }

    try {
      await VehicleAssignmentService.completeAssignment(assignmentId);
      
      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      toast({
        title: t('common:success'),
        description: t('drivers:assignments.assignmentEndedSuccess'),
      });

      if (onAssignmentUpdated) {
        onAssignmentUpdated();
      }
    } catch (error) {
      console.error('Error ending assignment:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:assignments.failedToEndAssignment'),
        variant: 'destructive',
      });
    }
  };

  const handleCancelAssignment = async (assignmentId: string) => {
    if (!confirm(t('drivers:assignments.confirmCancelAssignment'))) {
      return;
    }

    try {
      await VehicleAssignmentService.cancelAssignment(assignmentId);
      
      // Refresh assignments
      const updatedAssignments = await VehicleAssignmentService.getAllAssignments();
      setAssignments(updatedAssignments);
      
      toast({
        title: t('common:success'),
        description: t('drivers:assignments.assignmentCancelledSuccess'),
      });

      if (onAssignmentUpdated) {
        onAssignmentUpdated();
      }
    } catch (error) {
      console.error('Error cancelling assignment:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:assignments.failedToCancelAssignment'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            {t('drivers:assignments.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('drivers:assignments.description')}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center flex-wrap gap-4">
            <CardTitle>{t('drivers:assignments.title')}</CardTitle>
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center space-x-2">
                <Label htmlFor="statusFilter">{t('drivers:assignments.status')}:</Label>
                <select
                  id="statusFilter"
                  className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="ALL">{t('drivers:assignments.all')}</option>
                  <option value="ACTIVE">{t('drivers:assignments.active')}</option>
                  <option value="COMPLETED">{t('drivers:assignments.completed')}</option>
                  <option value="CANCELLED">{t('drivers:assignments.cancelled')}</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="sortField">{t('drivers:assignments.sortBy')}:</Label>
                <select
                  id="sortField"
                  className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm"
                  value={sortField}
                  onChange={(e) => setSortField(e.target.value)}
                >
                  <option value="startDate">{t('drivers:assignments.startDate')}</option>
                  <option value="endDate">{t('drivers:assignments.endDate')}</option>
                  <option value="driverName">{t('drivers:assignments.driverName')}</option>
                  <option value="vehicleName">{t('drivers:assignments.vehicle')}</option>
                </select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                  className="h-8 w-8"
                >
                  {sortDirection === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    {t('drivers:assignments.createAssignment')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[525px]">
                  <div className="space-y-2 mb-4">
                    <DialogTitle>{t('drivers:assignments.assignVehicleToDriver')}</DialogTitle>
                    <DialogDescription>
                      {t('drivers:assignments.createAssignmentDescription')}
                    </DialogDescription>
                  </div>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="driver" className="text-right">
                        {t('drivers:assignments.driver')} *
                      </Label>
                      <select
                        id="driver"
                        className="col-span-3 flex h-9 w-full rounded-md border border-input bg-background px-3 py-1"
                        value={newAssignment.driverId}
                        onChange={handleInputChange('driverId')}
                        required
                      >
                        <option value="">{t('drivers:assignments.selectDriver')}</option>
                        {availableDrivers.map(driver => (
                          <option key={driver.id} value={driver.id}>
                            {driver.firstName} {driver.lastName}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="vehicle" className="text-right">
                        {t('drivers:assignments.vehicle')} *
                      </Label>
                      <select
                        id="vehicle"
                        className="col-span-3 flex h-9 w-full rounded-md border border-input bg-background px-3 py-1"
                        value={newAssignment.vehicleId}
                        onChange={handleInputChange('vehicleId')}
                        required
                      >
                        <option value="">{t('drivers:assignments.selectVehicle')}</option>
                        {availableVehicles.map(vehicle => (
                          <option key={vehicle.id} value={vehicle.id}>
                            {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="startDate" className="text-right">
                        {t('drivers:assignments.startDate')} *
                      </Label>
                      <Input
                        id="startDate"
                        type="date"
                        value={newAssignment.startDate}
                        onChange={handleInputChange('startDate')}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="endDate" className="text-right">
                        {t('drivers:assignments.endDate')}
                      </Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={newAssignment.endDate}
                        onChange={handleInputChange('endDate')}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="notes" className="text-right">
                        {t('common:common.notes')}
                      </Label>
                      <Input
                        id="notes"
                        value={newAssignment.notes}
                        onChange={handleInputChange('notes')}
                        placeholder={t('drivers:assignments.notesPlaceholder')}
                        className="col-span-3"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 mt-4">
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      {t('common:actions.cancel')}
                    </Button>
                    <Button onClick={handleCreateAssignment} disabled={submitting}>
                      {submitting ? t('drivers:assignments.creating') : t('drivers:assignments.createAssignment')}
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredAndSortedAssignments.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-gray-500">
                {statusFilter === 'ALL'
                  ? t('drivers:assignments.noAssignmentsFound')
                  : t('drivers:assignments.noStatusAssignmentsFound', { status: statusFilter.toLowerCase() })}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('drivers:assignments.driver')}</TableHead>
                  <TableHead>{t('drivers:assignments.vehicle')}</TableHead>
                  <TableHead>{t('drivers:assignments.startDate')}</TableHead>
                  <TableHead>{t('drivers:assignments.endDate')}</TableHead>
                  <TableHead>{t('drivers:assignments.status')}</TableHead>
                  <TableHead>{t('common:actions.title')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedAssignments.map((assignment) => (
                  <TableRow key={assignment.id} className={assignment.status !== 'ACTIVE' ? 'bg-gray-50' : ''}>
                    <TableCell>
                      {assignment.driver ? (
                        <Link href={`/drivers/details/${assignment.driverId}`} className="text-blue-600 hover:underline">
                          {assignment.driver.firstName} {assignment.driver.lastName}
                        </Link>
                      ) : (
                        'Unknown Driver'
                      )}
                    </TableCell>
                    <TableCell>
                      {assignment.vehicle ? (
                        <Link href={`/fleet/vehicles/${assignment.vehicleId}`} className="text-blue-600 hover:underline">
                          {assignment.vehicle.make} {assignment.vehicle.model} ({assignment.vehicle.plateNumber})
                        </Link>
                      ) : (
                        'Unknown Vehicle'
                      )}
                    </TableCell>
                    <TableCell>{new Date(assignment.startDate).toLocaleDateString()}</TableCell>
                    <TableCell>
                      {assignment.endDate
                        ? new Date(assignment.endDate).toLocaleDateString()
                        : t('drivers:assignments.current')
                      }
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${
                        assignment.status === 'ACTIVE' 
                          ? 'bg-green-100 text-green-800' 
                          : assignment.status === 'COMPLETED' 
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                      }`}>
                        {getStatusLabel(assignment.status)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {assignment.status === 'ACTIVE' && (
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEndAssignment(assignment.id)}
                          >
                            {t('drivers:assignments.endAssignment')}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCancelAssignment(assignment.id)}
                            className="text-red-500 hover:text-red-700 border-red-200 hover:bg-red-50"
                          >
                            {t('drivers:assignments.cancel')}
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
