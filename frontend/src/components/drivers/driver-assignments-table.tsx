'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { VehicleAssignmentService, Assignment, CreateAssignmentDto } from '@/lib/api/vehicle-assignment-service';
import { FleetService } from '@/lib/api/fleet-service';
import { Vehicle } from '@/types/vehicle';
import { 
  Dialog, 
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';

interface DriverAssignmentsTableProps {
  assignments: Assignment[];
  onAssignmentUpdated?: () => void;
}

export function DriverAssignmentsTable({ assignments, onAssignmentUpdated }: DriverAssignmentsTableProps) {
  const { t } = useTranslation(['drivers', 'common']);
  const { toast } = useToast();
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return t('drivers:assignments.active');
      case 'COMPLETED':
        return t('drivers:assignments.completed');
      case 'CANCELLED':
        return t('drivers:assignments.cancelled');
      default:
        return status;
    }
  };

  const handleEndAssignment = async (assignmentId: string) => {
    if (!confirm(t('drivers:assignments.confirmEndAssignment'))) {
      return;
    }

    setLoading(prev => ({ ...prev, [assignmentId]: true }));
    
    try {
      await VehicleAssignmentService.completeAssignment(assignmentId);
      
      toast({
        title: t('common:success'),
        description: t('drivers:assignments.assignmentEndedSuccess'),
      });
      
      // Call the callback to refresh assignments
      if (onAssignmentUpdated) {
        onAssignmentUpdated();
      }
    } catch (error) {
      console.error('Error ending assignment:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:assignments.failedToEndAssignment'),
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, [assignmentId]: false }));
    }
  };

  return (
    <>
      {assignments.length === 0 ? (
        <div className="text-center py-6">
          <p className="text-gray-500">{t('drivers:assignments.noAssignmentsFoundForDriver')}</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('drivers:assignments.vehicle')}</TableHead>
              <TableHead>{t('drivers:assignments.startDate')}</TableHead>
              <TableHead>{t('drivers:assignments.endDate')}</TableHead>
              <TableHead>{t('drivers:assignments.status')}</TableHead>
              <TableHead>{t('common:actions.title')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assignments.map((assignment) => (
              <TableRow key={assignment.id} className={assignment.status !== 'ACTIVE' ? 'bg-gray-50' : ''}>
                <TableCell>
                  {assignment.vehicle ? (
                    <Link href={`/fleet/vehicles/${assignment.vehicleId}`} className="text-blue-600 hover:underline">
                      {assignment.vehicle.make} {assignment.vehicle.model} ({assignment.vehicle.plateNumber})
                    </Link>
                  ) : (
                    t('drivers:assignments.unknownVehicle')
                  )}
                </TableCell>
                <TableCell>{new Date(assignment.startDate).toLocaleDateString()}</TableCell>
                <TableCell>
                  {assignment.endDate 
                    ? new Date(assignment.endDate).toLocaleDateString()
                    : t('drivers:assignments.current')
                  }
                </TableCell>
                <TableCell>
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${
                    assignment.status === 'ACTIVE' 
                      ? 'bg-green-100 text-green-800' 
                      : assignment.status === 'COMPLETED' 
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                  }`}>
                    {getStatusLabel(assignment.status)}
                  </span>
                </TableCell>
                <TableCell>
                  {assignment.status === 'ACTIVE' && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      disabled={loading[assignment.id]}
                      onClick={() => handleEndAssignment(assignment.id)}
                    >
                      {loading[assignment.id] ? t('drivers:assignments.processing') : t('drivers:assignments.endAssignment')}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </>
  );
}
