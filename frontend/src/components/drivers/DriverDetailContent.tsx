'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Driver } from '@/types/user';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { DriverService } from '@/lib/api/driver-service';
import { DriverAssignmentsTable } from '@/components/drivers/driver-assignments-table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import { FleetService } from '@/lib/api/fleet-service';
import { Vehicle } from '@/types/vehicle';
import { VehicleAssignmentService, Assignment } from '@/lib/api/vehicle-assignment-service';
import { ArrowLeft, Edit, Trash2, Loader2 } from 'lucide-react';

const editDriverSchema = z.object({
  email: z.string().email('Nieprawidłowy adres email'),
  password: z.string().optional().refine((val) => !val || val.length >= 6, {
    message: 'Hasło musi mieć co najmniej 6 znaków jeśli zostanie podane',
  }),
  firstName: z.string().min(1, 'Imię jest wymagane').max(50, 'Imię jest za długie'),
  lastName: z.string().min(1, 'Nazwisko jest wymagane').max(50, 'Nazwisko jest za długie'),
  phoneNumber: z.string().optional(),
  licenseNumber: z.string().optional(),
});

type EditDriverFormData = z.infer<typeof editDriverSchema>;

interface DriverDetailContentProps {
  driverId: string;
  onBack?: () => void;
  onDriverUpdated?: () => void;
}

export const DriverDetailContent: React.FC<DriverDetailContentProps> = ({
  driverId,
  onBack,
  onDriverUpdated
}) => {
  const { t } = useTranslation(['drivers', 'common']);
  const router = useRouter();
  const { toast } = useToast();

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return t('drivers:status.active');
      case 'inactive':
        return t('drivers:status.inactive');
      case 'available':
        return t('drivers:status.available');
      case 'assigned':
        return t('drivers:status.assigned');
      case 'unavailable':
        return t('drivers:status.unavailable');
      case 'on leave':
      case 'onleave':
        return t('drivers:status.onLeave');
      case 'suspended':
        return t('drivers:status.suspended');
      case 'terminated':
        return t('drivers:status.terminated');
      default:
        return status || t('drivers:details.notProvided');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'on leave':
      case 'onleave':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-orange-100 text-orange-800';
      case 'terminated':
      case 'inactive':
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  const [driver, setDriver] = useState<Driver | null>(null);
  const [loading, setLoading] = useState(true);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [availableVehicles, setAvailableVehicles] = useState<Vehicle[]>([]);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newAssignment, setNewAssignment] = useState({
    vehicleId: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: '',
    notes: '',
  });

  const editForm = useForm<EditDriverFormData>({
    resolver: zodResolver(editDriverSchema),
    defaultValues: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      licenseNumber: '',
    },
  });

  useEffect(() => {
    async function fetchDriverData() {
      try {
        const driverData = await DriverService.getDriver(driverId);
        setDriver(driverData);
        
        // Also fetch assignments
        const assignmentData = await DriverService.getDriverAssignments(driverId);
        setAssignments(assignmentData);
        
        // Fetch available vehicles
        const vehicles = await FleetService.getVehicles();
        setAvailableVehicles(vehicles.filter(v => v.status === 'AVAILABLE'));
      } catch (error) {
        console.error('Error fetching driver details:', error);
        toast({
          title: t('common:error'),
          description: t('drivers:messages.failedToLoadDriverDetails'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchDriverData();
  }, [driverId, toast]);

  const handleDelete = async () => {
    if (!window.confirm(t('drivers:messages.confirmDeleteDriver'))) {
      return;
    }

    try {
      await DriverService.deleteDriver(driverId);
      toast({
        title: t('common:success'),
        description: t('drivers:messages.driverDeleted'),
      });
      onDriverUpdated?.();
      onBack?.();
    } catch (error) {
      toast({
        title: t('common:error'),
        description: t('drivers:messages.failedToDeleteDriver'),
        variant: 'destructive',
      });
    }
  };

  const handleEditDriver = () => {
    if (driver) {
      editForm.reset({
        email: driver.email,
        password: '', // Don't populate password for security
        firstName: driver.firstName,
        lastName: driver.lastName,
        phoneNumber: driver.phone || '',
        licenseNumber: driver.licenseNumber || '',
      });
      setIsEditDialogOpen(true);
    }
  };

  const handleEditSubmit = async (data: EditDriverFormData) => {
    if (!driver) return;

    setSubmitting(true);
    try {
      const updateData = {
        ...data,
        // Only include password if it's provided
        ...(data.password ? { password: data.password } : {}),
      };

      await DriverService.updateDriver(driver.id, updateData);

      toast({
        title: t('common:success'),
        description: t('drivers:messages.driverUpdated'),
      });

      setIsEditDialogOpen(false);
      editForm.reset();

      // Refresh driver data
      const updatedDriver = await DriverService.getDriver(driverId);
      setDriver(updatedDriver);

      onDriverUpdated?.();
    } catch (error) {
      console.error('Error updating driver:', error);
      toast({
        title: t('common:error'),
        description: error instanceof Error ? error.message : t('drivers:messages.failedToUpdateDriver'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setNewAssignment({
      ...newAssignment,
      [field]: e.target.value,
    });
  };

  const handleCreateAssignment = async () => {
    if (!newAssignment.vehicleId || !newAssignment.startDate) {
      toast({
        title: 'Missing information',
        description: 'Please fill out all required fields',
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    try {
      await VehicleAssignmentService.createAssignment({
        driverId: driverId,
        ...newAssignment,
        type: 'REGULAR',
        priority: 'NORMAL',
      });

      // Refresh assignments
      const updatedAssignments = await DriverService.getDriverAssignments(driverId);
      setAssignments(updatedAssignments);
      
      // Also refresh available vehicles
      const vehicles = await FleetService.getVehicles();
      setAvailableVehicles(vehicles.filter(v => v.status === 'AVAILABLE'));
      
      // Close dialog and reset form
      setIsAssignDialogOpen(false);
      setNewAssignment({
        vehicleId: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: '',
        notes: '',
      });

      toast({
        title: t('common:success'),
        description: t('drivers:assignments.vehicleAssignedSuccess'),
      });
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:assignments.failedToAssignVehicle'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="text-center py-10">{t('drivers:details.loadingDetails')}</div>;
  }

  if (!driver) {
    return (
      <div className="text-center py-10">
        <h2 className="text-xl font-semibold mb-4">{t('drivers:details.driverNotFound')}</h2>
        <Button onClick={onBack}>
          {t('drivers:details.backToDriversList')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drivers:details.backToDrivers')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {driver.firstName} {driver.lastName}
            </h1>
            <p className="text-sm text-gray-600">{driver.email}</p>
          </div>
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            onClick={handleEditDriver}
            className="gap-2"
          >
            <Edit className="h-4 w-4" />
            {t('drivers:editDriver')}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {t('drivers:deleteDriver')}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList>
          <TabsTrigger value="details">{t('drivers:details.personalDetails')}</TabsTrigger>
          <TabsTrigger value="docs">{t('drivers:details.documents')}</TabsTrigger>
          <TabsTrigger value="assignments">{t('drivers:details.assignments')}</TabsTrigger>
          <TabsTrigger value="history">{t('drivers:details.tripHistory')}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-4 py-4">
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('drivers:details.basicInformation')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">{t('common:common.name')}</div>
                  <div>{driver.firstName} {driver.lastName}</div>

                  <div className="font-medium">{t('drivers:fields.email')}</div>
                  <div>{driver.email}</div>

                  <div className="font-medium">{t('drivers:fields.phone')}</div>
                  <div>{driver.phone || t('drivers:details.notProvided')}</div>

                  <div className="font-medium">{t('drivers:fields.status')}</div>
                  <div>
                    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(driver.status)}`}>
                      {getStatusLabel(driver.status)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('drivers:details.licenseInformation')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">{t('drivers:fields.licenseNumber')}</div>
                  <div>{driver.licenseNumber || t('drivers:details.notProvided')}</div>

                  <div className="font-medium">{t('drivers:details.licenseType')}</div>
                  <div>{driver.licenseType || t('drivers:details.notProvided')}</div>

                  <div className="font-medium">{t('drivers:details.expirationDate')}</div>
                  <div>
                    {driver.licenseExpiry
                      ? new Date(driver.licenseExpiry).toLocaleDateString()
                      : t('drivers:details.notProvided')}
                  </div>

                  <div className="font-medium">{t('drivers:details.restrictions')}</div>
                  <div>{driver.licenseRestrictions || t('drivers:details.none')}</div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Additional content will be added in next part */}
        </TabsContent>
        
        {/* Other tabs content will be added */}
        <TabsContent value="docs" className="py-4">
          <div className="text-center py-10 text-gray-500">
            {t('drivers:details.documentsContentPlaceholder')}
          </div>
        </TabsContent>

        <TabsContent value="assignments" className="py-4">
          <div className="text-center py-10 text-gray-500">
            {t('drivers:details.assignmentsContentPlaceholder')}
          </div>
        </TabsContent>

        <TabsContent value="history" className="py-4">
          <div className="text-center py-10 text-gray-500">
            {t('drivers:details.tripHistoryContentPlaceholder')}
          </div>
        </TabsContent>
      </Tabs>

      {/* Edit Driver Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md" aria-describedby="edit-driver-dialog-description">
          <DialogHeader>
            <DialogTitle>
              <Edit className="h-5 w-5 mr-2 inline" />
              {t('drivers:editDriver')}
            </DialogTitle>
            <DialogDescription id="edit-driver-dialog-description">
              {t('drivers:editDriverDescription')}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-firstName">{t('drivers:fields.firstName')}</Label>
                <Input
                  id="edit-firstName"
                  {...editForm.register('firstName')}
                  placeholder={t('drivers:placeholders.firstName')}
                  aria-describedby="edit-firstName-error"
                />
                {editForm.formState.errors.firstName && (
                  <p id="edit-firstName-error" className="text-sm text-destructive" role="alert">
                    {editForm.formState.errors.firstName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-lastName">{t('drivers:fields.lastName')}</Label>
                <Input
                  id="edit-lastName"
                  {...editForm.register('lastName')}
                  placeholder={t('drivers:placeholders.lastName')}
                  aria-describedby="edit-lastName-error"
                />
                {editForm.formState.errors.lastName && (
                  <p id="edit-lastName-error" className="text-sm text-destructive" role="alert">
                    {editForm.formState.errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-email">{t('drivers:fields.email')}</Label>
              <Input
                id="edit-email"
                type="email"
                {...editForm.register('email')}
                placeholder={t('drivers:placeholders.email')}
                aria-describedby="edit-email-error"
              />
              {editForm.formState.errors.email && (
                <p id="edit-email-error" className="text-sm text-destructive" role="alert">
                  {editForm.formState.errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-password">{t('drivers:fields.passwordOptional')}</Label>
              <Input
                id="edit-password"
                type="password"
                {...editForm.register('password')}
                placeholder={t('drivers:placeholders.password')}
                aria-describedby="edit-password-error"
              />
              {editForm.formState.errors.password && (
                <p id="edit-password-error" className="text-sm text-destructive" role="alert">
                  {editForm.formState.errors.password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-phoneNumber">{t('drivers:fields.phone')} ({t('common:common.optional')})</Label>
              <Input
                id="edit-phoneNumber"
                type="tel"
                {...editForm.register('phoneNumber')}
                placeholder={t('drivers:placeholders.phone')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-licenseNumber">{t('drivers:fields.licenseNumber')} ({t('common:common.optional')})</Label>
              <Input
                id="edit-licenseNumber"
                {...editForm.register('licenseNumber')}
                placeholder={t('drivers:placeholders.licenseNumber')}
              />
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  editForm.reset();
                }}
                disabled={submitting}
              >
                {t('common:actions.cancel')}
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t('drivers:updating')}
                  </>
                ) : (
                  t('drivers:updateDriver')
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
