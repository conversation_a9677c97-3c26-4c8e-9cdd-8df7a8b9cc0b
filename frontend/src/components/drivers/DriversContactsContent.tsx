import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Driver } from '@/types/user';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DriverService } from '@/lib/api/driver-service';
import { Phone, Search } from 'lucide-react';

interface DriversContactsContentProps {
  onContactUpdated?: () => void;
}

export const DriversContactsContent: React.FC<DriversContactsContentProps> = ({
  onContactUpdated
}) => {
  const { t } = useTranslation(['drivers', 'common']);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    async function fetchDrivers() {
      setLoading(true);
      try {
        const data = await DriverService.getDrivers();
        setDrivers(data);
      } catch (error) {
        console.error('Error fetching drivers:', error);
        toast({
          title: t('common:error'),
          description: t('drivers:contacts.failedToLoadContacts'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchDrivers();
  }, [toast]);

  // Filter drivers based on search query
  const filteredDrivers = searchQuery
    ? drivers.filter(driver => 
        `${driver.firstName} ${driver.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        driver.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (driver.phone && driver.phone.includes(searchQuery))
      )
    : drivers;

  const handleUpdateContact = (driverId: string) => {
    // This would open a dialog or navigate to an edit page
    console.log('Update contact for driver:', driverId);
    toast({
      title: t('drivers:contacts.featureComingSoon'),
      description: t('drivers:contacts.contactUpdateSoon'),
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <span className="ml-2">{t('drivers:contacts.loadingContacts')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Phone className="h-5 w-5" />
            {t('drivers:contacts.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('drivers:contacts.description')}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <CardTitle>{t('drivers:contacts.driverContacts')}</CardTitle>
            <div className="flex items-center gap-2 flex-1 sm:max-w-xs">
              <Input
                placeholder={t('drivers:contacts.searchDrivers')}
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                className="flex-1"
              />
              <Button variant="outline" size="icon">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredDrivers.length === 0 ? (
            <div className="text-center py-6">
              <Phone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('drivers:contacts.noContactsFound')}</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? t('drivers:contacts.noMatchingDrivers') : t('drivers:contacts.noContactsAvailable')}
              </p>
              {searchQuery && (
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  {t('drivers:contacts.clearSearch')}
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('common:common.name')}</TableHead>
                  <TableHead>{t('drivers:contacts.phoneNumber')}</TableHead>
                  <TableHead>{t('drivers:fields.email')}</TableHead>
                  <TableHead>{t('drivers:contacts.emergencyContact')}</TableHead>
                  <TableHead className="text-right">{t('common:actions.title')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDrivers.map((driver) => (
                  <TableRow key={driver.id}>
                    <TableCell className="font-medium">
                      <Link href={`/drivers/details/${driver.id}`} className="text-blue-600 hover:underline">
                        {driver.firstName} {driver.lastName}
                      </Link>
                    </TableCell>
                    <TableCell>
                      {driver.phone ? (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <a href={`tel:${driver.phone}`} className="text-blue-600 hover:underline">
                            {driver.phone}
                          </a>
                        </div>
                      ) : (
                        <span className="text-gray-400">{t('drivers:contacts.notProvided')}</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <a href={`mailto:${driver.email}`} className="text-blue-600 hover:underline">
                        {driver.email}
                      </a>
                    </TableCell>
                    <TableCell>
                      {driver.emergencyContactName ? (
                        <div>
                          <div className="font-medium">{driver.emergencyContactName}</div>
                          {driver.emergencyContactPhone && (
                            <div className="text-sm text-gray-500 flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <a href={`tel:${driver.emergencyContactPhone}`} className="text-blue-600 hover:underline">
                                {driver.emergencyContactPhone}
                              </a>
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">{t('drivers:contacts.notProvided')}</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleUpdateContact(driver.id)}
                      >
                        {t('drivers:contacts.updateContact')}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="py-4">
          <div className="flex items-start space-x-3">
            <Phone className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">{t('drivers:contacts.contactTips')}</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p>• {t('drivers:contacts.tip1')}</p>
                <p>• {t('drivers:contacts.tip2')}</p>
                <p>• {t('drivers:contacts.tip3')}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
