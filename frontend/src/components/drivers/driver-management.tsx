'use client';

import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, Driver } from '@/types/user';
import { DriverService } from '@/lib/api/driver-service';
import { Plus, UserPlus, Loader2, AlertCircle } from 'lucide-react';

const driverSchema = z.object({
  email: z.string().email('Nieprawidłowy adres email'),
  password: z.string().min(6, 'Hasło musi mieć co najmniej 6 znaków'),
  firstName: z.string().min(1, 'Imię jest wymagane').max(50, 'Imię jest za długie'),
  lastName: z.string().min(1, 'Nazwisko jest wymagane').max(50, 'Nazwisko jest za długie'),
  phoneNumber: z.string().optional(),
  licenseNumber: z.string().optional(),
});

type DriverFormData = z.infer<typeof driverSchema>;

interface CreateDriverFormData extends DriverFormData {
  role: 'DRIVER';
  status: 'AVAILABLE';
}

export function DriverManagement() {
  const { t } = useTranslation(['drivers', 'common']);
  const router = useRouter();
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<DriverFormData>({
    resolver: zodResolver(driverSchema),
    defaultValues: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      licenseNumber: '',
    },
  });

  const fetchDrivers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DriverService.getDrivers();
      setDrivers(data);
    } catch (error) {
      console.error('Error fetching drivers:', error);
      toast({
        title: t('common:error'),
        description: t('drivers:messages.loadError'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchDrivers();
  }, [fetchDrivers]);

  const handleSubmit = async (data: DriverFormData) => {
    setIsSubmitting(true);
    try {
      const driverData: CreateDriverFormData = {
        ...data,
        role: 'DRIVER',
        status: 'AVAILABLE',
      };

      await DriverService.createDriver(driverData);
      
      toast({
        title: 'Success',
        description: 'Driver created successfully',
      });
      
      setOpen(false);
      form.reset();
      await fetchDrivers();
    } catch (error) {
      console.error('Error creating driver:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create driver',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (driverId: string) => {
    router.push(`/drivers/edit/${driverId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading drivers...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Driver Management</h1>
          <p className="text-muted-foreground">
            Manage your fleet drivers and their information
          </p>
        </div>
        
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Driver
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md" aria-describedby="driver-dialog-description">
            <DialogHeader>
              <DialogTitle>
                <UserPlus className="h-5 w-5 mr-2 inline" />
                Create New Driver
              </DialogTitle>
              <DialogDescription id="driver-dialog-description">
                Fill out the form below to create a new driver account.
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    {...form.register('firstName')}
                    placeholder="John"
                    aria-describedby="firstName-error"
                  />
                  {form.formState.errors.firstName && (
                    <p id="firstName-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.firstName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    {...form.register('lastName')}
                    placeholder="Doe"
                    aria-describedby="lastName-error"
                  />
                  {form.formState.errors.lastName && (
                    <p id="lastName-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  placeholder="<EMAIL>"
                  aria-describedby="email-error"
                />
                {form.formState.errors.email && (
                  <p id="email-error" className="text-sm text-destructive" role="alert">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  {...form.register('password')}
                  placeholder="••••••••"
                  aria-describedby="password-error"
                />
                {form.formState.errors.password && (
                  <p id="password-error" className="text-sm text-destructive" role="alert">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number (Optional)</Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  {...form.register('phoneNumber')}
                  placeholder="+****************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="licenseNumber">License Number (Optional)</Label>
                <Input
                  id="licenseNumber"
                  {...form.register('licenseNumber')}
                  placeholder="DL123456789"
                />
              </div>
              
              <div className="flex justify-end gap-3 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Driver'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {drivers.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No drivers found</h3>
              <p className="text-muted-foreground mb-4">
                Get started by creating your first driver account.
              </p>
              <Button onClick={() => setOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Driver
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>All Drivers ({drivers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>License</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {drivers.map((driver) => (
                  <TableRow key={driver.id}>
                    <TableCell className="font-medium">
                      {driver.firstName} {driver.lastName}
                    </TableCell>
                    <TableCell>{driver.email}</TableCell>
                    <TableCell>{driver.phone || 'N/A'}</TableCell>
                    <TableCell>{driver.licenseNumber || 'N/A'}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(driver.status || '')}>
                        {driver.status || 'N/A'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleEdit(driver.id)}
                      >
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="py-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Important Note</h4>
              <p className="text-sm text-blue-700">
                Drivers are required to create trips and vehicle assignments. 
                Make sure to create driver accounts before attempting to assign vehicles or create trips.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}