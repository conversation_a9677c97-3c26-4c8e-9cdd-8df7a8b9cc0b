import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, UserCheck, Phone, TrendingUp, TrendingDown, Car, Clock, BarChart3, RefreshCw } from 'lucide-react';
import { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { DriverService } from '@/lib/api/driver-service';

interface DashboardData {
  totalDrivers: number;
  activeDrivers: number;
  availableDrivers: number;
  assignedDrivers: number;
  totalAssignments: number;
  monthlyGrowth: number;
  driversByStatus: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  topDrivers: Array<{
    id: string;
    name: string;
    totalTrips: number;
    efficiency: number;
    status: string;
  }>;
  monthlyActivity: Array<{
    month: string;
    newDrivers: number;
    assignments: number;
  }>;
}

export const DriversDashboard: React.FC = () => {
  const { t } = useTranslation(['drivers', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // This would be replaced with actual API calls
      const mockData: DashboardData = {
        totalDrivers: 28,
        activeDrivers: 25,
        availableDrivers: 18,
        assignedDrivers: 7,
        totalAssignments: 45,
        monthlyGrowth: 6.2,
        driversByStatus: [
          { name: t('drivers:status.available'), value: 18, color: '#22c55e' },
          { name: t('drivers:status.assigned'), value: 7, color: '#3b82f6' },
          { name: t('drivers:status.inactive'), value: 3, color: '#6b7280' },
        ],
        topDrivers: [
          { id: '1', name: 'Jan Kowalski', totalTrips: 156, efficiency: 98, status: 'ASSIGNED' },
          { id: '2', name: 'Anna Nowak', totalTrips: 142, efficiency: 96, status: 'AVAILABLE' },
          { id: '3', name: 'Piotr Wiśniewski', totalTrips: 138, efficiency: 94, status: 'ASSIGNED' },
          { id: '4', name: 'Maria Kowalczyk', totalTrips: 125, efficiency: 92, status: 'AVAILABLE' },
          { id: '5', name: 'Tomasz Nowak', totalTrips: 118, efficiency: 90, status: 'ASSIGNED' },
        ],
        monthlyActivity: [
          { month: 'Jan', newDrivers: 2, assignments: 8 },
          { month: 'Feb', newDrivers: 1, assignments: 6 },
          { month: 'Mar', newDrivers: 3, assignments: 9 },
          { month: 'Apr', newDrivers: 2, assignments: 7 },
          { month: 'May', newDrivers: 4, assignments: 8 },
          { month: 'Jun', newDrivers: 3, assignments: 7 },
        ],
      };
      setData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return <Badge className="bg-green-100 text-green-800">{t('drivers:status.available')}</Badge>;
      case 'ASSIGNED':
        return <Badge className="bg-blue-100 text-blue-800">{t('drivers:status.assigned', 'Assigned')}</Badge>;
      case 'INACTIVE':
        return <Badge className="bg-gray-100 text-gray-800">{t('drivers:status.inactive')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('drivers:loadingDrivers')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('drivers:noDashboardData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('drivers:dashboard.overview')}</h2>
          <p className="text-sm text-muted-foreground">{t('drivers:dashboard.overviewDescription')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('drivers:dashboard.totalDrivers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalDrivers}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('drivers:dashboard.vsLastMonth')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('drivers:dashboard.availableDrivers')}</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.availableDrivers}</div>
            <p className="text-xs text-muted-foreground">{t('drivers:dashboard.readyForAssignment')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('drivers:dashboard.assignedDrivers')}</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.assignedDrivers}</div>
            <p className="text-xs text-muted-foreground">{t('drivers:dashboard.currentlyOnDuty')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('drivers:dashboard.totalAssignments')}</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalAssignments}</div>
            <p className="text-xs text-muted-foreground">{t('drivers:dashboard.allTimeAssignments')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Driver Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drivers:dashboard.driverStatusDistribution')}</CardTitle>
            <CardDescription>{t('drivers:dashboard.currentAvailabilityStatus')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.driversByStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.driversByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>{t('drivers:dashboard.monthlyActivity')}</CardTitle>
            <CardDescription>{t('drivers:dashboard.newDriversAndAssignments')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.monthlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Line yAxisId="left" type="monotone" dataKey="newDrivers" stroke="#8884d8" strokeWidth={2} name={t('drivers:dashboard.newDrivers')} />
                  <Line yAxisId="right" type="monotone" dataKey="assignments" stroke="#82ca9d" strokeWidth={2} name={t('drivers:dashboard.assignments')} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Drivers */}
      <Card>
        <CardHeader>
          <CardTitle>{t('drivers:dashboard.topPerformingDrivers')}</CardTitle>
          <CardDescription>{t('drivers:dashboard.mostActiveAndEfficient')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.topDrivers.map((driver, index) => (
              <div key={driver.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge variant="outline">#{index + 1}</Badge>
                  <div>
                    <p className="font-medium">{driver.name}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{driver.totalTrips} {t('drivers:dashboard.trips')}</span>
                      <span>•</span>
                      <span>{driver.efficiency}% {t('drivers:dashboard.efficiency')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(driver.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
