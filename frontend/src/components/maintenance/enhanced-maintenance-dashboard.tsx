'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Truck,
  Container,
  Wrench,
  AlertTriangle,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { EnhancedMaintenanceService, MaintenanceStats, MaintenanceRecommendation } from '@/lib/api/enhanced-maintenance-service';
import { MaintenanceLog, VehicleType } from '@/types/maintenance';
import { formatCurrency, formatDate } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';

interface DashboardData {
  stats: MaintenanceStats;
  upcomingTrucks: MaintenanceLog[];
  upcomingTrailers: MaintenanceLog[];
  recommendations: { [vehicleId: string]: MaintenanceRecommendation[] };
}

export function EnhancedMaintenanceDashboard() {
  const { t } = useTranslation(['fleet', 'common']);
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [selectedTab, setSelectedTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);


      if (!EnhancedMaintenanceService || typeof EnhancedMaintenanceService.getMaintenanceDashboard !== 'function') {
        // Fallback to mock data if service is not available
        console.warn('EnhancedMaintenanceService not available, using mock data');
        const mockData = {
          stats: {
            totalVehicles: 25,
            activeMaintenanceJobs: 8,
            upcomingServices: 12,
            criticalIssues: 3,
            averageDowntime: 2.5,
            maintenanceCostThisMonth: 15420.50,
            fuelEfficiencyTrend: 5.2,
            complianceScore: 94
          },
          recentActivities: [],
          upcomingMaintenance: [],
          recommendations: []
        };
        setDashboardData(mockData);
        return;
      }

      const data = await EnhancedMaintenanceService.getMaintenanceDashboard();
      setDashboardData(data);
    } catch (error) {
      toast({
        title: t('common:error'),
        description: t('fleet:dashboard.loadError'),
        variant: 'destructive',
      });
      console.error('Error loading dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">{t('fleet:dashboard.loading')}</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-10">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">{t('fleet:dashboard.unableToLoad')}</h3>
        <p className="text-gray-600 mb-4">{t('fleet:dashboard.loadErrorDescription')}</p>
        <Button onClick={loadDashboardData}>{t('common:actions.tryAgain')}</Button>
      </div>
    );
  }

  const { stats, upcomingTrucks, upcomingTrailers } = dashboardData;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{t('fleet:dashboard.title')}</h1>
        <Button onClick={loadDashboardData} variant="outline">
          {t('fleet:dashboard.refreshData')}
        </Button>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t('fleet:dashboard.overview')}</TabsTrigger>
          <TabsTrigger value="trucks">{t('fleet:vehicles.truck')}</TabsTrigger>
          <TabsTrigger value="trailers">{t('fleet:vehicles.trailer')}</TabsTrigger>
          <TabsTrigger value="analytics">{t('fleet:dashboard.analytics')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('fleet:dashboard.totalTruckMaintenance')}</CardTitle>
                <Truck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.trucks.totalMaintenance}</div>
                <p className="text-xs text-muted-foreground">
                  {t('fleet:dashboard.avg')}: {formatCurrency(stats.trucks.averageCost || 0)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('fleet:dashboard.totalTrailerMaintenance')}</CardTitle>
                <Container className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.trailers.totalMaintenance}</div>
                <p className="text-xs text-muted-foreground">
                  {t('fleet:dashboard.avg')}: {formatCurrency(stats.trailers.averageCost || 0)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('fleet:dashboard.totalMaintenanceCost')}</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency((stats.trucks.totalCost || 0) + (stats.trailers.totalCost || 0))}
                </div>
                <p className="text-xs text-muted-foreground">
                  {t('fleet:vehicles.truck')}: {formatCurrency(stats.trucks.totalCost || 0)} |
                  {t('fleet:vehicles.trailer')}: {formatCurrency(stats.trailers.totalCost || 0)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('fleet:dashboard.upcomingMaintenance')}</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{upcomingTrucks.length + upcomingTrailers.length}</div>
                <p className="text-xs text-muted-foreground">
                  {t('fleet:dashboard.next30Days')}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Maintenance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  {t('fleet:dashboard.upcomingTruckMaintenance')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingTrucks.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">{t('fleet:dashboard.noUpcomingTruckMaintenance')}</p>
                ) : (
                  <div className="space-y-3">
                    {upcomingTrucks.slice(0, 5).map((maintenance) => (
                      <div key={maintenance.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{maintenance.vehicle?.plateNumber}</p>
                          <p className="text-sm text-gray-600">{maintenance.description}</p>
                          <p className="text-xs text-gray-500">
                            {formatDate(maintenance.scheduledDate || maintenance.date)}
                          </p>
                        </div>
                        <Badge variant={maintenance.status === 'SCHEDULED' ? 'default' : 'secondary'}>
                          {t(`fleet:dashboard.${maintenance.status.toLowerCase()}`)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Container className="h-5 w-5" />
                  {t('fleet:dashboard.upcomingTrailerMaintenance')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {upcomingTrailers.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">{t('fleet:dashboard.noUpcomingTrailerMaintenance')}</p>
                ) : (
                  <div className="space-y-3">
                    {upcomingTrailers.slice(0, 5).map((maintenance) => (
                      <div key={maintenance.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{maintenance.vehicle?.plateNumber}</p>
                          <p className="text-sm text-gray-600">{maintenance.description}</p>
                          <p className="text-xs text-gray-500">
                            {formatDate(maintenance.scheduledDate || maintenance.date)}
                          </p>
                        </div>
                        <Badge variant={maintenance.status === 'SCHEDULED' ? 'default' : 'secondary'}>
                          {t(`fleet:dashboard.${maintenance.status.toLowerCase()}`)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trucks" className="space-y-6">
          <VehicleTypeMaintenanceView vehicleType={VehicleType.TRUCK} />
        </TabsContent>

        <TabsContent value="trailers" className="space-y-6">
          <VehicleTypeMaintenanceView vehicleType={VehicleType.TRAILER} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <MaintenanceAnalytics stats={stats} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface VehicleTypeMaintenanceViewProps {
  vehicleType: VehicleType;
}

function VehicleTypeMaintenanceView({ vehicleType }: VehicleTypeMaintenanceViewProps) {
  const { t } = useTranslation(['fleet', 'common']);
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<{
    maintenance: MaintenanceLog[];
    upcoming: MaintenanceLog[];
    categories: string[];
  } | null>(null);

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'ENGINE':
        return t('fleet:categories.engine');
      case 'TRANSMISSION':
        return t('fleet:categories.transmission');
      case 'BRAKES':
        return t('fleet:categories.brakes');
      case 'ELECTRICAL':
        return t('fleet:categories.electrical');
      case 'TIRES':
        return t('fleet:categories.tires');
      case 'COOLING_SYSTEM':
        return t('fleet:categories.coolingSystem');
      case 'FUEL_SYSTEM':
        return t('fleet:categories.fuelSystem');
      case 'EXHAUST_SYSTEM':
        return t('fleet:categories.exhaustSystem');
      case 'SUSPENSION_AXLES':
        return t('fleet:categories.suspensionAxles');
      case 'CARGO_AREA':
        return t('fleet:categories.cargoArea');
      case 'REFRIGERATION_UNIT':
        return t('fleet:categories.refrigerationUnit');
      case 'HYDRAULIC_SYSTEMS':
        return t('fleet:categories.hydraulicSystems');
      case 'LIGHTING_SYSTEM':
        return t('fleet:categories.lightingSystem');
      case 'OTHER':
        return t('fleet:categories.other');
      default:
        return category.replace('_', ' ');
    }
  };

  useEffect(() => {
    loadVehicleTypeData();
  }, [vehicleType]);

  const loadVehicleTypeData = async () => {
    try {
      setLoading(true);


      if (!EnhancedMaintenanceService || typeof EnhancedMaintenanceService.getMaintenanceOverview !== 'function') {
        // Fallback to mock data if service is not available
        console.warn('EnhancedMaintenanceService not available, using mock data for vehicle type');
        const mockData = {
          totalVehicles: vehicleType === VehicleType.TRUCK ? 15 : 10,
          activeJobs: vehicleType === VehicleType.TRUCK ? 5 : 3,
          upcomingServices: vehicleType === VehicleType.TRUCK ? 8 : 4,
          averageAge: vehicleType === VehicleType.TRUCK ? 4.2 : 3.8,
          maintenanceLogs: []
        };
        setData(mockData);
        return;
      }

      const result = await EnhancedMaintenanceService.getMaintenanceOverview(vehicleType);
      setData(result);
    } catch (error) {
      toast({
        title: t('common:error'),
        description: t('fleet:dashboard.failedToLoadVehicleData', { vehicleType: vehicleType.toLowerCase() }),
        variant: 'destructive',
      });
      console.error('Error loading vehicle type data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-600">{t('fleet:dashboard.failedToLoadVehicleData', { vehicleType: vehicleType.toLowerCase() })}</p>
        <Button onClick={loadVehicleTypeData} className="mt-4">{t('common:actions.tryAgain')}</Button>
      </div>
    );
  }

  const Icon = vehicleType === VehicleType.TRUCK ? Truck : Container;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Icon className="h-6 w-6" />
        <h2 className="text-2xl font-bold">{t(`fleet:vehicles.${vehicleType.toLowerCase()}`)} {t('fleet:dashboard.maintenance')}</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.availableCategories')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {data.categories.map((category) => (
                <Badge key={category} variant="outline">
                  {getCategoryLabel(category)}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.recentMaintenance')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.maintenance.length}</div>
            <p className="text-sm text-gray-600">{t('fleet:dashboard.totalRecords')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.upcoming')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.upcoming.length}</div>
            <p className="text-sm text-gray-600">{t('fleet:dashboard.next30Days')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Maintenance List */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.recentMaintenanceRecords')}</CardTitle>
        </CardHeader>
        <CardContent>
          {data.maintenance.length === 0 ? (
            <p className="text-gray-500 text-center py-4">{t('fleet:dashboard.noMaintenanceRecords')}</p>
          ) : (
            <div className="space-y-3">
              {data.maintenance.slice(0, 10).map((maintenance) => (
                <div key={maintenance.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium">{maintenance.vehicle?.plateNumber}</p>
                      <Badge variant="outline">{getCategoryLabel(maintenance.category)}</Badge>
                    </div>
                    <p className="text-sm text-gray-600">{maintenance.description}</p>
                    <p className="text-xs text-gray-500">
                      {formatDate(maintenance.date)} • {maintenance.technician || t('fleet:dashboard.unknownTechnician')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(maintenance.cost || 0)}</p>
                    <Badge variant={
                      maintenance.status === 'COMPLETED' ? 'default' :
                      maintenance.status === 'IN_PROGRESS' ? 'secondary' :
                      maintenance.status === 'SCHEDULED' ? 'outline' : 'destructive'
                    }>
                      {t(`fleet:dashboard.${maintenance.status.toLowerCase()}`)}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface MaintenanceAnalyticsProps {
  stats: MaintenanceStats;
}

function MaintenanceAnalytics({ stats }: MaintenanceAnalyticsProps) {
  const { t } = useTranslation(['fleet', 'common']);
  const totalMaintenance = stats.trucks.totalMaintenance + stats.trailers.totalMaintenance;
  const totalCost = (stats.trucks.totalCost || 0) + (stats.trailers.totalCost || 0);

  const truckPercentage = totalMaintenance > 0 ? (stats.trucks.totalMaintenance / totalMaintenance) * 100 : 0;
  const trailerPercentage = totalMaintenance > 0 ? (stats.trailers.totalMaintenance / totalMaintenance) * 100 : 0;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">{t('fleet:dashboard.maintenanceAnalytics')}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.maintenanceDistribution')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>{t('fleet:vehicles.truck')}</span>
                <span>{stats.trucks.totalMaintenance} ({truckPercentage.toFixed(1)}%)</span>
              </div>
              <Progress value={truckPercentage} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>{t('fleet:vehicles.trailer')}</span>
                <span>{stats.trailers.totalMaintenance} ({trailerPercentage.toFixed(1)}%)</span>
              </div>
              <Progress value={trailerPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.costAnalysis')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">{t('fleet:dashboard.truckCosts')}</p>
                <p className="text-lg font-semibold">{formatCurrency(stats.trucks.totalCost || 0)}</p>
                <p className="text-xs text-gray-500">
                  {t('fleet:dashboard.avg')}: {formatCurrency(stats.trucks.averageCost || 0)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">{t('fleet:dashboard.trailerCosts')}</p>
                <p className="text-lg font-semibold">{formatCurrency(stats.trailers.totalCost || 0)}</p>
                <p className="text-xs text-gray-500">
                  {t('fleet:dashboard.avg')}: {formatCurrency(stats.trailers.averageCost || 0)}
                </p>
              </div>
            </div>
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-600">{t('fleet:dashboard.totalMaintenanceCost')}</p>
              <p className="text-2xl font-bold">{formatCurrency(totalCost)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.costBreakdown')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Truck className="h-4 w-4" />
                {t('fleet:dashboard.truckMaintenance')}
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">{t('fleet:dashboard.averagePartsCost')}:</span>
                  <span className="font-medium">{formatCurrency(stats.trucks.averagePartsCost || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{t('fleet:dashboard.averageLaborCost')}:</span>
                  <span className="font-medium">{formatCurrency(stats.trucks.averageLaborCost || 0)}</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-sm font-medium">{t('fleet:dashboard.averageTotal')}:</span>
                  <span className="font-bold">{formatCurrency(stats.trucks.averageCost || 0)}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Container className="h-4 w-4" />
                {t('fleet:dashboard.trailerMaintenance')}
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">{t('fleet:dashboard.averagePartsCost')}:</span>
                  <span className="font-medium">{formatCurrency(stats.trailers.averagePartsCost || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{t('fleet:dashboard.averageLaborCost')}:</span>
                  <span className="font-medium">{formatCurrency(stats.trailers.averageLaborCost || 0)}</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-sm font-medium">{t('fleet:dashboard.averageTotal')}:</span>
                  <span className="font-bold">{formatCurrency(stats.trailers.averageCost || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
