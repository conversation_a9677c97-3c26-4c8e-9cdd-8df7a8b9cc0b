'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  DollarSign,
  Wrench,
  TrendingUp,
  Calendar
} from 'lucide-react';
import { EnhancedMaintenanceService, MaintenanceRecommendation } from '@/lib/api';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';

interface MaintenanceRecommendationsProps {
  vehicleId: string;
  vehicleName: string;
  onCreateMaintenance?: (recommendation: MaintenanceRecommendation) => void;
}

export function MaintenanceRecommendations({
  vehicleId,
  vehicleName,
  onCreateMaintenance
}: MaintenanceRecommendationsProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [recommendations, setRecommendations] = useState<MaintenanceRecommendation[]>([]);

  useEffect(() => {
    loadRecommendations();
  }, [vehicleId]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      const data = await EnhancedMaintenanceService.getMaintenanceRecommendations(vehicleId);
      setRecommendations(data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load maintenance recommendations',
        variant: 'destructive',
      });
      console.error('Error loading recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'MEDIUM':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'LOW':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Wrench className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityVariant = (priority: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (priority) {
      case 'HIGH':
        return 'destructive';
      case 'MEDIUM':
        return 'default';
      case 'LOW':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Maintenance Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">Loading recommendations...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Maintenance Recommendations for {vehicleName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {recommendations.length === 0 ? (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              No maintenance recommendations at this time. Your vehicle is up to date!
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            {recommendations.map((recommendation, index) => (
              <div
                key={index}
                className="border rounded-lg p-4 space-y-3 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(recommendation.priority)}
                    <div>
                      <h4 className="font-semibold">{recommendation.description}</h4>
                      <p className="text-sm text-gray-600">
                        Category: {recommendation.category.replace('_', ' ')}
                      </p>
                    </div>
                  </div>
                  <Badge variant={getPriorityVariant(recommendation.priority)}>
                    {recommendation.priority}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span>Estimated Cost: {formatCurrency(recommendation.estimatedCost)}</span>
                  </div>
                  
                  {recommendation.dueAtMileage && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-gray-500" />
                      <span>Due at: {recommendation.dueAtMileage.toLocaleString()} km</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <Wrench className="h-4 w-4 text-gray-500" />
                    <span>Type: {recommendation.type.replace('_', ' ')}</span>
                  </div>
                </div>

                {recommendation.note && (
                  <Alert>
                    <AlertDescription className="text-sm">
                      <strong>Note:</strong> {recommendation.note}
                    </AlertDescription>
                  </Alert>
                )}

                {onCreateMaintenance && (
                  <div className="flex justify-end">
                    <Button
                      size="sm"
                      onClick={() => onCreateMaintenance(recommendation)}
                      className="flex items-center gap-2"
                    >
                      <Calendar className="h-4 w-4" />
                      Schedule Maintenance
                    </Button>
                  </div>
                )}
              </div>
            ))}

            {/* Summary */}
            <div className="border-t pt-4 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <p className="font-semibold text-lg">
                    {recommendations.filter(r => r.priority === 'HIGH').length}
                  </p>
                  <p className="text-red-600">High Priority</p>
                </div>
                <div className="text-center">
                  <p className="font-semibold text-lg">
                    {recommendations.filter(r => r.priority === 'MEDIUM').length}
                  </p>
                  <p className="text-yellow-600">Medium Priority</p>
                </div>
                <div className="text-center">
                  <p className="font-semibold text-lg">
                    {formatCurrency(recommendations.reduce((sum, r) => sum + r.estimatedCost, 0))}
                  </p>
                  <p className="text-gray-600">Total Estimated Cost</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Enhanced Maintenance Form Component
interface EnhancedMaintenanceFormProps {
  vehicleId: string;
  vehicleType: 'TRUCK' | 'TRAILER';
  initialData?: Partial<MaintenanceRecommendation>;
  onSubmit?: (data: any) => void;
  onCancel?: () => void;
}

export function EnhancedMaintenanceForm({
  vehicleId,
  vehicleType,
  initialData,
  onSubmit,
  onCancel
}: EnhancedMaintenanceFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    type: initialData?.type || 'PREVENTIVE_MAINTENANCE',
    category: initialData?.category || '',
    description: initialData?.description || '',
    scheduledDate: '',
    mileage: '',
    partsCost: '',
    laborCost: '',
    technician: '',
    notes: '',
  });

  useEffect(() => {
    loadCategories();
  }, [vehicleType]);

  const loadCategories = async () => {
    try {
      const data = await EnhancedMaintenanceService.getMaintenanceCategoriesForVehicleType(vehicleType);
      setCategories(data);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load maintenance categories',
        variant: 'destructive',
      });
      console.error('Error loading categories:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.category || !formData.description || !formData.scheduledDate) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      
      const submitData = {
        vehicleId,
        type: formData.type,
        category: formData.category,
        description: formData.description,
        scheduledDate: formData.scheduledDate,
        mileage: formData.mileage ? parseInt(formData.mileage) : undefined,
        partsCost: formData.partsCost ? parseFloat(formData.partsCost) : undefined,
        laborCost: formData.laborCost ? parseFloat(formData.laborCost) : undefined,
        technician: formData.technician || undefined,
        notes: formData.notes || undefined,
      };

      await EnhancedMaintenanceService.createMaintenance(submitData);
      toast({
        title: 'Success',
        description: 'Maintenance scheduled successfully',
      });

      if (onSubmit) {
        onSubmit(submitData);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to schedule maintenance',
        variant: 'destructive',
      });
      console.error('Error creating maintenance:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule Enhanced Maintenance</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Category *</label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full p-2 border rounded-md"
                required
              >
                <option value="">Select category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category.replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Scheduled Date *</label>
              <input
                type="date"
                value={formData.scheduledDate}
                onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                className="w-full p-2 border rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Mileage</label>
              <input
                type="number"
                value={formData.mileage}
                onChange={(e) => handleInputChange('mileage', e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Current mileage"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Technician</label>
              <input
                type="text"
                value={formData.technician}
                onChange={(e) => handleInputChange('technician', e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Technician name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Parts Cost</label>
              <input
                type="number"
                step="0.01"
                value={formData.partsCost}
                onChange={(e) => handleInputChange('partsCost', e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="0.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Labor Cost</label>
              <input
                type="number"
                step="0.01"
                value={formData.laborCost}
                onChange={(e) => handleInputChange('laborCost', e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="0.00"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description *</label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full p-2 border rounded-md"
              placeholder="Maintenance description"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Notes</label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="w-full p-2 border rounded-md"
              rows={3}
              placeholder="Additional notes"
            />
          </div>

          <div className="flex justify-end gap-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? 'Scheduling...' : 'Schedule Maintenance'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
