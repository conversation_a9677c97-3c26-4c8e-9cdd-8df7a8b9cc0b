'use client';

import { useAuth } from '@/context/auth-context';
import { UserRole } from '@/lib/rbac/permissions';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export function RoleSwitcher() {
  const { user, setUserRole } = useAuth();

  // Available roles for switching
  const roles: UserRole[] = ['ADMIN', 'MANAGER', 'DRIVER'];

  // Only show if user is logged in
  if (!user) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Role: {user.role}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {roles.map((role) => (
          <DropdownMenuItem 
            key={role}
            onClick={() => setUserRole(role)}
            className={user.role === role ? 'bg-gray-100 font-bold' : ''}
          >
            {role}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
