'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ConsistentLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function ConsistentContainer({ children, className }: ConsistentLayoutProps) {
  // Add consistent container styling that matches the max-w-7xl and padding in the layout
  return (
    <div className={cn("mx-auto max-w-7xl w-full px-4 sm:px-6 lg:px-8", className)}>
      {children}
    </div>
  );
}

// This can be used as a wrapper for sections that need to align with the navbar
export function ContentSection({ children, className }: ConsistentLayoutProps) {
  return (
    <section className={cn("w-full", className)}>
      {children}
    </section>
  );
}
