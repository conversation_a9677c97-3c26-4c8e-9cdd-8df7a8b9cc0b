"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarComponent } from "@/components/ui/calendar-fixed"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { CalendarIcon } from "lucide-react"

interface DatePickerProps {
  value?: Date
  date?: Date
  onChange?: (date: Date | undefined) => void
  setDate?: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DatePicker({
  value,
  date,
  onChange,
  setDate,
  placeholder = "Pick a date",
  className,
  disabled = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  const currentValue = value || date

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>          
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !currentValue && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {currentValue ? format(currentValue, "PPP") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <CalendarComponent
          selected={currentValue}
          onSelect={(date: Date | undefined) => {
            onChange?.(date)
            setDate?.(date)
            setOpen(false)
          }}
          disabled={disabled}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}
