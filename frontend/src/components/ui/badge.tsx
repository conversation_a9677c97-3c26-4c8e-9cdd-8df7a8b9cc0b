import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // Assignment status variants
        pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
        active: "bg-green-100 text-green-800 border-green-200",
        completed: "bg-blue-100 text-blue-800 border-blue-200",
        cancelled: "bg-gray-100 text-gray-800 border-gray-200",
        delayed: "bg-orange-100 text-orange-800 border-orange-200",
        "on-hold": "bg-purple-100 text-purple-800 border-purple-200",
        // Assignment type variants
        regular: "bg-sky-100 text-sky-800 border-sky-200",
        temporary: "bg-indigo-100 text-indigo-800 border-indigo-200",
        emergency: "bg-red-100 text-red-800 border-red-200",
        maintenance: "bg-amber-100 text-amber-800 border-amber-200",
        training: "bg-emerald-100 text-emerald-800 border-emerald-200",
        // Priority variants
        low: "bg-blue-50 text-blue-700 border-blue-100",
        normal: "bg-green-50 text-green-700 border-green-100",
        high: "bg-orange-50 text-orange-700 border-orange-100",
        urgent: "bg-red-50 text-red-700 border-red-100",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  children?: React.ReactNode;
}

function Badge({ className, variant, children, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props}>
      {children}
    </div>
  );
}

export { Badge, badgeVariants };
