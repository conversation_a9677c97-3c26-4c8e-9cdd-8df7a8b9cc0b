'use client';

import React, { useState, useEffect } from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { Button } from './button';

interface ConflictNotificationProps {
  warnings: string[];
  onClose: () => void;
  duration?: number; // Duration in milliseconds, default 5000
}

export function ConflictNotification({ 
  warnings, 
  onClose, 
  duration = 5000 
}: ConflictNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState(duration / 1000);

  useEffect(() => {
    // Auto-close timer
    const autoCloseTimer = setTimeout(() => {
      handleClose();
    }, duration);

    // Countdown timer
    const countdownTimer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(countdownTimer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearTimeout(autoCloseTimer);
      clearInterval(countdownTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-2xl px-4">
      <div className={`
        bg-amber-50 border-2 border-amber-200 rounded-lg shadow-lg p-4
        transition-all duration-300 ease-in-out
        ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'}
      `}>
        <div className="flex items-start gap-3">
          {/* Warning Icon */}
          <div className="flex-shrink-0">
            <AlertTriangle className="h-6 w-6 text-amber-600 mt-0.5" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-amber-800">
                ⚠️ Schedule Conflicts Detected
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-amber-600 hover:text-amber-800 hover:bg-amber-100 p-1 h-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <p className="text-amber-700 mb-3 text-sm">
              Your trip was created successfully, but the following scheduling conflicts were detected:
            </p>

            <div className="space-y-2 mb-4">
              {warnings.map((warning, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-amber-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-amber-800 text-sm font-medium">{warning}</p>
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between">
              <p className="text-xs text-amber-600">
                Please review and adjust schedules as needed to avoid conflicts.
              </p>
              <div className="flex items-center gap-2 text-xs text-amber-600">
                <span>Auto-closing in {timeLeft}s</span>
                <div className="w-12 h-1 bg-amber-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-amber-500 transition-all duration-1000 ease-linear"
                    style={{ width: `${(timeLeft / (duration / 1000)) * 100}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
