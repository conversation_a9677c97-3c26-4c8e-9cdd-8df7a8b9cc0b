import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { MaintenanceLog } from '@/types/maintenance';

interface AddServiceRecordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => void;
  vehicleId: string;
  editingRecord?: MaintenanceLog | null;
}

export const AddServiceRecordDialog: React.FC<AddServiceRecordDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  vehicleId,
  editingRecord
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    type: 'PREVENTIVE',
    category: 'OTHER',
    description: '',
    scheduledDate: '',
    date: '',
    mileage: '',
    partsCost: '',
    laborCost: '',
    technician: '',
    notes: '',
    status: 'SCHEDULED'
  });

  useEffect(() => {
    if (editingRecord) {
      setFormData({
        type: editingRecord.type || 'PREVENTIVE',
        category: editingRecord.category || 'OTHER',
        description: editingRecord.description || '',
        scheduledDate: editingRecord.scheduledDate ? editingRecord.scheduledDate.split('T')[0] : '',
        date: editingRecord.date ? editingRecord.date.split('T')[0] : '',
        mileage: editingRecord.mileage?.toString() || '',
        partsCost: editingRecord.partsCost?.toString() || '',
        laborCost: editingRecord.laborCost?.toString() || '',
        technician: editingRecord.technician || '',
        notes: editingRecord.notes || '',
        status: editingRecord.status || 'SCHEDULED'
      });
    } else {
      // Reset form for new record
      setFormData({
        type: 'PREVENTIVE',
        category: 'OTHER',
        description: '',
        scheduledDate: new Date().toISOString().split('T')[0],
        date: '',
        mileage: '',
        partsCost: '',
        laborCost: '',
        technician: '',
        notes: '',
        status: 'SCHEDULED'
      });
    }
  }, [editingRecord, open]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.description.trim()) {
      newErrors.description = t('fleet:service.descriptionRequired');
    }

    if (!formData.scheduledDate) {
      newErrors.scheduledDate = t('fleet:service.scheduledDateRequired');
    }

    // If status is COMPLETED, date should be provided
    if (formData.status === 'COMPLETED' && !formData.date) {
      newErrors.date = t('fleet:service.completedDateRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // For the backend DTO that requires date field, we'll send today's date if not provided
      // This is a workaround until the backend DTO is fixed to make date optional
      const currentDate = new Date().toISOString();

      const submitData = {
        type: formData.type,
        category: formData.category,
        description: formData.description,
        scheduledDate: formData.scheduledDate ? new Date(formData.scheduledDate).toISOString() : currentDate,
        date: formData.date ? new Date(formData.date).toISOString() : currentDate, // Required by backend DTO
        mileage: formData.mileage ? parseInt(formData.mileage) : undefined,
        partsCost: formData.partsCost ? parseFloat(formData.partsCost) : undefined,
        laborCost: formData.laborCost ? parseFloat(formData.laborCost) : undefined,
        technician: formData.technician || undefined,
        notes: formData.notes || undefined,
        status: formData.status,
        vehicleId: vehicleId
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting service record:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }

    // Special handling for status changes
    if (field === 'status') {
      // Clear date error when status changes away from COMPLETED
      if (value !== 'COMPLETED' && errors.date) {
        setErrors(prev => ({
          ...prev,
          date: ''
        }));
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingRecord ? t('fleet:service.editService') : t('fleet:service.addService')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">{t('fleet:service.serviceType')}</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PREVENTIVE">{t('fleet:service.preventive')}</SelectItem>
                  <SelectItem value="REPAIR">{t('fleet:service.repair')}</SelectItem>
                  <SelectItem value="INSPECTION">{t('fleet:service.inspection')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category">{t('fleet:service.category')}</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ENGINE">{t('fleet:categories.engine')}</SelectItem>
                  <SelectItem value="TRANSMISSION">{t('fleet:categories.transmission')}</SelectItem>
                  <SelectItem value="BRAKES">{t('fleet:categories.brakes')}</SelectItem>
                  <SelectItem value="ELECTRICAL">{t('fleet:categories.electrical')}</SelectItem>
                  <SelectItem value="TIRES">{t('fleet:categories.tires')}</SelectItem>
                  <SelectItem value="COOLING_SYSTEM">{t('fleet:categories.coolingSystem')}</SelectItem>
                  <SelectItem value="FUEL_SYSTEM">{t('fleet:categories.fuelSystem')}</SelectItem>
                  <SelectItem value="EXHAUST_SYSTEM">{t('fleet:categories.exhaustSystem')}</SelectItem>
                  <SelectItem value="SUSPENSION_AXLES">{t('fleet:categories.suspensionAxles')}</SelectItem>
                  <SelectItem value="CARGO_AREA">{t('fleet:categories.cargoArea')}</SelectItem>
                  <SelectItem value="REFRIGERATION_UNIT">{t('fleet:categories.refrigerationUnit')}</SelectItem>
                  <SelectItem value="HYDRAULIC_SYSTEMS">{t('fleet:categories.hydraulicSystems')}</SelectItem>
                  <SelectItem value="LIGHTING_SYSTEM">{t('fleet:categories.lightingSystem')}</SelectItem>
                  <SelectItem value="OTHER">{t('fleet:categories.other')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">{t('fleet:service.description')} *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder={t('fleet:service.descriptionPlaceholder')}
              required
              className={errors.description ? 'border-red-500 focus:border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-600 mt-1">{errors.description}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="scheduledDate">{t('fleet:service.scheduledDate')} *</Label>
              <Input
                id="scheduledDate"
                type="date"
                value={formData.scheduledDate}
                onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                required
                className={errors.scheduledDate ? 'border-red-500 focus:border-red-500' : ''}
              />
              {errors.scheduledDate && (
                <p className="text-sm text-red-600 mt-1">{errors.scheduledDate}</p>
              )}
            </div>

            <div>
              <Label htmlFor="date">
                {t('fleet:service.serviceDate')}
                {formData.status === 'COMPLETED' && ' *'}
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                className={errors.date ? 'border-red-500 focus:border-red-500' : ''}
              />
              {errors.date && (
                <p className="text-sm text-red-600 mt-1">{errors.date}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                {t('fleet:service.serviceDateHelp')}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="mileage">{t('fleet:service.mileageAtService')}</Label>
              <Input
                id="mileage"
                type="number"
                value={formData.mileage}
                onChange={(e) => handleInputChange('mileage', e.target.value)}
                placeholder="0"
              />
            </div>

            <div>
              <Label htmlFor="status">{t('fleet:service.status')}</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SCHEDULED">{t('fleet:service.scheduled')}</SelectItem>
                  <SelectItem value="IN_PROGRESS">{t('fleet:service.inprogress')}</SelectItem>
                  <SelectItem value="COMPLETED">{t('fleet:service.completed')}</SelectItem>
                  <SelectItem value="CANCELLED">{t('fleet:service.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="partsCost">{t('fleet:service.partsCost')}</Label>
              <Input
                id="partsCost"
                type="number"
                step="0.01"
                value={formData.partsCost}
                onChange={(e) => handleInputChange('partsCost', e.target.value)}
                placeholder="0.00"
              />
            </div>

            <div>
              <Label htmlFor="laborCost">{t('fleet:service.laborCost')}</Label>
              <Input
                id="laborCost"
                type="number"
                step="0.01"
                value={formData.laborCost}
                onChange={(e) => handleInputChange('laborCost', e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="technician">{t('fleet:service.technician')}</Label>
            <Input
              id="technician"
              value={formData.technician}
              onChange={(e) => handleInputChange('technician', e.target.value)}
              placeholder={t('fleet:service.technicianPlaceholder')}
            />
          </div>

          <div>
            <Label htmlFor="notes">{t('fleet:service.additionalNotes')}</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder={t('fleet:service.noNotes')}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              {t('common:cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                editingRecord ? t('fleet:service.updating') : t('fleet:service.creating')
              ) : (
                editingRecord ? t('fleet:service.updateRecord') : t('fleet:service.createRecord')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
