import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { InsurancePolicyForm } from './InsurancePolicyForm';
import { ArrowLeft, Edit, Trash2, Shield, Calendar, DollarSign, FileText } from 'lucide-react';

interface InsurancePolicy {
  id: string;
  vehicleId: string;
  vehicle?: {
    make: string;
    model: string;
    plateNumber: string;
    year?: number;
  };
  policyNumber: string;
  provider: string;
  type: string;
  startDate: string;
  endDate: string;
  premium: number;
  status: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON';
  coverage: string;
}

interface FleetInsuranceDetailProps {
  policyId: string;
  onBack?: () => void;
  onInsuranceUpdated?: () => void;
}

export const FleetInsuranceDetail: React.FC<FleetInsuranceDetailProps> = ({
  policyId,
  onBack,
  onInsuranceUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const { toast } = useToast();
  const [policy, setPolicy] = useState<InsurancePolicy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  useEffect(() => {
    const fetchPolicyDetails = async () => {
      if (!policyId) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('Fetching insurance policy details for ID:', policyId);
        
        // Mock data for demonstration - in real app this would fetch from API
        const mockPolicy: InsurancePolicy = {
          id: policyId,
          vehicleId: 'v1',
          vehicle: { 
            make: 'Volvo', 
            model: 'FH16', 
            plateNumber: 'ABC-123',
            year: 2022
          },
          policyNumber: 'POL-2024-001',
          provider: 'Fleet Insurance Co.',
          type: 'Comprehensive',
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          premium: 2500,
          status: 'ACTIVE',
          coverage: 'Full coverage including liability, collision, and comprehensive. Covers damage to vehicle, third-party liability up to $1M, theft protection, and roadside assistance.'
        };
        
        console.log('Policy data received:', mockPolicy);
        setPolicy(mockPolicy);
      } catch (error) {
        console.error('Error fetching insurance policy details:', error);
        toast({
          variant: 'destructive',
          title: t('common:error'),
          description: t('fleet:messages.errorLoadingPolicyDetails'),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicyDetails();
  }, [policyId, toast]);

  const handleDelete = async () => {
    try {
      // Mock delete operation - in real app this would call API
      console.log('Deleting insurance policy:', policyId);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.policyDeletedSuccess'),
      });
      onInsuranceUpdated?.();
      onBack?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('common:error'),
        description: t('fleet:messages.failedToDeletePolicy'),
      });
    }
  };

  const handleEditComplete = () => {
    setShowEditForm(false);
    // Refresh the policy data
    const fetchPolicyDetails = async () => {
      try {
        // Mock refresh - in real app this would fetch updated data
        console.log('Refreshing policy data after edit');
        onInsuranceUpdated?.();
      } catch (error) {
        console.error('Error refreshing policy details:', error);
      }
    };
    fetchPolicyDetails();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500';
      case 'EXPIRED':
        return 'bg-red-500';
      case 'EXPIRING_SOON':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return t('fleet:table.active');
      case 'EXPIRED':
        return t('fleet:table.expired');
      case 'EXPIRING_SOON':
        return t('fleet:table.expiringSoon');
      default:
        return status;
    }
  };

  const getPolicyTypeLabel = (type: string) => {
    switch (type) {
      case 'Comprehensive':
      case 'COMPREHENSIVE':
        return t('fleet:insurance.comprehensive');
      case 'Liability':
      case 'LIABILITY':
        return t('fleet:insurance.liability');
      case 'Collision':
      case 'COLLISION':
        return t('fleet:insurance.collision');
      case 'Third Party':
      case 'THIRD_PARTY':
        return t('fleet:insurance.thirdParty');
      case 'Fire & Theft':
      case 'FIRE_THEFT':
        return t('fleet:insurance.fireTheft');
      default:
        return type;
    }
  };

  const getDaysUntilExpiry = () => {
    if (!policy) return null;
    const endDate = new Date(policy.endDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full py-10">
        <div className="text-center">
          <div>{t('fleet:insurance.loadingPolicy')}</div>
          <div className="text-sm text-gray-500 mt-2">{t('fleet:insurance.policyId')}: {policyId}</div>
        </div>
      </div>
    );
  }

  if (!policyId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:insurance.invalidPolicyId')}</h2>
              <p className="mb-4">{t('fleet:insurance.noPolicyIdProvided')}</p>
              <Button onClick={onBack}>
                {t('fleet:insurance.backToPolicies')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!policy) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:insurance.policyNotFound')}</h2>
              <p className="mb-4">{t('fleet:insurance.policyNotFoundDescription')}</p>
              <Button onClick={onBack}>
                {t('fleet:insurance.backToPolicies')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const daysUntilExpiry = getDaysUntilExpiry();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('fleet:insurance.backToPolicies')}
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold leading-7 text-gray-900">
                {t('fleet:insurance.policyDetails')}
              </h2>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-600">
                  {policy.vehicle?.make} {policy.vehicle?.model} ({policy.vehicle?.plateNumber})
                </p>
                <Badge className={getStatusColor(policy.status)}>
                  {getStatusLabel(policy.status)}
                </Badge>
                {daysUntilExpiry !== null && daysUntilExpiry <= 30 && daysUntilExpiry > 0 && (
                  <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                    {t('fleet:insurance.expiresInDays', { days: daysUntilExpiry })}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowEditForm(true)}
            className="gap-2"
          >
            <Edit className="h-4 w-4" />
            {t('fleet:insurance.editPolicy')}
          </Button>
          <Button
            variant="destructive"
            onClick={() => setConfirmDelete(true)}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {t('fleet:insurance.deletePolicy')}
          </Button>
        </div>
      </div>

      {/* Policy Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('fleet:insurance.policyInformation')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.policyNumber')}</dt>
                <dd className="text-sm text-gray-900 font-mono">{policy.policyNumber}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.provider')}</dt>
                <dd className="text-sm text-gray-900">{policy.provider}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.policyType')}</dt>
                <dd className="text-sm text-gray-900">{getPolicyTypeLabel(policy.type)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('common:status.title')}</dt>
                <dd className="text-sm text-gray-900">
                  <Badge className={getStatusColor(policy.status)}>
                    {getStatusLabel(policy.status)}
                  </Badge>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        {/* Coverage Period & Premium */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('fleet:insurance.coverageAndPremium')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.coverageStart')}</dt>
                <dd className="text-sm text-gray-900">{formatDate(policy.startDate)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.coverageEnd')}</dt>
                <dd className="text-sm text-gray-900">{formatDate(policy.endDate)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.annualPremium')}</dt>
                <dd className="text-sm text-gray-900 font-semibold flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  {formatCurrency(policy.premium)}
                </dd>
              </div>
              {daysUntilExpiry !== null && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.daysUntilExpiry')}</dt>
                  <dd className={`text-sm font-medium ${
                    daysUntilExpiry <= 0 ? 'text-red-600' :
                    daysUntilExpiry <= 30 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {daysUntilExpiry <= 0 ? t('fleet:table.expired') : t('fleet:insurance.daysCount', { days: daysUntilExpiry })}
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:insurance.vehicleInformation')}</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.title')}</dt>
              <dd className="text-sm text-gray-900">
                {policy.vehicle?.make} {policy.vehicle?.model} ({policy.vehicle?.year})
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.plateNumber')}</dt>
              <dd className="text-sm text-gray-900 font-medium">{policy.vehicle?.plateNumber}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:insurance.vehicleId')}</dt>
              <dd className="text-sm text-gray-900 font-mono text-xs">{policy.vehicleId}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Coverage Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:insurance.coverageDetails')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-900 whitespace-pre-line">
            {policy.coverage || t('fleet:insurance.noCoverageDetails')}
          </p>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('fleet:insurance.confirmDelete')}</DialogTitle>
            <DialogDescription>
              {t('fleet:insurance.confirmDeleteDescription')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(false)}>
              {t('common:actions.cancel')}
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              {t('common:actions.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Insurance Policy Form Dialog */}
      {showEditForm && (
        <InsurancePolicyForm
          policyId={policyId}
          onClose={() => setShowEditForm(false)}
          onPolicyUpdated={handleEditComplete}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
