import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { InsurancePolicy, InsuranceType, PolicyStatus } from '@/types/insurance';

interface AddInsuranceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => void;
  vehicleId: string;
  editingPolicy?: InsurancePolicy | null;
}

export const AddInsuranceDialog: React.FC<AddInsuranceDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  vehicleId,
  editingPolicy
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    policyNumber: '',
    provider: '',
    type: 'COMPREHENSIVE' as InsuranceType,
    startDate: '',
    endDate: '',
    premium: '',
    coverage: '',
    deductible: '',
    status: 'ACTIVE' as PolicyStatus,
    notes: ''
  });

  useEffect(() => {
    if (editingPolicy) {
      setFormData({
        policyNumber: editingPolicy.policyNumber || '',
        provider: editingPolicy.provider || '',
        type: editingPolicy.type || 'COMPREHENSIVE',
        startDate: editingPolicy.startDate ? editingPolicy.startDate.split('T')[0] : '',
        endDate: editingPolicy.endDate ? editingPolicy.endDate.split('T')[0] : '',
        premium: editingPolicy.premium?.toString() || '',
        coverage: editingPolicy.coverage?.toString() || '',
        deductible: editingPolicy.deductible?.toString() || '',
        status: editingPolicy.status || 'ACTIVE',
        notes: editingPolicy.notes || ''
      });
    } else {
      // Reset form for new policy
      const today = new Date();
      const nextYear = new Date();
      nextYear.setFullYear(today.getFullYear() + 1);
      
      setFormData({
        policyNumber: '',
        provider: '',
        type: 'COMPREHENSIVE',
        startDate: today.toISOString().split('T')[0],
        endDate: nextYear.toISOString().split('T')[0],
        premium: '',
        coverage: '',
        deductible: '',
        status: 'ACTIVE',
        notes: ''
      });
    }
  }, [editingPolicy, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        policyNumber: formData.policyNumber,
        provider: formData.provider,
        type: formData.type,
        startDate: formData.startDate,
        endDate: formData.endDate,
        premium: formData.premium ? parseFloat(formData.premium) : 0,
        coverage: formData.coverage ? parseFloat(formData.coverage) : 0,
        deductible: formData.deductible ? parseFloat(formData.deductible) : 0,
        status: formData.status,
        notes: formData.notes || undefined,
        vehicleId: vehicleId
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting insurance policy:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingPolicy ? t('fleet:insurance.editInsurance') : t('fleet:insurance.addInsurance')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="policyNumber">{t('fleet:insurance.policyNumber')}</Label>
              <Input
                id="policyNumber"
                value={formData.policyNumber}
                onChange={(e) => handleInputChange('policyNumber', e.target.value)}
                placeholder={t('fleet:insurance.policyNumberPlaceholder')}
                required
              />
            </div>

            <div>
              <Label htmlFor="provider">{t('fleet:insurance.provider')}</Label>
              <Input
                id="provider"
                value={formData.provider}
                onChange={(e) => handleInputChange('provider', e.target.value)}
                placeholder={t('fleet:insurance.providerPlaceholder')}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">{t('fleet:insurance.policyType')}</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COMPREHENSIVE">{t('fleet:insurance.comprehensive')}</SelectItem>
                  <SelectItem value="THIRD_PARTY">{t('fleet:insurance.thirdParty')}</SelectItem>
                  <SelectItem value="FIRE_THEFT">{t('fleet:insurance.fireTheft')}</SelectItem>
                  <SelectItem value="LIABILITY">{t('fleet:insurance.liability')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">{t('fleet:insurance.status')}</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ACTIVE">{t('fleet:table.active')}</SelectItem>
                  <SelectItem value="EXPIRED">{t('fleet:table.expired')}</SelectItem>
                  <SelectItem value="CANCELLED">{t('fleet:service.cancelled')}</SelectItem>
                  <SelectItem value="RENEWAL_DUE">{t('fleet:insurance.renewaldue')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">{t('fleet:insurance.startDate')}</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="endDate">{t('fleet:insurance.endDate')}</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="premium">{t('fleet:insurance.annualPremium')}</Label>
              <Input
                id="premium"
                type="number"
                step="0.01"
                value={formData.premium}
                onChange={(e) => handleInputChange('premium', e.target.value)}
                placeholder={t('fleet:insurance.premiumPlaceholder')}
                required
              />
            </div>

            <div>
              <Label htmlFor="coverage">{t('fleet:insurance.coverage')}</Label>
              <Input
                id="coverage"
                type="number"
                step="0.01"
                value={formData.coverage}
                onChange={(e) => handleInputChange('coverage', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            <div>
              <Label htmlFor="deductible">{t('fleet:insurance.deductible')}</Label>
              <Input
                id="deductible"
                type="number"
                step="0.01"
                value={formData.deductible}
                onChange={(e) => handleInputChange('deductible', e.target.value)}
                placeholder="0.00"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">{t('fleet:insurance.coverageDetails')}</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder={t('fleet:insurance.coveragePlaceholder')}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              {t('common:cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                editingPolicy ? t('fleet:insurance.updating') : t('fleet:insurance.creating')
              ) : (
                editingPolicy ? t('fleet:insurance.updatePolicy') : t('fleet:insurance.createPolicy')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
