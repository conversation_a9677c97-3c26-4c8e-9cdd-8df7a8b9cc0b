import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { MaintenanceService } from '@/lib/api/maintenance-service';
import { FleetService } from '@/lib/api/fleet-service';
import { Vehicle } from '@/types/vehicle';
import { MaintenanceLog } from '@/types/maintenance';
import { X, Save } from 'lucide-react';

interface ServiceRecordFormProps {
  recordId?: string;
  onClose: () => void;
  onRecordAdded?: () => void;
  onRecordUpdated?: () => void;
  showAsDialog?: boolean;
}

export const ServiceRecordForm: React.FC<ServiceRecordFormProps> = ({
  recordId,
  onClose,
  onRecordAdded,
  onRecordUpdated,
  showAsDialog = false
}) => {
  const { t } = useTranslation(['fleet', 'forms', 'common']);
  const { toast } = useToast();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    vehicleId: '',
    type: 'REPAIR',
    category: 'OTHER',
    description: '',
    notes: '',
    partsCost: '',
    laborCost: '',
    mileage: '',
    date: new Date().toISOString().split('T')[0],
    scheduledDate: new Date().toISOString().split('T')[0],
    status: 'COMPLETED',
    technician: ''
  });

  useEffect(() => {
    fetchVehicles();
    if (recordId) {
      fetchServiceRecord();
    }
  }, [recordId]);

  const fetchVehicles = async () => {
    try {
      const vehiclesData = await FleetService.getVehicles();
      setVehicles(vehiclesData);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:vehicles.loadError', 'Failed to load vehicles'),
        variant: 'destructive',
      });
    }
  };

  const fetchServiceRecord = async () => {
    if (!recordId) return;
    
    try {
      setLoading(true);
      const record = await MaintenanceService.getMaintenanceLog(recordId);
      setFormData({
        vehicleId: record.vehicleId,
        type: record.type || 'REPAIR',
        category: record.category || 'OTHER',
        description: record.description,
        notes: record.notes || '',
        partsCost: record.partsCost?.toString() || '',
        laborCost: record.laborCost?.toString() || '',
        mileage: record.mileage?.toString() || '',
        date: record.date ? record.date.split('T')[0] : '',
        scheduledDate: record.scheduledDate ? record.scheduledDate.split('T')[0] : new Date().toISOString().split('T')[0],
        status: record.status || 'COMPLETED',
        technician: record.technician || ''
      });
    } catch (error) {
      console.error('Error fetching service record:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:service.loadRecordError', 'Failed to load service record'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicleId || !formData.description) {
      toast({
        title: t('common:error'),
        description: t('forms:validation.requiredFields', 'Please fill in all required fields'),
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    
    try {
      const serviceData = {
        vehicleId: formData.vehicleId,
        type: formData.type,
        category: formData.category,
        description: formData.description,
        notes: formData.notes,
        partsCost: formData.partsCost ? parseFloat(formData.partsCost) : undefined,
        laborCost: formData.laborCost ? parseFloat(formData.laborCost) : undefined,
        mileage: formData.mileage ? parseInt(formData.mileage) : undefined,
        date: formData.date ? new Date(formData.date).toISOString() : undefined,
        scheduledDate: formData.scheduledDate ? new Date(formData.scheduledDate).toISOString() : new Date().toISOString(),
        status: formData.status,
        technician: formData.technician
      };

      if (recordId) {
        await MaintenanceService.updateMaintenanceLog(recordId, serviceData);
        toast({
          title: t('common:success'),
          description: t('fleet:service.updateSuccess', 'Service record updated successfully'),
        });
        onRecordUpdated?.();
      } else {
        await MaintenanceService.createMaintenanceLog(serviceData);
        toast({
          title: t('common:success'),
          description: t('fleet:service.createSuccess', 'Service record created successfully'),
        });
        onRecordAdded?.();
      }
      
      onClose();
    } catch (error) {
      console.error('Error saving service record:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:service.saveError', 'Failed to save service record'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vehicleId">{t('fleet:vehicles.title')} *</Label>
          <Select value={formData.vehicleId} onValueChange={(value) => handleInputChange('vehicleId', value)}>
            <SelectTrigger>
              <SelectValue placeholder={t('fleet:service.selectVehicle', 'Select vehicle')} />
            </SelectTrigger>
            <SelectContent>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.id} value={vehicle.id}>
                  {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="type">{t('fleet:service.serviceType')}</Label>
          <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="REPAIR">{t('fleet:service.repair')}</SelectItem>
              <SelectItem value="PREVENTIVE">{t('fleet:service.preventiveMaintenance')}</SelectItem>
              <SelectItem value="INSPECTION">{t('fleet:service.inspection')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">{t('fleet:service.description')} *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder={t('fleet:service.descriptionPlaceholder', 'Describe the service performed')}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="date">{t('fleet:service.serviceDate')}</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="scheduledDate">{t('fleet:service.scheduledDate')}</Label>
          <Input
            id="scheduledDate"
            type="date"
            value={formData.scheduledDate}
            onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="partsCost">{t('fleet:service.partsCost')}</Label>
          <Input
            id="partsCost"
            type="number"
            step="0.01"
            value={formData.partsCost}
            onChange={(e) => handleInputChange('partsCost', e.target.value)}
            placeholder="0.00"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="laborCost">{t('fleet:service.laborCost')}</Label>
          <Input
            id="laborCost"
            type="number"
            step="0.01"
            value={formData.laborCost}
            onChange={(e) => handleInputChange('laborCost', e.target.value)}
            placeholder="0.00"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="mileage">{t('fleet:vehicles.mileage')}</Label>
          <Input
            id="mileage"
            type="number"
            value={formData.mileage}
            onChange={(e) => handleInputChange('mileage', e.target.value)}
            placeholder={t('fleet:vehicles.currentMileage')}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="technician">{t('fleet:service.technician', 'Technician')}</Label>
        <Input
          id="technician"
          value={formData.technician}
          onChange={(e) => handleInputChange('technician', e.target.value)}
          placeholder={t('fleet:service.technicianPlaceholder', 'Technician name')}
        />
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" disabled={submitting} className="gap-2">
          {submitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {recordId ? t('fleet:service.updating') : t('fleet:service.creating')}
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              {recordId ? t('fleet:service.updateRecord') : t('fleet:service.createRecord')}
            </>
          )}
        </Button>
        <Button type="button" variant="outline" onClick={onClose}>
          {t('common:actions.cancel')}
        </Button>
      </div>
    </form>
  );

  if (showAsDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {recordId ? t('fleet:service.editService') : t('fleet:service.addService')}
              </h3>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            {loading ? (
              <div className="text-center py-8">{t('common:actions.loading')}</div>
            ) : (
              formContent
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{recordId ? t('fleet:service.editService') : t('fleet:service.addService')}</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">{t('common:actions.loading')}</div>
        ) : (
          formContent
        )}
      </CardContent>
    </Card>
  );
};
