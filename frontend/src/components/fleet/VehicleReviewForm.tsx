import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { FleetService } from '@/lib/api/fleet-service';
import { Vehicle } from '@/types/vehicle';
import { X, Save, Plus, Trash2 } from 'lucide-react';

interface VehicleReview {
  id: string;
  vehicleId: string;
  reviewType: string;
  scheduledDate: string;
  completedDate?: string;
  inspector: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  score?: number;
  notes?: string;
  nextReviewDate?: string;
  issues?: Array<{
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
  }>;
}

interface VehicleReviewFormProps {
  reviewId?: string;
  onClose: () => void;
  onReviewAdded?: () => void;
  onReviewUpdated?: () => void;
  showAsDialog?: boolean;
}

export const VehicleReviewForm: React.FC<VehicleReviewFormProps> = ({
  reviewId,
  onClose,
  onReviewAdded,
  onReviewUpdated,
  showAsDialog = false
}) => {
  const { t } = useTranslation(['fleet', 'common', 'forms']);
  const { toast } = useToast();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    vehicleId: '',
    reviewType: 'Annual Safety Inspection',
    scheduledDate: new Date().toISOString().split('T')[0],
    completedDate: '',
    inspector: '',
    status: 'SCHEDULED' as const,
    score: '',
    notes: '',
    nextReviewDate: ''
  });
  const [issues, setIssues] = useState<Array<{ severity: string; description: string }>>([]);

  useEffect(() => {
    fetchVehicles();
    if (reviewId) {
      fetchVehicleReview();
    }
  }, [reviewId]);

  const fetchVehicles = async () => {
    try {
      const vehiclesData = await FleetService.getVehicles();
      setVehicles(vehiclesData);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:vehicles.loadError'),
        variant: 'destructive',
      });
    }
  };

  const fetchVehicleReview = async () => {
    if (!reviewId) return;
    
    try {
      setLoading(true);
      // Mock data for editing - in real app this would fetch from API
      const mockReview: VehicleReview = {
        id: reviewId,
        vehicleId: 'v1',
        reviewType: 'Annual Safety Inspection',
        scheduledDate: '2024-06-25',
        completedDate: '2024-06-25',
        inspector: 'John Smith',
        status: 'COMPLETED',
        score: 95,
        notes: 'Vehicle passed all safety checks. Minor brake pad wear noted.',
        nextReviewDate: '2025-06-25',
        issues: [
          { severity: 'LOW', description: 'Brake pads at 30% wear' }
        ]
      };
      
      setFormData({
        vehicleId: mockReview.vehicleId,
        reviewType: mockReview.reviewType,
        scheduledDate: mockReview.scheduledDate,
        completedDate: mockReview.completedDate || '',
        inspector: mockReview.inspector,
        status: mockReview.status,
        score: mockReview.score?.toString() || '',
        notes: mockReview.notes || '',
        nextReviewDate: mockReview.nextReviewDate || ''
      });
      setIssues(mockReview.issues || []);
    } catch (error) {
      console.error('Error fetching vehicle review:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:reviews.failedToLoadReview'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addIssue = () => {
    setIssues(prev => [...prev, { severity: 'LOW', description: '' }]);
  };

  const updateIssue = (index: number, field: string, value: string) => {
    setIssues(prev => prev.map((issue, i) => 
      i === index ? { ...issue, [field]: value } : issue
    ));
  };

  const removeIssue = (index: number) => {
    setIssues(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicleId || !formData.reviewType || !formData.inspector) {
      toast({
        title: t('common:error'),
        description: t('forms:validation.required'),
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    
    try {
      const reviewData = {
        vehicleId: formData.vehicleId,
        reviewType: formData.reviewType,
        scheduledDate: formData.scheduledDate,
        completedDate: formData.completedDate || undefined,
        inspector: formData.inspector,
        status: formData.status,
        score: formData.score ? parseInt(formData.score) : undefined,
        notes: formData.notes,
        nextReviewDate: formData.nextReviewDate || undefined,
        issues: issues.filter(issue => issue.description.trim() !== '')
      };

      // Mock API calls - in real app these would be actual API calls
      if (reviewId) {
        console.log('Updating vehicle review:', reviewData);
        toast({
          title: t('common:success'),
          description: t('fleet:reviews.reviewUpdatedSuccess'),
        });
        onReviewUpdated?.();
      } else {
        console.log('Creating vehicle review:', reviewData);
        toast({
          title: t('common:success'),
          description: t('fleet:reviews.reviewScheduledSuccess'),
        });
        onReviewAdded?.();
      }

      onClose();
    } catch (error) {
      console.error('Error saving vehicle review:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:reviews.failedToSaveReview'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vehicleId">{t('fleet:vehicles.title')} *</Label>
          <Select value={formData.vehicleId} onValueChange={(value) => handleInputChange('vehicleId', value)}>
            <SelectTrigger>
              <SelectValue placeholder={t('fleet:reviews.selectVehicle')} />
            </SelectTrigger>
            <SelectContent>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.id} value={vehicle.id}>
                  {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="reviewType">{t('fleet:reviews.reviewType')} *</Label>
          <Select value={formData.reviewType} onValueChange={(value) => handleInputChange('reviewType', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Annual Safety Inspection">{t('fleet:reviews.annualSafetyInspection')}</SelectItem>
              <SelectItem value="Quarterly Maintenance Review">{t('fleet:reviews.quarterlyMaintenanceReview')}</SelectItem>
              <SelectItem value="DOT Inspection">{t('fleet:reviews.dotInspection')}</SelectItem>
              <SelectItem value="Pre-Trip Inspection">{t('fleet:reviews.preTripInspection')}</SelectItem>
              <SelectItem value="Post-Trip Inspection">{t('fleet:reviews.postTripInspection')}</SelectItem>
              <SelectItem value="Emissions Test">{t('fleet:reviews.emissionsTest')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="inspector">{t('fleet:reviews.inspector')} *</Label>
          <Input
            id="inspector"
            value={formData.inspector}
            onChange={(e) => handleInputChange('inspector', e.target.value)}
            placeholder={t('fleet:reviews.inspector')}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">{t('fleet:table.status')}</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="SCHEDULED">{t('fleet:dashboard.scheduled')}</SelectItem>
              <SelectItem value="IN_PROGRESS">{t('fleet:dashboard.inProgress')}</SelectItem>
              <SelectItem value="COMPLETED">{t('common:status.completed')}</SelectItem>
              <SelectItem value="FAILED">{t('fleet:reviews.failed')}</SelectItem>
              <SelectItem value="CANCELLED">{t('common:status.cancelled')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="scheduledDate">{t('fleet:reviews.scheduledDate')}</Label>
          <Input
            id="scheduledDate"
            type="date"
            value={formData.scheduledDate}
            onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="completedDate">{t('fleet:reviews.completedDate')}</Label>
          <Input
            id="completedDate"
            type="date"
            value={formData.completedDate}
            onChange={(e) => handleInputChange('completedDate', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="nextReviewDate">{t('fleet:reviews.nextReviewDate')}</Label>
          <Input
            id="nextReviewDate"
            type="date"
            value={formData.nextReviewDate}
            onChange={(e) => handleInputChange('nextReviewDate', e.target.value)}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="score">{t('fleet:reviews.score')} (%)</Label>
        <Input
          id="score"
          type="number"
          min="0"
          max="100"
          value={formData.score}
          onChange={(e) => handleInputChange('score', e.target.value)}
          placeholder={t('fleet:reviews.score')}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">{t('common:common.notes')}</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          placeholder={t('fleet:reviews.enterReviewNotes')}
          rows={3}
        />
      </div>

      {/* Issues Section */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label>{t('fleet:reviews.issues')}</Label>
          <Button type="button" variant="outline" size="sm" onClick={addIssue} className="gap-1">
            <Plus className="h-3 w-3" />
            {t('fleet:reviews.addIssue')}
          </Button>
        </div>
        {issues.map((issue, index) => (
          <div key={index} className="flex gap-2 items-start p-3 border rounded-md">
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-2">
              <Select 
                value={issue.severity} 
                onValueChange={(value) => updateIssue(index, 'severity', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">{t('fleet:reviews.low')}</SelectItem>
                  <SelectItem value="MEDIUM">{t('fleet:reviews.medium')}</SelectItem>
                  <SelectItem value="HIGH">{t('fleet:reviews.high')}</SelectItem>
                  <SelectItem value="CRITICAL">{t('fleet:reviews.critical')}</SelectItem>
                </SelectContent>
              </Select>
              <div className="md:col-span-2">
                <Input
                  value={issue.description}
                  onChange={(e) => updateIssue(index, 'description', e.target.value)}
                  placeholder={t('fleet:reviews.describeIssue')}
                />
              </div>
            </div>
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              onClick={() => removeIssue(index)}
              className="text-red-600 hover:text-red-800"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" disabled={submitting} className="gap-2">
          {submitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {reviewId ? t('fleet:reviews.updating') : t('fleet:reviews.scheduling')}
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              {reviewId ? t('fleet:reviews.updateReview') : t('fleet:reviews.scheduleReview')}
            </>
          )}
        </Button>
        <Button type="button" variant="outline" onClick={onClose}>
          {t('common:actions.cancel')}
        </Button>
      </div>
    </form>
  );

  if (showAsDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {reviewId ? t('fleet:reviews.editVehicleReview') : t('fleet:reviews.scheduleVehicleReview')}
              </h3>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            {loading ? (
              <div className="text-center py-8">{t('common:actions.loading')}</div>
            ) : (
              formContent
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{reviewId ? t('fleet:reviews.editVehicleReview') : t('fleet:reviews.scheduleVehicleReview')}</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">{t('common:actions.loading')}</div>
        ) : (
          formContent
        )}
      </CardContent>
    </Card>
  );
};
