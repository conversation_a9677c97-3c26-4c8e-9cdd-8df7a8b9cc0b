import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { InsurancePolicyForm } from './InsurancePolicyForm';
import { Shield, Plus, AlertTriangle } from 'lucide-react';

interface InsurancePolicy {
  id: string;
  vehicleId: string;
  vehicle?: {
    make: string;
    model: string;
    plateNumber: string;
  };
  policyNumber: string;
  provider: string;
  type: string;
  startDate: string;
  endDate: string;
  premium: number;
  status: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON';
  coverage: string;
}

interface FleetInsuranceContentProps {
  onInsuranceUpdated?: () => void;
}

export const FleetInsuranceContent: React.FC<FleetInsuranceContentProps> = ({
  onInsuranceUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [insurancePolicies, setInsurancePolicies] = useState<InsurancePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingPolicyId, setEditingPolicyId] = useState<string | null>(null);

  useEffect(() => {
    fetchInsurancePolicies();
  }, []);

  const fetchInsurancePolicies = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      const mockPolicies: InsurancePolicy[] = [
        {
          id: '1',
          vehicleId: 'v1',
          vehicle: { make: 'Volvo', model: 'FH16', plateNumber: 'ABC-123' },
          policyNumber: 'POL-2024-001',
          provider: 'Fleet Insurance Co.',
          type: 'Comprehensive',
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          premium: 2500,
          status: 'ACTIVE',
          coverage: 'Full coverage including liability, collision, and comprehensive'
        },
        {
          id: '2',
          vehicleId: 'v2',
          vehicle: { make: 'Mercedes', model: 'Actros', plateNumber: 'DEF-456' },
          policyNumber: 'POL-2024-002',
          provider: 'Commercial Auto Insurance',
          type: 'Liability',
          startDate: '2024-03-15',
          endDate: '2025-03-14',
          premium: 1800,
          status: 'ACTIVE',
          coverage: 'Liability coverage only'
        },
        {
          id: '3',
          vehicleId: 'v3',
          vehicle: { make: 'Scania', model: 'R450', plateNumber: 'GHI-789' },
          policyNumber: 'POL-2023-015',
          provider: 'Fleet Insurance Co.',
          type: 'Comprehensive',
          startDate: '2023-06-01',
          endDate: '2024-05-31',
          premium: 2200,
          status: 'EXPIRED',
          coverage: 'Full coverage including liability, collision, and comprehensive'
        },
        {
          id: '4',
          vehicleId: 'v4',
          vehicle: { make: 'DAF', model: 'XF', plateNumber: 'JKL-012' },
          policyNumber: 'POL-2024-008',
          provider: 'Auto Guard Insurance',
          type: 'Comprehensive',
          startDate: '2024-01-15',
          endDate: '2024-07-15',
          premium: 2100,
          status: 'EXPIRING_SOON',
          coverage: 'Full coverage including liability, collision, and comprehensive'
        },
      ];
      setInsurancePolicies(mockPolicies);
    } catch (error) {
      console.error('Error fetching insurance policies:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorLoadingPolicies'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800">{t('fleet:table.active')}</Badge>;
      case 'EXPIRED':
        return <Badge className="bg-red-100 text-red-800">{t('fleet:table.expired')}</Badge>;
      case 'EXPIRING_SOON':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('fleet:table.expiringSoon')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getPolicyTypeLabel = (type: string) => {
    switch (type) {
      case 'Comprehensive':
      case 'COMPREHENSIVE':
        return t('fleet:insurance.comprehensive');
      case 'Liability':
      case 'LIABILITY':
        return t('fleet:insurance.liability');
      case 'Collision':
      case 'COLLISION':
        return t('fleet:insurance.collision');
      case 'Third Party':
      case 'THIRD_PARTY':
        return t('fleet:insurance.thirdParty');
      case 'Fire & Theft':
      case 'FIRE_THEFT':
        return t('fleet:insurance.fireTheft');
      default:
        return type;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleDelete = async (id: string) => {
    if (confirm(t('fleet:messages.confirmDeletePolicy'))) {
      try {
        // Mock delete operation
        setInsurancePolicies(policies => policies.filter(policy => policy.id !== id));
        toast({
          title: t('common:success'),
          description: t('fleet:messages.policyDeletedSuccess'),
        });

        if (onInsuranceUpdated) {
          onInsuranceUpdated();
        }
      } catch (error) {
        console.error('Error deleting insurance policy:', error);
        toast({
          title: t('common:error'),
          description: t('fleet:messages.failedToDeletePolicy'),
          variant: 'destructive',
        });
      }
    }
  };

  const expiringPolicies = insurancePolicies.filter(p => p.status === 'EXPIRING_SOON' || p.status === 'EXPIRED');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {t('fleet:insurance.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('fleet:insurance.description', 'Manage vehicle insurance policies and renewals')}
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          {t('fleet:insurance.addPolicy')}
        </Button>
      </div>

      {/* Alerts for expiring policies */}
      {expiringPolicies.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900">{t('fleet:insurance.attentionRequired')}</h4>
                <p className="text-sm text-yellow-700">
                  {expiringPolicies.length} {t('fleet:insurance.policies')} {expiringPolicies.length === 1 ? t('fleet:insurance.policy') : t('fleet:insurance.policies')}
                  {expiringPolicies.filter(p => p.status === 'EXPIRED').length > 0 ? ` ${t('fleet:insurance.expiredOr')}` : ''} {t('fleet:insurance.expiringSoon')}.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="text-center py-10">{t('fleet:insurance.loading', 'Loading insurance policies...')}</div>
      ) : insurancePolicies.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('fleet:insurance.noPoliciesFound')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('fleet:insurance.noPoliciesDescription', 'No insurance policies found. Add your first insurance policy to get started.')}
            </p>
            <Button onClick={() => setShowAddForm(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('fleet:insurance.addFirstPolicy')}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:insurance.policies')} ({insurancePolicies.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.vehicle')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.policyDetails')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.coveragePeriod')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.premium')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.status')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {insurancePolicies.map((policy) => (
                    <tr key={policy.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {policy.vehicle?.make} {policy.vehicle?.model}
                            </div>
                            <div className="text-sm text-gray-500">
                              {t('fleet:table.plate')}: {policy.vehicle?.plateNumber}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{policy.policyNumber}</div>
                        <div className="text-sm text-gray-500">{policy.provider}</div>
                        <div className="text-sm text-gray-500">{getPolicyTypeLabel(policy.type)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{t('fleet:table.start')}: {formatDate(policy.startDate)}</div>
                        <div className="text-sm text-gray-500">{t('fleet:table.end')}: {formatDate(policy.endDate)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{formatCurrency(policy.premium)}</div>
                        <div className="text-sm text-gray-500">{t('fleet:table.annual')}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(policy.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => router.push(`/fleet?tab=insurance&policyId=${policy.id}`)}
                        >
                          {t('fleet:table.view')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => {
                            setEditingPolicyId(policy.id);
                            setShowEditForm(true);
                          }}
                        >
                          {t('fleet:table.edit')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(policy.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          {t('fleet:table.delete')}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Insurance Policy Form Dialog */}
      {showAddForm && (
        <InsurancePolicyForm
          onClose={() => setShowAddForm(false)}
          onPolicyAdded={() => {
            setShowAddForm(false);
            fetchInsurancePolicies();
            onInsuranceUpdated?.();
          }}
          showAsDialog={true}
        />
      )}

      {/* Edit Insurance Policy Form Dialog */}
      {showEditForm && editingPolicyId && (
        <InsurancePolicyForm
          policyId={editingPolicyId}
          onClose={() => {
            setShowEditForm(false);
            setEditingPolicyId(null);
          }}
          onPolicyUpdated={() => {
            setShowEditForm(false);
            setEditingPolicyId(null);
            fetchInsurancePolicies();
            onInsuranceUpdated?.();
          }}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
