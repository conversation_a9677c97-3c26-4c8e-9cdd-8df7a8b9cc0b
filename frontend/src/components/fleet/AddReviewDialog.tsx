import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { VehicleReview, ReviewType, ReviewStatus } from '@/types/review';

interface AddReviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => void;
  vehicleId: string;
  editingReview?: VehicleReview | null;
}

export const AddReviewDialog: React.FC<AddReviewDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  vehicleId,
  editingReview
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    reviewType: 'ANNUAL_INSPECTION' as ReviewType,
    reviewBy: '',
    scheduledDate: '',
    completedDate: '',
    location: '',
    status: 'SCHEDULED' as ReviewStatus,
    findings: '',
    recommendations: '',
    nextReviewDate: ''
  });

  useEffect(() => {
    if (editingReview) {
      setFormData({
        reviewType: editingReview.reviewType || 'ANNUAL_INSPECTION',
        reviewBy: editingReview.reviewBy || '',
        scheduledDate: editingReview.scheduledDate ? editingReview.scheduledDate.split('T')[0] : '',
        completedDate: editingReview.completedDate ? editingReview.completedDate.split('T')[0] : '',
        location: editingReview.location || '',
        status: editingReview.status || 'SCHEDULED',
        findings: editingReview.findings || '',
        recommendations: editingReview.recommendations || '',
        nextReviewDate: editingReview.nextReviewDate ? editingReview.nextReviewDate.split('T')[0] : ''
      });
    } else {
      // Reset form for new review
      const today = new Date();
      const nextYear = new Date();
      nextYear.setFullYear(today.getFullYear() + 1);
      
      setFormData({
        reviewType: 'ANNUAL_INSPECTION',
        reviewBy: '',
        scheduledDate: today.toISOString().split('T')[0],
        completedDate: '',
        location: '',
        status: 'SCHEDULED',
        findings: '',
        recommendations: '',
        nextReviewDate: nextYear.toISOString().split('T')[0]
      });
    }
  }, [editingReview, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        reviewType: formData.reviewType,
        reviewBy: formData.reviewBy || undefined,
        scheduledDate: formData.scheduledDate,
        completedDate: formData.completedDate || undefined,
        location: formData.location || undefined,
        status: formData.status,
        findings: formData.findings || undefined,
        recommendations: formData.recommendations || undefined,
        nextReviewDate: formData.nextReviewDate || undefined,
        vehicleId: vehicleId
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingReview ? t('fleet:reviews.editReview') : t('fleet:reviews.scheduleReview')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="reviewType">{t('fleet:reviews.reviewType')}</Label>
              <Select value={formData.reviewType} onValueChange={(value) => handleInputChange('reviewType', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ANNUAL_INSPECTION">{t('fleet:reviews.annualSafetyInspection')}</SelectItem>
                  <SelectItem value="TECHNICAL_INSPECTION">{t('fleet:reviews.quarterlyMaintenanceReview')}</SelectItem>
                  <SelectItem value="SAFETY_CHECK">{t('fleet:reviews.dotInspection')}</SelectItem>
                  <SelectItem value="EMISSIONS_TEST">{t('fleet:reviews.emissionsTest')}</SelectItem>
                  <SelectItem value="QUALITY_CONTROL">{t('fleet:reviews.preTripInspection')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">{t('fleet:reviews.result')}</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SCHEDULED">{t('fleet:reviews.scheduled')}</SelectItem>
                  <SelectItem value="IN_PROGRESS">{t('fleet:service.inprogress')}</SelectItem>
                  <SelectItem value="COMPLETED">{t('fleet:reviews.completed')}</SelectItem>
                  <SelectItem value="FAILED">{t('fleet:reviews.failed')}</SelectItem>
                  <SelectItem value="CANCELLED">{t('fleet:service.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="reviewBy">{t('fleet:reviews.inspector')}</Label>
              <Input
                id="reviewBy"
                value={formData.reviewBy}
                onChange={(e) => handleInputChange('reviewBy', e.target.value)}
                placeholder={t('fleet:reviews.inspector')}
              />
            </div>

            <div>
              <Label htmlFor="location">{t('fleet:reviews.location')}</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder={t('fleet:reviews.locationPlaceholder')}
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="scheduledDate">{t('fleet:reviews.scheduledDate')}</Label>
              <Input
                id="scheduledDate"
                type="date"
                value={formData.scheduledDate}
                onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="completedDate">{t('fleet:reviews.completedDate')}</Label>
              <Input
                id="completedDate"
                type="date"
                value={formData.completedDate}
                onChange={(e) => handleInputChange('completedDate', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="nextReviewDate">{t('fleet:reviews.nextReviewDate')}</Label>
              <Input
                id="nextReviewDate"
                type="date"
                value={formData.nextReviewDate}
                onChange={(e) => handleInputChange('nextReviewDate', e.target.value)}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="findings">{t('fleet:reviews.findings')}</Label>
            <Textarea
              id="findings"
              value={formData.findings}
              onChange={(e) => handleInputChange('findings', e.target.value)}
              placeholder={t('fleet:reviews.findingsPlaceholder')}
            />
          </div>

          <div>
            <Label htmlFor="recommendations">{t('fleet:reviews.recommendations')}</Label>
            <Textarea
              id="recommendations"
              value={formData.recommendations}
              onChange={(e) => handleInputChange('recommendations', e.target.value)}
              placeholder={t('fleet:reviews.recommendationsPlaceholder')}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              {t('common:cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                editingReview ? t('fleet:reviews.updating') : t('fleet:reviews.scheduling')
              ) : (
                editingReview ? t('fleet:reviews.updateReview') : t('fleet:reviews.scheduleReview')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
