import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { MaintenanceService } from '@/lib/api/maintenance-service';
import { MaintenanceLog } from '@/types/maintenance';
import { formatDate, formatCurrency } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { ServiceRecordForm } from './ServiceRecordForm';
import { Wrench, Plus } from 'lucide-react';

interface FleetServiceContentProps {
  onServiceUpdated?: () => void;
  onAddRecord?: () => void;
}

export const FleetServiceContent: React.FC<FleetServiceContentProps> = ({
  onServiceUpdated,
  onAddRecord
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [serviceRecords, setServiceRecords] = useState<MaintenanceLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingRecordId, setEditingRecordId] = useState<string | null>(null);

  useEffect(() => {
    fetchServiceRecords();
  }, []);

  const fetchServiceRecords = async () => {
    try {
      setLoading(true);
      const data = await MaintenanceService.getMaintenanceLogs();
      setServiceRecords(data);
    } catch (error) {
      console.error('Error fetching service records:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:service.loadError', 'Failed to load service records. Please try again later.'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'SCHEDULED':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return t('common:status.completed');
      case 'IN_PROGRESS':
        return t('fleet:dashboard.inProgress');
      case 'SCHEDULED':
        return t('fleet:dashboard.scheduled');
      case 'CANCELLED':
        return t('common:status.cancelled');
      default:
        return status.replace(/_/g, ' ');
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm(t('fleet:service.confirmDelete', 'Are you sure you want to delete this service record?'))) {
      try {
        await MaintenanceService.deleteMaintenanceLog(id);
        // Refresh the list
        setServiceRecords(records => records.filter(record => record.id !== id));
        toast({
          title: t('common:success'),
          description: t('fleet:service.deleteSuccess', 'Service record deleted successfully'),
        });
        
        if (onServiceUpdated) {
          onServiceUpdated();
        }
      } catch (error) {
        console.error('Error deleting service record:', error);
        toast({
          title: t('common:error'),
          description: t('fleet:service.deleteError', 'Failed to delete service record'),
          variant: 'destructive',
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {t('fleet:service.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('fleet:service.description', 'Track maintenance and service history for all fleet vehicles')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/fleet/maintenance-dashboard')}
          >
            {t('navigation:dashboard.title')}
          </Button>
          <Button
            onClick={() => {
              console.log('Add Service Record button clicked');
              setShowAddForm(true);
            }}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            {t('fleet:service.addService')}
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-4">
              <Skeleton className="h-6 w-1/4 mb-2" />
              <Skeleton className="h-4 w-3/4 mb-1" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      ) : serviceRecords.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('fleet:service.noRecordsFound')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('fleet:service.noRecordsDescription', 'No service records found. Add your first service record to get started.')}
            </p>
            <Button
              onClick={() => {
                console.log('Add First Service Record button clicked');
                setShowAddForm(true);
              }}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              {t('fleet:service.addFirstRecord', 'Add First Service Record')}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:service.title')} ({serviceRecords.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:service.vehicle', 'Vehicle')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:service.serviceDetails', 'Service Details')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:service.dates', 'Dates')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('common:status.title', 'Status')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('common:actions.title')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {serviceRecords.map((record) => (
                    <tr key={record.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {record.vehicle?.make} {record.vehicle?.model}
                            </div>
                            <div className="text-sm text-gray-500">
                              {t('fleet:service.plate', 'Plate')}: {record.vehicle?.plateNumber}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{record.description}</div>
                        <div className="text-sm text-gray-500">{record.notes || t('fleet:service.noNotes', 'No additional notes')}</div>
                        {record.cost && <div className="text-sm font-medium text-gray-900 mt-1">{formatCurrency(record.cost)}</div>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{t('fleet:service.serviceDate', 'Service')}: {formatDate(record.date)}</div>
                        {record.nextMaintenanceDate && (
                          <div className="text-sm text-gray-500">{t('fleet:service.nextService', 'Next')}: {formatDate(record.nextMaintenanceDate)}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(record.status)}`}>
                          {getStatusLabel(record.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => {
                            console.log('View button clicked for record:', record.id);
                            router.push(`/fleet?tab=service&serviceId=${record.id}`);
                          }}
                        >
                          {t('common:actions.view')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => {
                            console.log('Edit button clicked for record:', record.id);
                            setEditingRecordId(record.id);
                            setShowEditForm(true);
                          }}
                        >
                          {t('common:actions.edit')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            console.log('Delete button clicked for record:', record.id);
                            handleDelete(record.id);
                          }}
                          className="text-red-600 hover:text-red-800"
                        >
                          {t('common:actions.delete')}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Service Record Form Dialog */}
      {showAddForm && (
        <ServiceRecordForm
          onClose={() => setShowAddForm(false)}
          onRecordAdded={() => {
            setShowAddForm(false);
            fetchServiceRecords();
            onServiceUpdated?.();
          }}
          showAsDialog={true}
        />
      )}

      {/* Edit Service Record Form Dialog */}
      {showEditForm && editingRecordId && (
        <ServiceRecordForm
          recordId={editingRecordId}
          onClose={() => {
            setShowEditForm(false);
            setEditingRecordId(null);
          }}
          onRecordUpdated={() => {
            setShowEditForm(false);
            setEditingRecordId(null);
            fetchServiceRecords();
            onServiceUpdated?.();
          }}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
