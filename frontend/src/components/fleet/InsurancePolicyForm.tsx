import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { FleetService } from '@/lib/api/fleet-service';
import { Vehicle } from '@/types/vehicle';
import { X, Save } from 'lucide-react';

interface InsurancePolicy {
  id: string;
  vehicleId: string;
  policyNumber: string;
  provider: string;
  type: string;
  startDate: string;
  endDate: string;
  premium: number;
  status: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON';
  coverage: string;
}

interface InsurancePolicyFormProps {
  policyId?: string;
  onClose: () => void;
  onPolicyAdded?: () => void;
  onPolicyUpdated?: () => void;
  showAsDialog?: boolean;
}

export const InsurancePolicyForm: React.FC<InsurancePolicyFormProps> = ({
  policyId,
  onClose,
  onPolicyAdded,
  onPolicyUpdated,
  showAsDialog = false
}) => {
  const { t } = useTranslation(['fleet', 'common', 'forms']);
  const { toast } = useToast();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    vehicleId: '',
    policyNumber: '',
    provider: '',
    type: 'Comprehensive',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    premium: '',
    status: 'ACTIVE' as const,
    coverage: ''
  });

  useEffect(() => {
    fetchVehicles();
    if (policyId) {
      fetchInsurancePolicy();
    }
  }, [policyId]);

  const fetchVehicles = async () => {
    try {
      const vehiclesData = await FleetService.getVehicles();
      setVehicles(vehiclesData);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:vehicles.loadError'),
        variant: 'destructive',
      });
    }
  };

  const fetchInsurancePolicy = async () => {
    if (!policyId) return;
    
    try {
      setLoading(true);
      // Mock data for editing - in real app this would fetch from API
      const mockPolicy: InsurancePolicy = {
        id: policyId,
        vehicleId: 'v1',
        policyNumber: 'POL-2024-001',
        provider: 'Fleet Insurance Co.',
        type: 'Comprehensive',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        premium: 2500,
        status: 'ACTIVE',
        coverage: 'Full coverage including liability, collision, and comprehensive'
      };
      
      setFormData({
        vehicleId: mockPolicy.vehicleId,
        policyNumber: mockPolicy.policyNumber,
        provider: mockPolicy.provider,
        type: mockPolicy.type,
        startDate: mockPolicy.startDate,
        endDate: mockPolicy.endDate,
        premium: mockPolicy.premium.toString(),
        status: mockPolicy.status,
        coverage: mockPolicy.coverage
      });
    } catch (error) {
      console.error('Error fetching insurance policy:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorLoadingPolicies'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicleId || !formData.policyNumber || !formData.provider) {
      toast({
        title: t('common:error'),
        description: t('forms:validation.required'),
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    
    try {
      const policyData = {
        vehicleId: formData.vehicleId,
        policyNumber: formData.policyNumber,
        provider: formData.provider,
        type: formData.type,
        startDate: formData.startDate,
        endDate: formData.endDate,
        premium: formData.premium ? parseFloat(formData.premium) : 0,
        status: formData.status,
        coverage: formData.coverage
      };

      // Mock API calls - in real app these would be actual API calls
      if (policyId) {
        console.log('Updating insurance policy:', policyData);
        toast({
          title: t('common:success'),
          description: t('fleet:messages.policyUpdatedSuccess'),
        });
        onPolicyUpdated?.();
      } else {
        console.log('Creating insurance policy:', policyData);
        toast({
          title: t('common:success'),
          description: t('fleet:messages.policyCreatedSuccess'),
        });
        onPolicyAdded?.();
      }

      onClose();
    } catch (error) {
      console.error('Error saving insurance policy:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToSavePolicy'),
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const formContent = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="vehicleId">{t('fleet:vehicles.title')} *</Label>
          <Select value={formData.vehicleId} onValueChange={(value) => handleInputChange('vehicleId', value)}>
            <SelectTrigger>
              <SelectValue placeholder={t('fleet:insurance.selectVehicle')} />
            </SelectTrigger>
            <SelectContent>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.id} value={vehicle.id}>
                  {vehicle.make} {vehicle.model} ({vehicle.plateNumber})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="policyNumber">{t('fleet:insurance.policyNumber')} *</Label>
          <Input
            id="policyNumber"
            value={formData.policyNumber}
            onChange={(e) => handleInputChange('policyNumber', e.target.value)}
            placeholder={t('fleet:insurance.policyNumberPlaceholder')}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="provider">{t('fleet:insurance.provider')} *</Label>
          <Input
            id="provider"
            value={formData.provider}
            onChange={(e) => handleInputChange('provider', e.target.value)}
            placeholder={t('fleet:insurance.providerPlaceholder')}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="type">{t('fleet:insurance.policyType')}</Label>
          <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Comprehensive">{t('fleet:insurance.comprehensive')}</SelectItem>
              <SelectItem value="Liability">{t('fleet:insurance.liability')}</SelectItem>
              <SelectItem value="Collision">{t('fleet:insurance.collision')}</SelectItem>
              <SelectItem value="Third Party">{t('fleet:insurance.thirdParty')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startDate">{t('fleet:insurance.startDate')}</Label>
          <Input
            id="startDate"
            type="date"
            value={formData.startDate}
            onChange={(e) => handleInputChange('startDate', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="endDate">{t('fleet:insurance.endDate')}</Label>
          <Input
            id="endDate"
            type="date"
            value={formData.endDate}
            onChange={(e) => handleInputChange('endDate', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="premium">{t('fleet:insurance.annualPremium')}</Label>
          <Input
            id="premium"
            type="number"
            step="0.01"
            value={formData.premium}
            onChange={(e) => handleInputChange('premium', e.target.value)}
            placeholder={t('fleet:insurance.premiumPlaceholder')}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">{t('common:status.title')}</Label>
        <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ACTIVE">{t('fleet:table.active')}</SelectItem>
            <SelectItem value="EXPIRED">{t('fleet:table.expired')}</SelectItem>
            <SelectItem value="EXPIRING_SOON">{t('fleet:table.expiringSoon')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="coverage">{t('fleet:insurance.coverageDetails')}</Label>
        <Textarea
          id="coverage"
          value={formData.coverage}
          onChange={(e) => handleInputChange('coverage', e.target.value)}
          placeholder={t('fleet:insurance.coveragePlaceholder')}
          rows={3}
        />
      </div>

      <div className="flex gap-2 pt-4">
        <Button type="submit" disabled={submitting} className="gap-2">
          {submitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {policyId ? t('fleet:insurance.updating') : t('fleet:insurance.creating')}
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              {policyId ? t('fleet:insurance.updatePolicy') : t('fleet:insurance.createPolicy')}
            </>
          )}
        </Button>
        <Button type="button" variant="outline" onClick={onClose}>
          {t('common:actions.cancel')}
        </Button>
      </div>
    </form>
  );

  if (showAsDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {policyId ? t('fleet:insurance.editInsurance') : t('fleet:insurance.addInsurance')}
              </h3>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            {loading ? (
              <div className="text-center py-8">{t('common:actions.loading')}</div>
            ) : (
              formContent
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{policyId ? t('fleet:insurance.editInsurance') : t('fleet:insurance.addInsurance')}</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">{t('common:actions.loading')}</div>
        ) : (
          formContent
        )}
      </CardContent>
    </Card>
  );
};
