import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { DriverService } from '@/lib/api/driver-service';

interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
}

interface AddAssignmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any) => void;
  vehicleId: string;
  editingAssignment?: any;
}

export const AddAssignmentDialog: React.FC<AddAssignmentDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  vehicleId,
  editingAssignment
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const [loading, setLoading] = useState(false);
  const [loadingDrivers, setLoadingDrivers] = useState(false);
  const [availableDrivers, setAvailableDrivers] = useState<Driver[]>([]);
  const [formData, setFormData] = useState({
    driverId: '',
    startDate: '',
    endDate: '',
    type: 'REGULAR',
    priority: 'NORMAL',
    notes: ''
  });

  useEffect(() => {
    if (open) {
      fetchAvailableDrivers();
    }
  }, [open]);

  useEffect(() => {
    if (editingAssignment) {
      setFormData({
        driverId: editingAssignment.driverId || '',
        startDate: editingAssignment.startDate ? editingAssignment.startDate.split('T')[0] : '',
        endDate: editingAssignment.endDate ? editingAssignment.endDate.split('T')[0] : '',
        type: editingAssignment.type || 'REGULAR',
        priority: editingAssignment.priority || 'NORMAL',
        notes: editingAssignment.notes || ''
      });
    } else {
      // Reset form for new assignment
      const today = new Date();
      setFormData({
        driverId: '',
        startDate: today.toISOString().split('T')[0],
        endDate: '',
        type: 'REGULAR',
        priority: 'NORMAL',
        notes: ''
      });
    }
  }, [editingAssignment, open]);

  const fetchAvailableDrivers = async () => {
    try {
      setLoadingDrivers(true);
      const drivers = await DriverService.getDrivers();
      // Filter for available drivers (you might want to adjust this logic)
      const available = drivers.filter(driver => 
        driver.status === 'Active' || driver.status === 'Available'
      );
      setAvailableDrivers(available);
    } catch (error) {
      console.error('Error fetching drivers:', error);
      setAvailableDrivers([]);
    } finally {
      setLoadingDrivers(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const submitData = {
        driverId: formData.driverId,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        type: formData.type,
        priority: formData.priority,
        notes: formData.notes || undefined,
        vehicleId: vehicleId
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting assignment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editingAssignment ? t('fleet:assignments.editAssignment') : t('fleet:assignments.createAssignment')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="driverId">{t('fleet:assignments.selectDriver')}</Label>
            <Select value={formData.driverId} onValueChange={(value) => handleInputChange('driverId', value)}>
              <SelectTrigger>
                <SelectValue placeholder={loadingDrivers ? t('fleet:assignments.loadingDrivers') : t('fleet:assignments.selectDriver')} />
              </SelectTrigger>
              <SelectContent>
                {availableDrivers.map((driver) => (
                  <SelectItem key={driver.id} value={driver.id}>
                    {driver.firstName} {driver.lastName} ({driver.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">{t('fleet:assignments.assignmentType')}</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="REGULAR">{t('fleet:assignments.regular')}</SelectItem>
                  <SelectItem value="TEMPORARY">{t('fleet:assignments.temporary')}</SelectItem>
                  <SelectItem value="EMERGENCY">{t('fleet:assignments.emergency')}</SelectItem>
                  <SelectItem value="MAINTENANCE">{t('fleet:assignments.maintenance')}</SelectItem>
                  <SelectItem value="TRAINING">{t('fleet:assignments.training')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="priority">{t('fleet:assignments.priority')}</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">{t('fleet:assignments.low')}</SelectItem>
                  <SelectItem value="NORMAL">{t('fleet:assignments.normal')}</SelectItem>
                  <SelectItem value="HIGH">{t('fleet:assignments.high')}</SelectItem>
                  <SelectItem value="URGENT">{t('fleet:assignments.urgent')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">{t('fleet:assignments.startDate')}</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="endDate">{t('fleet:assignments.endDate')}</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">{t('fleet:assignments.notes')}</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder={t('fleet:assignments.notesPlaceholder')}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              {t('common:cancel')}
            </Button>
            <Button type="submit" disabled={loading || !formData.driverId}>
              {loading ? (
                editingAssignment ? t('fleet:assignments.updating') : t('fleet:assignments.creating')
              ) : (
                editingAssignment ? t('fleet:assignments.updateAssignment') : t('fleet:assignments.createAssignment')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
