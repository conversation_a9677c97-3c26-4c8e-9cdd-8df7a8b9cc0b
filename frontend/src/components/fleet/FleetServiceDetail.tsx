import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { MaintenanceService } from '@/lib/api/maintenance-service';
import { formatDate, formatCurrency } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { MaintenanceLog } from '@/types/maintenance';
import { ServiceRecordForm } from './ServiceRecordForm';
import { ArrowLeft, Edit, Trash2, Wrench } from 'lucide-react';

interface FleetServiceDetailProps {
  serviceId: string;
  onBack?: () => void;
  onServiceUpdated?: () => void;
}

export const FleetServiceDetail: React.FC<FleetServiceDetailProps> = ({
  serviceId,
  onBack,
  onServiceUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const { toast } = useToast();
  const [service, setService] = useState<MaintenanceLog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  useEffect(() => {
    const fetchServiceDetails = async () => {
      if (!serviceId) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('Fetching service details for ID:', serviceId);
        const serviceData = await MaintenanceService.getMaintenanceLog(serviceId);
        console.log('Service data received:', serviceData);
        setService(serviceData);
      } catch (error) {
        console.error('Error fetching service details:', error);
        toast({
          variant: 'destructive',
          title: t('common:error'),
          description: t('fleet:service.loadError', `Failed to load service details: ${error instanceof Error ? error.message : 'Unknown error'}`),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceDetails();
  }, [serviceId, toast]);

  const handleDelete = async () => {
    try {
      await MaintenanceService.deleteMaintenanceLog(serviceId);
      toast({
        title: t('common:success'),
        description: t('fleet:service.deleteSuccess'),
      });
      onServiceUpdated?.();
      onBack?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('common:error'),
        description: t('fleet:service.deleteError'),
      });
    }
  };

  const handleEditComplete = () => {
    setShowEditForm(false);
    // Refresh the service data
    const fetchServiceDetails = async () => {
      try {
        const serviceData = await MaintenanceService.getMaintenanceLog(serviceId);
        setService(serviceData);
        onServiceUpdated?.();
      } catch (error) {
        console.error('Error refreshing service details:', error);
      }
    };
    fetchServiceDetails();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full py-10">
        <div className="text-center">
          <div>{t('fleet:service.loadingRecord')}</div>
          <div className="text-sm text-gray-500 mt-2">{t('fleet:service.serviceId')}: {serviceId}</div>
        </div>
      </div>
    );
  }

  if (!serviceId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:service.invalidRecordId')}</h2>
              <p className="mb-4">{t('fleet:service.noRecordIdProvided')}</p>
              <Button onClick={onBack}>
                {t('fleet:service.backToRecords')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:service.recordNotFound')}</h2>
              <p className="mb-4">{t('fleet:service.recordNotFoundDescription')}</p>
              <Button onClick={onBack}>
                {t('fleet:service.backToRecords')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-500';
      case 'SCHEDULED':
        return 'bg-blue-500';
      case 'IN_PROGRESS':
        return 'bg-yellow-500';
      case 'CANCELLED':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return t('common:status.completed');
      case 'SCHEDULED':
        return t('fleet:dashboard.scheduled');
      case 'IN_PROGRESS':
        return t('fleet:dashboard.inProgress');
      case 'CANCELLED':
        return t('common:status.cancelled');
      default:
        return status.replace(/_/g, ' ');
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'REPAIR':
        return t('fleet:service.repair');
      case 'PREVENTIVE':
        return t('fleet:service.preventiveMaintenance');
      case 'INSPECTION':
        return t('fleet:service.inspection');
      default:
        return type;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PREVENTIVE':
        return 'bg-blue-500';
      case 'REPAIR':
        return 'bg-red-500';
      case 'INSPECTION':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('fleet:service.backToRecords')}
          </Button>
          <div className="flex items-center gap-3">
            <Wrench className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold leading-7 text-gray-900">
                {t('fleet:service.recordDetails')}
              </h2>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-600">
                  {service.vehicle?.make} {service.vehicle?.model} ({service.vehicle?.plateNumber})
                </p>
                <Badge className={getTypeColor(service.type || 'REPAIR')}>
                  {getTypeLabel(service.type || 'REPAIR')}
                </Badge>
                <Badge className={getStatusColor(service.status || 'COMPLETED')}>
                  {getStatusLabel(service.status || 'COMPLETED')}
                </Badge>
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowEditForm(true)}
            className="gap-2"
          >
            <Edit className="h-4 w-4" />
            {t('common:actions.edit')} {t('fleet:service.record')}
          </Button>
          <Button
            variant="destructive"
            onClick={() => setConfirmDelete(true)}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {t('common:actions.delete')} {t('fleet:service.record')}
          </Button>
        </div>
      </div>

      {/* Service Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:service.serviceInformation')}</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:service.serviceDate')}</dt>
                <dd className="text-sm text-gray-900">{formatDate(service.date)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:service.category')}</dt>
                <dd className="text-sm text-gray-900">{service.category || t('fleet:service.notSpecified')}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.mileage')}</dt>
                <dd className="text-sm text-gray-900">
                  {service.mileage ? `${service.mileage.toLocaleString()} km` : t('fleet:service.notRecorded')}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:service.cost')}</dt>
                <dd className="text-sm text-gray-900">
                  {service.cost ? formatCurrency(service.cost) : t('fleet:service.notSpecified')}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:service.technician')}</dt>
                <dd className="text-sm text-gray-900">{service.technician || t('fleet:service.notSpecified')}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        {/* Vehicle Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:service.vehicleInformation')}</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.title')}</dt>
                <dd className="text-sm text-gray-900">
                  {service.vehicle?.make} {service.vehicle?.model} ({service.vehicle?.year})
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.plateNumber')}</dt>
                <dd className="text-sm text-gray-900 font-medium">{service.vehicle?.plateNumber}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:service.vehicleId')}</dt>
                <dd className="text-sm text-gray-900 font-mono text-xs">{service.vehicleId}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:service.serviceDescription')}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-900 whitespace-pre-line">
            {service.description || t('fleet:service.noDescriptionProvided')}
          </p>
          {service.notes && (
            <>
              <Separator className="my-4" />
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-2">{t('fleet:service.additionalNotes')}</h4>
                <p className="text-sm text-gray-900 whitespace-pre-line">{service.notes}</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('fleet:service.confirmDelete')}</DialogTitle>
            <DialogDescription>
              {t('fleet:service.confirmDeleteDescription')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(false)}>
              {t('common:actions.cancel')}
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              {t('common:actions.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Service Record Form Dialog */}
      {showEditForm && (
        <ServiceRecordForm
          recordId={serviceId}
          onClose={() => setShowEditForm(false)}
          onRecordUpdated={handleEditComplete}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
