import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { VehicleReviewForm } from './VehicleReviewForm';
import { ArrowLeft, Edit, Trash2, Star, Calendar, User, AlertTriangle, CheckCircle } from 'lucide-react';

interface VehicleReview {
  id: string;
  vehicleId: string;
  vehicle?: {
    make: string;
    model: string;
    plateNumber: string;
    year?: number;
  };
  reviewType: string;
  scheduledDate: string;
  completedDate?: string;
  inspector: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  score?: number;
  notes?: string;
  nextReviewDate?: string;
  issues?: Array<{
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
  }>;
}

interface FleetReviewDetailProps {
  reviewId: string;
  onBack?: () => void;
  onReviewUpdated?: () => void;
}

export const FleetReviewDetail: React.FC<FleetReviewDetailProps> = ({
  reviewId,
  onBack,
  onReviewUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const { toast } = useToast();
  const [review, setReview] = useState<VehicleReview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  useEffect(() => {
    const fetchReviewDetails = async () => {
      if (!reviewId) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('Fetching vehicle review details for ID:', reviewId);
        
        // Mock data for demonstration - in real app this would fetch from API
        const mockReview: VehicleReview = {
          id: reviewId,
          vehicleId: 'v1',
          vehicle: { 
            make: 'Volvo', 
            model: 'FH16', 
            plateNumber: 'ABC-123',
            year: 2022
          },
          reviewType: 'Annual Safety Inspection',
          scheduledDate: '2024-06-25',
          completedDate: '2024-06-25',
          inspector: 'John Smith',
          status: 'COMPLETED',
          score: 95,
          notes: 'Vehicle passed all safety checks. Minor brake pad wear noted. All lights and signals functioning properly. Tire condition is good with adequate tread depth.',
          nextReviewDate: '2025-06-25',
          issues: [
            { severity: 'LOW', description: 'Brake pads at 30% wear - monitor for next service' },
            { severity: 'MEDIUM', description: 'Minor oil leak detected - schedule maintenance' }
          ]
        };
        
        console.log('Review data received:', mockReview);
        setReview(mockReview);
      } catch (error) {
        console.error('Error fetching vehicle review details:', error);
        toast({
          variant: 'destructive',
          title: t('common:error'),
          description: t('fleet:messages.failedToLoadReview'),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchReviewDetails();
  }, [reviewId, toast]);

  const handleDelete = async () => {
    try {
      // Mock delete operation - in real app this would call API
      console.log('Deleting vehicle review:', reviewId);
      toast({
        title: t('common:success'),
        description: t('fleet:reviews.reviewDeletedSuccess'),
      });
      onReviewUpdated?.();
      onBack?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: t('common:error'),
        description: t('fleet:reviews.failedToDeleteReview'),
      });
    }
  };

  const handleEditComplete = () => {
    setShowEditForm(false);
    // Refresh the review data
    const fetchReviewDetails = async () => {
      try {
        // Mock refresh - in real app this would fetch updated data
        console.log('Refreshing review data after edit');
        onReviewUpdated?.();
      } catch (error) {
        console.error('Error refreshing review details:', error);
      }
    };
    fetchReviewDetails();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-500';
      case 'IN_PROGRESS':
        return 'bg-blue-500';
      case 'SCHEDULED':
        return 'bg-yellow-500';
      case 'FAILED':
        return 'bg-red-500';
      case 'CANCELLED':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return t('common:status.completed');
      case 'IN_PROGRESS':
        return t('common:status.inProgress');
      case 'SCHEDULED':
        return t('common:status.scheduled');
      case 'FAILED':
        return t('fleet:reviews.failed');
      case 'CANCELLED':
        return t('common:status.cancelled');
      default:
        return status;
    }
  };

  const getSeverityLabel = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return t('fleet:reviews.critical');
      case 'HIGH':
        return t('fleet:reviews.high');
      case 'MEDIUM':
        return t('fleet:reviews.medium');
      case 'LOW':
        return t('fleet:reviews.low');
      default:
        return severity;
    }
  };

  const getReviewTypeLabel = (reviewType: string) => {
    switch (reviewType) {
      case 'Annual Safety Inspection':
        return t('fleet:reviews.annualSafetyInspection');
      case 'Quarterly Maintenance Review':
        return t('fleet:reviews.quarterlyMaintenanceReview');
      case 'DOT Inspection':
        return t('fleet:reviews.dotInspection');
      case 'Pre-Trip Inspection':
        return t('fleet:reviews.preTripInspection');
      case 'Post-Trip Inspection':
        return t('fleet:reviews.postTripInspection');
      default:
        return reviewType;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-500';
      case 'HIGH':
        return 'bg-orange-500';
      case 'MEDIUM':
        return 'bg-yellow-500';
      case 'LOW':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full py-10">
        <div className="text-center">
          <div>{t('fleet:reviews.loadingReview')}</div>
          <div className="text-sm text-gray-500 mt-2">{t('fleet:reviews.reviewId')}: {reviewId}</div>
        </div>
      </div>
    );
  }

  if (!reviewId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:reviews.invalidReviewId')}</h2>
              <p className="mb-4">{t('fleet:reviews.noReviewIdProvided')}</p>
              <Button onClick={onBack}>
                {t('fleet:reviews.backToReviews')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('fleet:reviews.reviewNotFound')}</h2>
              <p className="mb-4">{t('fleet:reviews.reviewNotFoundDescription')}</p>
              <Button onClick={onBack}>
                {t('fleet:reviews.backToReviews')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('fleet:reviews.backToReviews')}
          </Button>
          <div className="flex items-center gap-3">
            <Star className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold leading-7 text-gray-900">
                {t('fleet:reviews.reviewDetails')}
              </h2>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-600">
                  {review.vehicle?.make} {review.vehicle?.model} ({review.vehicle?.plateNumber})
                </p>
                <Badge className={getStatusColor(review.status)}>
                  {getStatusLabel(review.status)}
                </Badge>
                {review.score && (
                  <Badge variant="outline" className={getScoreColor(review.score)}>
                    {t('fleet:reviews.score')}: {review.score}%
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowEditForm(true)}
            className="gap-2"
          >
            <Edit className="h-4 w-4" />
            {t('fleet:reviews.editReview')}
          </Button>
          <Button
            variant="destructive"
            onClick={() => setConfirmDelete(true)}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {t('fleet:reviews.deleteReview')}
          </Button>
        </div>
      </div>

      {/* Review Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('fleet:reviews.reviewInformation')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.reviewType')}</dt>
                <dd className="text-sm text-gray-900">{getReviewTypeLabel(review.reviewType)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.inspector')}</dt>
                <dd className="text-sm text-gray-900 flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {review.inspector}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('common:status.title')}</dt>
                <dd className="text-sm text-gray-900">
                  <Badge className={getStatusColor(review.status)}>
                    {getStatusLabel(review.status)}
                  </Badge>
                </dd>
              </div>
              {review.score && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.score')}</dt>
                  <dd className={`text-sm font-semibold ${getScoreColor(review.score)}`}>
                    {review.score}%
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        {/* Dates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('fleet:reviews.schedule')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.scheduledDate')}</dt>
                <dd className="text-sm text-gray-900">{formatDate(review.scheduledDate)}</dd>
              </div>
              {review.completedDate && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.completedDate')}</dt>
                  <dd className="text-sm text-gray-900">{formatDate(review.completedDate)}</dd>
                </div>
              )}
              {review.nextReviewDate && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.nextReviewDate')}</dt>
                  <dd className="text-sm text-gray-900">{formatDate(review.nextReviewDate)}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:reviews.vehicleInformation')}</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.title')}</dt>
              <dd className="text-sm text-gray-900">
                {review.vehicle?.make} {review.vehicle?.model} ({review.vehicle?.year})
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.plateNumber')}</dt>
              <dd className="text-sm text-gray-900 font-medium">{review.vehicle?.plateNumber}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">{t('fleet:reviews.vehicleId')}</dt>
              <dd className="text-sm text-gray-900 font-mono text-xs">{review.vehicleId}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Issues Found */}
      {review.issues && review.issues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              {t('fleet:reviews.issuesFound')} ({review.issues.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {review.issues.map((issue, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
                  <Badge className={getSeverityColor(issue.severity)}>
                    {getSeverityLabel(issue.severity)}
                  </Badge>
                  <p className="text-sm text-gray-900 flex-1">{issue.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes */}
      {review.notes && (
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:reviews.reviewNotes')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-900 whitespace-pre-line">
              {review.notes}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('fleet:reviews.confirmDelete')}</DialogTitle>
            <DialogDescription>
              {t('fleet:reviews.confirmDeleteDescription')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(false)}>
              {t('common:actions.cancel')}
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              {t('common:actions.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Vehicle Review Form Dialog */}
      {showEditForm && (
        <VehicleReviewForm
          reviewId={reviewId}
          onClose={() => setShowEditForm(false)}
          onReviewUpdated={handleEditComplete}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
