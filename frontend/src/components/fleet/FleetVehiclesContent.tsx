import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Vehicle, VehicleType, VehicleStatus, TruckTrailerAssignment, CreateVehicleRequest, UpdateVehicleRequest } from '@/types/vehicle';
import { FleetService } from '@/lib/api/fleet-service';
import { VehicleAssignmentService } from '@/lib/api/vehicle-assignment-service';
import { TruckTrailerAssignmentService } from '@/lib/api/truck-trailer-assignment-service';
import { TruckTrailerAssignmentDialog } from '@/components/vehicles/truck-trailer-assignment-dialog';
import { TruckTrailerAssignmentsTable } from '@/components/vehicles/truck-trailer-assignments-table';
import { AddVehicleDialog } from '@/components/vehicles/add-vehicle-dialog';
import { Truck, TruckIcon, Link as LinkIcon, Plus, Car, Edit, Trash2 } from 'lucide-react';

interface FleetVehiclesContentProps {
  onVehicleUpdated?: () => void;
}

export const FleetVehiclesContent: React.FC<FleetVehiclesContentProps> = ({
  onVehicleUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [trucks, setTrucks] = useState<Vehicle[]>([]);
  const [trailers, setTrailers] = useState<Vehicle[]>([]);
  const [truckTrailerAssignments, setTruckTrailerAssignments] = useState<TruckTrailerAssignment[]>([]);
  const [vehicleAssignments, setVehicleAssignments] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [assignmentsLoading, setAssignmentsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [isFetching, setIsFetching] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchAllData();
  }, []); // Remove toast dependency to prevent infinite loops

  const fetchAllData = async () => {
    if (isFetching) {
      console.log('Already fetching data, skipping...');
      return;
    }

    setIsFetching(true);
    setLoading(true);
    try {
      const [allVehicles, trucksData, trailersData] = await Promise.all([
        FleetService.getVehicles(),
        FleetService.getTrucks(),
        FleetService.getTrailers(),
      ]);

      setVehicles(allVehicles);
      setTrucks(trucksData);
      setTrailers(trailersData);

      // Fetch vehicle assignments more efficiently
      try {
        const assignmentCounts: Record<string, number> = {};
        // Initialize all vehicles with 0 assignments to prevent loading states
        allVehicles.forEach(vehicle => {
          assignmentCounts[vehicle.id] = 0;
        });
        setVehicleAssignments(assignmentCounts);

        // Fetch assignments in smaller batches to prevent overwhelming the server
        const batchSize = 5;
        for (let i = 0; i < allVehicles.length; i += batchSize) {
          const batch = allVehicles.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (vehicle) => {
              try {
                const assignments = await VehicleAssignmentService.getVehicleAssignments(vehicle.id);
                assignmentCounts[vehicle.id] = assignments.filter(
                  assignment => assignment.status === 'ACTIVE'
                ).length;
              } catch (error) {
                console.error(`Error fetching assignments for vehicle ${vehicle.id}:`, error);
                assignmentCounts[vehicle.id] = 0;
              }
            })
          );
          // Update state after each batch
          setVehicleAssignments({...assignmentCounts});
        }
      } catch (error) {
        console.error('Error fetching vehicle assignments:', error);
      }

      // Fetch truck-trailer assignments
      await fetchTruckTrailerAssignments();

      // Don't call onVehicleUpdated here to prevent infinite loops
      // onVehicleUpdated should only be called when data is actually updated, not on initial load
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:vehicles.loadError', 'Failed to load vehicles. Please try again.'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const fetchTruckTrailerAssignments = async () => {
    setAssignmentsLoading(true);
    try {
      const assignments = await TruckTrailerAssignmentService.getAll();
      setTruckTrailerAssignments(assignments);
    } catch (error) {
      console.error('Error fetching truck-trailer assignments:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorLoadingAssignments'),
        variant: 'destructive',
      });
    } finally {
      setAssignmentsLoading(false);
    }
  };

  const handleCreateAssignment = async (data: any) => {
    try {
      await TruckTrailerAssignmentService.create(data);
      await fetchTruckTrailerAssignments();
      // Refresh vehicle assignment counts
      await fetchAllData();
      toast({
        title: t('common:success'),
        description: t('fleet:messages.assignmentCreatedSuccess'),
      });

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorCreatingAssignment'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleUpdateAssignment = async (id: string, data: any) => {
    try {
      await TruckTrailerAssignmentService.update(id, data);
      await fetchTruckTrailerAssignments();
      // Refresh vehicle assignment counts
      await fetchAllData();
      toast({
        title: t('common:success'),
        description: t('fleet:messages.assignmentUpdatedSuccess'),
      });

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorUpdatingAssignment'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleCompleteAssignment = async (id: string) => {
    try {
      await TruckTrailerAssignmentService.complete(id);
      await fetchTruckTrailerAssignments();
      // Refresh vehicle assignment counts
      await fetchAllData();

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error completing assignment:', error);
      throw error;
    }
  };

  const handleCancelAssignment = async (id: string) => {
    try {
      await TruckTrailerAssignmentService.cancel(id);
      await fetchTruckTrailerAssignments();
      // Refresh vehicle assignment counts
      await fetchAllData();

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error cancelling assignment:', error);
      throw error;
    }
  };

  const handleDeleteAssignment = async (id: string) => {
    try {
      await TruckTrailerAssignmentService.delete(id);
      await fetchTruckTrailerAssignments();
      // Refresh vehicle assignment counts
      await fetchAllData();

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error deleting assignment:', error);
      throw error;
    }
  };

  const handleCreateVehicle = async (data: any) => {
    try {
      const vehicleData: CreateVehicleRequest = {
        plateNumber: data.plateNumber,
        make: data.make,
        model: data.model,
        year: data.year,
        vehicleType: data.vehicleType,
        // Include all optional fields from the enhanced form
        ...(data.vin && { vin: data.vin }),
        ...(data.color && { color: data.color }),
        ...(data.mileage && { mileage: data.mileage }),
        ...(data.fuelType && { fuelType: data.fuelType }),
        ...(data.purchaseDate && { purchaseDate: data.purchaseDate }),
        // Truck-specific fields
        ...(data.engineType && { engineType: data.engineType }),
        ...(data.transmission && { transmission: data.transmission }),
        ...(data.fuelCapacity && { fuelCapacity: data.fuelCapacity }),
        ...(data.axleConfiguration && { axleConfiguration: data.axleConfiguration }),
        ...(data.cabConfiguration && { cabConfiguration: data.cabConfiguration }),
        // Trailer-specific fields
        ...(data.trailerType && { trailerType: data.trailerType }),
        ...(data.cargoCapacity && { cargoCapacity: data.cargoCapacity }),
        ...(data.maxWeight && { maxWeight: data.maxWeight }),
        ...(data.length && { length: data.length }),
        ...(data.width && { width: data.width }),
        ...(data.height && { height: data.height }),
        ...(data.hasRefrigeration !== undefined && { hasRefrigeration: data.hasRefrigeration }),
        // Note: status is NOT included for creation - backend sets it automatically
      };

      await FleetService.createVehicle(vehicleData);
      await fetchAllData();
      toast({
        title: t('common:success'),
        description: t('fleet:messages.vehicleCreatedSuccess'),
      });

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error creating vehicle:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorCreatingVehicle'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleUpdateVehicle = async (data: any) => {
    if (!editingVehicle) return;

    try {
      const vehicleData: UpdateVehicleRequest = {
        plateNumber: data.plateNumber,
        make: data.make,
        model: data.model,
        year: data.year,
        vehicleType: data.vehicleType,
        status: data.status, // Status CAN be updated for existing vehicles
        // Include all optional fields from the enhanced form
        ...(data.vin && { vin: data.vin }),
        ...(data.color && { color: data.color }),
        ...(data.mileage && { mileage: data.mileage }),
        ...(data.fuelType && { fuelType: data.fuelType }),
        ...(data.purchaseDate && { purchaseDate: data.purchaseDate }),
        // Truck-specific fields
        ...(data.engineType && { engineType: data.engineType }),
        ...(data.transmission && { transmission: data.transmission }),
        ...(data.fuelCapacity && { fuelCapacity: data.fuelCapacity }),
        ...(data.axleConfiguration && { axleConfiguration: data.axleConfiguration }),
        ...(data.cabConfiguration && { cabConfiguration: data.cabConfiguration }),
        // Trailer-specific fields
        ...(data.trailerType && { trailerType: data.trailerType }),
        ...(data.cargoCapacity && { cargoCapacity: data.cargoCapacity }),
        ...(data.maxWeight && { maxWeight: data.maxWeight }),
        ...(data.length && { length: data.length }),
        ...(data.width && { width: data.width }),
        ...(data.height && { height: data.height }),
        ...(data.hasRefrigeration !== undefined && { hasRefrigeration: data.hasRefrigeration }),
      };

      await FleetService.updateVehicle(editingVehicle.id, vehicleData);
      await fetchAllData();
      setEditingVehicle(null);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.vehicleUpdatedSuccess'),
      });

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error updating vehicle:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToUpdateVehicle'),
        variant: 'destructive',
      });
      throw error;
    }
  };

  const handleDeleteVehicle = async (vehicle: Vehicle) => {
    if (!confirm(`${t('fleet:messages.confirmDeleteVehicle')} ${vehicle.plateNumber}?`)) {
      return;
    }

    try {
      await FleetService.deleteVehicle(vehicle.id);
      await fetchAllData();
      toast({
        title: t('common:success'),
        description: t('fleet:messages.vehicleDeletedSuccess'),
      });

      if (onVehicleUpdated) {
        onVehicleUpdated();
      }
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToDeleteVehicle'),
        variant: 'destructive',
      });
    }
  };

  const getVehicleTypeIcon = (type: VehicleType) => {
    return type === VehicleType.TRUCK ? (
      <Truck className="h-4 w-4" />
    ) : (
      <TruckIcon className="h-4 w-4" />
    );
  };

  const getVehicleTypeBadge = (type: VehicleType) => {
    const getTypeLabel = (vehicleType: VehicleType) => {
      switch (vehicleType) {
        case VehicleType.TRUCK:
          return t('fleet:vehicles.truck');
        case VehicleType.TRAILER:
          return t('fleet:vehicles.trailer');
        case VehicleType.VAN:
          return t('fleet:vehicles.van');
        default:
          return vehicleType;
      }
    };

    return (
      <Badge variant={type === VehicleType.TRUCK ? 'default' : 'secondary'}>
        {getTypeLabel(type)}
      </Badge>
    );
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Car className="h-5 w-5" />
            {t('fleet:vehicles.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('fleet:vehicles.description', 'Manage vehicles and truck-trailer assignments')}
          </p>
        </div>
        <div className="flex gap-2">
          <TruckTrailerAssignmentDialog onSubmit={handleCreateAssignment} />
          <AddVehicleDialog
            onSubmit={handleCreateVehicle}
            open={showAddDialog}
            onOpenChange={setShowAddDialog}
            trigger={
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                {t('fleet:vehicles.addVehicle')}
              </Button>
            }
          />
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">{t('fleet:vehicles.loading', 'Loading vehicles...')}</div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">{t('fleet:vehicles.allVehicles')} ({vehicles.length})</TabsTrigger>
            <TabsTrigger value="trucks">{t('fleet:vehicles.trucks')} ({trucks.length})</TabsTrigger>
            <TabsTrigger value="trailers">{t('fleet:vehicles.trailers')} ({trailers.length})</TabsTrigger>
            <TabsTrigger value="assignments">
              <LinkIcon className="h-4 w-4 mr-2" />
              {t('fleet:vehicles.assignments')} ({truckTrailerAssignments.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <VehiclesList
              vehicles={vehicles}
              vehicleAssignments={vehicleAssignments}
              router={router}
              getVehicleTypeIcon={getVehicleTypeIcon}
              getVehicleTypeBadge={getVehicleTypeBadge}
              onEditVehicle={setEditingVehicle}
              onDeleteVehicle={handleDeleteVehicle}
            />
          </TabsContent>

          <TabsContent value="trucks" className="space-y-4">
            <VehiclesList
              vehicles={trucks}
              vehicleAssignments={vehicleAssignments}
              router={router}
              getVehicleTypeIcon={getVehicleTypeIcon}
              getVehicleTypeBadge={getVehicleTypeBadge}
              onEditVehicle={setEditingVehicle}
              onDeleteVehicle={handleDeleteVehicle}
            />
          </TabsContent>

          <TabsContent value="trailers" className="space-y-4">
            <VehiclesList
              vehicles={trailers}
              vehicleAssignments={vehicleAssignments}
              router={router}
              getVehicleTypeIcon={getVehicleTypeIcon}
              getVehicleTypeBadge={getVehicleTypeBadge}
              onEditVehicle={setEditingVehicle}
              onDeleteVehicle={handleDeleteVehicle}
            />
          </TabsContent>

          <TabsContent value="assignments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('fleet:vehicles.truckTrailerAssignments')}</CardTitle>
                <CardDescription>
                  {t('fleet:vehicles.assignmentsDescription', 'Manage truck and trailer pairings for operations')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TruckTrailerAssignmentsTable
                  assignments={truckTrailerAssignments}
                  onUpdate={handleUpdateAssignment}
                  onComplete={handleCompleteAssignment}
                  onCancel={handleCancelAssignment}
                  onDelete={handleDeleteAssignment}
                  isLoading={assignmentsLoading}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Edit Vehicle Dialog */}
      {editingVehicle && (
        <AddVehicleDialog
          vehicle={editingVehicle}
          onSubmit={handleUpdateVehicle}
          open={!!editingVehicle}
          onOpenChange={(open) => !open && setEditingVehicle(null)}
        />
      )}
    </div>
  );
};

// Separate component for vehicle list to avoid repetition
function VehiclesList({
  vehicles,
  vehicleAssignments,
  router,
  getVehicleTypeIcon,
  getVehicleTypeBadge,
  onEditVehicle,
  onDeleteVehicle
}: {
  vehicles: Vehicle[];
  vehicleAssignments: Record<string, number>;
  router: any;
  getVehicleTypeIcon: (type: VehicleType) => JSX.Element;
  getVehicleTypeBadge: (type: VehicleType) => JSX.Element;
  onEditVehicle: (vehicle: Vehicle) => void;
  onDeleteVehicle: (vehicle: Vehicle) => void;
}) {
  const { t } = useTranslation(['fleet', 'common']);

  const getStatusLabel = (status: string | undefined) => {
    if (!status) return t('fleet:table.available');

    switch (status) {
      case 'AVAILABLE':
        return t('fleet:table.available');
      case 'IN_USE':
        return t('fleet:vehicles.inUse');
      case 'MAINTENANCE':
        return t('fleet:vehicles.maintenance');
      case 'OUT_OF_SERVICE':
        return t('fleet:vehicles.outOfService');
      default:
        return status;
    }
  };

  if (vehicles.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h3 className="text-lg font-medium text-gray-900">{t('fleet:table.noVehiclesFound')}</h3>
          <p className="mt-2 text-sm text-gray-500">
            {t('fleet:table.getStartedMessage')}
          </p>
          <div className="mt-6">
            <AddVehicleDialog
              onSubmit={async () => {}} // This will be handled by parent
              trigger={
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('fleet:table.addNewVehicle')}
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('fleet:table.vehicleDetails')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('fleet:table.type')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('fleet:table.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('fleet:table.assignments')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('fleet:table.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {vehicles.map((vehicle) => (
                <tr key={vehicle.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="mr-3">
                        {getVehicleTypeIcon(vehicle.vehicleType)}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {vehicle.make} {vehicle.model} ({vehicle.year})
                        </div>
                        <div className="text-sm text-gray-500">
                          {t('fleet:table.plate')}: {vehicle.plateNumber}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getVehicleTypeBadge(vehicle.vehicleType)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant={vehicle.status === 'AVAILABLE' ? 'default' : 'secondary'}>
                      {getStatusLabel(vehicle.status)}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {vehicleAssignments[vehicle.id] !== undefined
                      ? `${vehicleAssignments[vehicle.id]} ${t('fleet:table.active')}`
                      : t('fleet:table.loading')
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/fleet?tab=vehicles&vehicleId=${vehicle.id}`)}
                      >
                        {t('fleet:table.view')}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEditVehicle(vehicle)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDeleteVehicle(vehicle)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
