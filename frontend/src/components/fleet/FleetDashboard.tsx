import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Car, Wrench, Shield, TrendingUp, TrendingDown, AlertTriangle, CheckCircle, BarChart3, RefreshCw } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { FleetService } from '@/lib/api/fleet-service';
import { MaintenanceService } from '@/lib/api/maintenance-service';

interface DashboardData {
  totalVehicles: number;
  availableVehicles: number;
  assignedVehicles: number;
  maintenanceVehicles: number;
  totalServiceRecords: number;
  upcomingMaintenance: number;
  monthlyGrowth: number;
  vehiclesByStatus: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  vehiclesByType: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  maintenanceStats: Array<{
    month: string;
    completed: number;
    scheduled: number;
    cost: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    vehicle: string;
    description: string;
    date: string;
    status: string;
  }>;
}

export const FleetDashboard: React.FC = () => {
  const { t } = useTranslation(['fleet', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // This would be replaced with actual API calls
      const mockData: DashboardData = {
        totalVehicles: 45,
        availableVehicles: 32,
        assignedVehicles: 10,
        maintenanceVehicles: 3,
        totalServiceRecords: 234,
        upcomingMaintenance: 8,
        monthlyGrowth: 4.2,
        vehiclesByStatus: [
          { name: t('common:status.available'), value: 32, color: '#22c55e' },
          { name: t('common:status.assigned'), value: 10, color: '#3b82f6' },
          { name: t('fleet:service.maintenance'), value: 3, color: '#f59e0b' },
        ],
        vehiclesByType: [
          { name: t('fleet:vehicles.truck'), value: 28, color: '#0088FE' },
          { name: t('fleet:vehicles.trailer'), value: 17, color: '#00C49F' },
        ],
        maintenanceStats: [
          { month: 'Jan', completed: 18, scheduled: 5, cost: 12500 },
          { month: 'Feb', completed: 22, scheduled: 3, cost: 15200 },
          { month: 'Mar', completed: 19, scheduled: 7, cost: 11800 },
          { month: 'Apr', completed: 25, scheduled: 4, cost: 16900 },
          { month: 'May', completed: 21, scheduled: 6, cost: 13400 },
          { month: 'Jun', completed: 23, scheduled: 8, cost: 14700 },
        ],
        recentActivity: [
          { id: '1', type: 'Maintenance', vehicle: 'Truck-001', description: 'Oil change completed', date: '2025-06-22', status: 'COMPLETED' },
          { id: '2', type: 'Insurance', vehicle: 'Trailer-015', description: 'Policy renewal due', date: '2025-06-21', status: 'PENDING' },
          { id: '3', type: 'Review', vehicle: 'Truck-007', description: 'Annual inspection scheduled', date: '2025-06-20', status: 'SCHEDULED' },
          { id: '4', type: 'Maintenance', vehicle: 'Truck-012', description: 'Brake service required', date: '2025-06-19', status: 'IN_PROGRESS' },
          { id: '5', type: 'Assignment', vehicle: 'Truck-003', description: 'Assigned to driver', date: '2025-06-18', status: 'ACTIVE' },
        ],
      };
      setData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'Maintenance':
        return <Wrench className="h-4 w-4" />;
      case 'Insurance':
        return <Shield className="h-4 w-4" />;
      case 'Assignment':
        return <Car className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">{t('common:status.completed')}</Badge>;
      case 'IN_PROGRESS':
        return <Badge className="bg-blue-100 text-blue-800">{t('fleet:dashboard.inProgress', 'In Progress')}</Badge>;
      case 'SCHEDULED':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('fleet:dashboard.scheduled', 'Scheduled')}</Badge>;
      case 'PENDING':
        return <Badge className="bg-orange-100 text-orange-800">{t('common:status.pending')}</Badge>;
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800">{t('common:status.active')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('common:actions.loading')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('fleet:dashboard.noDataAvailable', 'No dashboard data available')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('fleet:vehicles.title')} - {t('navigation:main.dashboard')}</h2>
          <p className="text-sm text-muted-foreground">{t('fleet:vehicles.vehicleDetails')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.totalVehicles', 'Total Vehicles')}</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalVehicles}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fleet:dashboard.vsLastMonth', 'vs last month')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.availableVehicles', 'Available Vehicles')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.availableVehicles}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.readyForAssignment', 'Ready for assignment')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.inMaintenance', 'In Maintenance')}</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.maintenanceVehicles}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.currentlyServiced', 'Currently serviced')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.upcomingMaintenance', 'Upcoming Maintenance')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.upcomingMaintenance}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.dueThisMonth', 'Due this month')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.vehicleStatusDistribution', 'Vehicle Status Distribution')}</CardTitle>
            <CardDescription>{t('fleet:dashboard.currentStatusDescription', 'Current status of all fleet vehicles')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.vehiclesByStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.vehiclesByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.fleetComposition', 'Fleet Composition')}</CardTitle>
            <CardDescription>{t('fleet:dashboard.breakdownByType', 'Breakdown by vehicle type')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.vehiclesByType}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.vehiclesByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.maintenanceActivity', 'Maintenance Activity')}</CardTitle>
          <CardDescription>{t('fleet:dashboard.monthlyMaintenanceDescription', 'Monthly maintenance completed vs scheduled')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.maintenanceStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completed" fill="#22c55e" name="Completed" />
                <Bar dataKey="scheduled" fill="#f59e0b" name="Scheduled" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.recentActivity', 'Recent Fleet Activity')}</CardTitle>
          <CardDescription>{t('fleet:dashboard.latestUpdates', 'Latest updates and activities across your fleet')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getActivityIcon(activity.type)}
                  <div>
                    <p className="font-medium">{activity.vehicle}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{activity.description}</span>
                      <span>•</span>
                      <span>{activity.date}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(activity.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
