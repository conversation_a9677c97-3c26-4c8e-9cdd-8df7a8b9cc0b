import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Car, Wrench, Shield, TrendingUp, TrendingDown, AlertTriangle, CheckCircle, BarChart3, RefreshCw } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { FleetService } from '@/lib/api/fleet-service';
import { MaintenanceService } from '@/lib/api/maintenance-service';

interface DashboardData {
  totalVehicles: number;
  availableVehicles: number;
  assignedVehicles: number;
  maintenanceVehicles: number;
  totalServiceRecords: number;
  upcomingMaintenance: number;
  monthlyGrowth: number;
  vehiclesByStatus: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  vehiclesByType: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  maintenanceStats: Array<{
    month: string;
    completed: number;
    scheduled: number;
    cost: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    vehicle: string;
    description: string;
    date: string;
    status: string;
  }>;
}

// Helper function to generate maintenance statistics for the last 6 months
const generateMaintenanceStats = (maintenanceLogs: any[]) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const currentDate = new Date();
  const stats = [];

  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    const monthName = months[date.getMonth()];

    const monthLogs = maintenanceLogs.filter(log => {
      const logDate = new Date(log.date || log.scheduledDate);
      return logDate.getMonth() === date.getMonth() && logDate.getFullYear() === date.getFullYear();
    });

    const completed = monthLogs.filter(log => log.status === 'COMPLETED').length;
    const scheduled = monthLogs.filter(log => log.status === 'SCHEDULED').length;
    const cost = monthLogs
      .filter(log => log.status === 'COMPLETED')
      .reduce((sum, log) => sum + (log.cost || 0), 0);

    stats.push({
      month: monthName,
      completed,
      scheduled,
      cost
    });
  }

  return stats;
};

// Helper function to generate recent activity
const generateRecentActivity = (maintenanceLogs: any[], vehicles: any[], upcomingMaintenance: any[]) => {
  const activities = [];

  // Add recent maintenance activities
  const recentMaintenance = maintenanceLogs
    .sort((a, b) => new Date(b.date || b.scheduledDate).getTime() - new Date(a.date || a.scheduledDate).getTime())
    .slice(0, 3);

  recentMaintenance.forEach(log => {
    const vehicle = vehicles.find(v => v.id === log.vehicleId);
    activities.push({
      id: log.id,
      type: 'Maintenance',
      vehicle: vehicle?.plateNumber || 'Unknown Vehicle',
      description: log.description,
      date: new Date(log.date || log.scheduledDate).toISOString().split('T')[0],
      status: log.status
    });
  });

  // Add upcoming maintenance
  upcomingMaintenance.slice(0, 2).forEach(log => {
    const vehicle = vehicles.find(v => v.id === log.vehicleId);
    activities.push({
      id: `upcoming-${log.id}`,
      type: 'Maintenance',
      vehicle: vehicle?.plateNumber || 'Unknown Vehicle',
      description: `Upcoming: ${log.description}`,
      date: new Date(log.scheduledDate).toISOString().split('T')[0],
      status: 'SCHEDULED'
    });
  });

  return activities.slice(0, 5); // Return top 5 activities
};

export const FleetDashboard: React.FC = () => {
  const { t } = useTranslation(['fleet', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch real data from multiple API endpoints
      const [
        vehicles,
        maintenanceLogs,
        upcomingMaintenance,
        maintenanceStats
      ] = await Promise.all([
        FleetService.getVehicles(),
        MaintenanceService.getMaintenanceLogs({ limit: 100 }),
        MaintenanceService.getUpcomingMaintenance(),
        MaintenanceService.getMaintenanceStats()
      ]);

      // Calculate vehicle statistics
      const totalVehicles = vehicles.length;
      const availableVehicles = vehicles.filter(v => v.status === 'AVAILABLE').length;
      const assignedVehicles = vehicles.filter(v => v.status === 'ASSIGNED').length;
      const maintenanceVehicles = vehicles.filter(v => v.status === 'MAINTENANCE').length;
      const outOfServiceVehicles = vehicles.filter(v => v.status === 'OUT_OF_SERVICE').length;

      // Calculate vehicle type distribution
      const truckCount = vehicles.filter(v => v.vehicleType === 'TRUCK').length;
      const trailerCount = vehicles.filter(v => v.vehicleType === 'TRAILER').length;

      // Calculate maintenance statistics
      const totalServiceRecords = maintenanceLogs.length;
      const upcomingMaintenanceCount = upcomingMaintenance.length;

      // Calculate monthly growth (simplified - comparing current vs previous month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const currentMonthLogs = maintenanceLogs.filter(log => {
        const logDate = new Date(log.date || log.scheduledDate);
        return logDate.getMonth() === currentMonth && logDate.getFullYear() === currentYear;
      });
      const previousMonthLogs = maintenanceLogs.filter(log => {
        const logDate = new Date(log.date || log.scheduledDate);
        const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
        return logDate.getMonth() === prevMonth && logDate.getFullYear() === prevYear;
      });
      const monthlyGrowth = previousMonthLogs.length > 0
        ? ((currentMonthLogs.length - previousMonthLogs.length) / previousMonthLogs.length) * 100
        : 0;

      // Prepare chart data
      const vehiclesByStatus = [
        { name: t('common:status.available'), value: availableVehicles, color: '#22c55e' },
        { name: t('common:status.assigned'), value: assignedVehicles, color: '#3b82f6' },
        { name: t('fleet:service.maintenance'), value: maintenanceVehicles, color: '#f59e0b' },
      ];

      // Only include statuses that have vehicles
      const filteredVehiclesByStatus = vehiclesByStatus.filter(item => item.value > 0);
      if (outOfServiceVehicles > 0) {
        filteredVehiclesByStatus.push({
          name: t('fleet:status.outofservice'),
          value: outOfServiceVehicles,
          color: '#ef4444'
        });
      }

      const vehiclesByType = [
        { name: t('fleet:vehicles.truck'), value: truckCount, color: '#0088FE' },
        { name: t('fleet:vehicles.trailer'), value: trailerCount, color: '#00C49F' },
      ].filter(item => item.value > 0);

      // Generate maintenance stats for the last 6 months
      const maintenanceStatsData = generateMaintenanceStats(maintenanceLogs);

      // Generate recent activity from maintenance logs and vehicle data
      const recentActivity = generateRecentActivity(maintenanceLogs, vehicles, upcomingMaintenance);

      const dashboardData: DashboardData = {
        totalVehicles,
        availableVehicles,
        assignedVehicles,
        maintenanceVehicles,
        totalServiceRecords,
        upcomingMaintenance: upcomingMaintenanceCount,
        monthlyGrowth,
        vehiclesByStatus: filteredVehiclesByStatus,
        vehiclesByType,
        maintenanceStats: maintenanceStatsData,
        recentActivity,
      };

      setData(dashboardData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Fallback to empty data structure
      setData({
        totalVehicles: 0,
        availableVehicles: 0,
        assignedVehicles: 0,
        maintenanceVehicles: 0,
        totalServiceRecords: 0,
        upcomingMaintenance: 0,
        monthlyGrowth: 0,
        vehiclesByStatus: [],
        vehiclesByType: [],
        maintenanceStats: [],
        recentActivity: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'Maintenance':
        return <Wrench className="h-4 w-4" />;
      case 'Insurance':
        return <Shield className="h-4 w-4" />;
      case 'Assignment':
        return <Car className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">{t('common:status.completed')}</Badge>;
      case 'IN_PROGRESS':
        return <Badge className="bg-blue-100 text-blue-800">{t('fleet:dashboard.inProgress', 'In Progress')}</Badge>;
      case 'SCHEDULED':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('fleet:dashboard.scheduled', 'Scheduled')}</Badge>;
      case 'PENDING':
        return <Badge className="bg-orange-100 text-orange-800">{t('common:status.pending')}</Badge>;
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800">{t('common:status.active')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('common:actions.loading')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('fleet:dashboard.noDataAvailable', 'No dashboard data available')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('fleet:vehicles.title')} - {t('navigation:main.dashboard')}</h2>
          <p className="text-sm text-muted-foreground">{t('fleet:vehicles.vehicleDetails')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.totalVehicles', 'Total Vehicles')}</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalVehicles}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fleet:dashboard.vsLastMonth', 'vs last month')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.availableVehicles', 'Available Vehicles')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.availableVehicles}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.readyForAssignment', 'Ready for assignment')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.inMaintenance', 'In Maintenance')}</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.maintenanceVehicles}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.currentlyServiced', 'Currently serviced')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fleet:dashboard.upcomingMaintenance', 'Upcoming Maintenance')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.upcomingMaintenance}</div>
            <p className="text-xs text-muted-foreground">{t('fleet:dashboard.dueThisMonth', 'Due this month')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.vehicleStatusDistribution', 'Vehicle Status Distribution')}</CardTitle>
            <CardDescription>{t('fleet:dashboard.currentStatusDescription', 'Current status of all fleet vehicles')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.vehiclesByStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.vehiclesByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:dashboard.fleetComposition', 'Fleet Composition')}</CardTitle>
            <CardDescription>{t('fleet:dashboard.breakdownByType', 'Breakdown by vehicle type')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.vehiclesByType}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.vehiclesByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.maintenanceActivity', 'Maintenance Activity')}</CardTitle>
          <CardDescription>{t('fleet:dashboard.monthlyMaintenanceDescription', 'Monthly maintenance completed vs scheduled')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.maintenanceStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completed" fill="#22c55e" name="Completed" />
                <Bar dataKey="scheduled" fill="#f59e0b" name="Scheduled" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fleet:dashboard.recentActivity', 'Recent Fleet Activity')}</CardTitle>
          <CardDescription>{t('fleet:dashboard.latestUpdates', 'Latest updates and activities across your fleet')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getActivityIcon(activity.type)}
                  <div>
                    <p className="font-medium">{activity.vehicle}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{activity.description}</span>
                      <span>•</span>
                      <span>{activity.date}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(activity.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
