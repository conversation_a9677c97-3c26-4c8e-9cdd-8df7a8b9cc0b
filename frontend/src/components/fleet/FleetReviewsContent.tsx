import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { VehicleReviewForm } from './VehicleReviewForm';
import { Star, Plus, Calendar, AlertCircle } from 'lucide-react';

interface VehicleReview {
  id: string;
  vehicleId: string;
  vehicle?: {
    make: string;
    model: string;
    plateNumber: string;
  };
  reviewType: string;
  scheduledDate: string;
  completedDate?: string;
  inspector: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  score?: number;
  notes?: string;
  nextReviewDate?: string;
  issues?: Array<{
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
  }>;
}

interface FleetReviewsContentProps {
  onReviewUpdated?: () => void;
}

export const FleetReviewsContent: React.FC<FleetReviewsContentProps> = ({
  onReviewUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [reviews, setReviews] = useState<VehicleReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingReviewId, setEditingReviewId] = useState<string | null>(null);

  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      const mockReviews: VehicleReview[] = [
        {
          id: '1',
          vehicleId: 'v1',
          vehicle: { make: 'Volvo', model: 'FH16', plateNumber: 'ABC-123' },
          reviewType: 'Annual Safety Inspection',
          scheduledDate: '2024-06-25',
          completedDate: '2024-06-25',
          inspector: 'John Smith',
          status: 'COMPLETED',
          score: 95,
          notes: 'Vehicle passed all safety checks. Minor brake pad wear noted.',
          nextReviewDate: '2025-06-25',
          issues: [
            { severity: 'LOW', description: 'Brake pads at 30% wear' }
          ]
        },
        {
          id: '2',
          vehicleId: 'v2',
          vehicle: { make: 'Mercedes', model: 'Actros', plateNumber: 'DEF-456' },
          reviewType: 'Quarterly Maintenance Review',
          scheduledDate: '2024-06-30',
          inspector: 'Sarah Johnson',
          status: 'SCHEDULED',
          nextReviewDate: '2024-09-30'
        },
        {
          id: '3',
          vehicleId: 'v3',
          vehicle: { make: 'Scania', model: 'R450', plateNumber: 'GHI-789' },
          reviewType: 'DOT Inspection',
          scheduledDate: '2024-06-20',
          completedDate: '2024-06-20',
          inspector: 'Mike Wilson',
          status: 'FAILED',
          score: 65,
          notes: 'Failed due to tire condition and lighting issues.',
          issues: [
            { severity: 'HIGH', description: 'Front tire tread below minimum' },
            { severity: 'MEDIUM', description: 'Tail light not functioning' }
          ]
        },
        {
          id: '4',
          vehicleId: 'v4',
          vehicle: { make: 'DAF', model: 'XF', plateNumber: 'JKL-012' },
          reviewType: 'Pre-Trip Inspection',
          scheduledDate: '2024-06-22',
          inspector: 'Lisa Brown',
          status: 'IN_PROGRESS',
        },
      ];
      setReviews(mockReviews);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:reviews.errorLoadingReviews'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">{t('common:status.completed')}</Badge>;
      case 'IN_PROGRESS':
        return <Badge className="bg-blue-100 text-blue-800">{t('common:status.inProgress')}</Badge>;
      case 'SCHEDULED':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('common:status.scheduled')}</Badge>;
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800">{t('fleet:reviews.failed')}</Badge>;
      case 'CANCELLED':
        return <Badge className="bg-gray-100 text-gray-800">{t('common:status.cancelled')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <Badge className="bg-red-100 text-red-800">{t('fleet:reviews.critical')}</Badge>;
      case 'HIGH':
        return <Badge className="bg-orange-100 text-orange-800">{t('fleet:reviews.high')}</Badge>;
      case 'MEDIUM':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('fleet:reviews.medium')}</Badge>;
      case 'LOW':
        return <Badge className="bg-blue-100 text-blue-800">{t('fleet:reviews.low')}</Badge>;
      default:
        return <Badge variant="secondary">{severity}</Badge>;
    }
  };

  const getReviewTypeLabel = (reviewType: string) => {
    switch (reviewType) {
      case 'Annual Safety Inspection':
        return t('fleet:reviews.annualSafetyInspection');
      case 'Quarterly Maintenance Review':
        return t('fleet:reviews.quarterlyMaintenanceReview');
      case 'DOT Inspection':
        return t('fleet:reviews.dotInspection');
      case 'Pre-Trip Inspection':
        return t('fleet:reviews.preTripInspection');
      case 'Post-Trip Inspection':
        return t('fleet:reviews.postTripInspection');
      default:
        return reviewType;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleDelete = async (id: string) => {
    if (confirm(t('fleet:reviews.confirmDeleteReview'))) {
      try {
        // Mock delete operation
        setReviews(reviews => reviews.filter(review => review.id !== id));
        toast({
          title: t('common:success'),
          description: t('fleet:reviews.reviewDeletedSuccess'),
        });

        if (onReviewUpdated) {
          onReviewUpdated();
        }
      } catch (error) {
        console.error('Error deleting review:', error);
        toast({
          title: t('common:error'),
          description: t('fleet:reviews.failedToDeleteReview'),
          variant: 'destructive',
        });
      }
    }
  };

  const upcomingReviews = reviews.filter(r => r.status === 'SCHEDULED' || r.status === 'IN_PROGRESS');
  const failedReviews = reviews.filter(r => r.status === 'FAILED');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Star className="h-5 w-5" />
            {t('fleet:reviews.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('fleet:reviews.description', 'Schedule and track vehicle reviews and inspections')}
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          {t('fleet:reviews.scheduleReview')}
        </Button>
      </div>

      {/* Alerts */}
      <div className="grid gap-4 md:grid-cols-2">
        {upcomingReviews.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">{t('fleet:reviews.upcomingReviews')}</h4>
                  <p className="text-sm text-blue-700">
                    {upcomingReviews.length} {upcomingReviews.length === 1 ? t('fleet:reviews.upcomingReviewsDescription') : t('fleet:reviews.upcomingReviewsDescriptionPlural')}.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {failedReviews.length > 0 && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900">{t('fleet:reviews.failedReviews')}</h4>
                  <p className="text-sm text-red-700">
                    {failedReviews.length} {failedReviews.length === 1 ? t('fleet:reviews.failedReviewsDescription') : t('fleet:reviews.failedReviewsDescriptionPlural')}.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {loading ? (
        <div className="text-center py-10">{t('fleet:reviews.loading', 'Loading vehicle reviews...')}</div>
      ) : reviews.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('fleet:reviews.noReviewsFound')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('fleet:reviews.noReviewsDescription', 'No vehicle reviews found. Schedule your first review to get started.')}
            </p>
            <Button onClick={() => setShowAddForm(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('fleet:reviews.scheduleFirstReview')}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t('fleet:reviews.vehicleReviews')} ({reviews.length})</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.vehicle')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:reviews.reviewDetails')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:reviews.dates')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:reviews.score')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.status')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('fleet:table.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reviews.map((review) => (
                    <tr key={review.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {review.vehicle?.make} {review.vehicle?.model}
                            </div>
                            <div className="text-sm text-gray-500">
                              {t('fleet:table.plate')}: {review.vehicle?.plateNumber}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{getReviewTypeLabel(review.reviewType)}</div>
                        <div className="text-sm text-gray-500">{t('fleet:reviews.inspector')}: {review.inspector}</div>
                        {review.issues && review.issues.length > 0 && (
                          <div className="mt-1 space-x-1">
                            {review.issues.slice(0, 2).map((issue, index) => (
                              <span key={index} className="inline-block">
                                {getSeverityBadge(issue.severity)}
                              </span>
                            ))}
                            {review.issues.length > 2 && (
                              <span className="text-xs text-gray-500">+{review.issues.length - 2} {t('fleet:reviews.more')}</span>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{t('fleet:reviews.scheduled')}: {formatDate(review.scheduledDate)}</div>
                        {review.completedDate && (
                          <div className="text-sm text-gray-500">{t('fleet:reviews.completed')}: {formatDate(review.completedDate)}</div>
                        )}
                        {review.nextReviewDate && (
                          <div className="text-sm text-gray-500">{t('fleet:reviews.next')}: {formatDate(review.nextReviewDate)}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {review.score ? (
                          <div className="text-sm font-medium text-gray-900">{review.score}%</div>
                        ) : (
                          <span className="text-sm text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(review.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => router.push(`/fleet?tab=reviews&reviewId=${review.id}`)}
                        >
                          {t('fleet:table.view')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={() => {
                            setEditingReviewId(review.id);
                            setShowEditForm(true);
                          }}
                        >
                          {t('fleet:table.edit')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(review.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          {t('fleet:table.delete')}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Review Form Dialog */}
      {showAddForm && (
        <VehicleReviewForm
          onClose={() => setShowAddForm(false)}
          onReviewAdded={() => {
            setShowAddForm(false);
            fetchReviews();
            onReviewUpdated?.();
          }}
          showAsDialog={true}
        />
      )}

      {/* Edit Review Form Dialog */}
      {showEditForm && editingReviewId && (
        <VehicleReviewForm
          reviewId={editingReviewId}
          onClose={() => {
            setShowEditForm(false);
            setEditingReviewId(null);
          }}
          onReviewUpdated={() => {
            setShowEditForm(false);
            setEditingReviewId(null);
            fetchReviews();
            onReviewUpdated?.();
          }}
          showAsDialog={true}
        />
      )}
    </div>
  );
};
