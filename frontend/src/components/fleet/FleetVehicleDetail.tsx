import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Vehicle, VehicleType, VehicleStatus } from '@/types/vehicle';
import { InsurancePolicy } from '@/types/insurance';
import { VehicleReview } from '@/types/review';
import { MaintenanceLog } from '@/types/maintenance';
import { FleetService } from '@/lib/api/fleet-service';
import { InsuranceService } from '@/lib/api/insurance-service';
import { ReviewService } from '@/lib/api/review-service';
import { VehicleAssignmentService } from '@/lib/api/vehicle-assignment-service';
import { MaintenanceService } from '@/lib/api/maintenance-service';
import { AddVehicleDialog } from '@/components/vehicles/add-vehicle-dialog';
import { AddServiceRecordDialog } from '@/components/fleet/AddServiceRecordDialog';
import { AddInsuranceDialog } from '@/components/fleet/AddInsuranceDialog';
import { AddReviewDialog } from '@/components/fleet/AddReviewDialog';
import { AddAssignmentDialog } from '@/components/fleet/AddAssignmentDialog';
import { formatDate, formatCurrency } from '@/lib/utils';
import { Truck, TruckIcon, Edit, Trash2, ArrowLeft, Plus, Wrench, Shield, Star, Users, Settings, Package } from 'lucide-react';

const reviewService = new ReviewService();

interface FleetVehicleDetailProps {
  vehicleId: string;
  onBack?: () => void;
  onVehicleUpdated?: () => void;
}

export const FleetVehicleDetail: React.FC<FleetVehicleDetailProps> = ({
  vehicleId,
  onBack,
  onVehicleUpdated
}) => {
  const { t } = useTranslation(['fleet', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [policies, setPolicies] = useState<InsurancePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingPolicies, setLoadingPolicies] = useState(true);
  const [reviews, setReviews] = useState<VehicleReview[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [maintenanceLogs, setMaintenanceLogs] = useState<MaintenanceLog[]>([]);
  const [loadingMaintenance, setLoadingMaintenance] = useState(true);
  const [assignments, setAssignments] = useState<any[]>([]);
  const [loadingAssignments, setLoadingAssignments] = useState(true);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showAddServiceDialog, setShowAddServiceDialog] = useState(false);
  const [editingServiceRecord, setEditingServiceRecord] = useState<MaintenanceLog | null>(null);
  const [showAddInsuranceDialog, setShowAddInsuranceDialog] = useState(false);
  const [editingInsurancePolicy, setEditingInsurancePolicy] = useState<InsurancePolicy | null>(null);
  const [showAddReviewDialog, setShowAddReviewDialog] = useState(false);
  const [editingReview, setEditingReview] = useState<VehicleReview | null>(null);
  const [showAddAssignmentDialog, setShowAddAssignmentDialog] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<any>(null);

  useEffect(() => {
    async function fetchVehicleDetails() {
      try {
        const data = await FleetService.getVehicleById(vehicleId);
        setVehicle(data);
      } catch (error) {
        console.error('Error fetching vehicle details:', error);
        toast({
          title: t('common:error'),
          description: t('fleet:vehicles.loadError'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    async function fetchInsurancePolicies() {
      try {
        setLoadingPolicies(true);
        const policiesData = await InsuranceService.getPolicies({vehicleId});
        setPolicies(policiesData);
      } catch (error) {
        console.error('Error fetching insurance policies:', error);
        toast({
          title: t('common:error'),
          description: t('fleet:messages.errorLoadingPolicies'),
          variant: 'destructive',
        });
      } finally {
        setLoadingPolicies(false);
      }
    }
    
    async function fetchVehicleReviews() {
      try {
        setLoadingReviews(true);
        const reviewsData = await reviewService.getVehicleReviews(vehicleId);
        setReviews(reviewsData);
      } catch (error) {
        console.error('Error fetching vehicle reviews:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicle reviews.',
          variant: 'destructive',
        });
      } finally {
        setLoadingReviews(false);
      }
    }

    async function fetchMaintenanceLogs() {
      try {
        setLoadingMaintenance(true);
        console.log('Fetching maintenance logs for vehicle:', vehicleId);

        // Try FleetService first, fallback to MaintenanceService
        let logsData;
        try {
          logsData = await FleetService.getVehicleMaintenanceLogs(vehicleId);
        } catch (fleetError) {
          console.warn('FleetService failed, trying MaintenanceService:', fleetError);
          logsData = await MaintenanceService.getVehicleMaintenanceLogs(vehicleId);
        }

        console.log('Maintenance logs received:', logsData);
        setMaintenanceLogs(logsData || []);
      } catch (error) {
        console.error('Error fetching maintenance logs:', error);
        toast({
          title: 'Error',
          description: `Failed to load maintenance logs: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: 'destructive',
        });
        setMaintenanceLogs([]);
      } finally {
        setLoadingMaintenance(false);
      }
    }

    async function fetchVehicleAssignments() {
      try {
        setLoadingAssignments(true);
        const assignmentsData = await VehicleAssignmentService.getVehicleAssignments(vehicleId);
        setAssignments(assignmentsData);
      } catch (error) {
        console.error('Error fetching vehicle assignments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load vehicle assignments.',
          variant: 'destructive',
        });
      } finally {
        setLoadingAssignments(false);
      }
    }

    if (vehicleId) {
      fetchVehicleDetails();
      fetchInsurancePolicies();
      fetchVehicleReviews();
      fetchMaintenanceLogs();
      fetchVehicleAssignments();
    }
  }, [vehicleId, toast]);

  const handleUpdateVehicle = async (data: any) => {
    if (!vehicle) return;

    try {
      const vehicleData = {
        plateNumber: data.plateNumber,
        make: data.make,
        model: data.model,
        year: data.year,
        vehicleType: data.vehicleType,
        status: data.status,
        vin: data.vin,
        color: data.color,
        mileage: data.mileage,
        fuelType: data.fuelType,
        purchaseDate: data.purchaseDate,
        // Truck-specific fields
        engineType: data.engineType,
        transmission: data.transmission,
        fuelCapacity: data.fuelCapacity,
        axleConfiguration: data.axleConfiguration,
        cabConfiguration: data.cabConfiguration,
        // Trailer-specific fields
        trailerType: data.trailerType,
        cargoCapacity: data.cargoCapacity,
        maxWeight: data.maxWeight,
        length: data.length,
        width: data.width,
        height: data.height,
        hasRefrigeration: data.hasRefrigeration,
      };

      const updatedVehicle = await FleetService.updateVehicle(vehicle.id, vehicleData);
      setVehicle(updatedVehicle);
      setShowEditDialog(false);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.vehicleUpdatedSuccess'),
      });
      onVehicleUpdated?.();
    } catch (error) {
      console.error('Error updating vehicle:', error);
      throw error;
    }
  };

  const handleDeleteVehicle = async () => {
    if (!vehicle) return;

    if (!confirm(`${t('fleet:messages.confirmDeleteVehicle')} ${vehicle.plateNumber}?`)) {
      return;
    }

    try {
      await FleetService.deleteVehicle(vehicle.id);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.vehicleDeletedSuccess'),
      });
      onVehicleUpdated?.();
      onBack?.();
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToDeleteVehicle'),
        variant: 'destructive',
      });
    }
  };

  const handleEditServiceRecord = (record: MaintenanceLog) => {
    setEditingServiceRecord(record);
    setShowAddServiceDialog(true);
  };

  const handleDeleteServiceRecord = async (recordId: string) => {
    if (!confirm(t('fleet:service.confirmDelete'))) {
      return;
    }

    try {
      await MaintenanceService.deleteMaintenanceLog(recordId);
      toast({
        title: t('common:success'),
        description: t('fleet:service.deleteSuccess'),
      });

      // Refresh maintenance logs
      if (vehicleId) {
        try {
          const logsData = await FleetService.getVehicleMaintenanceLogs(vehicleId);
          setMaintenanceLogs(logsData || []);
        } catch (error) {
          const logsData = await MaintenanceService.getVehicleMaintenanceLogs(vehicleId);
          setMaintenanceLogs(logsData || []);
        }
      }
    } catch (error) {
      console.error('Error deleting service record:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:service.deleteError'),
        variant: 'destructive',
      });
    }
  };

  const handleServiceRecordSubmit = async (data: any) => {
    try {
      if (editingServiceRecord) {
        // Update existing record
        await MaintenanceService.updateMaintenanceLog(editingServiceRecord.id, data);
        toast({
          title: t('common:success'),
          description: t('fleet:service.updateSuccess'),
        });
      } else {
        // Create new record
        await MaintenanceService.createMaintenanceLog({
          ...data,
          vehicleId: vehicleId,
        });
        toast({
          title: t('common:success'),
          description: t('fleet:service.createSuccess'),
        });
      }

      // Refresh maintenance logs
      if (vehicleId) {
        try {
          const logsData = await FleetService.getVehicleMaintenanceLogs(vehicleId);
          setMaintenanceLogs(logsData || []);
        } catch (error) {
          const logsData = await MaintenanceService.getVehicleMaintenanceLogs(vehicleId);
          setMaintenanceLogs(logsData || []);
        }
      }

      setShowAddServiceDialog(false);
      setEditingServiceRecord(null);
    } catch (error) {
      console.error('Error saving service record:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:service.saveError'),
        variant: 'destructive',
      });
    }
  };

  const handleEditInsurancePolicy = (policy: InsurancePolicy) => {
    setEditingInsurancePolicy(policy);
    setShowAddInsuranceDialog(true);
  };

  const handleDeleteInsurancePolicy = async (policyId: string) => {
    if (!confirm(t('fleet:insurance.confirmDelete'))) {
      return;
    }

    try {
      await InsuranceService.deletePolicy(policyId);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.policyDeletedSuccess'),
      });

      // Refresh insurance policies
      if (vehicleId) {
        const policiesData = await InsuranceService.getPolicies({vehicleId});
        setPolicies(policiesData);
      }
    } catch (error) {
      console.error('Error deleting insurance policy:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToDeletePolicy'),
        variant: 'destructive',
      });
    }
  };

  const handleInsurancePolicySubmit = async (data: any) => {
    try {
      if (editingInsurancePolicy) {
        // Update existing policy
        await InsuranceService.updatePolicy(editingInsurancePolicy.id, data);
        toast({
          title: t('common:success'),
          description: t('fleet:messages.policyUpdatedSuccess'),
        });
      } else {
        // Create new policy
        await InsuranceService.createPolicy({
          ...data,
          vehicleId: vehicleId,
        });
        toast({
          title: t('common:success'),
          description: t('fleet:messages.policyCreatedSuccess'),
        });
      }

      // Refresh insurance policies
      if (vehicleId) {
        const policiesData = await InsuranceService.getPolicies({vehicleId});
        setPolicies(policiesData);
      }

      setShowAddInsuranceDialog(false);
      setEditingInsurancePolicy(null);
    } catch (error) {
      console.error('Error saving insurance policy:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:messages.failedToSavePolicy'),
        variant: 'destructive',
      });
    }
  };

  const handleEditReview = (review: VehicleReview) => {
    setEditingReview(review);
    setShowAddReviewDialog(true);
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm(t('fleet:reviews.confirmDeleteReview'))) {
      return;
    }

    try {
      await ReviewService.deleteReview(reviewId);
      toast({
        title: t('common:success'),
        description: t('fleet:reviews.reviewDeletedSuccess'),
      });

      // Refresh reviews
      if (vehicleId) {
        const reviewsData = await ReviewService.getVehicleReviews(vehicleId);
        setReviews(reviewsData);
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:reviews.failedToDeleteReview'),
        variant: 'destructive',
      });
    }
  };

  const handleReviewSubmit = async (data: any) => {
    try {
      if (editingReview) {
        // Update existing review
        await ReviewService.updateReview(editingReview.id, data);
        toast({
          title: t('common:success'),
          description: t('fleet:reviews.reviewUpdatedSuccess'),
        });
      } else {
        // Create new review
        await ReviewService.createReview({
          ...data,
          vehicleId: vehicleId,
        });
        toast({
          title: t('common:success'),
          description: t('fleet:reviews.reviewScheduledSuccess'),
        });
      }

      // Refresh reviews
      if (vehicleId) {
        const reviewsData = await ReviewService.getVehicleReviews(vehicleId);
        setReviews(reviewsData);
      }

      setShowAddReviewDialog(false);
      setEditingReview(null);
    } catch (error) {
      console.error('Error saving review:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:reviews.failedToSaveReview'),
        variant: 'destructive',
      });
    }
  };

  const handleEditAssignment = (assignment: any) => {
    setEditingAssignment(assignment);
    setShowAddAssignmentDialog(true);
  };

  const handleCompleteAssignment = async (assignmentId: string) => {
    if (!confirm(t('fleet:assignments.confirmComplete'))) {
      return;
    }

    try {
      await VehicleAssignmentService.completeAssignment(assignmentId);
      toast({
        title: t('common:success'),
        description: t('fleet:assignments.assignmentCompletedSuccess'),
      });

      // Refresh assignments
      if (vehicleId) {
        const assignmentsData = await VehicleAssignmentService.getVehicleAssignments(vehicleId);
        setAssignments(assignmentsData);
      }
    } catch (error) {
      console.error('Error completing assignment:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:assignments.failedToCompleteAssignment'),
        variant: 'destructive',
      });
    }
  };

  const handleCancelAssignment = async (assignmentId: string) => {
    if (!confirm(t('fleet:assignments.confirmCancel'))) {
      return;
    }

    try {
      await VehicleAssignmentService.cancelAssignment(assignmentId);
      toast({
        title: t('common:success'),
        description: t('fleet:assignments.assignmentCancelledSuccess'),
      });

      // Refresh assignments
      if (vehicleId) {
        const assignmentsData = await VehicleAssignmentService.getVehicleAssignments(vehicleId);
        setAssignments(assignmentsData);
      }
    } catch (error) {
      console.error('Error cancelling assignment:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:assignments.failedToCancelAssignment'),
        variant: 'destructive',
      });
    }
  };

  const handleAssignmentSubmit = async (data: any) => {
    try {
      if (editingAssignment) {
        // Update existing assignment (if update functionality exists)
        toast({
          title: t('common:info'),
          description: t('fleet:assignments.updateNotSupported'),
        });
      } else {
        // Create new assignment
        await VehicleAssignmentService.createAssignment({
          ...data,
          vehicleId: vehicleId,
        });
        toast({
          title: t('common:success'),
          description: t('fleet:assignments.assignmentCreatedSuccess'),
        });
      }

      // Refresh assignments
      if (vehicleId) {
        const assignmentsData = await VehicleAssignmentService.getVehicleAssignments(vehicleId);
        setAssignments(assignmentsData);
      }

      setShowAddAssignmentDialog(false);
      setEditingAssignment(null);
    } catch (error) {
      console.error('Error saving assignment:', error);
      toast({
        title: t('common:error'),
        description: t('fleet:assignments.failedToSaveAssignment'),
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return <div className="text-center py-10">{t('fleet:vehicles.loadingDetails')}</div>;
  }

  if (!vehicle) {
    return (
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <h3 className="text-lg font-medium text-gray-900">{t('fleet:vehicles.vehicleNotFound')}</h3>
        <p className="mt-2 text-sm text-gray-500">
          {t('fleet:vehicles.vehicleNotFoundDescription')}
        </p>
        <div className="mt-6">
          <Button onClick={onBack}>
            {t('fleet:vehicles.backToFleet')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('fleet:vehicles.backToVehicles')}
          </Button>
          <div className="flex items-center gap-3">
            {vehicle.vehicleType === VehicleType.TRUCK ? (
              <Truck className="h-6 w-6 text-blue-600" />
            ) : (
              <TruckIcon className="h-6 w-6 text-green-600" />
            )}
            <div>
              <h2 className="text-xl font-bold leading-7 text-gray-900">
                {vehicle.make} {vehicle.model} ({vehicle.year})
              </h2>
              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-600">
                  {t('fleet:vehicles.plateNumber')}: <span className="font-medium">{vehicle.plateNumber}</span>
                </p>
                <Badge variant={vehicle.vehicleType === VehicleType.TRUCK ? 'default' : 'secondary'}>
                  {t(`fleet:vehicles.${vehicle.vehicleType.toLowerCase()}`)}
                </Badge>
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowEditDialog(true)}
            className="gap-2"
          >
            <Edit className="h-4 w-4" />
            {t('fleet:vehicles.editVehicle')}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDeleteVehicle}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {t('fleet:vehicles.deleteVehicle')}
          </Button>
        </div>
      </div>

      {/* Vehicle Details Tabs */}
      <Tabs defaultValue="details">
        <TabsList className="mb-6">
          <TabsTrigger value="details">{t('fleet:vehicles.details')}</TabsTrigger>
          <TabsTrigger value="service">{t('fleet:vehicles.serviceHistory')}</TabsTrigger>
          <TabsTrigger value="insurance">{t('fleet:vehicles.insurance')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('fleet:vehicles.reviewsInspections')}</TabsTrigger>
          <TabsTrigger value="assignments">{t('fleet:vehicles.driverAssignments')}</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                  {vehicle.vehicleType === VehicleType.TRUCK ? (
                    <Truck className="h-5 w-5 text-blue-600" />
                  ) : (
                    <TruckIcon className="h-5 w-5 text-green-600" />
                  )}
                  {t('fleet:vehicles.basicInformation')}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">{t('fleet:vehicles.basicInformationDescription')}</p>
              </div>
              <div className="border-t border-gray-200">
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.vehicleType')}</dt>
                    <dd className="mt-1 sm:mt-0 sm:col-span-2">
                      <Badge variant={vehicle.vehicleType === VehicleType.TRUCK ? 'default' : 'secondary'}>
                        {t(`fleet:vehicles.${vehicle.vehicleType.toLowerCase()}`)}
                      </Badge>
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.make')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.make}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.model')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.model}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.year')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.year}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.plateNumber')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 font-medium">{vehicle.plateNumber}</dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.vin')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.vin || t('fleet:vehicles.notAvailable')}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.status')}</dt>
                    <dd className="mt-1 sm:mt-0 sm:col-span-2">
                      <Badge variant={vehicle.status === VehicleStatus.AVAILABLE ? 'default' : 'secondary'}>
                        {vehicle.status ? t(`fleet:vehicles.${vehicle.status.toLowerCase().replace('_', '')}`) : t('fleet:vehicles.available')}
                      </Badge>
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.color')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.color || t('fleet:vehicles.notSpecified')}</dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.mileage')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {vehicle.mileage ? `${vehicle.mileage.toLocaleString()} km` : t('fleet:vehicles.notAvailable')}
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.purchaseDate')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {vehicle.purchaseDate ? formatDate(vehicle.purchaseDate) : t('fleet:vehicles.notAvailable')}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Vehicle-Specific Information */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                  {vehicle.vehicleType === VehicleType.TRUCK ? (
                    <Settings className="h-5 w-5 text-blue-600" />
                  ) : (
                    <Package className="h-5 w-5 text-green-600" />
                  )}
                  {vehicle.vehicleType === VehicleType.TRUCK ? t('fleet:vehicles.truckSpecifications') : t('fleet:vehicles.trailerSpecifications')}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {vehicle.vehicleType === VehicleType.TRUCK
                    ? t('fleet:vehicles.truckSpecificationsDescription')
                    : t('fleet:vehicles.trailerSpecificationsDescription')
                  }
                </p>
              </div>
              <div className="border-t border-gray-200">
                <dl>
                  {vehicle.vehicleType === VehicleType.TRUCK ? (
                    <>
                      {/* Truck-specific fields */}
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.engineType')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.engineType || t('fleet:vehicles.notSpecified')}</dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.transmission')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.transmission || t('fleet:vehicles.notSpecified')}</dd>
                      </div>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.fuelCapacity')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.fuelCapacity ? `${vehicle.fuelCapacity} L` : t('fleet:vehicles.notSpecified')}
                        </dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.axleConfiguration')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.axleConfiguration || t('fleet:vehicles.notSpecified')}</dd>
                      </div>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.cabConfiguration')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{vehicle.cabConfiguration || t('fleet:vehicles.notSpecified')}</dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.fuelType')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.fuelType ? t(`fleet:vehicles.${vehicle.fuelType.toLowerCase()}`) : t('fleet:vehicles.notSpecified')}
                        </dd>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* Trailer-specific fields */}
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.trailerType')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.trailerType ? t(`fleet:vehicles.${vehicle.trailerType.toLowerCase().replace('_', '')}`) : t('fleet:vehicles.notSpecified')}
                        </dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.cargoCapacity')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.cargoCapacity ? `${vehicle.cargoCapacity} m³` : t('fleet:vehicles.notSpecified')}
                        </dd>
                      </div>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.maxWeight')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.maxWeight ? `${vehicle.maxWeight} kg` : t('fleet:vehicles.notSpecified')}
                        </dd>
                      </div>
                      <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.dimensions')}</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {vehicle.length || vehicle.width || vehicle.height ? (
                            <div className="space-y-1">
                              {vehicle.length && <div>{t('fleet:vehicles.length')}: {vehicle.length} m</div>}
                              {vehicle.width && <div>{t('fleet:vehicles.width')}: {vehicle.width} m</div>}
                              {vehicle.height && <div>{t('fleet:vehicles.height')}: {vehicle.height} m</div>}
                            </div>
                          ) : (
                            t('fleet:vehicles.notSpecified')
                          )}
                        </dd>
                      </div>
                      <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">{t('fleet:vehicles.hasRefrigeration')}</dt>
                        <dd className="mt-1 sm:mt-0 sm:col-span-2">
                          <Badge variant={vehicle.hasRefrigeration ? 'default' : 'secondary'}>
                            {vehicle.hasRefrigeration ? t('fleet:vehicles.yes') : t('fleet:vehicles.no')}
                          </Badge>
                        </dd>
                      </div>
                    </>
                  )}
                </dl>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Service History Tab */}
        <TabsContent value="service">
          <div className="space-y-6">
            {/* Service History Header */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                    <Wrench className="h-5 w-5 text-blue-600" />
                    {t('fleet:vehicles.serviceHistory')}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">{t('fleet:vehicles.serviceHistoryDescription')}</p>
                </div>
                <Button
                  onClick={() => setShowAddServiceDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t('fleet:service.addService')}
                </Button>
              </div>
            </div>

            {/* Service Records */}
            {loadingMaintenance ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">{t('fleet:service.loading')}</p>
                </div>
              </div>
            ) : maintenanceLogs.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <Wrench className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('fleet:service.noRecordsFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('fleet:service.noRecordsDescription')}</p>
                  <div className="mt-6">
                    <Button
                      onClick={() => setShowAddServiceDialog(true)}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      {t('fleet:service.addFirstRecord')}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.serviceDate')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.serviceType')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.category')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.description')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.cost')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:service.status')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:table.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {maintenanceLogs.map((record) => (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(record.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant={record.type === 'PREVENTIVE' ? 'default' : record.type === 'REPAIR' ? 'destructive' : 'secondary'}>
                              {t(`fleet:service.${record.type.toLowerCase()}`)}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {t(`fleet:categories.${record.category.toLowerCase().replace('_', '')}`)}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                            {record.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {record.cost ? formatCurrency(record.cost) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant={
                              record.status === 'COMPLETED' ? 'default' :
                              record.status === 'IN_PROGRESS' ? 'secondary' :
                              record.status === 'SCHEDULED' ? 'outline' : 'destructive'
                            }>
                              {t(`fleet:service.${record.status.toLowerCase().replace('_', '')}`)}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditServiceRecord(record)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              {t('fleet:table.edit')}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteServiceRecord(record.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              {t('fleet:table.delete')}
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Insurance Policies Tab */}
        <TabsContent value="insurance">
          <div className="space-y-6">
            {/* Insurance Policies Header */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    {t('fleet:insurance.title')}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">{t('fleet:insurance.description')}</p>
                </div>
                <Button
                  onClick={() => setShowAddInsuranceDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t('fleet:insurance.addInsurance')}
                </Button>
              </div>
            </div>

            {/* Insurance Policies */}
            {loadingPolicies ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">{t('fleet:insurance.loading')}</p>
                </div>
              </div>
            ) : policies.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <Shield className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('fleet:insurance.noPoliciesFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('fleet:insurance.noPoliciesDescription')}</p>
                  <div className="mt-6">
                    <Button
                      onClick={() => setShowAddInsuranceDialog(true)}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      {t('fleet:insurance.addFirstPolicy')}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.policyNumber')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.provider')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.policyType')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.coveragePeriod')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.premium')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:insurance.status')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:table.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {policies.map((policy) => {
                        const daysUntilExpiry = Math.ceil((new Date(policy.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                        const isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry > 0;
                        const isExpired = daysUntilExpiry <= 0;

                        return (
                          <tr key={policy.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {policy.policyNumber}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {policy.provider}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline">
                                {t(`fleet:insurance.${policy.type.toLowerCase().replace('_', '')}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div className="space-y-1">
                                <div>{formatDate(policy.startDate)} - {formatDate(policy.endDate)}</div>
                                {isExpired ? (
                                  <div className="text-red-600 text-xs font-medium">
                                    {t('fleet:table.expired')}
                                  </div>
                                ) : isExpiringSoon ? (
                                  <div className="text-yellow-600 text-xs font-medium">
                                    {t('fleet:insurance.expiresInDays', { days: daysUntilExpiry })}
                                  </div>
                                ) : null}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(policy.premium)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant={
                                policy.status === 'ACTIVE' ? 'default' :
                                policy.status === 'EXPIRED' ? 'destructive' :
                                policy.status === 'RENEWAL_DUE' ? 'secondary' : 'outline'
                              }>
                                {t(`fleet:insurance.${policy.status.toLowerCase().replace('_', '')}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditInsurancePolicy(policy)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                {t('fleet:table.edit')}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteInsurancePolicy(policy.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                {t('fleet:table.delete')}
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Vehicle Reviews & Inspections Tab */}
        <TabsContent value="reviews">
          <div className="space-y-6">
            {/* Reviews Header */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-600" />
                    {t('fleet:reviews.title')}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">{t('fleet:reviews.description')}</p>
                </div>
                <Button
                  onClick={() => setShowAddReviewDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t('fleet:reviews.scheduleReview')}
                </Button>
              </div>
            </div>

            {/* Vehicle Reviews */}
            {loadingReviews ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">{t('fleet:reviews.loading')}</p>
                </div>
              </div>
            ) : reviews.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <Star className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('fleet:reviews.noReviewsFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('fleet:reviews.noReviewsDescription')}</p>
                  <div className="mt-6">
                    <Button
                      onClick={() => setShowAddReviewDialog(true)}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      {t('fleet:reviews.scheduleFirstReview')}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.reviewType')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.scheduledDate')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.completedDate')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.inspector')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.result')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:reviews.nextReviewDate')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:table.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {reviews.map((review) => {
                        const isOverdue = review.scheduledDate && new Date(review.scheduledDate) < new Date() && review.status === 'SCHEDULED';
                        const isUpcoming = review.scheduledDate && new Date(review.scheduledDate) <= new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) && review.status === 'SCHEDULED';

                        return (
                          <tr key={review.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline">
                                {t(`fleet:reviews.${review.reviewType?.toLowerCase().replace('_', '') || 'annualsafetyinspection'}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div className="space-y-1">
                                <div>{formatDate(review.scheduledDate)}</div>
                                {isOverdue && (
                                  <div className="text-red-600 text-xs font-medium">
                                    {t('fleet:reviews.overdue')}
                                  </div>
                                )}
                                {isUpcoming && !isOverdue && (
                                  <div className="text-yellow-600 text-xs font-medium">
                                    {t('fleet:reviews.upcoming')}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {review.completedDate ? formatDate(review.completedDate) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {review.reviewBy || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant={
                                review.status === 'COMPLETED' ? 'default' :
                                review.status === 'FAILED' ? 'destructive' :
                                review.status === 'IN_PROGRESS' ? 'secondary' :
                                review.status === 'SCHEDULED' ? 'outline' : 'outline'
                              }>
                                {t(`fleet:reviews.${review.status?.toLowerCase().replace('_', '') || 'scheduled'}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {review.nextReviewDate ? formatDate(review.nextReviewDate) : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditReview(review)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                {t('fleet:table.edit')}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteReview(review.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                {t('fleet:table.delete')}
                              </Button>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Driver Assignments Tab */}
        <TabsContent value="assignments">
          <div className="space-y-6">
            {/* Assignments Header */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900 flex items-center gap-2">
                    <Users className="h-5 w-5 text-purple-600" />
                    {t('fleet:assignments.title')}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">{t('fleet:assignments.description')}</p>
                </div>
                <Button
                  onClick={() => setShowAddAssignmentDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t('fleet:assignments.createAssignment')}
                </Button>
              </div>
            </div>

            {/* Vehicle Assignments */}
            {loadingAssignments ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">{t('fleet:assignments.loading')}</p>
                </div>
              </div>
            ) : assignments.length === 0 ? (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">{t('fleet:assignments.noAssignmentsFound')}</h3>
                  <p className="mt-1 text-sm text-gray-500">{t('fleet:assignments.noAssignmentsDescription')}</p>
                  <div className="mt-6">
                    <Button
                      onClick={() => setShowAddAssignmentDialog(true)}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      {t('fleet:assignments.createFirstAssignment')}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:assignments.driver')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:assignments.assignmentPeriod')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:assignments.type')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:assignments.priority')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:assignments.status')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('fleet:table.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {assignments.map((assignment) => {
                        const isActive = assignment.status === 'ACTIVE';
                        const isExpired = assignment.endDate && new Date(assignment.endDate) < new Date();
                        const isExpiringSoon = assignment.endDate &&
                          new Date(assignment.endDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) &&
                          new Date(assignment.endDate) > new Date();

                        return (
                          <tr key={assignment.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                    <Users className="h-5 w-5 text-purple-600" />
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {assignment.driver ? `${assignment.driver.firstName} ${assignment.driver.lastName}` : 'Unknown Driver'}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {assignment.driver?.email || 'No email'}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div className="space-y-1">
                                <div>
                                  <span className="font-medium">{t('fleet:assignments.start')}:</span> {formatDate(assignment.startDate)}
                                </div>
                                {assignment.endDate && (
                                  <div>
                                    <span className="font-medium">{t('fleet:assignments.end')}:</span> {formatDate(assignment.endDate)}
                                  </div>
                                )}
                                {isExpired && (
                                  <div className="text-red-600 text-xs font-medium">
                                    {t('fleet:assignments.expired')}
                                  </div>
                                )}
                                {isExpiringSoon && !isExpired && (
                                  <div className="text-yellow-600 text-xs font-medium">
                                    {t('fleet:assignments.expiringSoon')}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline">
                                {t(`fleet:assignments.${assignment.type?.toLowerCase() || 'regular'}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant={
                                assignment.priority === 'HIGH' ? 'destructive' :
                                assignment.priority === 'URGENT' ? 'destructive' :
                                assignment.priority === 'NORMAL' ? 'default' : 'secondary'
                              }>
                                {t(`fleet:assignments.${assignment.priority?.toLowerCase() || 'normal'}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant={
                                assignment.status === 'ACTIVE' ? 'default' :
                                assignment.status === 'COMPLETED' ? 'secondary' :
                                assignment.status === 'CANCELLED' ? 'destructive' : 'outline'
                              }>
                                {t(`fleet:assignments.${assignment.status?.toLowerCase() || 'active'}`)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              {isActive && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleCompleteAssignment(assignment.id)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  {t('fleet:assignments.complete')}
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditAssignment(assignment)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                {t('fleet:table.edit')}
                              </Button>
                              {isActive && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleCancelAssignment(assignment.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  {t('fleet:assignments.cancel')}
                                </Button>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Edit Vehicle Dialog */}
      {showEditDialog && (
        <AddVehicleDialog
          vehicle={vehicle}
          onSubmit={handleUpdateVehicle}
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
        />
      )}

      {/* Add/Edit Service Record Dialog */}
      <AddServiceRecordDialog
        open={showAddServiceDialog}
        onOpenChange={(open) => {
          setShowAddServiceDialog(open);
          if (!open) {
            setEditingServiceRecord(null);
          }
        }}
        onSubmit={handleServiceRecordSubmit}
        vehicleId={vehicleId}
        editingRecord={editingServiceRecord}
      />

      {/* Add/Edit Insurance Policy Dialog */}
      <AddInsuranceDialog
        open={showAddInsuranceDialog}
        onOpenChange={(open) => {
          setShowAddInsuranceDialog(open);
          if (!open) {
            setEditingInsurancePolicy(null);
          }
        }}
        onSubmit={handleInsurancePolicySubmit}
        vehicleId={vehicleId}
        editingPolicy={editingInsurancePolicy}
      />

      {/* Add/Edit Review Dialog */}
      <AddReviewDialog
        open={showAddReviewDialog}
        onOpenChange={(open) => {
          setShowAddReviewDialog(open);
          if (!open) {
            setEditingReview(null);
          }
        }}
        onSubmit={handleReviewSubmit}
        vehicleId={vehicleId}
        editingReview={editingReview}
      />

      {/* Add/Edit Assignment Dialog */}
      <AddAssignmentDialog
        open={showAddAssignmentDialog}
        onOpenChange={(open) => {
          setShowAddAssignmentDialog(open);
          if (!open) {
            setEditingAssignment(null);
          }
        }}
        onSubmit={handleAssignmentSubmit}
        vehicleId={vehicleId}
        editingAssignment={editingAssignment}
      />
    </div>
  );
};
