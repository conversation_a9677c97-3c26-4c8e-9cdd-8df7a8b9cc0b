'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/auth-context';
import { ConsistentContainer } from '@/components/ui/consistent-layout';
import { NavigationBar } from '@/components/navigation-bar';
import { ModuleNavigation, getCurrentModule } from '@/components/module-navigation';
import { hasModuleAccess, hasSubmoduleAccess } from '@/lib/rbac';

// Base navigation structure for all modules (simplified - submenus handled by ModuleNavigation)
// Navigation items will be translated dynamically
export const getBaseNavigation = (t: any) => [
  { name: t('navigation:dashboard.title'), href: '/dashboard' },
  { name: t('navigation:main.fleet'), href: '/fleet', module: 'fleet' },
  { name: t('navigation:main.drivers'), href: '/drivers', module: 'drivers' },
  { name: t('navigation:main.trips'), href: '/trips', module: 'trips' },
  { name: t('navigation:main.businessPartners'), href: '/business-partners', module: 'business-partners' },
  { name: t('navigation:main.fuel'), href: '/fuel', module: 'fuel' },
  { name: t('navigation:main.documents'), href: '/documents', module: 'documents' },
];

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const router = useRouter();
  const { t } = useTranslation(['navigation', 'common']);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  // Get current module for sub-navigation
  const currentModule = getCurrentModule(pathname);

  // Filter navigation based on user role
  const navigation = useMemo(() => {
    if (!user) return [];

    return getBaseNavigation(t).filter(item =>
      !item.module || hasModuleAccess(user.role, item.module as any)
    );
  }, [user, t]);

  const isActiveRoute = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };

  const toggleSubmenu = (name: string) => {
    setActiveSubmenu(activeSubmenu === name ? null : name);
  };

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/login');
    }
  }, [user, router]);

  // Don't render the layout if user is not authenticated
  if (!user) {
    return null;
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <NavigationBar
        navigation={navigation}
        isActiveRoute={isActiveRoute}
        toggleSubmenu={toggleSubmenu}
        activeSubmenu={activeSubmenu}
        mobileMenuOpen={mobileMenuOpen}
        setMobileMenuOpen={setMobileMenuOpen}
        handleLogout={handleLogout}
      />

      {/* Module-specific sub-navigation */}
      {currentModule && (
        <ModuleNavigation module={currentModule} />
      )}

      <main className="py-10">
        <ConsistentContainer>
          {children}
        </ConsistentContainer>
      </main>
    </div>
  );
}
