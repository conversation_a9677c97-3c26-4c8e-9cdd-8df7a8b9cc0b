// Vehicle Components
export { AddVehicleDialog } from './vehicles/add-vehicle-dialog';

// Driver Components
// Note: Driver components are now integrated into single-page architecture

// Trip Components
export { TripForm } from './trips/trip-form';
export { PartnerLocationSelector } from './trips/partner-location-selector';

// Business Partners Components
export { BusinessPartnersManagement } from './business-partners/business-partners-management';
export { AddBusinessPartnerDialog } from './business-partners/add-business-partner-dialog';
export { BusinessPartnerDetailsDialog } from './business-partners/business-partner-details-dialog';
export { AddLocationDialog } from './business-partners/add-location-dialog';

// Maintenance Components
export { EnhancedMaintenanceDashboard } from './maintenance/enhanced-maintenance-dashboard';
export { MaintenanceRecommendations, EnhancedMaintenanceForm } from './maintenance/maintenance-recommendations';

// Layout Components
export { default as AppLayout } from './app-layout';
export { NavigationBar } from './navigation-bar';

// Common Components
export { RoleSwitcher } from './role-switcher';
