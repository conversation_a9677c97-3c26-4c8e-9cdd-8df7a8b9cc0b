'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import { RoleSwitcher } from '@/components/role-switcher';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { ConsistentContainer } from '@/components/ui/consistent-layout';

interface NavigationProps {
  navigation: any[];
  isActiveRoute: (href: string) => boolean;
  toggleSubmenu: (name: string) => void;
  activeSubmenu: string | null;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  handleLogout: () => void;
}

export function NavigationBar({
  navigation,
  isActiveRoute,
  toggleSubmenu,
  activeSubmenu,
  mobileMenuOpen,
  setMobileMenuOpen,
  handleLogout
}: NavigationProps) {
  const { user } = useAuth();
  const { t } = useTranslation('common');
  
  return (
    <nav className="bg-white shadow-sm z-10">
      <ConsistentContainer>
        <div className="flex h-16 justify-between items-center">
          {/* Logo and desktop navigation */}
          <div className="flex">
            <div className="flex flex-shrink-0 items-center">
              <span className="text-xl font-bold text-gray-800">Fleet Fusion</span>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-6">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium ${
                    isActiveRoute(item.href)
                      ? 'border-blue-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
          
          {/* Mobile menu button */}
          <div className="flex items-center sm:hidden">
            <button 
              type="button" 
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-700 focus:outline-none"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
          
          {/* User profile section */}
          <div className="flex items-center">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                {user?.firstName} {user?.lastName}
              </span>
              {/* Language Switcher */}
              <LanguageSwitcher />
              {/* Role Switcher for testing */}
              <div className="hidden md:block">
                {process.env.NODE_ENV !== 'production' && (
                  <RoleSwitcher />
                )}
              </div>
              <Button
                variant="outline"
                onClick={handleLogout}
              >
                {t('navigation:main.logout')}
              </Button>
            </div>
          </div>
        </div>
      </ConsistentContainer>
      
      {/* Mobile menu */}
      <div className={`sm:hidden ${mobileMenuOpen ? 'block' : 'hidden'}`}>
        <ConsistentContainer className="space-y-1 pb-3 pt-2">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`block py-2 px-2 border-l-4 text-base font-medium ${
                isActiveRoute(item.href)
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-700'
              }`}
            >
              {item.name}
            </Link>
          ))}
        </ConsistentContainer>
      </div>
    </nav>
  );
}
