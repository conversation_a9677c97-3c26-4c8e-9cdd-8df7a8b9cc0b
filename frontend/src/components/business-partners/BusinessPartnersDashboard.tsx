import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, TrendingUp, TrendingDown, Users, Package, Truck, BarChart3, RefreshCw } from 'lucide-react';
import { Pie<PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { getAuthHeaders } from '@/lib/auth-headers';

interface DashboardData {
  totalPartners: number;
  totalLocations: number;
  activeShippers: number;
  activeLogistics: number;
  totalTrips: number;
  monthlyGrowth: number;
  partnersByType: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  topPartners: Array<{
    id: string;
    name: string;
    type: string;
    totalTrips: number;
    locations: number;
  }>;
  monthlyActivity: Array<{
    month: string;
    trips: number;
    newPartners: number;
  }>;
}

export const BusinessPartnersDashboard: React.FC = () => {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // This would be replaced with actual API calls
      const mockData: DashboardData = {
        totalPartners: 45,
        totalLocations: 128,
        activeShippers: 28,
        activeLogistics: 17,
        totalTrips: 1250,
        monthlyGrowth: 12.5,
        partnersByType: [
          { name: t('businessPartners:types.shipper'), value: 28, color: '#0088FE' },
          { name: t('businessPartners:types.logistics'), value: 17, color: '#00C49F' },
        ],
        topPartners: [
          { id: '1', name: 'Global Logistics Corp', type: 'LOGISTICS_PARTNER', totalTrips: 245, locations: 8 },
          { id: '2', name: 'Metro Shipping Ltd', type: 'SHIPPER', totalTrips: 198, locations: 12 },
          { id: '3', name: 'Express Delivery Co', type: 'LOGISTICS_PARTNER', totalTrips: 156, locations: 6 },
          { id: '4', name: 'Industrial Freight', type: 'SHIPPER', totalTrips: 134, locations: 9 },
          { id: '5', name: 'Regional Transport', type: 'LOGISTICS_PARTNER', totalTrips: 112, locations: 4 },
        ],
        monthlyActivity: [
          { month: 'Jan', trips: 180, newPartners: 2 },
          { month: 'Feb', trips: 165, newPartners: 1 },
          { month: 'Mar', trips: 220, newPartners: 3 },
          { month: 'Apr', trips: 195, newPartners: 2 },
          { month: 'May', trips: 240, newPartners: 4 },
          { month: 'Jun', trips: 250, newPartners: 3 },
        ],
      };
      setData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('businessPartners:dashboard.loadingDashboard')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('businessPartners:dashboard.noDashboardData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('businessPartners:dashboard.title')}</h2>
          <p className="text-sm text-muted-foreground">{t('businessPartners:dashboard.description')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.totalPartners')}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalPartners}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.vsLastMonth')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.totalLocations')}</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalLocations}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.pickupDeliveryPoints')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.activeShippers')}</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeShippers}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.shippingPartners')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.logisticsPartners')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeLogistics}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.logisticsProviders')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Partner Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('businessPartners:dashboard.charts.partnerDistribution.title')}</CardTitle>
            <CardDescription>{t('businessPartners:dashboard.charts.partnerDistribution.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.partnersByType}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.partnersByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>{t('businessPartners:dashboard.charts.monthlyActivity.title')}</CardTitle>
            <CardDescription>{t('businessPartners:dashboard.charts.monthlyActivity.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.monthlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Line yAxisId="left" type="monotone" dataKey="trips" stroke="#8884d8" strokeWidth={2} name={t('businessPartners:dashboard.charts.trips')} />
                  <Line yAxisId="right" type="monotone" dataKey="newPartners" stroke="#82ca9d" strokeWidth={2} name={t('businessPartners:dashboard.charts.newPartners')} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Partners */}
      <Card>
        <CardHeader>
          <CardTitle>{t('businessPartners:dashboard.topPartners.title')}</CardTitle>
          <CardDescription>{t('businessPartners:dashboard.topPartners.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.topPartners.map((partner, index) => (
              <div key={partner.id} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="outline">#{index + 1}</Badge>
                  <div>
                    <p className="font-medium">{partner.name}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Badge variant={partner.type === 'SHIPPER' ? 'default' : 'secondary'}>
                        {partner.type === 'SHIPPER' ? t('businessPartners:types.shipper') : t('businessPartners:types.logistics')}
                      </Badge>
                      <span>• {partner.locations} {t('businessPartners:dashboard.locations')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{partner.totalTrips} {t('businessPartners:dashboard.trips')}</p>
                  <p className="text-sm text-muted-foreground">{t('businessPartners:dashboard.totalActivity')}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
