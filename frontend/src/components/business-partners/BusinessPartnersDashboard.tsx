import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, TrendingUp, TrendingDown, Users, Package, Truck, BarChart3, RefreshCw } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { BusinessPartnersService } from '@/lib/api/business-partners-service';
import { TripService } from '@/lib/api/trip-service';

interface DashboardData {
  totalPartners: number;
  totalLocations: number;
  activeShippers: number;
  activeLogistics: number;
  totalTrips: number;
  monthlyGrowth: number;
  partnersByType: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  topPartners: Array<{
    id: string;
    name: string;
    type: string;
    totalTrips: number;
    locations: number;
  }>;
  monthlyActivity: Array<{
    month: string;
    trips: number;
    newPartners: number;
  }>;
}

// Helper function to generate monthly activity statistics
const generateMonthlyActivity = (partners: any[], trips: any[]) => {
  if (!Array.isArray(partners) || !Array.isArray(trips)) return [];

  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const currentDate = new Date();
  const stats = [];

  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    const monthName = months[date.getMonth()];

    // Count new partners created in this month
    const newPartners = partners.filter(partner => {
      if (!partner?.createdAt) return false;
      try {
        const partnerDate = new Date(partner.createdAt);
        return partnerDate.getMonth() === date.getMonth() && partnerDate.getFullYear() === date.getFullYear();
      } catch {
        return false;
      }
    }).length;

    // Count trips involving partners in this month
    const monthTrips = trips.filter(trip => {
      if (!trip?.startTime && !trip?.createdAt) return false;
      if (!trip?.pickupPartnerId && !trip?.deliveryPartnerId) return false;
      try {
        const tripDate = new Date(trip.startTime || trip.createdAt);
        return tripDate.getMonth() === date.getMonth() && tripDate.getFullYear() === date.getFullYear();
      } catch {
        return false;
      }
    }).length;

    stats.push({
      month: monthName,
      trips: monthTrips,
      newPartners
    });
  }

  return stats;
};

// Helper function to generate top partners based on trip activity
const generateTopPartners = (partners: any[], trips: any[]) => {
  if (!Array.isArray(partners) || !Array.isArray(trips)) return [];

  return partners
    .map(partner => {
      // Count trips where this partner is involved (pickup or delivery)
      const partnerTrips = trips.filter(trip =>
        trip?.pickupPartnerId === partner?.id || trip?.deliveryPartnerId === partner?.id
      );

      const totalTrips = partnerTrips.length;
      const locations = partner?.locations?.length || 0;

      return {
        id: partner.id,
        name: partner.name || 'Unknown Partner',
        type: partner.type || 'UNKNOWN',
        totalTrips,
        locations
      };
    })
    .filter(partner => partner.totalTrips > 0) // Only include partners with trips
    .sort((a, b) => b.totalTrips - a.totalTrips) // Sort by trip count
    .slice(0, 5); // Top 5 partners
};

export const BusinessPartnersDashboard: React.FC = () => {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Fetch real data from multiple API endpoints with proper error handling
      const [
        partnersResponse,
        tripsResponse
      ] = await Promise.allSettled([
        BusinessPartnersService.getBusinessPartners().catch(() => []),
        TripService.getTrips().catch(() => [])
      ]);

      // Extract data with null safety
      const partners = partnersResponse.status === 'fulfilled' ? (partnersResponse.value || []) : [];
      const trips = tripsResponse.status === 'fulfilled' ? (tripsResponse.value || []) : [];

      // Calculate partner statistics with null safety
      const totalPartners = partners.length;
      const activeShippers = partners.filter(p => p?.type === 'SHIPPER' && p?.status === 'ACTIVE').length;
      const activeLogistics = partners.filter(p => p?.type === 'LOGISTICS_PARTNER' && p?.status === 'ACTIVE').length;

      // Calculate total locations across all partners
      const totalLocations = partners.reduce((sum, partner) => sum + (partner?.locations?.length || 0), 0);

      // Calculate total trips involving business partners
      const totalTrips = trips.filter(trip => trip?.pickupPartnerId || trip?.deliveryPartnerId).length;

      // Calculate monthly growth (comparing current vs previous month partners)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const currentMonthPartners = partners.filter(partner => {
        if (!partner?.createdAt) return false;
        try {
          const partnerDate = new Date(partner.createdAt);
          return partnerDate.getMonth() === currentMonth && partnerDate.getFullYear() === currentYear;
        } catch {
          return false;
        }
      });
      const previousMonthPartners = partners.filter(partner => {
        if (!partner?.createdAt) return false;
        try {
          const partnerDate = new Date(partner.createdAt);
          const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
          const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
          return partnerDate.getMonth() === prevMonth && partnerDate.getFullYear() === prevYear;
        } catch {
          return false;
        }
      });
      const monthlyGrowth = previousMonthPartners.length > 0
        ? ((currentMonthPartners.length - previousMonthPartners.length) / previousMonthPartners.length) * 100
        : 0;

      // Prepare chart data for partner type distribution
      const partnersByType = [
        { name: t('businessPartners:types.shipper'), value: activeShippers, color: '#0088FE' },
        { name: t('businessPartners:types.logistics'), value: activeLogistics, color: '#00C49F' },
      ];

      // Filter out types with zero partners
      const filteredPartnersByType = partnersByType.filter(item => item.value > 0);

      // Generate top partners and monthly activity
      const topPartners = generateTopPartners(partners, trips);
      const monthlyActivity = generateMonthlyActivity(partners, trips);

      const dashboardData: DashboardData = {
        totalPartners,
        totalLocations,
        activeShippers,
        activeLogistics,
        totalTrips,
        monthlyGrowth,
        partnersByType: filteredPartnersByType,
        topPartners,
        monthlyActivity,
      };

      setData(dashboardData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Fallback to empty data structure
      setData({
        totalPartners: 0,
        totalLocations: 0,
        activeShippers: 0,
        activeLogistics: 0,
        totalTrips: 0,
        monthlyGrowth: 0,
        partnersByType: [],
        topPartners: [],
        monthlyActivity: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('businessPartners:dashboard.loadingDashboard')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('businessPartners:dashboard.noDashboardData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('businessPartners:dashboard.title')}</h2>
          <p className="text-sm text-muted-foreground">{t('businessPartners:dashboard.description')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.totalPartners')}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalPartners}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.vsLastMonth')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.totalLocations')}</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalLocations}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.pickupDeliveryPoints')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.activeShippers')}</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeShippers}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.shippingPartners')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('businessPartners:dashboard.metrics.logisticsPartners')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeLogistics}</div>
            <p className="text-xs text-muted-foreground">{t('businessPartners:dashboard.logisticsProviders')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Partner Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('businessPartners:dashboard.charts.partnerDistribution.title')}</CardTitle>
            <CardDescription>{t('businessPartners:dashboard.charts.partnerDistribution.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.partnersByType}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.partnersByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>{t('businessPartners:dashboard.charts.monthlyActivity.title')}</CardTitle>
            <CardDescription>{t('businessPartners:dashboard.charts.monthlyActivity.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.monthlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Line yAxisId="left" type="monotone" dataKey="trips" stroke="#8884d8" strokeWidth={2} name={t('businessPartners:dashboard.charts.trips')} />
                  <Line yAxisId="right" type="monotone" dataKey="newPartners" stroke="#82ca9d" strokeWidth={2} name={t('businessPartners:dashboard.charts.newPartners')} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Partners */}
      <Card>
        <CardHeader>
          <CardTitle>{t('businessPartners:dashboard.topPartners.title')}</CardTitle>
          <CardDescription>{t('businessPartners:dashboard.topPartners.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.topPartners.map((partner, index) => (
              <div key={partner.id} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="outline">#{index + 1}</Badge>
                  <div>
                    <p className="font-medium">{partner.name}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Badge variant={partner.type === 'SHIPPER' ? 'default' : 'secondary'}>
                        {partner.type === 'SHIPPER' ? t('businessPartners:types.shipper') : t('businessPartners:types.logistics')}
                      </Badge>
                      <span>• {partner.locations} {t('businessPartners:dashboard.locations')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{partner.totalTrips} {t('businessPartners:dashboard.trips')}</p>
                  <p className="text-sm text-muted-foreground">{t('businessPartners:dashboard.totalActivity')}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
