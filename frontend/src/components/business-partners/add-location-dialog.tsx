import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MapPin, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/fixed-select';
import { useToast } from '@/components/ui/use-toast';

const createLocationSchema = (t: any) => z.object({
  name: z.string().min(1, t('businessPartners:addLocationDialog.validation.locationNameRequired')),
  type: z.enum(['PICKUP_POINT', 'DELIVERY_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER'], {
    required_error: t('businessPartners:addLocationDialog.validation.locationTypeRequired'),
  }),
  address: z.string().min(1, t('businessPartners:addLocationDialog.validation.addressRequired')),
  city: z.string().min(1, t('businessPartners:addLocationDialog.validation.cityRequired')),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().min(1, t('businessPartners:addLocationDialog.validation.countryRequired')).default('Poland'),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email(t('businessPartners:addLocationDialog.validation.invalidEmail')).optional().or(z.literal('')),
  operatingHours: z.string().optional(),
  specialInstructions: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

type LocationFormData = z.infer<ReturnType<typeof createLocationSchema>>;

interface AddLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  partnerId: string;
  onAddLocation: (location: LocationFormData) => Promise<void>;
}

export function AddLocationDialog({
  open,
  onOpenChange,
  partnerId,
  onAddLocation,
}: AddLocationDialogProps) {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<LocationFormData>({
    resolver: zodResolver(createLocationSchema(t)),
    defaultValues: {
      name: '',
      type: 'PICKUP_POINT',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'Poland',
      contactPerson: '',
      phone: '',
      email: '',
      operatingHours: '',
      specialInstructions: '',
      isActive: true,
      isDefault: false,
    },
  });

  const handleSubmit = async (data: LocationFormData) => {
    setIsSubmitting(true);
    try {
      // Clean up empty strings to undefined for optional fields
      const cleanedData = {
        ...data,
        state: data.state || undefined,
        postalCode: data.postalCode || undefined,
        contactPerson: data.contactPerson || undefined,
        phone: data.phone || undefined,
        email: data.email || undefined,
        operatingHours: data.operatingHours || undefined,
        specialInstructions: data.specialInstructions || undefined,
      };

      await onAddLocation(cleanedData);
      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error('Error adding location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:addLocationDialog.messages.error'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('businessPartners:addLocationDialog.title')}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:addLocationDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('businessPartners:addLocationDialog.fields.locationName')} *</Label>
              <Input
                id="name"
                {...form.register('name')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.locationName')}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">{t('businessPartners:addLocationDialog.fields.locationType')} *</Label>
              <Select
                value={form.watch('type')}
                onValueChange={(value: any) => form.setValue('type', value)}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder={t('businessPartners:addLocationDialog.placeholders.selectLocationType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PICKUP_POINT">{t('businessPartners:addLocationDialog.locationTypes.PICKUP_POINT')}</SelectItem>
                  <SelectItem value="DELIVERY_POINT">{t('businessPartners:addLocationDialog.locationTypes.DELIVERY_POINT')}</SelectItem>
                  <SelectItem value="WAREHOUSE">{t('businessPartners:addLocationDialog.locationTypes.WAREHOUSE')}</SelectItem>
                  <SelectItem value="DISTRIBUTION_CENTER">{t('businessPartners:addLocationDialog.locationTypes.DISTRIBUTION_CENTER')}</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.type && (
                <p className="text-sm text-red-600">{form.formState.errors.type.message}</p>
              )}
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('businessPartners:addLocationDialog.fields.address')}</h3>

            <div className="space-y-2">
              <Label htmlFor="address">{t('businessPartners:addLocationDialog.fields.address')} *</Label>
              <Input
                id="address"
                {...form.register('address')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.streetAddress')}
              />
              {form.formState.errors.address && (
                <p className="text-sm text-red-600">{form.formState.errors.address.message}</p>
              )}
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">{t('businessPartners:addLocationDialog.fields.city')} *</Label>
                <Input
                  id="city"
                  {...form.register('city')}
                  placeholder={t('businessPartners:addLocationDialog.placeholders.cityName')}
                />
                {form.formState.errors.city && (
                  <p className="text-sm text-red-600">{form.formState.errors.city.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">{t('businessPartners:addLocationDialog.fields.state')}</Label>
                <Input
                  id="state"
                  {...form.register('state')}
                  placeholder={t('businessPartners:addLocationDialog.placeholders.stateName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">{t('businessPartners:addLocationDialog.fields.postalCode')}</Label>
                <Input
                  id="postalCode"
                  {...form.register('postalCode')}
                  placeholder={t('businessPartners:addLocationDialog.placeholders.postalCode')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">{t('businessPartners:addLocationDialog.fields.country')} *</Label>
              <Input
                id="country"
                {...form.register('country')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.countryName')}
              />
              {form.formState.errors.country && (
                <p className="text-sm text-red-600">{form.formState.errors.country.message}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('businessPartners:locationDialog.sections.contactInfo')}</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactPerson">{t('businessPartners:addLocationDialog.fields.contactPerson')}</Label>
                <Input
                  id="contactPerson"
                  {...form.register('contactPerson')}
                  placeholder={t('businessPartners:addLocationDialog.placeholders.contactName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('businessPartners:addLocationDialog.fields.phone')}</Label>
                <Input
                  id="phone"
                  {...form.register('phone')}
                  placeholder={t('businessPartners:addLocationDialog.placeholders.phoneNumber')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('businessPartners:addLocationDialog.fields.email')}</Label>
              <Input
                id="email"
                type="email"
                {...form.register('email')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.emailAddress')}
              />
              {form.formState.errors.email && (
                <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('businessPartners:locationDialog.sections.operatingInfo')}</h3>

            <div className="space-y-2">
              <Label htmlFor="operatingHours">{t('businessPartners:addLocationDialog.fields.operatingHours')}</Label>
              <Input
                id="operatingHours"
                {...form.register('operatingHours')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.operatingHours')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialInstructions">{t('businessPartners:addLocationDialog.fields.specialInstructions')}</Label>
              <Textarea
                id="specialInstructions"
                {...form.register('specialInstructions')}
                placeholder={t('businessPartners:addLocationDialog.placeholders.specialInstructions')}
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isActive"
                  checked={form.watch('isActive')}
                  onCheckedChange={(checked) => form.setValue('isActive', !!checked)}
                />
                <Label htmlFor="isActive">{t('businessPartners:addLocationDialog.fields.isActive')}</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isDefault"
                  checked={form.watch('isDefault')}
                  onCheckedChange={(checked) => form.setValue('isDefault', !!checked)}
                />
                <Label htmlFor="isDefault">{t('businessPartners:addLocationDialog.fields.isDefault')}</Label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {t('businessPartners:addLocationDialog.actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t('businessPartners:addLocationDialog.actions.adding')}
                </>
              ) : (
                t('businessPartners:addLocationDialog.actions.addLocation')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
