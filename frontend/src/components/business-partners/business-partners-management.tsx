import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Search, Building2, MapPin, Phone, Mail, Edit, Trash2, Eye, Star, Users, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/fixed-select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { getAuthHeaders } from '@/lib/auth-headers';
import { AddBusinessPartnerDialog } from './add-business-partner-dialog';
import { AddLocationDialog } from './add-location-dialog';
import { EditLocationDialog } from './edit-location-dialog';
import { LocationDetailsDialog } from './location-details-dialog';
import { BusinessPartnerDetailsDialog } from './business-partner-details-dialog';

interface BusinessPartner {
  id: string;
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  locations: PartnerLocation[];
  _count: {
    tripPickups: number;
    tripDeliveries: number;
  };
}

interface PartnerLocation {
  id: string;
  partnerId: string;
  name: string;
  type: 'PICKUP_POINT' | 'DELIVERY_POINT' | 'WAREHOUSE' | 'DISTRIBUTION_CENTER';
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface BusinessPartnersManagementProps {
  onPartnerUpdated?: () => void;
}

export function BusinessPartnersManagement({ onPartnerUpdated }: BusinessPartnersManagementProps = {}) {
  const { t } = useTranslation(['businessPartners', 'partners', 'common']);
  const [partners, setPartners] = useState<BusinessPartner[]>([]);
  const [selectedPartnerId, setSelectedPartnerId] = useState<string>('');
  const [selectedPartner, setSelectedPartner] = useState<BusinessPartner | null>(null);
  const [locations, setLocations] = useState<PartnerLocation[]>([]);
  const [loading, setLoading] = useState(true);
  const [locationsLoading, setLocationsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddPartnerDialog, setShowAddPartnerDialog] = useState(false);
  const [showAddLocationDialog, setShowAddLocationDialog] = useState(false);
  const [editingLocation, setEditingLocation] = useState<PartnerLocation | null>(null);
  const [viewingLocation, setViewingLocation] = useState<PartnerLocation | null>(null);
  const [viewingPartner, setViewingPartner] = useState<BusinessPartner | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchPartners();
  }, []);

  useEffect(() => {
    if (selectedPartnerId) {
      const partner = partners.find(p => p.id === selectedPartnerId);
      setSelectedPartner(partner || null);
      if (partner) {
        setLocations(partner.locations || []);
      }
    } else {
      setSelectedPartner(null);
      setLocations([]);
    }
  }, [selectedPartnerId, partners]);

  const fetchPartners = async () => {
    try {
      setLoading(true);
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners?status=ACTIVE`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business partners');
      }

      const data = await response.json();
      setPartners(data);
    } catch (error) {
      console.error('Error fetching partners:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:messages.failedToLoadPartners'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddPartner = async (partnerData: any) => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners`, {
        method: 'POST',
        headers: {
          ...getAuthHeaders(),
        },
        body: JSON.stringify(partnerData),
      });

      if (!response.ok) {
        throw new Error('Failed to create business partner');
      }

      const newPartner = await response.json();

      // Add to local state
      setPartners(prev => [...prev, newPartner]);

      toast({
        title: t('common:success'),
        description: t('businessPartners:addDialog.messages.success'),
      });

      setShowAddPartnerDialog(false);
    } catch (error) {
      console.error('Error creating partner:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:addDialog.messages.error'),
        variant: 'destructive',
      });
    }
  };

  const handleAddLocation = async (locationData: any) => {
    if (!selectedPartnerId) return;

    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners/${selectedPartnerId}/locations`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(locationData),
      });

      if (!response.ok) {
        throw new Error('Failed to add location');
      }

      const newLocation = await response.json();

      // Update local state
      setLocations(prev => [...prev, newLocation]);
      setPartners(prev => prev.map(p =>
        p.id === selectedPartnerId
          ? { ...p, locations: [...(p.locations || []), newLocation] }
          : p
      ));

      toast({
        title: t('common:success'),
        description: t('businessPartners:messages.locationAddedSuccess'),
      });

      setShowAddLocationDialog(false);
    } catch (error) {
      console.error('Error adding location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:messages.failedToAddLocation'),
        variant: 'destructive',
      });
    }
  };

  const handleEditLocation = async (locationData: any) => {
    if (!editingLocation) return;

    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners/locations/${editingLocation.id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(locationData),
      });

      if (!response.ok) {
        throw new Error('Failed to update location');
      }

      const updatedLocation = await response.json();

      // Update local state
      setLocations(prev => prev.map(l => l.id === editingLocation.id ? updatedLocation : l));
      setPartners(prev => prev.map(p =>
        p.id === selectedPartnerId
          ? { ...p, locations: (p.locations || []).map(l => l.id === editingLocation.id ? updatedLocation : l) }
          : p
      ));

      toast({
        title: t('common:success'),
        description: t('businessPartners:messages.locationUpdatedSuccess'),
      });

      setEditingLocation(null);
    } catch (error) {
      console.error('Error updating location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:messages.failedToUpdateLocation'),
        variant: 'destructive',
      });
    }
  };

  const handleDeleteLocation = async (locationId: string) => {
    if (!confirm('Are you sure you want to delete this location?')) {
      return;
    }

    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners/locations/${locationId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to delete location');
      }

      // Update local state
      setLocations(prev => prev.filter(l => l.id !== locationId));
      setPartners(prev => prev.map(p =>
        p.id === selectedPartnerId
          ? { ...p, locations: (p.locations || []).filter(l => l.id !== locationId) }
          : p
      ));

      toast({
        title: t('common:success'),
        description: t('businessPartners:messages.locationDeletedSuccess'),
      });
    } catch (error) {
      console.error('Error deleting location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:messages.failedToDeleteLocation'),
        variant: 'destructive',
      });
    }
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'PICKUP_POINT': return t('businessPartners:locationTypes.PICKUP_POINT');
      case 'DELIVERY_POINT': return t('businessPartners:locationTypes.DELIVERY_POINT');
      case 'WAREHOUSE': return t('businessPartners:locationTypes.WAREHOUSE');
      case 'DISTRIBUTION_CENTER': return t('businessPartners:locationTypes.DISTRIBUTION_CENTER');
      default: return type;
    }
  };

  const getLocationTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'PICKUP_POINT': return 'default';
      case 'DELIVERY_POINT': return 'secondary';
      case 'WAREHOUSE': return 'outline';
      case 'DISTRIBUTION_CENTER': return 'outline';
      default: return 'secondary';
    }
  };

  // Filter partners based on search term
  const filteredPartners = partners.filter(partner => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    // Search in partner name, contact person
    const partnerMatch = partner.name.toLowerCase().includes(searchLower) ||
                        partner.contactPerson?.toLowerCase().includes(searchLower);

    // Search in location addresses and cities
    const locationMatch = partner.locations?.some(location =>
      location.address.toLowerCase().includes(searchLower) ||
      location.city.toLowerCase().includes(searchLower)
    );

    return partnerMatch || locationMatch;
  });

  // Filter locations based on search term
  const filteredLocations = locations.filter(location => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return location.name.toLowerCase().includes(searchLower) ||
           location.address.toLowerCase().includes(searchLower) ||
           location.city.toLowerCase().includes(searchLower);
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('businessPartners:management.title')}</h2>
          <p className="text-sm text-muted-foreground">
            {t('businessPartners:management.welcomeDescription')}
          </p>
        </div>
        <Button onClick={() => setShowAddPartnerDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {t('businessPartners:addDialog.actions.addPartner')}
        </Button>
      </div>

      {/* Partner Selection & Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Partner Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('businessPartners:businessPartner')}</label>
              <Select
                value={selectedPartnerId}
                onValueChange={setSelectedPartnerId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('businessPartners:placeholders.chooseBusinessPartner')} />
                </SelectTrigger>
                <SelectContent>
                  {filteredPartners.map((partner) => (
                    <SelectItem key={partner.id} value={partner.id}>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        {partner.name}
                        <Badge variant={partner.type === 'SHIPPER' ? 'default' : 'secondary'}>
                          {partner.type === 'SHIPPER' ? t('businessPartners:types.shipper') : t('businessPartners:types.logistics')}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('businessPartners:actions.search')}</label>
              <div className="flex gap-2">
                <Input
                  placeholder={t('businessPartners:placeholders.searchPartners')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Button variant="outline" size="icon">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Area */}
      {loading ? (
        <div className="text-center py-8">{t('businessPartners:management.loadingPartners')}</div>
      ) : !selectedPartnerId ? (
        /* No Partner Selected State */
        <Card>
          <CardContent className="text-center py-12">
            <div className="max-w-md mx-auto">
              <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
              <h3 className="text-xl font-semibold mb-2">{t('businessPartners:management.welcomeTitle')}</h3>
              <p className="text-muted-foreground mb-6">
                {t('businessPartners:management.welcomeDescription')}
              </p>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{partners.length}</div>
                  <div className="text-sm text-muted-foreground">{t('businessPartners:management.partners')}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {partners.reduce((sum, p) => sum + (p.locations?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">{t('businessPartners:management.locations')}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {partners.reduce((sum, p) => sum + (p._count?.tripPickups || 0) + (p._count?.tripDeliveries || 0), 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">{t('businessPartners:management.totalTrips')}</div>
                </div>
              </div>

              {/* Recent Activity */}
              {partners.length > 0 && (
                <div className="text-left">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    {t('businessPartners:management.mostActivePartners')}
                  </h4>
                  <div className="space-y-2">
                    {partners
                      .sort((a, b) => ((b._count?.tripPickups || 0) + (b._count?.tripDeliveries || 0)) -
                                     ((a._count?.tripPickups || 0) + (a._count?.tripDeliveries || 0)))
                      .slice(0, 3)
                      .map((partner) => (
                        <div key={partner.id} className="flex items-center justify-between p-2 bg-muted rounded">
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{partner.name}</span>
                          </div>
                          <Badge variant="outline">
                            {(partner._count?.tripPickups || 0) + (partner._count?.tripDeliveries || 0)} {t('businessPartners:management.trips')}
                          </Badge>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Partner Selected - Show Locations */
        <div className="space-y-4">
          {/* Partner Info Header */}
          {selectedPartner && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Building2 className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <h2 className="text-xl font-semibold">{selectedPartner.name}</h2>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Badge variant={selectedPartner.type === 'SHIPPER' ? 'default' : 'secondary'}>
                          {selectedPartner.type === 'SHIPPER' ? t('businessPartners:types.shipper') : t('partners:types.logisticsPartner')}
                        </Badge>
                        {selectedPartner.contactPerson && (
                          <span>• {t('businessPartners:management.contact')}: {selectedPartner.contactPerson}</span>
                        )}
                        {selectedPartner.phone && (
                          <span>• {selectedPartner.phone}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingPartner(selectedPartner)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {t('businessPartners:actions.viewDetails')}
                    </Button>
                    <Badge variant="outline">
                      {t('businessPartners:management.locationCount', { count: locations.length })}
                    </Badge>
                    <Badge variant="outline">
                      {t('businessPartners:management.tripCount', { count: (selectedPartner._count?.tripPickups || 0) + (selectedPartner._count?.tripDeliveries || 0) })}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Locations Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  {t('businessPartners:management.locations')}
                </CardTitle>
                <Button onClick={() => setShowAddLocationDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('businessPartners:actions.addLocation')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {filteredLocations.length === 0 ? (
                <div className="text-center py-8">
                  <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('businessPartners:management.noLocationsFound')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? t('businessPartners:management.noLocationsMatch') : t('businessPartners:management.noLocationsYet')}
                  </p>
                  {!searchTerm && (
                    <Button onClick={() => setShowAddLocationDialog(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      {t('businessPartners:management.addFirstLocation')}
                    </Button>
                  )}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('businessPartners:management.tableHeaders.name')}</TableHead>
                      <TableHead>{t('businessPartners:management.tableHeaders.address')}</TableHead>
                      <TableHead>{t('businessPartners:management.tableHeaders.type')}</TableHead>
                      <TableHead>{t('businessPartners:management.tableHeaders.default')}</TableHead>
                      <TableHead className="text-right">{t('businessPartners:management.tableHeaders.actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLocations.map((location) => (
                      <TableRow key={location.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{location.name}</div>
                              {location.operatingHours && (
                                <div className="text-xs text-muted-foreground">
                                  {location.operatingHours}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div>{location.address}</div>
                            <div className="text-sm text-muted-foreground">
                              {location.city}{location.state && `, ${location.state}`}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getLocationTypeBadgeVariant(location.type)}>
                            {getLocationTypeLabel(location.type)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {location.isDefault && (
                            <Badge variant="outline" className="flex items-center gap-1 w-fit">
                              <Star className="h-3 w-3" />
                              {t('businessPartners:default')}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setViewingLocation(location)}
                              title={t('businessPartners:management.tooltips.viewDetails')}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setEditingLocation(location)}
                              title={t('businessPartners:management.tooltips.editLocation')}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteLocation(location.id)}
                              title={t('businessPartners:management.tooltips.deleteLocation')}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Dialogs */}
      <AddBusinessPartnerDialog
        open={showAddPartnerDialog}
        onOpenChange={setShowAddPartnerDialog}
        onAddPartner={handleAddPartner}
      />

      {selectedPartnerId && (
        <AddLocationDialog
          open={showAddLocationDialog}
          onOpenChange={setShowAddLocationDialog}
          partnerId={selectedPartnerId}
          onAddLocation={handleAddLocation}
        />
      )}

      {editingLocation && (
        <EditLocationDialog
          open={!!editingLocation}
          onOpenChange={(open) => !open && setEditingLocation(null)}
          location={editingLocation}
          onEditLocation={handleEditLocation}
        />
      )}

      {viewingLocation && (
        <LocationDetailsDialog
          open={!!viewingLocation}
          onOpenChange={(open) => !open && setViewingLocation(null)}
          location={viewingLocation}
        />
      )}

      {viewingPartner && (
        <BusinessPartnerDetailsDialog
          open={!!viewingPartner}
          onOpenChange={(open) => !open && setViewingPartner(null)}
          partner={viewingPartner}
          onPartnerUpdated={() => {
            fetchPartners();
            if (selectedPartnerId) {
              fetchLocations(selectedPartnerId);
            }
          }}
        />
      )}
    </div>
  );
}
