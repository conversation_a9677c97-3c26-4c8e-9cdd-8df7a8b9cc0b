import React from 'react';
import { useTranslation } from 'react-i18next';
import { MapPin, Phone, Mail, Clock, FileText, Star, Building2, CheckCircle, XCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface PartnerLocation {
  id: string;
  name: string;
  type: string;
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
  latitude?: number;
  longitude?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface LocationDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  location: PartnerLocation;
}

export function LocationDetailsDialog({
  open,
  onOpenChange,
  location,
}: LocationDetailsDialogProps) {
  const { t } = useTranslation(['businessPartners', 'common']);

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'PICKUP_POINT': return t('businessPartners:locationTypes.PICKUP_POINT');
      case 'DELIVERY_POINT': return t('businessPartners:locationTypes.DELIVERY_POINT');
      case 'WAREHOUSE': return t('businessPartners:locationTypes.WAREHOUSE');
      case 'DISTRIBUTION_CENTER': return t('businessPartners:locationTypes.DISTRIBUTION_CENTER');
      default: return type;
    }
  };

  const getLocationTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'PICKUP_POINT': return 'default';
      case 'DELIVERY_POINT': return 'secondary';
      case 'WAREHOUSE': return 'outline';
      case 'DISTRIBUTION_CENTER': return 'outline';
      default: return 'secondary';
    }
  };

  const formatAddress = () => {
    const parts = [location.address, location.city];
    if (location.state) parts.push(location.state);
    if (location.postalCode) parts.push(location.postalCode);
    if (location.country) parts.push(location.country);
    return parts.join(', ');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('businessPartners:locationDialog.title')}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:locationDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{location.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant={getLocationTypeBadgeVariant(location.type) as any}>
                        {getLocationTypeLabel(location.type)}
                      </Badge>
                      {location.isDefault && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {t('businessPartners:locationDialog.statuses.default')}
                        </Badge>
                      )}
                      <Badge variant={location.isActive ? 'default' : 'secondary'} className="flex items-center gap-1">
                        {location.isActive ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <XCircle className="h-3 w-3" />
                        )}
                        {location.isActive ? t('businessPartners:locationDialog.statuses.active') : t('businessPartners:locationDialog.statuses.inactive')}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                {t('businessPartners:locationDialog.sections.addressInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.fullAddress')}</h4>
                <p className="text-sm">{formatAddress()}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.city')}</h4>
                  <p className="text-sm">{location.city}</p>
                </div>
                {location.state && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.stateProvince')}</h4>
                    <p className="text-sm">{location.state}</p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                {location.postalCode && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.postalCode')}</h4>
                    <p className="text-sm">{location.postalCode}</p>
                  </div>
                )}
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.country')}</h4>
                  <p className="text-sm">{location.country}</p>
                </div>
              </div>

              {(location.latitude && location.longitude) && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.coordinates')}</h4>
                  <p className="text-sm">{location.latitude}, {location.longitude}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Information */}
          {(location.contactPerson || location.phone || location.email) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  {t('businessPartners:locationDialog.sections.contactInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {location.contactPerson && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.contactPerson')}</h4>
                    <p className="text-sm">{location.contactPerson}</p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {location.phone && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.phoneNumber')}</h4>
                      <p className="text-sm flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {location.phone}
                      </p>
                    </div>
                  )}

                  {location.email && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.emailAddress')}</h4>
                      <p className="text-sm flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {location.email}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Operating Information */}
          {(location.operatingHours || location.specialInstructions) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  {t('businessPartners:locationDialog.sections.operatingInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {location.operatingHours && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.operatingHours')}</h4>
                    <p className="text-sm">{location.operatingHours}</p>
                  </div>
                )}

                {location.specialInstructions && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.specialInstructions')}</h4>
                    <div className="bg-muted p-3 rounded-md">
                      <p className="text-sm">{location.specialInstructions}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          {(location.createdAt || location.updatedAt) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('businessPartners:locationDialog.sections.recordInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {location.createdAt && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.created')}</h4>
                      <p className="text-sm">{new Date(location.createdAt).toLocaleDateString()}</p>
                    </div>
                  )}

                  {location.updatedAt && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">{t('businessPartners:locationDialog.fields.lastUpdated')}</h4>
                      <p className="text-sm">{new Date(location.updatedAt).toLocaleDateString()}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
