import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Building2, MapPin, Phone, Mail, Eye, Edit, Trash2, Search, Plus, Package } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { getAuthHeaders } from '@/lib/auth-headers';
import { BusinessPartnerDetailsDialog } from './business-partner-details-dialog';
import { AddBusinessPartnerDialog } from './add-business-partner-dialog';

interface BusinessPartner {
  id: string;
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  locations: Array<{
    id: string;
    name: string;
    address: string;
    city: string;
    type: string;
  }>;
  _count: {
    tripPickups: number;
    tripDeliveries: number;
  };
}

interface BusinessPartnersLogisticsProps {
  onPartnerUpdated?: () => void;
}

export const BusinessPartnersLogistics: React.FC<BusinessPartnersLogisticsProps> = ({
  onPartnerUpdated
}) => {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [partners, setPartners] = useState<BusinessPartner[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewingPartner, setViewingPartner] = useState<BusinessPartner | null>(null);
  const [editingPartner, setEditingPartner] = useState<BusinessPartner | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { toast } = useToast();

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE': return t('businessPartners:logistics.status.ACTIVE');
      case 'INACTIVE': return t('businessPartners:logistics.status.INACTIVE');
      case 'SUSPENDED': return t('businessPartners:logistics.status.SUSPENDED');
      default: return status;
    }
  };

  const handleViewPartner = (partner: BusinessPartner) => {
    setViewingPartner(partner);
  };

  const handleEditPartner = (partner: BusinessPartner) => {
    setEditingPartner(partner);
  };

  const handlePartnerUpdated = () => {
    fetchLogisticsPartners();
    if (onPartnerUpdated) {
      onPartnerUpdated();
    }
  };

  useEffect(() => {
    fetchLogisticsPartners();
  }, []);

  const fetchLogisticsPartners = async () => {
    try {
      setLoading(true);
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners?type=LOGISTICS_PARTNER&status=ACTIVE`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch logistics partners');
      }

      const data = await response.json();
      setPartners(data);
    } catch (error) {
      console.error('Error fetching logistics partners:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:logistics.messages.loadFailed'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePartner = async (partnerId: string) => {
    if (!confirm(t('businessPartners:logistics.confirmDelete'))) {
      return;
    }

    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners/${partnerId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to delete logistics partner');
      }

      setPartners(prev => prev.filter(p => p.id !== partnerId));
      
      toast({
        title: t('common:success'),
        description: t('businessPartners:logistics.messages.deleteSuccess'),
      });

      if (onPartnerUpdated) {
        onPartnerUpdated();
      }
    } catch (error) {
      console.error('Error deleting logistics partner:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:logistics.messages.deleteFailed'),
        variant: 'destructive',
      });
    }
  };

  // Filter partners based on search term
  const filteredPartners = partners.filter(partner => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      partner.name.toLowerCase().includes(searchLower) ||
      partner.contactPerson?.toLowerCase().includes(searchLower) ||
      partner.email?.toLowerCase().includes(searchLower) ||
      partner.locations.some(location =>
        location.address.toLowerCase().includes(searchLower) ||
        location.city.toLowerCase().includes(searchLower)
      )
    );
  });

  if (loading) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('businessPartners:logistics.loading')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('businessPartners:logistics.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('businessPartners:logistics.description')}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline">
            {t('businessPartners:logistics.logisticsCount', { count: filteredPartners.length })}
          </Badge>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('businessPartners:addPartnerDialog.title')}
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-2">
            <Input
              placeholder={t('businessPartners:logistics.searchLogistics')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button variant="outline" size="icon">
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logistics Partners Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('businessPartners:logistics.tableTitle')}</CardTitle>
          <CardDescription>
            {t('businessPartners:logistics.tableDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredPartners.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t('businessPartners:logistics.noLogisticsFound')}</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? t('businessPartners:logistics.noLogisticsMatch') : t('businessPartners:logistics.noLogisticsYet')}
              </p>
              {!searchTerm && (
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('businessPartners:logistics.addFirstLogistics')}
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('businessPartners:logistics.tableHeaders.company')}</TableHead>
                  <TableHead>{t('businessPartners:logistics.tableHeaders.contact')}</TableHead>
                  <TableHead>{t('businessPartners:logistics.tableHeaders.locations')}</TableHead>
                  <TableHead>{t('businessPartners:logistics.tableHeaders.activity')}</TableHead>
                  <TableHead>{t('businessPartners:logistics.tableHeaders.status')}</TableHead>
                  <TableHead className="text-right">{t('businessPartners:logistics.tableHeaders.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPartners.map((partner) => (
                  <TableRow key={partner.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{partner.name}</div>
                          {partner.website && (
                            <div className="text-sm text-muted-foreground">{partner.website}</div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {partner.contactPerson && (
                          <div className="text-sm">{partner.contactPerson}</div>
                        )}
                        {partner.email && (
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Mail className="h-3 w-3" />
                            {partner.email}
                          </div>
                        )}
                        {partner.phone && (
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Phone className="h-3 w-3" />
                            {partner.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{t('businessPartners:logistics.locationCount', { count: partner.locations.length })}</span>
                      </div>
                      {partner.locations.length > 0 && (
                        <div className="text-sm text-muted-foreground mt-1">
                          {partner.locations[0].city}
                          {partner.locations.length > 1 && ` ${t('businessPartners:logistics.moreLocations', { count: partner.locations.length - 1 })}`}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{t('businessPartners:logistics.activity.pickups', { count: partner._count?.tripPickups || 0 })}</div>
                        <div className="text-muted-foreground">
                          {t('businessPartners:logistics.activity.deliveries', { count: partner._count?.tripDeliveries || 0 })}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          partner.status === 'ACTIVE' ? 'default' :
                          partner.status === 'INACTIVE' ? 'secondary' : 'destructive'
                        }
                      >
                        {getStatusLabel(partner.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewPartner(partner)}
                          title={t('businessPartners:logistics.tooltips.viewDetails')}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditPartner(partner)}
                          title={t('businessPartners:logistics.tooltips.editPartner')}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePartner(partner.id)}
                          title={t('businessPartners:logistics.tooltips.deletePartner')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      {viewingPartner && (
        <BusinessPartnerDetailsDialog
          open={!!viewingPartner}
          onOpenChange={(open) => !open && setViewingPartner(null)}
          partner={viewingPartner}
          onPartnerUpdated={handlePartnerUpdated}
        />
      )}

      {editingPartner && (
        <AddBusinessPartnerDialog
          open={!!editingPartner}
          onOpenChange={(open) => !open && setEditingPartner(null)}
          editingPartner={editingPartner}
          onPartnerAdded={handlePartnerUpdated}
        />
      )}

      {showAddDialog && (
        <AddBusinessPartnerDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onPartnerAdded={handlePartnerUpdated}
        />
      )}
    </div>
  );
};
