import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building2, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/fixed-select';
import { useToast } from '@/components/ui/use-toast';

const createPartnerSchema = (t: any) => z.object({
  name: z.string().min(1, t('businessPartners:addDialog.validation.partnerNameRequired')),
  type: z.enum(['SHIPPER', 'LOGISTICS_PARTNER'], {
    required_error: t('businessPartners:addDialog.validation.partnerTypeRequired'),
  }),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']).default('ACTIVE'),
  contactPerson: z.string().optional(),
  email: z.string().email(t('businessPartners:addDialog.validation.invalidEmail')).optional().or(z.literal('')),
  phone: z.string().optional(),
  website: z.string().url(t('businessPartners:addDialog.validation.invalidWebsite')).optional().or(z.literal('')),
  taxId: z.string().optional(),
  notes: z.string().optional(),
});

type PartnerFormData = z.infer<ReturnType<typeof createPartnerSchema>>;

interface AddBusinessPartnerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddPartner?: (partner: PartnerFormData) => Promise<void>;
  onPartnerAdded?: () => void;
}

export function AddBusinessPartnerDialog({
  open,
  onOpenChange,
  onAddPartner,
  onPartnerAdded,
}: AddBusinessPartnerDialogProps) {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<PartnerFormData>({
    resolver: zodResolver(createPartnerSchema(t)),
    defaultValues: {
      name: '',
      type: 'SHIPPER',
      status: 'ACTIVE',
      contactPerson: '',
      email: '',
      phone: '',
      website: '',
      taxId: '',
      notes: '',
    },
  });

  const handleSubmit = async (data: PartnerFormData) => {
    setIsSubmitting(true);
    try {
      // Clean up empty strings to undefined for optional fields
      const cleanedData = {
        ...data,
        contactPerson: data.contactPerson || undefined,
        email: data.email || undefined,
        phone: data.phone || undefined,
        website: data.website || undefined,
        taxId: data.taxId || undefined,
        notes: data.notes || undefined,
      };

      if (onAddPartner) {
        await onAddPartner(cleanedData);
      } else {
        // Default API call if no custom handler provided
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
        const response = await fetch(`${backendUrl}/api/business-partners`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(cleanedData),
        });

        if (!response.ok) {
          throw new Error(t('businessPartners:addDialog.messages.createFailed'));
        }

        toast({
          title: t('common:success'),
          description: t('businessPartners:addDialog.messages.success'),
        });
      }

      form.reset();
      onOpenChange(false);

      if (onPartnerAdded) {
        onPartnerAdded();
      }
    } catch (error) {
      console.error('Error adding partner:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:addDialog.messages.error'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {t('businessPartners:addDialog.title')}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:addDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('businessPartners:addDialog.fields.partnerName')} *</Label>
              <Input
                id="name"
                {...form.register('name')}
                placeholder={t('businessPartners:addDialog.placeholders.partnerName')}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">{t('businessPartners:addDialog.fields.partnerType')} *</Label>
              <Select
                value={form.watch('type')}
                onValueChange={(value: 'SHIPPER' | 'LOGISTICS_PARTNER') =>
                  form.setValue('type', value)
                }
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder={t('businessPartners:addDialog.placeholders.selectPartnerType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SHIPPER">{t('businessPartners:addDialog.partnerTypes.shipper')}</SelectItem>
                  <SelectItem value="LOGISTICS_PARTNER">{t('businessPartners:addDialog.partnerTypes.logisticsPartner')}</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.type && (
                <p className="text-sm text-red-600">{form.formState.errors.type.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">{t('businessPartners:addDialog.fields.status')}</Label>
            <Select
              value={form.watch('status')}
              onValueChange={(value: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED') =>
                form.setValue('status', value)
              }
            >
              <SelectTrigger id="status">
                <SelectValue placeholder={t('businessPartners:addDialog.placeholders.selectStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ACTIVE">{t('businessPartners:addDialog.statuses.active')}</SelectItem>
                <SelectItem value="INACTIVE">{t('businessPartners:addDialog.statuses.inactive')}</SelectItem>
                <SelectItem value="SUSPENDED">{t('businessPartners:addDialog.statuses.suspended')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('businessPartners:addDialog.sections.contactInfo')}</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactPerson">{t('businessPartners:addDialog.fields.contactPerson')}</Label>
                <Input
                  id="contactPerson"
                  {...form.register('contactPerson')}
                  placeholder={t('businessPartners:addDialog.placeholders.contactName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('businessPartners:addDialog.fields.phone')}</Label>
                <Input
                  id="phone"
                  {...form.register('phone')}
                  placeholder={t('businessPartners:addDialog.placeholders.phoneNumber')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t('businessPartners:addDialog.fields.email')}</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  placeholder={t('businessPartners:addDialog.placeholders.emailAddress')}
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">{t('businessPartners:addDialog.fields.website')}</Label>
                <Input
                  id="website"
                  {...form.register('website')}
                  placeholder={t('businessPartners:addDialog.placeholders.websiteUrl')}
                />
                {form.formState.errors.website && (
                  <p className="text-sm text-red-600">{form.formState.errors.website.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('businessPartners:addDialog.sections.additionalInfo')}</h3>

            <div className="space-y-2">
              <Label htmlFor="taxId">{t('businessPartners:addDialog.fields.taxId')}</Label>
              <Input
                id="taxId"
                {...form.register('taxId')}
                placeholder={t('businessPartners:addDialog.placeholders.taxNumber')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">{t('businessPartners:addDialog.fields.notes')}</Label>
              <Textarea
                id="notes"
                {...form.register('notes')}
                placeholder={t('businessPartners:addDialog.placeholders.additionalNotes')}
                rows={3}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {t('businessPartners:addDialog.actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t('businessPartners:addDialog.actions.adding')}
                </>
              ) : (
                t('businessPartners:addDialog.actions.addPartner')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
