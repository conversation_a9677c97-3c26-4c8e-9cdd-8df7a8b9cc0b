import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MapPin, Building2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

const createLocationSchema = (t: any) => z.object({
  name: z.string().min(1, t('businessPartners:editLocationDialog.validation.nameRequired')),
  type: z.enum(['PICKUP_POINT', 'DELIVERY_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER']),
  address: z.string().min(1, t('businessPartners:editLocationDialog.validation.addressRequired')),
  city: z.string().min(1, t('businessPartners:editLocationDialog.validation.cityRequired')),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().min(1, t('businessPartners:editLocationDialog.validation.countryRequired')).default('Poland'),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email(t('businessPartners:editLocationDialog.validation.invalidEmail')).optional().or(z.literal('')),
  operatingHours: z.string().optional(),
  specialInstructions: z.string().optional(),
  isActive: z.boolean(),
  isDefault: z.boolean(),
});

type LocationFormData = z.infer<ReturnType<typeof createLocationSchema>>;

interface PartnerLocation {
  id: string;
  name: string;
  type: string;
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface EditLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  location: PartnerLocation;
  onEditLocation: (location: LocationFormData) => Promise<void>;
}

export function EditLocationDialog({
  open,
  onOpenChange,
  location,
  onEditLocation,
}: EditLocationDialogProps) {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<LocationFormData>({
    resolver: zodResolver(createLocationSchema(t)),
    defaultValues: {
      name: '',
      type: 'PICKUP_POINT',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'Poland',
      contactPerson: '',
      phone: '',
      email: '',
      operatingHours: '',
      specialInstructions: '',
      isActive: true,
      isDefault: false,
    },
  });

  // Update form when location changes
  useEffect(() => {
    if (location) {
      form.reset({
        name: location.name,
        type: location.type as any,
        address: location.address,
        city: location.city,
        state: location.state || '',
        postalCode: location.postalCode || '',
        country: location.country,
        contactPerson: location.contactPerson || '',
        phone: location.phone || '',
        email: location.email || '',
        operatingHours: location.operatingHours || '',
        specialInstructions: location.specialInstructions || '',
        isActive: location.isActive,
        isDefault: location.isDefault,
      });
    }
  }, [location, form]);

  const handleSubmit = async (data: LocationFormData) => {
    setIsSubmitting(true);
    try {
      // Clean up empty strings to undefined for optional fields
      const cleanedData = {
        ...data,
        state: data.state || undefined,
        postalCode: data.postalCode || undefined,
        contactPerson: data.contactPerson || undefined,
        phone: data.phone || undefined,
        email: data.email || undefined,
        operatingHours: data.operatingHours || undefined,
        specialInstructions: data.specialInstructions || undefined,
      };

      await onEditLocation(cleanedData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:editLocationDialog.messages.error'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('businessPartners:editLocationDialog.title')}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:editLocationDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t('businessPartners:detailsDialog.sections.basicInfo')}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('businessPartners:editLocationDialog.fields.locationName')} *</Label>
                <Input
                  id="name"
                  {...form.register('name')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.locationName')}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">{t('businessPartners:editLocationDialog.fields.locationType')} *</Label>
                <Select
                  value={form.watch('type')}
                  onValueChange={(value) => form.setValue('type', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('businessPartners:editLocationDialog.placeholders.selectLocationType')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PICKUP_POINT">{t('businessPartners:editLocationDialog.locationTypes.PICKUP_POINT')}</SelectItem>
                    <SelectItem value="DELIVERY_POINT">{t('businessPartners:editLocationDialog.locationTypes.DELIVERY_POINT')}</SelectItem>
                    <SelectItem value="WAREHOUSE">{t('businessPartners:editLocationDialog.locationTypes.WAREHOUSE')}</SelectItem>
                    <SelectItem value="DISTRIBUTION_CENTER">{t('businessPartners:editLocationDialog.locationTypes.DISTRIBUTION_CENTER')}</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.type && (
                  <p className="text-sm text-red-500">{form.formState.errors.type.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t('businessPartners:locationDialog.sections.addressInfo')}</h3>

            <div className="space-y-2">
              <Label htmlFor="address">{t('businessPartners:editLocationDialog.fields.address')} *</Label>
              <Input
                id="address"
                {...form.register('address')}
                placeholder={t('businessPartners:editLocationDialog.placeholders.streetAddress')}
              />
              {form.formState.errors.address && (
                <p className="text-sm text-red-500">{form.formState.errors.address.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">{t('businessPartners:editLocationDialog.fields.city')} *</Label>
                <Input
                  id="city"
                  {...form.register('city')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.cityName')}
                />
                {form.formState.errors.city && (
                  <p className="text-sm text-red-500">{form.formState.errors.city.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">{t('businessPartners:editLocationDialog.fields.state')}</Label>
                <Input
                  id="state"
                  {...form.register('state')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.stateName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">{t('businessPartners:editLocationDialog.fields.postalCode')}</Label>
                <Input
                  id="postalCode"
                  {...form.register('postalCode')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.postalCode')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">{t('businessPartners:editLocationDialog.fields.country')} *</Label>
              <Input
                id="country"
                {...form.register('country')}
                placeholder={t('businessPartners:editLocationDialog.placeholders.countryName')}
              />
              {form.formState.errors.country && (
                <p className="text-sm text-red-500">{form.formState.errors.country.message}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t('businessPartners:locationDialog.sections.contactInfo')}</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactPerson">{t('businessPartners:editLocationDialog.fields.contactPerson')}</Label>
                <Input
                  id="contactPerson"
                  {...form.register('contactPerson')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.contactName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('businessPartners:editLocationDialog.fields.phone')}</Label>
                <Input
                  id="phone"
                  {...form.register('phone')}
                  placeholder={t('businessPartners:editLocationDialog.placeholders.phoneNumber')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('businessPartners:editLocationDialog.fields.email')}</Label>
              <Input
                id="email"
                type="email"
                {...form.register('email')}
                placeholder={t('businessPartners:editLocationDialog.placeholders.emailAddress')}
              />
              {form.formState.errors.email && (
                <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t('businessPartners:locationDialog.sections.operatingInfo')}</h3>

            <div className="space-y-2">
              <Label htmlFor="operatingHours">{t('businessPartners:editLocationDialog.fields.operatingHours')}</Label>
              <Input
                id="operatingHours"
                {...form.register('operatingHours')}
                placeholder={t('businessPartners:editLocationDialog.placeholders.operatingHours')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialInstructions">{t('businessPartners:editLocationDialog.fields.specialInstructions')}</Label>
              <Textarea
                id="specialInstructions"
                {...form.register('specialInstructions')}
                placeholder={t('businessPartners:editLocationDialog.placeholders.specialInstructions')}
                rows={3}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={form.watch('isActive')}
                  onCheckedChange={(checked) => form.setValue('isActive', checked)}
                />
                <Label htmlFor="isActive">{t('businessPartners:editLocationDialog.fields.isActive')}</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isDefault"
                  checked={form.watch('isDefault')}
                  onCheckedChange={(checked) => form.setValue('isDefault', checked)}
                />
                <Label htmlFor="isDefault">{t('businessPartners:editLocationDialog.fields.isDefault')}</Label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              {t('businessPartners:editLocationDialog.actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? t('businessPartners:editLocationDialog.actions.saving') : t('businessPartners:editLocationDialog.actions.saveChanges')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
