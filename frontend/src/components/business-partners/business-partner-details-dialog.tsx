import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Building2, MapPin, Phone, Mail, Globe, FileText, Plus, Edit, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { getAuthHeaders } from '@/lib/auth-headers';
import { AddLocationDialog } from './add-location-dialog';

interface BusinessPartner {
  id: string;
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PARTNER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  locations: PartnerLocation[];
  _count: {
    tripPickups: number;
    tripDeliveries: number;
  };
}

interface PartnerLocation {
  id: string;
  name: string;
  type: 'PICKUP_POINT' | 'DELIVERY_POINT' | 'WAREHOUSE' | 'DISTRIBUTION_CENTER';
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  contactPerson?: string;
  phone?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface BusinessPartnerDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  partner: BusinessPartner;
  onPartnerUpdated: () => void;
}

export function BusinessPartnerDetailsDialog({
  open,
  onOpenChange,
  partner,
  onPartnerUpdated,
}: BusinessPartnerDetailsDialogProps) {
  const { t } = useTranslation(['businessPartners', 'common']);
  const [showAddLocationDialog, setShowAddLocationDialog] = useState(false);
  const [locations, setLocations] = useState<PartnerLocation[]>(partner.locations);
  const { toast } = useToast();

  const getPartnerTypeLabel = (type: string) => {
    switch (type) {
      case 'SHIPPER': return t('businessPartners:types.shipper');
      case 'LOGISTICS_PARTNER': return t('partners:types.logisticsPartner');
      default: return type;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'ACTIVE': return t('businessPartners:detailsDialog.statuses.active');
      case 'INACTIVE': return t('businessPartners:detailsDialog.statuses.inactive');
      case 'SUSPENDED': return t('businessPartners:detailsDialog.statuses.suspended');
      default: return status;
    }
  };

  // Update local locations when partner prop changes
  useEffect(() => {
    setLocations(partner.locations);
  }, [partner.locations]);

  const getPartnerTypeBadgeVariant = (type: string) => {
    return type === 'SHIPPER' ? 'default' : 'secondary';
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'default';
      case 'INACTIVE': return 'secondary';
      case 'SUSPENDED': return 'destructive';
      default: return 'secondary';
    }
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'PICKUP_POINT': return t('businessPartners:locationTypes.PICKUP_POINT');
      case 'DELIVERY_POINT': return t('businessPartners:locationTypes.DELIVERY_POINT');
      case 'WAREHOUSE': return t('businessPartners:locationTypes.WAREHOUSE');
      case 'DISTRIBUTION_CENTER': return t('businessPartners:locationTypes.DISTRIBUTION_CENTER');
      default: return type;
    }
  };

  const handleAddLocation = async (locationData: any) => {
    try {
      const response = await fetch(`http://localhost:3001/business-partners/${partner.id}/locations`, {
        method: 'POST',
        headers: {
          ...getAuthHeaders(),
        },
        body: JSON.stringify(locationData),
      });

      if (!response.ok) {
        throw new Error('Failed to add location');
      }

      const newLocation = await response.json();

      // Immediately add the new location to local state for instant UI update
      setLocations(prevLocations => [...prevLocations, newLocation]);

      toast({
        title: 'Success',
        description: 'Location added successfully',
      });

      // Also update the parent component's data
      onPartnerUpdated();
      setShowAddLocationDialog(false);
    } catch (error) {
      console.error('Error adding location:', error);
      toast({
        title: 'Error',
        description: 'Failed to add location',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteLocation = async (locationId: string) => {
    if (!confirm('Are you sure you want to delete this location?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:3001/business-partners/locations/${locationId}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to delete location');
      }

      // Immediately remove the location from local state for instant UI update
      setLocations(prevLocations => prevLocations.filter(loc => loc.id !== locationId));

      toast({
        title: t('common:success'),
        description: t('businessPartners:messages.locationDeletedSuccess'),
      });

      // Also update the parent component's data
      onPartnerUpdated();
    } catch (error) {
      console.error('Error deleting location:', error);
      toast({
        title: t('common:error'),
        description: t('businessPartners:messages.failedToDeleteLocation'),
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {partner.name}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:detailsDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">{t('businessPartners:detailsDialog.tabs.details')}</TabsTrigger>
            <TabsTrigger value="locations">{t('businessPartners:detailsDialog.tabs.locations')} ({locations.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{t('businessPartners:detailsDialog.sections.basicInfo')}</span>
                  <div className="flex gap-2">
                    <Badge variant={getPartnerTypeBadgeVariant(partner.type)}>
                      {getPartnerTypeLabel(partner.type)}
                    </Badge>
                    <Badge variant={getStatusBadgeVariant(partner.status)}>
                      {getStatusLabel(partner.status)}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {partner.contactPerson && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.contactPerson')}</label>
                      <p className="text-sm">{partner.contactPerson}</p>
                    </div>
                  )}
                  {partner.phone && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.phone')}</label>
                      <p className="text-sm flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {partner.phone}
                      </p>
                    </div>
                  )}
                  {partner.email && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.email')}</label>
                      <p className="text-sm flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {partner.email}
                      </p>
                    </div>
                  )}
                  {partner.website && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.website')}</label>
                      <p className="text-sm flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <a href={partner.website} target="_blank" rel="noopener noreferrer"
                           className="text-blue-600 hover:underline">
                          {partner.website}
                        </a>
                      </p>
                    </div>
                  )}
                  {partner.taxId && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.taxId')}</label>
                      <p className="text-sm">{partner.taxId}</p>
                    </div>
                  )}
                </div>

                {partner.notes && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.notes')}</label>
                    <p className="text-sm mt-1 p-3 bg-muted rounded-md">{partner.notes}</p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.totalTrips')}</label>
                    <p className="text-sm">
                      {t('businessPartners:detailsDialog.tripStats', {
                        total: partner._count.tripPickups + partner._count.tripDeliveries,
                        pickups: partner._count.tripPickups,
                        deliveries: partner._count.tripDeliveries
                      })}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('businessPartners:detailsDialog.fields.created')}</label>
                    <p className="text-sm">{new Date(partner.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="locations" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">{t('businessPartners:detailsDialog.sections.partnerLocations')}</h3>
              <Button onClick={() => setShowAddLocationDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('businessPartners:actions.addLocation')}
              </Button>
            </div>

            {locations.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('businessPartners:detailsDialog.noLocations.title')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {t('businessPartners:detailsDialog.noLocations.description')}
                  </p>
                  <Button onClick={() => setShowAddLocationDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    {t('businessPartners:detailsDialog.noLocations.addFirst')}
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {locations.map((location) => (
                  <Card key={location.id}>
                    <CardContent className="pt-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <h4 className="font-semibold">{location.name}</h4>
                            <Badge variant="outline">
                              {getLocationTypeLabel(location.type)}
                            </Badge>
                            {location.isDefault && (
                              <Badge variant="secondary">Default</Badge>
                            )}
                            {!location.isActive && (
                              <Badge variant="destructive">Inactive</Badge>
                            )}
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            {location.address}, {location.city}
                            {location.state && `, ${location.state}`}
                            {location.postalCode && ` ${location.postalCode}`}
                          </p>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            {location.contactPerson && (
                              <div>
                                <span className="font-medium">Contact:</span> {location.contactPerson}
                              </div>
                            )}
                            {location.phone && (
                              <div>
                                <span className="font-medium">Phone:</span> {location.phone}
                              </div>
                            )}
                            {location.operatingHours && (
                              <div>
                                <span className="font-medium">Hours:</span> {location.operatingHours}
                              </div>
                            )}
                          </div>

                          {location.specialInstructions && (
                            <div className="mt-2 p-2 bg-muted rounded text-sm">
                              <span className="font-medium">Special Instructions:</span> {location.specialInstructions}
                            </div>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteLocation(location.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <AddLocationDialog
          open={showAddLocationDialog}
          onOpenChange={setShowAddLocationDialog}
          partnerId={partner.id}
          onAddLocation={handleAddLocation}
        />
      </DialogContent>
    </Dialog>
  );
}
