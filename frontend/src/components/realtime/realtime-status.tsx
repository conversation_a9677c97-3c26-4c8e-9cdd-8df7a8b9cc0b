'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Wifi, WifiOff, Activity, Users, Truck, User, Bell } from 'lucide-react';

interface RealtimeUpdate {
  id: string;
  type: 'trip' | 'vehicle' | 'driver' | 'notification' | 'system';
  message: string;
  timestamp: string;
  data?: any;
}

export function RealtimeStatus() {
  const { t } = useTranslation(['common', 'navigation']);
  const [updates, setUpdates] = useState<RealtimeUpdate[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');

  const { isConnected, joinRoom, leaveRoom } = useWebSocket({
    onTripUpdate: (data) => {
      addUpdate({
        type: 'trip',
        message: `${t('navigation:main.trips')} ${data.action}: ${data.tripId}`,
        data,
      });
    },
    onVehicleUpdate: (data) => {
      addUpdate({
        type: 'vehicle',
        message: `${t('navigation:main.fleet')} ${data.action}: ${data.vehicleId}`,
        data,
      });
    },
    onDriverUpdate: (data) => {
      addUpdate({
        type: 'driver',
        message: `${t('navigation:main.drivers')} ${data.action}: ${data.driverId}`,
        data,
      });
    },
    onNotification: (data) => {
      addUpdate({
        type: 'notification',
        message: data.message || 'New notification received',
        data,
      });
    },
    onSystemAlert: (data) => {
      addUpdate({
        type: 'system',
        message: data.message || 'System alert',
        data,
      });
    },
  });

  useEffect(() => {
    setConnectionStatus(isConnected ? 'connected' : 'disconnected');
  }, [isConnected]);

  const addUpdate = (update: Omit<RealtimeUpdate, 'id' | 'timestamp'>) => {
    const newUpdate: RealtimeUpdate = {
      ...update,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
    };

    setUpdates(prev => [newUpdate, ...prev.slice(0, 19)]); // Keep last 20 updates
  };

  const clearUpdates = () => {
    setUpdates([]);
  };

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'trip':
        return <Truck className="h-4 w-4" />;
      case 'vehicle':
        return <Truck className="h-4 w-4" />;
      case 'driver':
        return <User className="h-4 w-4" />;
      case 'notification':
        return <Bell className="h-4 w-4" />;
      case 'system':
        return <Activity className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getUpdateColor = (type: string) => {
    switch (type) {
      case 'trip':
        return 'bg-blue-100 text-blue-800';
      case 'vehicle':
        return 'bg-green-100 text-green-800';
      case 'driver':
        return 'bg-purple-100 text-purple-800';
      case 'notification':
        return 'bg-yellow-100 text-yellow-800';
      case 'system':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <span className="flex items-center gap-2">
            {connectionStatus === 'connected' ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            {t('common:status.realtime')}
          </span>
          <Badge 
            variant={connectionStatus === 'connected' ? 'default' : 'destructive'}
            className="text-xs"
          >
            {connectionStatus === 'connected' ? t('common:status.connected') : t('common:status.disconnected')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-muted-foreground">
            {t('common:updates')}: {updates.length}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={clearUpdates}
            disabled={updates.length === 0}
          >
            {t('common:actions.clear')}
          </Button>
        </div>

        <div className="space-y-2 max-h-64 overflow-y-auto">
          {updates.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground py-4">
              {t('common:noUpdates')}
            </div>
          ) : (
            updates.map((update) => (
              <div
                key={update.id}
                className="flex items-start gap-2 p-2 rounded-lg border bg-card text-card-foreground"
              >
                <div className={`p-1 rounded ${getUpdateColor(update.type)}`}>
                  {getUpdateIcon(update.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {update.message}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(update.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>

        {process.env.NODE_ENV === 'development' && (
          <div className="pt-2 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => joinRoom('test-room')}
                className="text-xs"
              >
                Join Test Room
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => leaveRoom('test-room')}
                className="text-xs"
              >
                Leave Room
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
