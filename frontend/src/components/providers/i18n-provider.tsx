'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { appWithTranslation } from 'next-i18next';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i18n';

interface I18nProviderProps {
  children: React.ReactNode;
  locale?: string;
}

export function I18nProvider({ children, locale = 'pl' }: I18nProviderProps) {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Load preferred language from localStorage
    const preferredLanguage = localStorage.getItem('preferred-language') || locale;

    // Initialize i18n with the preferred locale
    if (i18n.language !== preferredLanguage) {
      i18n.changeLanguage(preferredLanguage);
    }
  }, [locale]);

  return (
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  );
}
