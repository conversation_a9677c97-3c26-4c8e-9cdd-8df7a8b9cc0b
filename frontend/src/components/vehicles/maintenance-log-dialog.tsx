'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MaintenanceLog } from './maintenance-log-types';

interface MaintenanceLogDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (logData: Omit<MaintenanceLog, 'id'>) => void;
  vehicleId: string;
  vehicleName: string;
  initialLog?: MaintenanceLog;
}

export function MaintenanceLogDialog({
  isOpen,
  onClose,
  onSubmit,
  vehicleId,
  vehicleName,
  initialLog,
}: MaintenanceLogDialogProps) {
  const [formData, setFormData] = useState<Omit<MaintenanceLog, 'id'>>(
    initialLog ?? {
      type: 'PREVENTIVE',
      category: 'ENGINE',
      description: '',
      date: new Date().toISOString().split('T')[0],
      status: 'SCHEDULED',
      cost: undefined,
      partsCost: undefined,
      laborCost: undefined,
      technician: '',
      notes: '',
      mileage: undefined,
      scheduledDate: undefined,
      nextMaintenanceDate: undefined,
      nextMaintenanceMileage: undefined,
    }
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Calculate total cost from parts and labor
    const totalCost = 
      (formData.partsCost || 0) + (formData.laborCost || 0);
    onSubmit({
      ...formData,
      cost: totalCost > 0 ? totalCost : undefined
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">
          {initialLog ? 'Edit Maintenance Log' : 'Add Maintenance Log'}
        </h2>
        <p className="text-sm text-gray-500 mb-4">
          Vehicle: {vehicleName}
        </p>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as MaintenanceLog['status'] })}
              required
            >
              <option value="SCHEDULED">Scheduled</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>

          {/* Type and Category in a grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Type
              </label>
              <select
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as MaintenanceLog['type'] })}
                required
              >
                <option value="PREVENTIVE">Preventive Maintenance</option>
                <option value="REPAIR">Repair</option>
                <option value="INSPECTION">Inspection</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value as MaintenanceLog['category'] })}
                required
              >
                <option value="ENGINE">Engine</option>
                <option value="TRANSMISSION">Transmission</option>
                <option value="BRAKES">Brakes</option>
                <option value="ELECTRICAL">Electrical</option>
                <option value="TIRES">Tires</option>
                <option value="OTHER">Other</option>
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <input
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              placeholder="e.g., Oil Change, Brake Inspection"
            />
          </div>

          {/* Dates in a grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                {formData.status === 'SCHEDULED' ? 'Scheduled Date' : 'Service Date'}
              </label>
              <input
                type="date"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.status === 'SCHEDULED' ? formData.scheduledDate : formData.date}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  ...(formData.status === 'SCHEDULED' 
                    ? { scheduledDate: e.target.value } 
                    : { date: e.target.value })
                })}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Next Service Date
              </label>
              <input
                type="date"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.nextMaintenanceDate || ''}
                onChange={(e) => setFormData({ ...formData, nextMaintenanceDate: e.target.value })}
              />
            </div>
          </div>

          {/* Mileage in a grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Current Mileage
              </label>
              <input
                type="number"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.mileage || ''}
                onChange={(e) => setFormData({ ...formData, mileage: parseInt(e.target.value) || undefined })}
                placeholder="Enter current mileage"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Next Service Mileage
              </label>
              <input
                type="number"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.nextMaintenanceMileage || ''}
                onChange={(e) => setFormData({ ...formData, nextMaintenanceMileage: parseInt(e.target.value) || undefined })}
                placeholder="Enter next service mileage"
              />
            </div>
          </div>

          {/* Costs in a grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Parts Cost
              </label>
              <input
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.partsCost || ''}
                onChange={(e) => setFormData({ ...formData, partsCost: parseFloat(e.target.value) || undefined })}
                placeholder="Enter parts cost"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Labor Cost
              </label>
              <input
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={formData.laborCost || ''}
                onChange={(e) => setFormData({ ...formData, laborCost: parseFloat(e.target.value) || undefined })}
                placeholder="Enter labor cost"
              />
            </div>
          </div>

          {/* Total Cost Display */}
          <div className="p-3 bg-gray-50 rounded-md">
            <label className="block text-sm font-medium text-gray-700">
              Total Cost
            </label>
            <p className="mt-1 text-lg font-semibold">
              ${((formData.partsCost || 0) + (formData.laborCost || 0)).toFixed(2)}
            </p>
          </div>

          {/* Technician */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Technician
            </label>
            <input
              type="text"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.technician || ''}
              onChange={(e) => setFormData({ ...formData, technician: e.target.value })}
              placeholder="Enter technician name"
            />
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Notes
            </label>
            <textarea
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.notes || ''}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              placeholder="Enter additional notes"
            />
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              onClick={onClose}
              className="bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-500">
              {initialLog ? 'Save Changes' : 'Add Log'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
