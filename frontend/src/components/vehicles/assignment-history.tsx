import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Assignment,
  AssignmentStatus,
  AssignmentType,
  AssignmentPriority,
  assignmentStatusVariants,
  assignmentTypeVariants,
  assignmentPriorityVariants
} from '@/types/assignment';
import { formatDate } from '@/lib/utils';
import { useState } from 'react';

interface AssignmentHistoryProps {
  assignments: Assignment[];
  onUpdateStatus?: (assignmentId: string, newStatus: AssignmentStatus) => Promise<void>;
}

export function AssignmentHistory({ assignments, onUpdateStatus }: AssignmentHistoryProps) {
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  const toggleRow = (id: string) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const getStatusActions = (status: AssignmentStatus) => {
    switch (status) {
      case AssignmentStatus.PENDING:
        return [AssignmentStatus.ACTIVE, AssignmentStatus.CANCELLED];
      case AssignmentStatus.ACTIVE:
        return [AssignmentStatus.COMPLETED, AssignmentStatus.ON_HOLD, AssignmentStatus.CANCELLED];
      case AssignmentStatus.ON_HOLD:
        return [AssignmentStatus.ACTIVE, AssignmentStatus.CANCELLED];
      case AssignmentStatus.DELAYED:
        return [AssignmentStatus.ACTIVE, AssignmentStatus.CANCELLED];
      default:
        return [];
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold">Assignment History</h3>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Driver</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assignments.map((assignment) => (
                <>
                  <TableRow 
                    key={assignment.id}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleRow(assignment.id)}
                  >
                    <TableCell>
                      <Badge
                        variant={assignmentTypeVariants[assignment.type]}
                      >
                        {assignment.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {`${assignment.driver.firstName} ${assignment.driver.lastName}`}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={assignmentStatusVariants[assignment.status]}
                      >
                        {assignment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={assignmentPriorityVariants[assignment.priority]}
                      >
                        {assignment.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(assignment.startDate)}</TableCell>
                    <TableCell>
                      {assignment.endDate ? formatDate(assignment.endDate) : '-'}
                    </TableCell>
                    <TableCell>
                      {onUpdateStatus && getStatusActions(assignment.status).map((newStatus) => (
                        <Button
                          key={newStatus}
                          variant="outline"
                          size="sm"
                          className="mr-2"
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            onUpdateStatus(assignment.id, newStatus);
                          }}
                        >
                          {newStatus.charAt(0) + newStatus.slice(1).toLowerCase()}
                        </Button>
                      ))}
                    </TableCell>
                  </TableRow>
                  {expandedRow === assignment.id && (
                    <TableRow>
                      <TableCell colSpan={7} className="bg-gray-50 p-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold mb-2">Assignment Details</h4>
                            <dl className="space-y-2">
                              {assignment.reason && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">Reason</dt>
                                  <dd className="text-sm text-gray-900">{assignment.reason}</dd>
                                </div>
                              )}
                              {assignment.notes && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">Notes</dt>
                                  <dd className="text-sm text-gray-900">{assignment.notes}</dd>
                                </div>
                              )}
                              {assignment.mileage && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">Mileage</dt>
                                  <dd className="text-sm text-gray-900">{assignment.mileage} km</dd>
                                </div>
                              )}
                            </dl>
                          </div>
                          <div>
                            <h4 className="font-semibold mb-2">Location Information</h4>
                            <dl className="space-y-2">
                              {assignment.locationStart && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">Start Location</dt>
                                  <dd className="text-sm text-gray-900">{assignment.locationStart}</dd>
                                </div>
                              )}
                              {assignment.locationEnd && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">End Location</dt>
                                  <dd className="text-sm text-gray-900">{assignment.locationEnd}</dd>
                                </div>
                              )}
                              {assignment.approvedBy && (
                                <div>
                                  <dt className="text-sm font-medium text-gray-500">Approved By</dt>
                                  <dd className="text-sm text-gray-900">{assignment.approvedBy}</dd>
                                </div>
                              )}
                            </dl>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))}
              {assignments.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center text-muted-foreground">
                    No assignments found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
