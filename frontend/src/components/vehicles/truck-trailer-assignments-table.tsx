'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import { TruckTrailerAssignment, AssignmentStatus } from '@/types/vehicle';
import { TruckTrailerAssignmentDialog } from './truck-trailer-assignment-dialog';
import { MoreHorizontal, CheckCircle, XCircle, Edit, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

interface TruckTrailerAssignmentsTableProps {
  assignments: TruckTrailerAssignment[];
  onUpdate: (id: string, data: any) => Promise<void>;
  onComplete: (id: string) => Promise<void>;
  onCancel: (id: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  isLoading?: boolean;
}

export function TruckTrailerAssignmentsTable({
  assignments,
  onUpdate,
  onComplete,
  onCancel,
  onDelete,
  isLoading = false,
}: TruckTrailerAssignmentsTableProps) {
  const { t } = useTranslation(['fleet', 'common']);
  const [editingAssignment, setEditingAssignment] = useState<TruckTrailerAssignment | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const getStatusBadge = (status: AssignmentStatus) => {
    const statusConfig = {
      [AssignmentStatus.ACTIVE]: { variant: 'default' as const, label: t('fleet:table.active') },
      [AssignmentStatus.COMPLETED]: { variant: 'secondary' as const, label: t('common:status.completed') },
      [AssignmentStatus.CANCELLED]: { variant: 'destructive' as const, label: t('common:status.cancelled') },
      [AssignmentStatus.PENDING]: { variant: 'outline' as const, label: t('common:status.pending') },
      [AssignmentStatus.ON_HOLD]: { variant: 'outline' as const, label: t('fleet:vehicles.onHold') },
      [AssignmentStatus.DELAYED]: { variant: 'outline' as const, label: t('fleet:vehicles.delayed') },
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const handleEdit = (assignment: TruckTrailerAssignment) => {
    setEditingAssignment(assignment);
    setIsDialogOpen(true);
  };

  const handleComplete = async (assignment: TruckTrailerAssignment) => {
    try {
      await onComplete(assignment.id);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.assignmentCompletedSuccess'),
      });
    } catch (error) {
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorCompletingAssignment'),
        variant: 'destructive',
      });
    }
  };

  const handleCancel = async (assignment: TruckTrailerAssignment) => {
    try {
      await onCancel(assignment.id);
      toast({
        title: t('common:success'),
        description: t('fleet:messages.assignmentCancelledSuccess'),
      });
    } catch (error) {
      toast({
        title: t('common:error'),
        description: t('fleet:messages.errorCancellingAssignment'),
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async (assignment: TruckTrailerAssignment) => {
    if (window.confirm(t('fleet:messages.confirmDeleteAssignment'))) {
      try {
        await onDelete(assignment.id);
        toast({
          title: t('common:success'),
          description: t('fleet:messages.assignmentDeletedSuccess'),
        });
      } catch (error) {
        toast({
          title: t('common:error'),
          description: t('fleet:messages.errorDeletingAssignment'),
          variant: 'destructive',
        });
      }
    }
  };

  const handleDialogSubmit = async (data: any) => {
    if (editingAssignment) {
      await onUpdate(editingAssignment.id, data);
    }
    setIsDialogOpen(false);
    setEditingAssignment(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-muted-foreground">{t('fleet:vehicles.loadingAssignments')}</div>
      </div>
    );
  }

  if (assignments.length === 0) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-muted-foreground">No truck-trailer assignments found</div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Truck</TableHead>
              <TableHead>Trailer</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Assigned By</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assignments.map((assignment) => (
              <TableRow key={assignment.id}>
                <TableCell>
                  <div className="font-medium">
                    {assignment.truck?.plateNumber || 'Unknown'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {assignment.truck?.make} {assignment.truck?.model}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">
                    {assignment.trailer?.plateNumber || 'Unknown'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {assignment.trailer?.make} {assignment.trailer?.model}
                    {assignment.trailer?.trailerType && (
                      <span className="ml-1">
                        ({assignment.trailer.trailerType.replace('_', ' ')})
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {format(new Date(assignment.startDate), 'MMM dd, yyyy')}
                </TableCell>
                <TableCell>
                  {assignment.endDate 
                    ? format(new Date(assignment.endDate), 'MMM dd, yyyy')
                    : 'Ongoing'
                  }
                </TableCell>
                <TableCell>
                  {getStatusBadge(assignment.status)}
                </TableCell>
                <TableCell>
                  {assignment.assignedBy}
                </TableCell>
                <TableCell>
                  <div className="max-w-[200px] truncate">
                    {assignment.notes || '-'}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEdit(assignment)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      {assignment.status === AssignmentStatus.ACTIVE && (
                        <DropdownMenuItem onClick={() => handleComplete(assignment)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Complete
                        </DropdownMenuItem>
                      )}
                      {assignment.status === AssignmentStatus.ACTIVE && (
                        <DropdownMenuItem onClick={() => handleCancel(assignment)}>
                          <XCircle className="mr-2 h-4 w-4" />
                          Cancel
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        onClick={() => handleDelete(assignment)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <TruckTrailerAssignmentDialog
        assignment={editingAssignment || undefined}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSubmit={handleDialogSubmit}
      />
    </>
  );
}
