'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Vehicle, VehicleStatus, VehicleType, TrailerType, FuelType } from '@/types/vehicle';
import { Plus, Edit, Loader2 } from 'lucide-react';

const vehicleSchema = z.object({
  // Basic required fields
  plateNumber: z.string().min(1, 'Plate number is required').max(20, 'Plate number too long'),
  make: z.string().min(1, 'Make is required').max(50, 'Make too long'),
  model: z.string().min(1, 'Model is required').max(50, 'Model too long'),
  year: z.number()
    .min(1900, 'Invalid year')
    .max(new Date().getFullYear() + 1, 'Year cannot be in the future'),
  vehicleType: z.nativeEnum(VehicleType),
  status: z.nativeEnum(VehicleStatus).optional(),

  // Optional basic fields
  vin: z.string().optional(),
  color: z.string().optional(),
  mileage: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  fuelType: z.nativeEnum(FuelType).optional(),
  purchaseDate: z.string().optional(),

  // Truck-specific fields (conditional)
  engineType: z.string().optional(),
  transmission: z.string().optional(),
  fuelCapacity: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  axleConfiguration: z.string().optional(),
  cabConfiguration: z.string().optional(),

  // Trailer-specific fields (conditional)
  trailerType: z.nativeEnum(TrailerType).optional(),
  cargoCapacity: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  maxWeight: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  length: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  width: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  height: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined || isNaN(Number(val))) return undefined;
    return Number(val);
  }, z.number().min(0).optional()),
  hasRefrigeration: z.boolean().optional(),
});

type VehicleFormData = z.infer<typeof vehicleSchema>;

interface VehicleDialogProps {
  vehicle?: Vehicle;
  onSubmit: (data: VehicleFormData) => Promise<void>;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function AddVehicleDialog({
  vehicle,
  onSubmit,
  trigger,
  open: controlledOpen,
  onOpenChange
}: VehicleDialogProps) {
  const { t } = useTranslation(['fleet', 'common', 'forms']);
  const [internalOpen, setInternalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled ? (onOpenChange || (() => {})) : setInternalOpen;

  const form = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      plateNumber: '',
      make: '',
      model: '',
      year: new Date().getFullYear(),
      vehicleType: VehicleType.TRUCK,
      status: undefined, // Status is set by backend for new vehicles
      vin: '',
      color: '',
      mileage: undefined,
      fuelType: undefined,
      purchaseDate: '',
      engineType: '',
      transmission: '',
      fuelCapacity: undefined,
      axleConfiguration: '',
      cabConfiguration: '',
      trailerType: undefined,
      cargoCapacity: undefined,
      maxWeight: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      hasRefrigeration: false,
    },
  });

  // Reset form when vehicle changes or dialog opens
  useEffect(() => {
    if (open) {
      if (vehicle) {
        form.reset({
          plateNumber: vehicle.plateNumber,
          make: vehicle.make,
          model: vehicle.model,
          year: vehicle.year,
          vehicleType: vehicle.vehicleType,
          status: vehicle.status,
          vin: vehicle.vin || '',
          color: vehicle.color || '',
          mileage: vehicle.mileage,
          fuelType: vehicle.fuelType,
          purchaseDate: vehicle.purchaseDate || '',
          engineType: vehicle.engineType || '',
          transmission: vehicle.transmission || '',
          fuelCapacity: vehicle.fuelCapacity,
          axleConfiguration: vehicle.axleConfiguration || '',
          cabConfiguration: vehicle.cabConfiguration || '',
          trailerType: vehicle.trailerType,
          cargoCapacity: vehicle.cargoCapacity,
          maxWeight: vehicle.maxWeight,
          length: vehicle.length,
          width: vehicle.width,
          height: vehicle.height,
          hasRefrigeration: vehicle.hasRefrigeration || false,
        });
      } else {
        form.reset({
          plateNumber: '',
          make: '',
          model: '',
          year: new Date().getFullYear(),
          vehicleType: VehicleType.TRUCK,
          status: undefined, // Status is set by backend for new vehicles
          vin: '',
          color: '',
          mileage: undefined,
          fuelType: undefined,
          purchaseDate: '',
          engineType: '',
          transmission: '',
          fuelCapacity: undefined,
          axleConfiguration: '',
          cabConfiguration: '',
          trailerType: undefined,
          cargoCapacity: undefined,
          maxWeight: undefined,
          length: undefined,
          width: undefined,
          height: undefined,
          hasRefrigeration: false,
        });
      }
    }
  }, [vehicle, open, form]);

  const handleSubmit = async (data: VehicleFormData) => {
    setIsSubmitting(true);
    try {
      // For creation, exclude status field (backend sets it automatically)
      // For updates, include status field
      const submitData = vehicle ? data : {
        ...data,
        status: undefined // Remove status for creation
      };

      // Clean up undefined values
      const cleanedData = Object.fromEntries(
        Object.entries(submitData).filter(([_, value]) => value !== undefined)
      );

      await onSubmit(cleanedData as VehicleFormData);
      setOpen(false);
      form.reset();
      toast({
        title: t('common:success'),
        description: vehicle ? t('fleet:messages.vehicleUpdatedSuccess') : t('fleet:messages.vehicleCreatedSuccess'),
      });
    } catch (error) {
      console.error('Error submitting vehicle:', error);
      toast({
        title: t('common:error'),
        description: vehicle ? t('fleet:messages.failedToUpdateVehicle') : t('fleet:messages.errorCreatingVehicle'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultTrigger = (
    <Button variant={vehicle ? 'outline' : 'default'}>
      {vehicle ? (
        <>
          <Edit className="h-4 w-4 mr-2" />
          {t('fleet:vehicles.editVehicle')}
        </>
      ) : (
        <>
          <Plus className="h-4 w-4 mr-2" />
          {t('fleet:vehicles.addVehicle')}
        </>
      )}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="vehicle-dialog-description">
        <DialogHeader>
          <DialogTitle>
            {vehicle ? t('fleet:vehicles.editVehicle') : t('fleet:vehicles.addNewVehicle')}
          </DialogTitle>
          <DialogDescription id="vehicle-dialog-description">
            {vehicle
              ? t('fleet:vehicles.updateVehicleDescription')
              : t('fleet:vehicles.addVehicleDescription')
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">{t('fleet:vehicles.basicInformation')}</TabsTrigger>
              <TabsTrigger value="truck" disabled={form.watch('vehicleType') !== VehicleType.TRUCK}>
                {t('fleet:vehicles.truckDetails')}
              </TabsTrigger>
              <TabsTrigger value="trailer" disabled={form.watch('vehicleType') !== VehicleType.TRAILER}>
                {t('fleet:vehicles.trailerDetails')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="plateNumber">{t('fleet:vehicles.plateNumber')} *</Label>
                <Input
                  id="plateNumber"
                  {...form.register('plateNumber')}
                  placeholder="e.g., ABC123"
                  aria-describedby="plateNumber-error"
                />
                {form.formState.errors.plateNumber && (
                  <p id="plateNumber-error" className="text-sm text-destructive" role="alert">
                    {form.formState.errors.plateNumber.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="make">{t('fleet:vehicles.make')} *</Label>
                  <Input
                    id="make"
                    {...form.register('make')}
                    placeholder="e.g., Volvo"
                    aria-describedby="make-error"
                  />
                  {form.formState.errors.make && (
                    <p id="make-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.make.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">{t('fleet:vehicles.model')} *</Label>
                  <Input
                    id="model"
                    {...form.register('model')}
                    placeholder="e.g., FH16"
                    aria-describedby="model-error"
                  />
                  {form.formState.errors.model && (
                    <p id="model-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.model.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="year">{t('fleet:vehicles.year')} *</Label>
                  <Input
                    id="year"
                    type="number"
                    {...form.register('year', { valueAsNumber: true })}
                    min={1900}
                    max={new Date().getFullYear() + 1}
                    aria-describedby="year-error"
                  />
                  {form.formState.errors.year && (
                    <p id="year-error" className="text-sm text-destructive" role="alert">
                      {form.formState.errors.year.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="vehicleType">{t('fleet:vehicles.vehicleType')} *</Label>
                  <Select
                    value={form.watch('vehicleType')}
                    onValueChange={(value: string) => form.setValue('vehicleType', value as VehicleType)}
                  >
                    <SelectTrigger id="vehicleType" aria-label="Vehicle type">
                      <SelectValue placeholder={t('fleet:vehicles.selectType')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(VehicleType).map((type) => (
                        <SelectItem key={type} value={type}>
                          {t(`fleet:vehicles.${type.toLowerCase()}`)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.vehicleType && (
                    <p className="text-sm text-destructive" role="alert">
                      {form.formState.errors.vehicleType.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vin">{t('fleet:vehicles.vin')}</Label>
                  <Input
                    id="vin"
                    {...form.register('vin')}
                    placeholder="Vehicle Identification Number"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">{t('fleet:vehicles.color')}</Label>
                  <Input
                    id="color"
                    {...form.register('color')}
                    placeholder="e.g., White"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mileage">{t('fleet:vehicles.mileage')} (km)</Label>
                  <Input
                    id="mileage"
                    type="number"
                    {...form.register('mileage', { valueAsNumber: true })}
                    min={0}
                    placeholder={t('fleet:vehicles.currentMileage')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="purchaseDate">{t('fleet:vehicles.purchaseDate')}</Label>
                  <Input
                    id="purchaseDate"
                    type="date"
                    {...form.register('purchaseDate')}
                  />
                </div>
              </div>

              {vehicle && (
                <div className="space-y-2">
                  <Label htmlFor="status">{t('fleet:vehicles.status')}</Label>
                  <Select
                    value={form.watch('status')}
                    onValueChange={(value: string) => form.setValue('status', value as VehicleStatus)}
                  >
                    <SelectTrigger id="status" aria-label="Vehicle status">
                      <SelectValue placeholder={t('fleet:vehicles.selectStatus')} />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(VehicleStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          {t(`fleet:vehicles.${status.toLowerCase().replace('_', '')}`)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.status && (
                    <p className="text-sm text-destructive" role="alert">
                      {form.formState.errors.status.message}
                    </p>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="truck" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="fuelType">{t('fleet:vehicles.fuelType')}</Label>
                <Select
                  value={form.watch('fuelType') || ''}
                  onValueChange={(value: string) => form.setValue('fuelType', value as FuelType)}
                >
                  <SelectTrigger id="fuelType">
                    <SelectValue placeholder={t('fleet:vehicles.selectFuelType')} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(FuelType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {t(`fleet:vehicles.${type.toLowerCase()}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="engineType">{t('fleet:vehicles.engineType')}</Label>
                  <Input
                    id="engineType"
                    {...form.register('engineType')}
                    placeholder="e.g., D13K"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="transmission">{t('fleet:vehicles.transmission')}</Label>
                  <Select
                    value={form.watch('transmission') || ''}
                    onValueChange={(value: string) => form.setValue('transmission', value)}
                  >
                    <SelectTrigger id="transmission">
                      <SelectValue placeholder={t('fleet:vehicles.transmission')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MANUAL">{t('fleet:vehicles.manual')}</SelectItem>
                      <SelectItem value="AUTOMATIC">{t('fleet:vehicles.automatic')}</SelectItem>
                      <SelectItem value="SEMI_AUTOMATIC">{t('fleet:vehicles.semiAutomatic')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fuelCapacity">{t('fleet:vehicles.fuelCapacity')} (L)</Label>
                  <Input
                    id="fuelCapacity"
                    type="number"
                    {...form.register('fuelCapacity', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 400"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="axleConfiguration">{t('fleet:vehicles.axleConfiguration')}</Label>
                  <Select
                    value={form.watch('axleConfiguration') || ''}
                    onValueChange={(value: string) => form.setValue('axleConfiguration', value)}
                  >
                    <SelectTrigger id="axleConfiguration">
                      <SelectValue placeholder={t('fleet:vehicles.axleConfiguration')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="4x2">4x2</SelectItem>
                      <SelectItem value="6x2">6x2</SelectItem>
                      <SelectItem value="6x4">6x4</SelectItem>
                      <SelectItem value="8x4">8x4</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cabConfiguration">{t('fleet:vehicles.cabConfiguration')}</Label>
                <Select
                  value={form.watch('cabConfiguration') || ''}
                  onValueChange={(value: string) => form.setValue('cabConfiguration', value)}
                >
                  <SelectTrigger id="cabConfiguration">
                    <SelectValue placeholder={t('fleet:vehicles.cabConfiguration')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAY_CAB">{t('fleet:vehicles.daycab')}</SelectItem>
                    <SelectItem value="SLEEPER_CAB">{t('fleet:vehicles.sleeper')}</SelectItem>
                    <SelectItem value="EXTENDED_CAB">{t('fleet:vehicles.extendedCab')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="trailer" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="trailerType">{t('fleet:vehicles.trailerType')}</Label>
                <Select
                  value={form.watch('trailerType') || ''}
                  onValueChange={(value: string) => form.setValue('trailerType', value as TrailerType)}
                >
                  <SelectTrigger id="trailerType">
                    <SelectValue placeholder={t('fleet:vehicles.trailerType')} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TrailerType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {t(`fleet:vehicles.${type.toLowerCase().replace('_', '')}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cargoCapacity">{t('fleet:vehicles.cargoCapacity')} (m³)</Label>
                  <Input
                    id="cargoCapacity"
                    type="number"
                    {...form.register('cargoCapacity', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 90"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxWeight">{t('fleet:vehicles.maxWeight')} (kg)</Label>
                  <Input
                    id="maxWeight"
                    type="number"
                    {...form.register('maxWeight', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 40000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="length">{t('fleet:vehicles.length')} (m)</Label>
                  <Input
                    id="length"
                    type="number"
                    step="0.1"
                    {...form.register('length', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 13.6"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="width">{t('fleet:vehicles.width')} (m)</Label>
                  <Input
                    id="width"
                    type="number"
                    step="0.1"
                    {...form.register('width', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 2.5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="height">{t('fleet:vehicles.height')} (m)</Label>
                  <Input
                    id="height"
                    type="number"
                    step="0.1"
                    {...form.register('height', { valueAsNumber: true })}
                    min={0}
                    placeholder="e.g., 4.0"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasRefrigeration"
                  checked={form.watch('hasRefrigeration')}
                  onCheckedChange={(checked) => form.setValue('hasRefrigeration', !!checked)}
                />
                <Label htmlFor="hasRefrigeration">{t('fleet:vehicles.hasRefrigeration')}</Label>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              {t('common:actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {vehicle ? t('fleet:vehicles.updating') : t('fleet:vehicles.creating')}
                </>
              ) : (
                vehicle ? t('fleet:vehicles.updateVehicle') : t('fleet:vehicles.createVehicle')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
