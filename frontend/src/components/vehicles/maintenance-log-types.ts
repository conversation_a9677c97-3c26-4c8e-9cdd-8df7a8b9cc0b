// Shared types for maintenance logs
export interface MaintenanceLog {
  id: string;
  type: 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  category: 'ENGINE' | 'TRANSMISSION' | 'BRAKES' | 'ELECTRICAL' | 'TIRES' | 'OTHER';
  description: string;
  date: string;
  scheduledDate?: string;
  mileage?: number;
  cost?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
}
