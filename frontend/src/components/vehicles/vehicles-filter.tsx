'use client';

import { Button } from '@/components/ui/button';

interface VehiclesFilterProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedStatus: string;
  onStatusChange: (status: string) => void;
  onClearFilters: () => void;
}

export function VehiclesFilter({
  searchQuery,
  onSearchChange,
  selectedStatus,
  onStatusChange,
  onClearFilters,
}: VehiclesFilterProps) {
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6 space-y-4">
      <div className="sm:flex sm:space-x-4 space-y-4 sm:space-y-0">
        <div className="flex-1">
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <input
            type="text"
            id="search"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            placeholder="Search by plate number, make, or model..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        <div className="sm:w-48">
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            id="status"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            value={selectedStatus}
            onChange={(e) => onStatusChange(e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="AVAILABLE">Available</option>
            <option value="ASSIGNED">Assigned</option>
            <option value="MAINTENANCE">Maintenance</option>
          </select>
        </div>
        <div className="sm:w-32 flex items-end">
          <Button
            onClick={onClearFilters}
            className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200"
          >
            Clear Filters
          </Button>
        </div>
      </div>
    </div>
  );
}