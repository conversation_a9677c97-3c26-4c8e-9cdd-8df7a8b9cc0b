'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { 
  Vehicle, 
  VehicleType, 
  CreateTruckTrailerAssignmentRequest,
  TruckTrailerAssignment 
} from '@/types/vehicle';
import { FleetService } from '@/lib/api/fleet-service';
import { Plus, Link, Loader2 } from 'lucide-react';

const assignmentSchema = z.object({
  truckId: z.string().min(1, 'Truck is required'),
  trailerId: z.string().min(1, 'Trailer is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  notes: z.string().optional(),
});

type AssignmentFormData = z.infer<typeof assignmentSchema>;

interface TruckTrailerAssignmentDialogProps {
  assignment?: TruckTrailerAssignment;
  onSubmit: (data: CreateTruckTrailerAssignmentRequest) => Promise<void>;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function TruckTrailerAssignmentDialog({
  assignment,
  onSubmit,
  trigger,
  open: controlledOpen,
  onOpenChange
}: TruckTrailerAssignmentDialogProps) {
  const { t } = useTranslation(['fleet', 'common']);
  const [internalOpen, setInternalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableTrucks, setAvailableTrucks] = useState<Vehicle[]>([]);
  const [availableTrailers, setAvailableTrailers] = useState<Vehicle[]>([]);
  const [isLoadingVehicles, setIsLoadingVehicles] = useState(false);
  const { toast } = useToast();

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled ? (onOpenChange || (() => {})) : setInternalOpen;

  const form = useForm<AssignmentFormData>({
    resolver: zodResolver(assignmentSchema),
    defaultValues: {
      truckId: '',
      trailerId: '',
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      notes: '',
    },
  });

  // Load available vehicles when dialog opens
  useEffect(() => {
    if (open) {
      loadAvailableVehicles();
    }
  }, [open]);

  // Reset form when assignment changes or dialog opens
  useEffect(() => {
    if (open) {
      if (assignment) {
        form.reset({
          truckId: assignment.truckId,
          trailerId: assignment.trailerId,
          startDate: assignment.startDate.split('T')[0],
          endDate: assignment.endDate ? assignment.endDate.split('T')[0] : '',
          notes: assignment.notes || '',
        });
      } else {
        form.reset({
          truckId: '',
          trailerId: '',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          notes: '',
        });
      }
    }
  }, [assignment, open, form]);

  const loadAvailableVehicles = async () => {
    setIsLoadingVehicles(true);
    try {
      const [trucks, trailers] = await Promise.all([
        FleetService.getAvailableTrucks(),
        FleetService.getAvailableTrailers(),
      ]);
      setAvailableTrucks(trucks);
      setAvailableTrailers(trailers);
    } catch (error) {
      console.error('Error loading vehicles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load available vehicles',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingVehicles(false);
    }
  };

  const handleSubmit = async (data: AssignmentFormData) => {
    setIsSubmitting(true);
    try {
      const assignmentData: CreateTruckTrailerAssignmentRequest = {
        truckId: data.truckId,
        trailerId: data.trailerId,
        startDate: data.startDate,
        endDate: data.endDate || undefined,
        notes: data.notes || undefined,
      };

      await onSubmit(assignmentData);
      setOpen(false);
      form.reset();
      toast({
        title: t('common:success'),
        description: assignment ? t('fleet:messages.assignmentUpdatedSuccess') : t('fleet:messages.assignmentCreatedSuccess'),
      });
    } catch (error) {
      console.error('Error submitting assignment:', error);
      toast({
        title: t('common:error'),
        description: assignment ? t('fleet:messages.errorUpdatingAssignment') : t('fleet:messages.errorCreatingAssignment'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultTrigger = (
    <Button variant={assignment ? 'outline' : 'default'}>
      {assignment ? (
        <>
          <Link className="h-4 w-4 mr-2" />
          {t('fleet:vehicles.editAssignment')}
        </>
      ) : (
        <>
          <Plus className="h-4 w-4 mr-2" />
          {t('fleet:vehicles.createAssignment')}
        </>
      )}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-md" aria-describedby="assignment-dialog-description">
        <DialogHeader>
          <DialogTitle>
            {assignment ? t('fleet:vehicles.editAssignment') : t('fleet:vehicles.createAssignment')}
          </DialogTitle>
          <DialogDescription id="assignment-dialog-description">
            {assignment
              ? t('fleet:vehicles.updateAssignmentDescription')
              : t('fleet:vehicles.createAssignmentDescription')
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="truckId">{t('fleet:vehicles.truck')} *</Label>
            <Select
              value={form.watch('truckId')}
              onValueChange={(value: string) => form.setValue('truckId', value)}
              disabled={isLoadingVehicles}
            >
              <SelectTrigger>
                <SelectValue placeholder={isLoadingVehicles ? t('fleet:vehicles.loadingTrucks') : t('fleet:vehicles.selectTruck')} />
              </SelectTrigger>
              <SelectContent>
                {availableTrucks.map((truck) => (
                  <SelectItem key={truck.id} value={truck.id}>
                    {truck.plateNumber} - {truck.make} {truck.model} ({truck.year})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.truckId && (
              <p className="text-sm text-destructive">
                {form.formState.errors.truckId.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="trailerId">{t('fleet:vehicles.trailer')} *</Label>
            <Select
              value={form.watch('trailerId')}
              onValueChange={(value: string) => form.setValue('trailerId', value)}
              disabled={isLoadingVehicles}
            >
              <SelectTrigger>
                <SelectValue placeholder={isLoadingVehicles ? t('fleet:vehicles.loadingTrailers') : t('fleet:vehicles.selectTrailer')} />
              </SelectTrigger>
              <SelectContent>
                {availableTrailers.map((trailer) => (
                  <SelectItem key={trailer.id} value={trailer.id}>
                    {trailer.plateNumber} - {trailer.make} {trailer.model} ({trailer.year})
                    {trailer.trailerType && ` - ${trailer.trailerType.replace('_', ' ')}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.trailerId && (
              <p className="text-sm text-destructive">
                {form.formState.errors.trailerId.message}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">{t('fleet:vehicles.startDate')} *</Label>
              <Input
                id="startDate"
                type="date"
                {...form.register('startDate')}
              />
              {form.formState.errors.startDate && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.startDate.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">{t('fleet:vehicles.endDate')}</Label>
              <Input
                id="endDate"
                type="date"
                {...form.register('endDate')}
              />
            </div>
          </div>



          <div className="space-y-2">
            <Label htmlFor="notes">{t('common:common.notes')}</Label>
            <Textarea
              id="notes"
              {...form.register('notes')}
              placeholder={t('fleet:vehicles.assignmentNotesPlaceholder')}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isSubmitting}
            >
              {t('common:actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoadingVehicles}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {assignment ? t('fleet:vehicles.updating') : t('fleet:vehicles.creating')}
                </>
              ) : (
                assignment ? t('fleet:vehicles.updateAssignment') : t('fleet:vehicles.createAssignment')
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
