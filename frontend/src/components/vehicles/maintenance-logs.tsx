'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MaintenanceLogDialog } from './maintenance-log-dialog';

// Import the MaintenanceLog type from the dialog component
import type { MaintenanceLog } from './maintenance-log-types';

interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
}

interface MaintenanceLogsProps {
  vehicle: Vehicle;
  logs: MaintenanceLog[];
  onAddLog: (log: Omit<MaintenanceLog, 'id'>) => void;
  onEditLog: (id: string, log: Omit<MaintenanceLog, 'id'>) => void;
  onDeleteLog: (id: string) => void;
}

export function MaintenanceLogs({
  vehicle,
  logs,
  onAddLog,
  onEditLog,
  onDeleteLog,
}: MaintenanceLogsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<MaintenanceLog | undefined>(undefined);
  const [filterStatus, setFilterStatus] = useState<MaintenanceLog['status'] | 'ALL'>('ALL');
  const [filterCategory, setFilterCategory] = useState<MaintenanceLog['category'] | 'ALL'>('ALL');

  const handleAddClick = () => {
    setSelectedLog(undefined);
    setIsDialogOpen(true);
  };

  const handleEditClick = (log: MaintenanceLog) => {
    setSelectedLog(log);
    setIsDialogOpen(true);
  };

  const handleSubmit = (logData: Omit<MaintenanceLog, 'id'>) => {
    if (selectedLog) {
      onEditLog(selectedLog.id, logData);
    } else {
      onAddLog(logData);
    }
  };

  const getTypeColor = (type: MaintenanceLog['type']) => {
    switch (type) {
      case 'PREVENTIVE':
        return 'text-blue-700 bg-blue-50';
      case 'REPAIR':
        return 'text-red-700 bg-red-50';
      case 'INSPECTION':
        return 'text-green-700 bg-green-50';
      default:
        return 'text-gray-700 bg-gray-50';
    }
  };

  const getStatusColor = (status: MaintenanceLog['status']) => {
    switch (status) {
      case 'SCHEDULED':
        return 'text-yellow-700 bg-yellow-50';
      case 'IN_PROGRESS':
        return 'text-blue-700 bg-blue-50';
      case 'COMPLETED':
        return 'text-green-700 bg-green-50';
      case 'CANCELLED':
        return 'text-gray-700 bg-gray-50';
      default:
        return 'text-gray-700 bg-gray-50';
    }
  };

  const filteredLogs = logs.filter(log => {
    if (filterStatus !== 'ALL' && log.status !== filterStatus) return false;
    if (filterCategory !== 'ALL' && log.category !== filterCategory) return false;
    return true;
  });

  const scheduledMaintenance = logs.filter(log => log.status === 'SCHEDULED');
  const upcomingMaintenance = logs.filter(log => {
    if (!log.nextMaintenanceDate) return false;
    const nextDate = new Date(log.nextMaintenanceDate);
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return nextDate >= today && nextDate <= thirtyDaysFromNow;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Maintenance History</h3>
        <Button
          onClick={handleAddClick}
          className="bg-blue-600 hover:bg-blue-500"
        >
          Add Maintenance Log
        </Button>
      </div>

      {/* Maintenance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white shadow rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-500">Scheduled Maintenance</h4>
          <p className="mt-2 text-2xl font-semibold text-gray-900">{scheduledMaintenance.length}</p>
          {scheduledMaintenance.length > 0 && (
            <p className="mt-2 text-sm text-gray-500">
              Next: {new Date(scheduledMaintenance[0].scheduledDate!).toLocaleDateString()}
            </p>
          )}
        </div>
        
        <div className="bg-white shadow rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-500">Upcoming Maintenance</h4>
          <p className="mt-2 text-2xl font-semibold text-gray-900">{upcomingMaintenance.length}</p>
          {upcomingMaintenance.length > 0 && (
            <p className="mt-2 text-sm text-gray-500">
              Within next 30 days
            </p>
          )}
        </div>

        <div className="bg-white shadow rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-500">Total Cost (YTD)</h4>
          <p className="mt-2 text-2xl font-semibold text-gray-900">
            ${logs
              .filter(log => log.status === 'COMPLETED' && new Date(log.date).getFullYear() === new Date().getFullYear())
              .reduce((sum, log) => sum + (log.cost || 0), 0)
              .toFixed(2)}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <select
          className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as MaintenanceLog['status'] | 'ALL')}
        >
          <option value="ALL">All Statuses</option>
          <option value="SCHEDULED">Scheduled</option>
          <option value="IN_PROGRESS">In Progress</option>
          <option value="COMPLETED">Completed</option>
          <option value="CANCELLED">Cancelled</option>
        </select>

        <select
          className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value as MaintenanceLog['category'] | 'ALL')}
        >
          <option value="ALL">All Categories</option>
          <option value="ENGINE">Engine</option>
          <option value="TRANSMISSION">Transmission</option>
          <option value="BRAKES">Brakes</option>
          <option value="ELECTRICAL">Electrical</option>
          <option value="TIRES">Tires</option>
          <option value="OTHER">Other</option>
        </select>
      </div>

      {logs.length === 0 ? (
        <p className="text-gray-500 text-center py-4">No maintenance logs found</p>
      ) : (
        <div className="overflow-x-auto shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                  Date
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Type
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Category
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Description
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Cost
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Next Service
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {filteredLogs.map((log) => (
                <tr key={log.id}>
                  <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6">
                    {new Date(log.status === 'SCHEDULED' ? log.scheduledDate! : log.date).toLocaleDateString()}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${getStatusColor(log.status)}`}>
                      {log.status}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${getTypeColor(log.type)}`}>
                      {log.type}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {log.category}
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {log.description}
                    {log.notes && (
                      <p className="text-xs text-gray-400 mt-1">{log.notes}</p>
                    )}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    ${(log.cost || 0).toFixed(2)}
                    {(log.partsCost || 0) > 0 && (
                      <p className="text-xs text-gray-400">Parts: ${log.partsCost?.toFixed(2)}</p>
                    )}
                    {(log.laborCost || 0) > 0 && (
                      <p className="text-xs text-gray-400">Labor: ${log.laborCost?.toFixed(2)}</p>
                    )}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {log.nextMaintenanceDate ? (
                      <div>
                        <p>{new Date(log.nextMaintenanceDate).toLocaleDateString()}</p>
                        {log.nextMaintenanceMileage && (
                          <p className="text-xs text-gray-400">{log.nextMaintenanceMileage.toLocaleString()} miles</p>
                        )}
                      </div>
                    ) : '-'}
                  </td>
                  <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                    <button
                      onClick={() => handleEditClick(log)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => onDeleteLog(log.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <MaintenanceLogDialog
        isOpen={isDialogOpen}
        onClose={() => {
          setIsDialogOpen(false);
          setSelectedLog(undefined);
        }}
        onSubmit={handleSubmit}
        vehicleId={vehicle.id}
        vehicleName={`${vehicle.make} ${vehicle.model} (${vehicle.plateNumber})`}
        initialLog={selectedLog}
      />
    </div>
  );
}
