'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  VehicleType, 
  TrailerType, 
  FuelType, 
  VehicleStatus, 
  CreateVehicleRequest,
  Vehicle 
} from '@/types/vehicle';
import { Loader2 } from 'lucide-react';

// Enhanced schema with conditional validation
const vehicleSchema = z.object({
  plateNumber: z.string().min(1, 'Plate number is required').max(20, 'Plate number too long'),
  make: z.string().min(1, 'Make is required').max(50, 'Make too long'),
  model: z.string().min(1, 'Model is required').max(50, 'Model too long'),
  year: z.number()
    .min(1900, 'Invalid year')
    .max(new Date().getFullYear() + 1, 'Year cannot be in the future'),
  vehicleType: z.nativeEnum(VehicleType),
  vin: z.string().optional(),
  color: z.string().optional(),
  mileage: z.number().min(0, 'Mileage cannot be negative').optional(),
  fuelType: z.nativeEnum(FuelType).optional(),
  purchaseDate: z.string().optional(),
  
  // Truck-specific fields
  engineType: z.string().optional(),
  transmission: z.string().optional(),
  fuelCapacity: z.number().min(0, 'Fuel capacity cannot be negative').optional(),
  axleConfiguration: z.string().optional(),
  cabConfiguration: z.string().optional(),
  
  // Trailer-specific fields
  trailerType: z.nativeEnum(TrailerType).optional(),
  cargoCapacity: z.number().min(0, 'Cargo capacity cannot be negative').optional(),
  maxWeight: z.number().min(0, 'Max weight cannot be negative').optional(),
  length: z.number().min(0, 'Length cannot be negative').optional(),
  width: z.number().min(0, 'Width cannot be negative').optional(),
  height: z.number().min(0, 'Height cannot be negative').optional(),
  hasRefrigeration: z.boolean().optional(),
}).refine((data) => {
  // Conditional validation for truck fields
  if (data.vehicleType === VehicleType.TRUCK) {
    return true; // All truck fields are optional
  }
  return true;
}, {
  message: "Invalid truck configuration",
}).refine((data) => {
  // Conditional validation for trailer fields
  if (data.vehicleType === VehicleType.TRAILER) {
    return true; // All trailer fields are optional
  }
  return true;
}, {
  message: "Invalid trailer configuration",
});

type VehicleFormData = z.infer<typeof vehicleSchema>;

interface EnhancedVehicleFormProps {
  vehicle?: Vehicle;
  onSubmit: (data: CreateVehicleRequest) => Promise<void>;
  onCancel?: () => void;
  isSubmitting?: boolean;
}

export function EnhancedVehicleForm({ 
  vehicle, 
  onSubmit, 
  onCancel, 
  isSubmitting = false 
}: EnhancedVehicleFormProps) {
  const [selectedVehicleType, setSelectedVehicleType] = useState<VehicleType>(VehicleType.TRUCK);

  const form = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      plateNumber: '',
      make: '',
      model: '',
      year: new Date().getFullYear(),
      vehicleType: VehicleType.TRUCK,
      vin: '',
      color: '',
      mileage: 0,
      fuelType: FuelType.DIESEL,
      purchaseDate: '',
      
      // Truck defaults
      engineType: '',
      transmission: '',
      fuelCapacity: 0,
      axleConfiguration: '',
      cabConfiguration: '',
      
      // Trailer defaults
      trailerType: TrailerType.DRY_VAN,
      cargoCapacity: 0,
      maxWeight: 0,
      length: 0,
      width: 0,
      height: 0,
      hasRefrigeration: false,
    },
  });

  // Update form when vehicle changes
  useEffect(() => {
    if (vehicle) {
      form.reset({
        plateNumber: vehicle.plateNumber,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year,
        vehicleType: vehicle.vehicleType,
        vin: vehicle.vin || '',
        color: vehicle.color || '',
        mileage: vehicle.mileage || 0,
        fuelType: vehicle.fuelType || FuelType.DIESEL,
        purchaseDate: vehicle.purchaseDate || '',
        
        // Truck fields
        engineType: vehicle.engineType || '',
        transmission: vehicle.transmission || '',
        fuelCapacity: vehicle.fuelCapacity || 0,
        axleConfiguration: vehicle.axleConfiguration || '',
        cabConfiguration: vehicle.cabConfiguration || '',
        
        // Trailer fields
        trailerType: vehicle.trailerType || TrailerType.DRY_VAN,
        cargoCapacity: vehicle.cargoCapacity || 0,
        maxWeight: vehicle.maxWeight || 0,
        length: vehicle.length || 0,
        width: vehicle.width || 0,
        height: vehicle.height || 0,
        hasRefrigeration: vehicle.hasRefrigeration || false,
      });
      setSelectedVehicleType(vehicle.vehicleType);
    }
  }, [vehicle, form]);

  // Watch vehicle type changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'vehicleType' && value.vehicleType) {
        setSelectedVehicleType(value.vehicleType as VehicleType);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const handleSubmit = async (data: VehicleFormData) => {
    // Clean up data based on vehicle type
    const cleanedData: CreateVehicleRequest = {
      plateNumber: data.plateNumber,
      make: data.make,
      model: data.model,
      year: data.year,
      vehicleType: data.vehicleType,
      vin: data.vin || undefined,
      color: data.color || undefined,
      mileage: data.mileage || undefined,
      fuelType: data.fuelType || undefined,
      purchaseDate: data.purchaseDate || undefined,
    };

    // Add type-specific fields
    if (data.vehicleType === VehicleType.TRUCK) {
      cleanedData.engineType = data.engineType || undefined;
      cleanedData.transmission = data.transmission || undefined;
      cleanedData.fuelCapacity = data.fuelCapacity || undefined;
      cleanedData.axleConfiguration = data.axleConfiguration || undefined;
      cleanedData.cabConfiguration = data.cabConfiguration || undefined;
    } else if (data.vehicleType === VehicleType.TRAILER) {
      cleanedData.trailerType = data.trailerType || undefined;
      cleanedData.cargoCapacity = data.cargoCapacity || undefined;
      cleanedData.maxWeight = data.maxWeight || undefined;
      cleanedData.length = data.length || undefined;
      cleanedData.width = data.width || undefined;
      cleanedData.height = data.height || undefined;
      cleanedData.hasRefrigeration = data.hasRefrigeration || undefined;
    }

    await onSubmit(cleanedData);
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Enter the basic details for the vehicle
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="plateNumber">Plate Number *</Label>
              <Input
                id="plateNumber"
                {...form.register('plateNumber')}
                placeholder="e.g., ABC123"
              />
              {form.formState.errors.plateNumber && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.plateNumber.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="vehicleType">Vehicle Type *</Label>
              <Select
                value={form.watch('vehicleType')}
                onValueChange={(value: VehicleType) => form.setValue('vehicleType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select vehicle type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={VehicleType.TRUCK}>Truck</SelectItem>
                  <SelectItem value={VehicleType.TRAILER}>Trailer</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.vehicleType && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.vehicleType.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="make">Make *</Label>
              <Input
                id="make"
                {...form.register('make')}
                placeholder="e.g., Volvo"
              />
              {form.formState.errors.make && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.make.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="model">Model *</Label>
              <Input
                id="model"
                {...form.register('model')}
                placeholder="e.g., VNL 860"
              />
              {form.formState.errors.model && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.model.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Year *</Label>
              <Input
                id="year"
                type="number"
                {...form.register('year', { valueAsNumber: true })}
                min={1900}
                max={new Date().getFullYear() + 1}
              />
              {form.formState.errors.year && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.year.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vin">VIN</Label>
              <Input
                id="vin"
                {...form.register('vin')}
                placeholder="17-character VIN"
                maxLength={17}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Input
                id="color"
                {...form.register('color')}
                placeholder="e.g., White"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mileage">Mileage</Label>
              <Input
                id="mileage"
                type="number"
                {...form.register('mileage', { valueAsNumber: true })}
                min={0}
                placeholder="Current mileage"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fuelType">Fuel Type</Label>
              <Select
                value={form.watch('fuelType')}
                onValueChange={(value: FuelType) => form.setValue('fuelType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select fuel type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(FuelType).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="purchaseDate">Purchase Date</Label>
              <Input
                id="purchaseDate"
                type="date"
                {...form.register('purchaseDate')}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Conditional Type-Specific Fields */}
      {selectedVehicleType === VehicleType.TRUCK && (
        <Card>
          <CardHeader>
            <CardTitle>Truck Specifications</CardTitle>
            <CardDescription>
              Enter truck-specific details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="engineType">Engine Type</Label>
                <Input
                  id="engineType"
                  {...form.register('engineType')}
                  placeholder="e.g., D13TC"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="transmission">Transmission</Label>
                <Input
                  id="transmission"
                  {...form.register('transmission')}
                  placeholder="e.g., I-Shift"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fuelCapacity">Fuel Capacity (gallons)</Label>
                <Input
                  id="fuelCapacity"
                  type="number"
                  {...form.register('fuelCapacity', { valueAsNumber: true })}
                  min={0}
                  step="0.1"
                  placeholder="e.g., 150"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="axleConfiguration">Axle Configuration</Label>
                <Input
                  id="axleConfiguration"
                  {...form.register('axleConfiguration')}
                  placeholder="e.g., 6x4"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cabConfiguration">Cab Configuration</Label>
                <Input
                  id="cabConfiguration"
                  {...form.register('cabConfiguration')}
                  placeholder="e.g., Sleeper"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {selectedVehicleType === VehicleType.TRAILER && (
        <Card>
          <CardHeader>
            <CardTitle>Trailer Specifications</CardTitle>
            <CardDescription>
              Enter trailer-specific details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="trailerType">Trailer Type</Label>
                <Select
                  value={form.watch('trailerType')}
                  onValueChange={(value: TrailerType) => form.setValue('trailerType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select trailer type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TrailerType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type.replace('_', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cargoCapacity">Cargo Capacity (cubic feet)</Label>
                <Input
                  id="cargoCapacity"
                  type="number"
                  {...form.register('cargoCapacity', { valueAsNumber: true })}
                  min={0}
                  step="0.1"
                  placeholder="e.g., 3000"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxWeight">Max Weight (lbs)</Label>
                <Input
                  id="maxWeight"
                  type="number"
                  {...form.register('maxWeight', { valueAsNumber: true })}
                  min={0}
                  placeholder="e.g., 80000"
                />
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="space-y-2">
                  <Label htmlFor="length">Length (ft)</Label>
                  <Input
                    id="length"
                    type="number"
                    {...form.register('length', { valueAsNumber: true })}
                    min={0}
                    step="0.1"
                    placeholder="53"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="width">Width (ft)</Label>
                  <Input
                    id="width"
                    type="number"
                    {...form.register('width', { valueAsNumber: true })}
                    min={0}
                    step="0.1"
                    placeholder="8.5"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="height">Height (ft)</Label>
                  <Input
                    id="height"
                    type="number"
                    {...form.register('height', { valueAsNumber: true })}
                    min={0}
                    step="0.1"
                    placeholder="13.6"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasRefrigeration"
                checked={form.watch('hasRefrigeration')}
                onCheckedChange={(checked: boolean) => form.setValue('hasRefrigeration', !!checked)}
              />
              <Label htmlFor="hasRefrigeration">Has Refrigeration Unit</Label>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Form Actions */}
      <div className="flex justify-end gap-3">
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {vehicle ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            vehicle ? 'Update Vehicle' : 'Create Vehicle'
          )}
        </Button>
      </div>
    </form>
  );
}
