import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { RefreshCw, TrendingUp, TrendingDown, AlertCircle, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { useToast } from '@/components/ui/use-toast';
import { fuelPricesApi, FuelPrice } from '@/lib/api/fuel-api';



interface ScrapingStatus {
  success: boolean;
  lastAttempt: string;
  nextRetry?: string;
  errorMessage?: string;
  retryCount: number;
}

export const FuelPriceMonitor: React.FC = () => {
  const { t } = useTranslation(['fuel', 'common']);
  const [currentPrice, setCurrentPrice] = useState<FuelPrice | null>(null);
  const [priceHistory, setPriceHistory] = useState<FuelPrice[]>([]);
  const [scrapingStatus, setScrapingStatus] = useState<ScrapingStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [manualFetchLoading, setManualFetchLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadCurrentPrice();
    loadPriceHistory();
    loadScrapingStatus();
  }, []);

  const loadCurrentPrice = async () => {
    try {
      const data = await fuelPricesApi.getCurrentPrice();
      // Map backend format to frontend format
      if (data) {
        setCurrentPrice({
          id: '1',
          effectiveDate: data.effectiveDate,
          dieselPriceNet: data.priceNet,
          dieselPriceGross: data.priceGross,
          vatRate: 0.23,
          source: 'orlen.pl',
          scrapedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Failed to load current price:', error);
    }
  };

  const loadPriceHistory = async () => {
    try {
      const data = await fuelPricesApi.getPriceHistory(30);
      // Map backend format to frontend format
      if (data && Array.isArray(data)) {
        const mappedHistory = data.map((price: any, index: number) => ({
          id: String(index),
          effectiveDate: price.effectiveDate,
          dieselPriceNet: price.dieselPriceNet,
          dieselPriceGross: price.dieselPriceGross,
          vatRate: price.vatRate || 0.23,
          source: price.source || 'orlen.pl',
          scrapedAt: price.scrapedAt || price.createdAt || new Date().toISOString(),
        }));
        setPriceHistory(mappedHistory);
      }
    } catch (error) {
      console.error('Failed to load price history:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadScrapingStatus = async () => {
    try {
      const data = await fuelPricesApi.getScrapingStatus();
      setScrapingStatus(data);
    } catch (error) {
      console.error('Failed to load scraping status:', error);
    }
  };

  const handleManualFetch = async () => {
    setManualFetchLoading(true);
    try {
      await fuelPricesApi.manualFetch();

      toast({
        title: t('common:success'),
        description: t('fuel:prices.messages.fetchSuccess'),
      });

      // Reload data
      await Promise.all([
        loadCurrentPrice(),
        loadPriceHistory(),
        loadScrapingStatus(),
      ]);
    } catch (error) {
      console.error('Manual fetch failed:', error);
      toast({
        title: t('common:error'),
        description: t('fuel:prices.messages.fetchError'),
        variant: 'destructive',
      });
    } finally {
      setManualFetchLoading(false);
    }
  };

  const calculatePriceTrend = () => {
    if (!priceHistory || priceHistory.length < 2) return null;

    // Sort chronologically to get the correct latest and previous
    const sortedHistory = priceHistory.sort((a, b) => new Date(a.effectiveDate).getTime() - new Date(b.effectiveDate).getTime());
    const latest = sortedHistory[sortedHistory.length - 1];
    const previous = sortedHistory[sortedHistory.length - 2];

    if (!latest || !previous || !latest.dieselPriceGross || !previous.dieselPriceGross) {
      return null;
    }

    // Use rounded prices for calculation to match displayed values
    const latestRounded = Number(latest.dieselPriceGross.toFixed(2));
    const previousRounded = Number(previous.dieselPriceGross.toFixed(2));

    const change = latestRounded - previousRounded;
    const percentChange = (change / previousRounded) * 100;

    return {
      change,
      percentChange,
      isIncrease: change > 0,
    };
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) {
      return '---';
    }
    try {
      return new Date(dateString).toLocaleDateString('pl-PL');
    } catch (error) {
      return '---';
    }
  };

  const formatCurrency = (amount: number | undefined | null) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '---';
    }
    return amount.toFixed(2) + ' PLN';
  };

  const chartData = priceHistory && priceHistory.length > 0 ? priceHistory
    .sort((a, b) => new Date(a.effectiveDate).getTime() - new Date(b.effectiveDate).getTime()) // Sort chronologically
    .map(price => ({
      date: formatDate(price.effectiveDate),
      net: Number(price.dieselPriceNet?.toFixed(2)) || 0,
      gross: Number(price.dieselPriceGross?.toFixed(2)) || 0,
    })) : [];

  const trend = calculatePriceTrend();

  return (
    <div className="space-y-6">
      {/* Current Price Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:prices.currentPriceGross')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currentPrice ? formatCurrency(currentPrice.dieselPriceGross) : '---'}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-xs text-muted-foreground">
                {t('fuel:prices.netPrice')}: {currentPrice ? formatCurrency(currentPrice.dieselPriceNet) : '---'}
              </p>
              <Badge variant="secondary" className="text-xs">
                {t('fuel:prices.vatIncluded')}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:prices.priceTrend')}</CardTitle>
            {trend?.isIncrease ? (
              <TrendingUp className="h-4 w-4 text-red-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {trend ? (
                <span className={trend.isIncrease ? 'text-red-500' : 'text-green-500'}>
                  {trend.isIncrease ? '+' : ''}{trend.percentChange.toFixed(2)}%
                </span>
              ) : (
                '---'
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {trend ? `${trend.isIncrease ? '+' : ''}${trend.change.toFixed(2)} PLN ${t('fuel:prices.vsYesterday')}` : t('fuel:prices.noData')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:prices.scrapingStatus')}</CardTitle>
            {scrapingStatus?.success ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={scrapingStatus?.success ? "default" : "destructive"}>
                {scrapingStatus?.success ? t('fuel:prices.active') : t('fuel:prices.issues')}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {t('fuel:prices.lastUpdate')}: {scrapingStatus ? formatDate(scrapingStatus.lastAttempt) : '---'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Scraping Status Alert */}
      {scrapingStatus && !scrapingStatus.success && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>{t('fuel:prices.priceScrapingIssues')}</AlertTitle>
          <AlertDescription>
            {scrapingStatus.errorMessage || t('fuel:prices.scrapingIssuesDesc')}
            {scrapingStatus.retryCount > 0 && ` (${t('fuel:prices.retryAttempt')}: ${scrapingStatus.retryCount})`}
          </AlertDescription>
        </Alert>
      )}

      {/* Price History Chart */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{t('fuel:prices.priceHistory30Days')}</CardTitle>
            <CardDescription>
              {t('fuel:prices.orlenPricesDesc')}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualFetch}
            disabled={manualFetchLoading}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${manualFetchLoading ? 'animate-spin' : ''}`} />
            {manualFetchLoading ? t('fuel:prices.fetching') : t('fuel:prices.manualFetch')}
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-[300px] flex items-center justify-center">
              <p className="text-muted-foreground">{t('fuel:prices.loadingPriceHistory')}</p>
            </div>
          ) : chartData.length > 0 ? (
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `${value.toFixed(2)}`}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-2 shadow-sm">
                            <div className="grid grid-cols-2 gap-2">
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  {t('fuel:prices.date')}
                                </span>
                                <span className="font-bold text-muted-foreground">
                                  {label}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  {t('fuel:prices.netPriceLabel')}
                                </span>
                                <span className="font-bold">
                                  {payload[0]?.value?.toFixed(2)} PLN
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                  {t('fuel:prices.grossPrice')}
                                </span>
                                <span className="font-bold text-blue-600">
                                  {payload[1]?.value?.toFixed(2)} PLN
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="net"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={false}
                    name={t('fuel:prices.netPriceLabel')}
                  />
                  <Line
                    type="monotone"
                    dataKey="gross"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    dot={false}
                    name={t('fuel:prices.grossPriceLabel')}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-[300px] flex items-center justify-center">
              <p className="text-muted-foreground">{t('fuel:prices.noPriceHistoryData')}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Price Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fuel:prices.currentPriceDetails')}</CardTitle>
          <CardDescription>
            {t('fuel:prices.latestFuelPriceInfo')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentPrice ? (
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.effectiveDate')}:</span>
                  <span className="text-sm">{formatDate(currentPrice.effectiveDate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.netPriceLabel')}:</span>
                  <span className="text-sm font-mono">{formatCurrency(currentPrice.dieselPriceNet)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.vatLabel')}:</span>
                  <span className="text-sm font-mono">
                    {formatCurrency(currentPrice.dieselPriceGross - currentPrice.dieselPriceNet)}
                  </span>
                </div>
                <div className="flex justify-between font-medium">
                  <span className="text-sm">{t('fuel:prices.grossPriceLabel')}:</span>
                  <span className="text-sm font-mono">{formatCurrency(currentPrice.dieselPriceGross)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.source')}:</span>
                  <Badge variant="outline">{currentPrice.source}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.lastScraped')}:</span>
                  <span className="text-sm">{formatDate(currentPrice.scrapedAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{t('fuel:prices.vatRate')}:</span>
                  <span className="text-sm">{(currentPrice.vatRate * 100).toFixed(0)}%</span>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">{t('fuel:prices.noCurrentPriceData')}</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
