import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart3, TrendingUp, TrendingDown, Fuel, Calendar, RefreshCw } from 'lucide-react';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { fuelRecordsApi, vehiclesApi, driversApi } from '@/lib/api/fuel-api';

interface AnalyticsData {
  totalCost: number;
  totalLiters: number;
  averageEfficiency: number;
  recordCount: number;
  costTrend: number;
  volumeTrend: number;
  topVehicles: Array<{
    id: string;
    plateNumber: string;
    totalCost: number;
    totalLiters: number;
    efficiency: number;
  }>;
  topDrivers: Array<{
    id: string;
    name: string;
    totalCost: number;
    totalLiters: number;
    efficiency: number;
  }>;
  monthlyData: Array<{
    month: string;
    cost: number;
    liters: number;
  }>;
  costBreakdown: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

export const FuelAnalytics: React.FC = () => {
  const { t } = useTranslation(['fuel', 'common']);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // This is a placeholder - you would implement actual analytics API calls
      const mockData: AnalyticsData = {
        totalCost: 45230.50,
        totalLiters: 7850.25,
        averageEfficiency: 28.5,
        recordCount: 156,
        costTrend: 12.5,
        volumeTrend: -3.2,
        topVehicles: [
          { id: '1', plateNumber: 'WZ 12345', totalCost: 8500, totalLiters: 1200, efficiency: 25.2 },
          { id: '2', plateNumber: 'WZ 67890', totalCost: 7200, totalLiters: 1050, efficiency: 27.8 },
          { id: '3', plateNumber: 'WZ 11111', totalCost: 6800, totalLiters: 980, efficiency: 29.1 },
        ],
        topDrivers: [
          { id: '1', name: 'Jan Kowalski', totalCost: 5200, totalLiters: 750, efficiency: 26.5 },
          { id: '2', name: 'Anna Nowak', totalCost: 4800, totalLiters: 720, efficiency: 28.2 },
          { id: '3', name: 'Piotr Wiśniewski', totalCost: 4200, totalLiters: 650, efficiency: 30.1 },
        ],
        monthlyData: [
          { month: t('fuel:analytics.months.jan'), cost: 12500, liters: 1800 },
          { month: t('fuel:analytics.months.feb'), cost: 11200, liters: 1650 },
          { month: t('fuel:analytics.months.mar'), cost: 13800, liters: 1950 },
          { month: t('fuel:analytics.months.apr'), cost: 14200, liters: 2100 },
          { month: t('fuel:analytics.months.may'), cost: 15500, liters: 2200 },
          { month: t('fuel:analytics.months.jun'), cost: 16800, liters: 2350 },
        ],
        costBreakdown: [
          { name: t('fuel:analytics.vehicleTypes.trucks'), value: 32500, color: '#0088FE' },
          { name: t('fuel:analytics.vehicleTypes.deliveryVehicles'), value: 8200, color: '#00C49F' },
          { name: t('fuel:analytics.vehicleTypes.serviceVehicles'), value: 4530, color: '#FFBB28' },
        ],
      };
      setData(mockData);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    });
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-red-500' : 'text-green-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('fuel:analytics.loadingAnalytics')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('fuel:analytics.noAnalyticsData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">{t('fuel:analytics.timeRanges.last7Days')}</SelectItem>
              <SelectItem value="30">{t('fuel:analytics.timeRanges.last30Days')}</SelectItem>
              <SelectItem value="90">{t('fuel:analytics.timeRanges.last3Months')}</SelectItem>
              <SelectItem value="365">{t('fuel:analytics.timeRanges.lastYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('fuel:analytics.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:analytics.metrics.totalFuelCost')}</CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.totalCost)}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:analytics.metrics.vsPreviousPeriod')}</p>
              {formatTrend(data.costTrend)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:analytics.metrics.totalVolume')}</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalLiters.toLocaleString()} L</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:analytics.metrics.vsPreviousPeriod')}</p>
              {formatTrend(data.volumeTrend)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:analytics.metrics.avgEfficiency')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.averageEfficiency} L/100km</div>
            <p className="text-xs text-muted-foreground">{t('fuel:analytics.metrics.fleetAverage')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:analytics.metrics.totalRecords')}</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.recordCount}</div>
            <p className="text-xs text-muted-foreground">{t('fuel:analytics.metrics.fuelRecords')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Monthly Trend */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:analytics.charts.monthlyFuelConsumption')}</CardTitle>
            <CardDescription>{t('fuel:analytics.charts.costAndVolumeTrends')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" label={{ value: t('fuel:analytics.chartLabels.pln'), angle: -90, position: 'insideLeft' }} />
                  <YAxis yAxisId="right" orientation="right" label={{ value: t('fuel:analytics.chartLabels.liters'), angle: 90, position: 'insideRight' }} />
                  <Tooltip
                    labelFormatter={(label) => `${t('fuel:analytics.chartLabels.month')}: ${label}`}
                    formatter={(value, name, props) => [
                      value,
                      props.dataKey === 'cost' ? t('fuel:analytics.chartLabels.cost') : t('fuel:analytics.chartLabels.volume')
                    ]}
                  />
                  <Line yAxisId="left" type="monotone" dataKey="cost" stroke="#8884d8" strokeWidth={2} name={t('fuel:analytics.chartLabels.cost')} />
                  <Line yAxisId="right" type="monotone" dataKey="liters" stroke="#82ca9d" strokeWidth={2} name={t('fuel:analytics.chartLabels.volume')} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Cost Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:analytics.charts.costBreakdownByVehicleType')}</CardTitle>
            <CardDescription>{t('fuel:analytics.charts.distributionOfFuelCosts')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.costBreakdown}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.costBreakdown.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [formatCurrency(Number(value)), t('fuel:analytics.chartLabels.cost')]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Vehicles */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:analytics.topPerformers.topVehiclesByCost')}</CardTitle>
            <CardDescription>{t('fuel:analytics.topPerformers.highestFuelConsumptionVehicles')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.topVehicles.map((vehicle, index) => (
                <div key={vehicle.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">#{index + 1}</Badge>
                    <div>
                      <p className="font-medium">{vehicle.plateNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {vehicle.efficiency} L/100km
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(vehicle.totalCost)}</p>
                    <p className="text-sm text-muted-foreground">{vehicle.totalLiters} L</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Drivers */}
        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:analytics.topPerformers.topDriversByEfficiency')}</CardTitle>
            <CardDescription>{t('fuel:analytics.topPerformers.mostFuelEfficientDrivers')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.topDrivers.map((driver, index) => (
                <div key={driver.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">#{index + 1}</Badge>
                    <div>
                      <p className="font-medium">{driver.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {driver.efficiency} L/100km
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(driver.totalCost)}</p>
                    <p className="text-sm text-muted-foreground">{driver.totalLiters} L</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
