import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { X, Upload, Calculator } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { fuelRecordsApi, vehiclesApi, driversApi, Vehicle, Driver } from '@/lib/api/fuel-api';

const createFuelRecordSchema = (t: any) => z.object({
  vehicleId: z.string().min(1, t('fuel:validation.vehicleRequired')),
  driverId: z.string().min(1, t('fuel:validation.driverRequired')),
  quantity: z.number().min(0.1, t('fuel:validation.quantityMin')).max(1000, t('fuel:validation.quantityMax')),
  totalCost: z.number().min(0.01, t('fuel:validation.totalCostMin')).max(10000, t('fuel:validation.totalCostMax')),
  location: z.string().min(1, t('fuel:validation.locationRequired')),
  fuelingDate: z.string().min(1, t('fuel:validation.fuelingDateRequired')),
  odometerReading: z.number().min(0, t('fuel:validation.odometerPositive')).max(9999999, t('fuel:validation.odometerMax')),
  receiptNumber: z.string().optional(),
  notes: z.string().optional(),
  enteredBy: z.string().min(1, t('fuel:validation.enteredByRequired')),
});

type FuelRecordFormData = z.infer<ReturnType<typeof createFuelRecordSchema>>;

interface FuelRecordFormProps {
  onClose?: () => void;
  onSuccess?: () => void;
  onRecordAdded?: () => void;
  onSubmitStart?: () => void;
  editRecord?: any; // For editing existing records
  isSubmitting?: boolean;
  showAsDialog?: boolean;
}



export const FuelRecordForm: React.FC<FuelRecordFormProps> = ({
  onClose,
  onSuccess,
  onRecordAdded,
  onSubmitStart,
  editRecord,
  isSubmitting = false,
  showAsDialog = true,
}) => {
  const { t } = useTranslation(['fuel', 'common']);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(false);
  const [receiptFile, setReceiptFile] = useState<File | null>(null);
  const { toast } = useToast();

  const form = useForm<FuelRecordFormData>({
    resolver: zodResolver(createFuelRecordSchema(t)),
    defaultValues: {
      vehicleId: editRecord?.vehicleId || '',
      driverId: editRecord?.driverId || '',
      quantity: editRecord?.quantity || 0,
      totalCost: editRecord?.totalCost || 0,
      location: editRecord?.location || '',
      fuelingDate: editRecord?.fuelingDate ? new Date(editRecord.fuelingDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      odometerReading: editRecord?.odometerReading || 0,
      receiptNumber: editRecord?.receiptNumber || '',
      notes: editRecord?.notes || '',
      enteredBy: editRecord?.enteredBy || 'Admin', // TODO: Get from current user
    },
  });

  useEffect(() => {
    loadVehicles();
    loadDrivers();
  }, []);

  const loadVehicles = async () => {
    try {
      const data = await vehiclesApi.getTrucks();
      setVehicles(data);
    } catch (error) {
      console.error('Failed to load vehicles:', error);
    }
  };

  const loadDrivers = async () => {
    try {
      const data = await driversApi.getActiveDrivers();
      setDrivers(data);
    } catch (error) {
      console.error('Failed to load drivers:', error);
    }
  };

  const calculatePricePerLiter = (quantity: number, totalCost: number): string => {
    if (quantity > 0 && totalCost > 0) {
      return (totalCost / quantity).toFixed(2);
    }
    return '0.00';
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: t('fuel:validation.invalidFileType'),
          description: t('fuel:validation.fileTypeDescription'),
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: t('fuel:validation.fileTooLarge'),
          description: t('fuel:validation.fileSizeDescription'),
          variant: 'destructive',
        });
        return;
      }

      setReceiptFile(file);
    }
  };

  const onSubmit = async (data: FuelRecordFormData) => {
    if (onSubmitStart) {
      onSubmitStart();
    }
    setLoading(true);

    try {
      if (editRecord) {
        // Update existing record
        await fuelRecordsApi.updateRecord(editRecord.id, data, receiptFile || undefined);
      } else {
        // Create new record
        await fuelRecordsApi.createRecord(data, receiptFile || undefined);
      }

      toast({
        title: t('common:success'),
        description: editRecord ? t('fuel:messages.updateSuccess') : t('fuel:messages.createSuccess'),
      });

      if (onSuccess) {
        onSuccess();
      }
      if (onRecordAdded) {
        onRecordAdded();
      }
    } catch (error) {
      console.error('Failed to save fuel record:', error);
      toast({
        title: t('common:error'),
        description: editRecord ? t('fuel:messages.updateError') : t('fuel:messages.createError'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const quantity = form.watch('quantity');
  const totalCost = form.watch('totalCost');
  const pricePerLiter = calculatePricePerLiter(quantity, totalCost);

  const formContent = (

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Vehicle and Driver Selection */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="vehicleId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('fuel:fields.vehicle')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('fuel:form.selectVehicle')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {vehicles.map((vehicle) => (
                          <SelectItem key={vehicle.id} value={vehicle.id}>
                            {vehicle.plateNumber} - {vehicle.make} {vehicle.model}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="driverId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('fuel:fields.driver')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t('fuel:form.selectDriver')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {drivers.map((driver) => (
                          <SelectItem key={driver.id} value={driver.id}>
                            {driver.firstName} {driver.lastName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Fuel Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('fuel:form.fuelDetails')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="quantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fuel:form.quantityLiters')}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="totalCost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fuel:form.totalCostPLN')}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Price per liter calculation */}
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <Calculator className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {t('fuel:form.pricePerLiter')}: <Badge variant="secondary">{pricePerLiter} PLN/L</Badge>
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fuel:fields.location')}</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Orlen Warszawa" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="fuelingDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('fuel:form.fuelingDate')}</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="odometerReading"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fuel:form.odometerReading')}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="123456"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Receipt and Additional Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('fuel:form.additionalInfo')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="receiptNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fuel:form.receiptNumber')} ({t('fuel:form.receiptNumberOptional')})</FormLabel>
                      <FormControl>
                        <Input placeholder="Receipt reference number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Receipt Upload */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t('fuel:form.uploadReceipt')}</label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={handleFileChange}
                      className="flex-1"
                    />
                    {receiptFile && (
                      <Badge variant="secondary" className="gap-1">
                        <Upload className="h-3 w-3" />
                        {receiptFile.name}
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {t('fuel:form.supportedFormats')}
                  </p>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Additional notes about this fuel record..."
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enteredBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('fuel:form.enteredBy')}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('fuel:form.currentUser')} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end gap-2">
              {onClose && (
                <Button type="button" variant="outline" onClick={onClose}>
                  {t('fuel:form.cancel')}
                </Button>
              )}
              <Button type="submit" disabled={loading || isSubmitting}>
                {loading || isSubmitting ?
                  (editRecord ? t('fuel:form.updating') : t('fuel:form.saving')) :
                  (editRecord ? t('fuel:form.update') : t('fuel:form.save'))
                }
              </Button>
            </div>
          </form>
        </Form>
  );

  if (showAsDialog) {
    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editRecord ? t('fuel:form.editTitle') : t('fuel:form.createTitle')}
            </DialogTitle>
            <DialogDescription>
              {editRecord ? t('fuel:form.editDescription') : t('fuel:form.description')}
            </DialogDescription>
          </DialogHeader>
          {formContent}
        </DialogContent>
      </Dialog>
    );
  }

  return formContent;
};
