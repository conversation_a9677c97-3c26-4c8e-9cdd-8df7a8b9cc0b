import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Edit, Trash2, FileText, Filter, Search, Calendar, Plus, Fuel } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { FuelRecordForm } from './FuelRecordForm';
import { useToast } from '@/components/ui/use-toast';
import { fuelRecordsApi, vehiclesApi, driversApi, FuelRecord } from '@/lib/api/fuel-api';
import { useAuth } from '@/context/auth-context';



interface FuelRecordsListProps {
  onRecordUpdated: () => void;
  onAddRecord?: () => void;
}

export const FuelRecordsList: React.FC<FuelRecordsListProps> = ({ onRecordUpdated, onAddRecord }) => {
  const { t } = useTranslation(['fuel', 'common']);
  const [records, setRecords] = useState<FuelRecord[]>([]);
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [drivers, setDrivers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingRecord, setEditingRecord] = useState<FuelRecord | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    search: '',
    vehicleId: '',
    driverId: '',
    startDate: '',
    endDate: '',
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    loadFuelRecords();
  }, [filters, pagination.page]);

  useEffect(() => {
    // Only load vehicles and drivers when user is authenticated
    if (user) {
      console.log('🔐 User authenticated, loading vehicles and drivers');
      loadVehicles();
      loadDrivers();
    } else {
      console.log('🔐 User not authenticated, skipping vehicles/drivers load');
    }
  }, [user]);

  const loadVehicles = async () => {
    try {
      const data = await vehiclesApi.getTrucks();
      setVehicles(data);
    } catch (error) {
      console.error('Failed to load vehicles:', error);
    }
  };

  const loadDrivers = async () => {
    try {
      const data = await driversApi.getActiveDrivers();
      setDrivers(data);
    } catch (error) {
      console.error('Failed to load drivers:', error);
    }
  };

  const loadFuelRecords = async () => {
    setLoading(true);
    try {
      const response = await fuelRecordsApi.getRecords({
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search || undefined,
        vehicleId: filters.vehicleId || undefined,
        driverId: filters.driverId || undefined,
        startDate: filters.startDate || undefined,
        endDate: filters.endDate || undefined,
      });

      setRecords(response.data);
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total,
        totalPages: response.pagination.totalPages,
      }));
    } catch (error) {
      console.error('Failed to load fuel records:', error);
      toast({
        title: t('common:error'),
        description: t('fuel:messages.loadError', 'Failed to load fuel records.'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRecord = async (id: string) => {
    try {
      await fuelRecordsApi.deleteRecord(id);

      toast({
        title: t('common:success'),
        description: t('fuel:messages.recordDeleted'),
      });

      loadFuelRecords();
      onRecordUpdated();
    } catch (error) {
      console.error('Failed to delete fuel record:', error);
      toast({
        title: t('common:error'),
        description: t('fuel:messages.deleteError', 'Failed to delete fuel record.'),
        variant: 'destructive',
      });
    }
  };

  const handleDeleteClick = (id: string) => {
    setRecordToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (recordToDelete) {
      handleDeleteRecord(recordToDelete);
      setRecordToDelete(null);
    }
  };

  const handleEditRecord = (record: FuelRecord) => {
    setEditingRecord(record);
  };

  const handleRecordUpdated = () => {
    setEditingRecord(null);
    loadFuelRecords();
    onRecordUpdated();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pl-PL');
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Fuel className="h-5 w-5" />
            {t('fuel:tabs.records')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('fuel:recordsDescription', 'Manage and view all fuel consumption records')}
          </p>
        </div>
        {onAddRecord && (
          <Button onClick={onAddRecord} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('fuel:addRecord')}
          </Button>
        )}
      </div>

      <Card>
        <CardContent className="pt-6">
          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-6">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('fuel:searchRecords')}
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-8"
                />
              </div>
            </div>
            
            <Select
              value={filters.vehicleId || "all"}
              onValueChange={(value) => setFilters(prev => ({ ...prev, vehicleId: value === "all" ? "" : value }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('fuel:filters.allVehicles', 'All Vehicles')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('fuel:filters.allVehicles', 'All Vehicles')}</SelectItem>
                {vehicles && vehicles.map((vehicle) => (
                  <SelectItem key={vehicle.id} value={vehicle.id}>
                    {vehicle.plateNumber} ({vehicle.make} {vehicle.model})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.driverId || "all"}
              onValueChange={(value) => setFilters(prev => ({ ...prev, driverId: value === "all" ? "" : value }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('fuel:filters.allDrivers', 'All Drivers')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('fuel:filters.allDrivers', 'All Drivers')}</SelectItem>
                {drivers && drivers.map((driver) => (
                  <SelectItem key={driver.id} value={driver.id}>
                    {driver.firstName} {driver.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
                className="w-[140px]"
              />
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
                className="w-[140px]"
              />
            </div>
          </div>

          {/* Records Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('fuel:fields.date')}</TableHead>
                  <TableHead>{t('fuel:fields.vehicle')}</TableHead>
                  <TableHead>{t('fuel:fields.driver')}</TableHead>
                  <TableHead>{t('fuel:fields.location')}</TableHead>
                  <TableHead className="text-right">{t('fuel:fields.quantity')} (L)</TableHead>
                  <TableHead className="text-right">{t('fuel:fields.totalCost')}</TableHead>
                  <TableHead className="text-right">{t('fuel:fields.pricePerLiter')}</TableHead>
                  <TableHead className="text-right">{t('fuel:fields.odometer')}</TableHead>
                  <TableHead className="w-[100px]">{t('common:actions.title')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      {t('fuel:loadingRecords')}
                    </TableCell>
                  </TableRow>
                ) : records.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      {t('fuel:noRecordsFound')}
                    </TableCell>
                  </TableRow>
                ) : (
                  records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{formatDate(record.fuelingDate)}</span>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(record.createdAt)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{record.vehicle.plateNumber}</span>
                          <span className="text-xs text-muted-foreground">
                            {record.vehicle.make} {record.vehicle.model}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">
                          {record.driver.firstName} {record.driver.lastName}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{record.location}</span>
                          {record.receiptNumber && (
                            <Badge variant="outline" className="text-xs w-fit">
                              {record.receiptNumber}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {record.quantity.toFixed(1)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {formatCurrency(record.totalCost)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {record.pricePerLiter.toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {record.odometerReading.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditRecord(record)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {record.receiptUrl && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(record.receiptUrl, '_blank')}
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClick(record.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {!loading && records.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                {t('fuel:pagination.showing', 'Showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('fuel:pagination.to', 'to')}{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} {t('fuel:pagination.of', 'of')}{' '}
                {pagination.total} {t('fuel:pagination.records', 'records')}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page === 1}
                >
                  {t('common:pagination.previous')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={pagination.page === pagination.totalPages}
                >
                  {t('common:pagination.next')}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Record Modal */}
      {editingRecord && (
        <FuelRecordForm
          editRecord={editingRecord}
          onClose={() => setEditingRecord(null)}
          onSuccess={handleRecordUpdated}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title={t('fuel:deleteRecord')}
        description={t('fuel:messages.confirmDelete')}
        confirmText={t('common:actions.delete')}
        cancelText={t('common:actions.cancel')}
        variant="destructive"
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
};
