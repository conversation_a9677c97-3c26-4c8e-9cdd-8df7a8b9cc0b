import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Fuel, TrendingUp, TrendingDown, Users, Truck } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { dashboardApi, FuelDashboardStats } from '@/lib/api/fuel-api';

export const FuelDashboard: React.FC = () => {
  const { t } = useTranslation(['fuel', 'common']);
  const [stats, setStats] = useState<FuelDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      const data = await dashboardApi.getDashboardStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('pl-PL', {
      style: 'currency',
      currency: 'PLN',
    });
  };

  const formatTrend = (trend: number, isEfficiency = false) => {
    const isPositive = isEfficiency ? trend < 0 : trend > 0;
    const color = isPositive ? 'text-green-600' : 'text-red-600';
    const icon = isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />;
    
    return (
      <div className={`flex items-center gap-1 ${color}`}>
        {icon}
        <span className="text-xs">
          {trend > 0 ? '+' : ''}{trend.toFixed(1)}%
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('fuel:dashboard.loadingError')}</p>
      </div>
    );
  }

  const budgetUsagePercentage = (stats.monthlySpent / stats.monthlyBudget) * 100;

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:stats.totalCost')}</CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalFuelCost)}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:filters.thisMonth')}</p>
              {formatTrend(stats.recentTrends.costTrend)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:stats.fleetEfficiency')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageEfficiency} L/100km</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:dashboard.averageConsumptionDesc')}</p>
              {formatTrend(stats.recentTrends.efficiencyTrend, true)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:stats.totalVolume')}</CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLiters.toLocaleString()} L</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:dashboard.thisMonth')}</p>
              {formatTrend(stats.recentTrends.volumeTrend)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('fuel:stats.activeFleet')}</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVehicles}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('fuel:dashboard.activeDrivers', { count: stats.activeDrivers })}</p>
              <Users className="h-3 w-3 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Progress */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fuel:dashboard.monthlyBudget')}</CardTitle>
          <CardDescription>
            {t('fuel:dashboard.budgetDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('fuel:dashboard.spent', { amount: formatCurrency(stats.monthlySpent) })}</span>
              <span>{t('fuel:dashboard.budget', { amount: formatCurrency(stats.monthlyBudget) })}</span>
            </div>
            <Progress value={budgetUsagePercentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('fuel:dashboard.used', { percentage: budgetUsagePercentage.toFixed(1) })}</span>
              <span>{t('fuel:dashboard.remaining', { amount: formatCurrency(stats.monthlyBudget - stats.monthlySpent) })}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Performers */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:dashboard.mostEfficientVehicles')}</CardTitle>
            <CardDescription>
              {t('fuel:dashboard.mostEfficientVehiclesDesc')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.topPerformers.vehicles.map((vehicle, index) => (
                <div key={vehicle.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{vehicle.plateNumber}</p>
                      <p className="text-xs text-muted-foreground">
                        {t('fuel:dashboard.spentAmount', { amount: formatCurrency(vehicle.totalCost) })}
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary">
                    {vehicle.efficiency} L/100km
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('fuel:dashboard.mostEfficientDrivers')}</CardTitle>
            <CardDescription>
              {t('fuel:dashboard.mostEfficientDriversDesc')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.topPerformers.drivers.map((driver, index) => (
                <div key={driver.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{driver.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {t('fuel:dashboard.spentAmount', { amount: formatCurrency(driver.totalCost) })}
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary">
                    {driver.efficiency} L/100km
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
