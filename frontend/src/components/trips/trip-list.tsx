import * as React from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Trip,
  TripStatus,
  TripStopStatus,
  tripTypeVariants,
  tripStatusVariants,
  tripStopStatusVariants,
  tripPriorityVariants,
} from '@/types/trip';
import { formatDate, formatDateTime } from '@/lib/utils';

interface TripListProps {
  trips: Trip[];
  onUpdateStatus?: (tripId: string, newStatus: TripStatus) => Promise<void>;
  onEdit?: (trip: Trip) => void;
  onView?: (trip: Trip) => void;
  onDelete?: (trip: Trip) => void;
}

export function TripList({ trips, onUpdateStatus, onEdit, onView, onDelete }: TripListProps) {
  const { t } = useTranslation(['trips', 'common']);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  const toggleRow = (id: string) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const getStatusActions = (status: TripStatus) => {
    switch (status) {
      case TripStatus.SCHEDULED:
        return [TripStatus.IN_PROGRESS, TripStatus.CANCELLED];
      case TripStatus.IN_PROGRESS:
        return [TripStatus.COMPLETED, TripStatus.CANCELLED];
      default:
        return [];
    }
  };

  const getBadgeVariant = (variant: string) => {
    switch (variant) {
      case 'green':
        return 'default';
      case 'blue':
        return 'secondary';
      case 'purple':
        return 'outline';
      case 'amber':
        return 'maintenance';
      case 'sky':
        return 'active';
      case 'gray':
        return 'regular';
      case 'pending':
        return 'pending';
      case 'active':
        return 'active';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      case 'delayed':
        return 'delayed';
      case 'low':
        return 'regular';
      case 'normal':
        return 'default';
      case 'high':
        return 'secondary';
      case 'urgent':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const handleRowClick = (trip: Trip) => {
    if (onView) {
      onView(trip);
    } else {
      toggleRow(trip.id);
    }
  };

  type ButtonEvent = {
    stopPropagation: () => void;
    preventDefault: () => void;
  };

  const handleActionClick = (event: ButtonEvent, action: () => void) => {
    event.stopPropagation();
    event.preventDefault();
    action();
  };

  // Create an array that will hold all rows (normal rows and expanded rows)
  const tableRows = trips.flatMap((trip) => {
    const mainRow = (
      <TableRow
        key={`trip-${trip.id}`}
        className="cursor-pointer hover:bg-gray-50"
        onClick={() => handleRowClick(trip)}
      >
        <TableCell>
          <Badge variant={getBadgeVariant(tripTypeVariants[trip.type])}>
            {t(`trips:types.${trip.type.toLowerCase()}`)}
          </Badge>
        </TableCell>
        <TableCell>
          {`${trip.driver.firstName} ${trip.driver.lastName}`}
        </TableCell>
        <TableCell>
          {`${trip.vehicle.make} ${trip.vehicle.model} (${trip.vehicle.plateNumber})`}
        </TableCell>
        <TableCell>
          <Badge variant={getBadgeVariant(tripStatusVariants[trip.status])}>
            {t(`trips:status.${trip.status.toLowerCase()}`)}
          </Badge>
        </TableCell>
        <TableCell>
          <Badge variant={getBadgeVariant(tripPriorityVariants[trip.priority])}>
            {t(`trips:priority.${trip.priority.toLowerCase()}`)}
          </Badge>
        </TableCell>
        <TableCell>{formatDateTime(trip.startTime)}</TableCell>
        <TableCell>
          {trip.endTime ? formatDateTime(trip.endTime) : '-'}
        </TableCell>
        <TableCell>
          <div className="flex gap-2">
            {onUpdateStatus &&
              getStatusActions(trip.status).map((newStatus) => (
                <Button
                  key={newStatus}
                  variant="outline"
                  size="sm"
                  onClick={(e: ButtonEvent) => 
                    handleActionClick(e, () => onUpdateStatus(trip.id, newStatus))
                  }
                >
                  {t(`trips:status.${newStatus.toLowerCase()}`)}
                </Button>
              ))}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e: ButtonEvent) => 
                  handleActionClick(e, () => onEdit(trip))
                }
              >
                {t('trips:actions.editTrip')}
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={(e: ButtonEvent) => 
                  handleActionClick(e, () => onDelete(trip))
                }
              >
                {t('trips:actions.deleteTrip')}
              </Button>
            )}
          </div>
        </TableCell>
      </TableRow>
    );

    // If the row is expanded, add the expanded row details
    const rows = [mainRow];
    
    if (expandedRow === trip.id) {
      rows.push(
        <TableRow key={`trip-expanded-${trip.id}`}>
          <TableCell colSpan={8} className="bg-gray-50 p-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">{t('trips:sections.tripDetails')}</h4>
                <dl className="space-y-2">
                  {trip.purpose && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.purpose')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {trip.purpose}
                      </dd>
                    </div>
                  )}
                  {trip.notes && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.notes')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {trip.notes}
                      </dd>
                    </div>
                  )}
                  {trip.distance && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.distance')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {trip.distance} km
                      </dd>
                    </div>
                  )}
                  {trip.estimatedDuration && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.estimatedDuration')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {Math.round(trip.estimatedDuration / 60)} {t('trips:units.hours')}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
              <div>
                <h4 className="font-semibold mb-2">{t('trips:sections.cargoInformation')}</h4>
                <dl className="space-y-2">
                  {trip.cargo && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.cargo')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {trip.cargo}
                      </dd>
                    </div>
                  )}
                  {trip.cargoWeight && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">
                        {t('trips:fields.cargoWeight')}
                      </dt>
                      <dd className="text-sm text-gray-900">
                        {trip.cargoWeight} kg
                      </dd>
                    </div>
                  )}
                </dl>
                {trip.stops.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">{t('trips:fields.stops')}</h4>
                    <div className="space-y-2">
                      {trip.stops.map((stop) => (
                        <div
                          key={stop.id}
                          className="border rounded p-2"
                        >
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">
                              {stop.location}
                            </span>
                            <Badge
                              variant={getBadgeVariant(
                                tripStopStatusVariants[stop.status]
                              )}
                            >
                              {stop.status}
                            </Badge>
                          </div>
                          {stop.arrivalTime && (
                            <div className="text-xs text-gray-500">
                              {t('trips:fields.arrived')}: {formatDateTime(stop.arrivalTime)}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TableCell>
        </TableRow>
      );
    }
    
    return rows;
  });

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('trips:fields.type')}</TableHead>
              <TableHead>{t('trips:fields.driver')}</TableHead>
              <TableHead>{t('trips:fields.vehicle')}</TableHead>
              <TableHead>{t('trips:fields.status')}</TableHead>
              <TableHead>{t('trips:fields.priority')}</TableHead>
              <TableHead>{t('trips:fields.startTime')}</TableHead>
              <TableHead>{t('trips:fields.endTime')}</TableHead>
              <TableHead>{t('common:actions.title')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableRows.length > 0 ? (
              tableRows
            ) : (
              <TableRow>
                <TableCell
                  colSpan={8}
                  className="text-center text-muted-foreground"
                >
                  {t('trips:noTripsFound')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
