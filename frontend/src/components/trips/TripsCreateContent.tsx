import React from 'react';
import { useTranslation } from 'react-i18next';
import { TripForm } from '@/components/trips/trip-form';
import { Plus, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface TripsCreateContentProps {
  onTripCreated?: () => void;
}

export const TripsCreateContent: React.FC<TripsCreateContentProps> = ({
  onTripCreated
}) => {
  const { t } = useTranslation(['trips', 'common']);
  const router = useRouter();

  const handleBackToList = () => {
    router.push('/trips?tab=list');
  };

  const handleTripCreated = () => {
    if (onTripCreated) {
      onTripCreated();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleBackToList}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('trips:backToTrips', 'Back to Trips')}
          </Button>
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Plus className="h-5 w-5" />
              {t('trips:createTrip')}
            </h2>
            <p className="text-sm text-muted-foreground">
              {t('trips:descriptions.createTripDescription')}
            </p>
          </div>
        </div>
      </div>

      {/* Trip Form */}
      <div className="bg-white rounded-lg shadow p-6">
        <TripForm onTripCreated={handleTripCreated} />
      </div>
    </div>
  );
};
