import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MapPin, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/fixed-select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface QuickLocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'pickup' | 'delivery';
  partnerName: string;
  onAddLocation: (locationData: any, saveToPartner: boolean) => void;
}

export function QuickLocationDialog({
  open,
  onOpenChange,
  type,
  partnerName,
  onAddLocation,
}: QuickLocationDialogProps) {
  const { t } = useTranslation(['trips', 'common']);
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    contactPerson: '',
    phone: '',
    operatingHours: '',
    specialInstructions: '',
  });
  const [saveToPartner, setSaveToPartner] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.address || !formData.city) {
      toast({
        title: t('common:errors.validationError'),
        description: t('trips:businessPartners.validation.fillRequiredFields'),
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const locationData = {
        ...formData,
        type: type === 'pickup' ? 'PICKUP_POINT' : 'DELIVERY_POINT',
        country: 'Poland',
        isActive: true,
        isDefault: false,
      };

      await onAddLocation(locationData, saveToPartner);
      
      // Reset form
      setFormData({
        name: '',
        address: '',
        city: '',
        state: '',
        postalCode: '',
        contactPerson: '',
        phone: '',
        operatingHours: '',
        specialInstructions: '',
      });
      setSaveToPartner(true);
      
    } catch (error) {
      console.error('Error in quick location dialog:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      contactPerson: '',
      phone: '',
      operatingHours: '',
      specialInstructions: '',
    });
    setSaveToPartner(true);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {t('trips:businessPartners.actions.addNewLocation')} {type === 'pickup' ? t('trips:fields.pickup') : t('trips:fields.delivery')}
          </DialogTitle>
          <DialogDescription>
            {t('businessPartners:messages.addLocationDescription', { partnerName })}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('trips:businessPartners.fields.locationName')} *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.locationName')}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPerson">{t('trips:businessPartners.fields.contactPerson')}</Label>
                <Input
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.contactName')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">{t('common:common.address')} *</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder={t('trips:businessPartners.placeholders.streetAddress')}
                required
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">{t('trips:businessPartners.fields.city')} *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.city')}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">{t('trips:businessPartners.fields.state')}</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.state')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">{t('trips:businessPartners.fields.postalCode')}</Label>
                <Input
                  id="postalCode"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.postalCode')}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">{t('common:common.phone')}</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.phoneNumber')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="operatingHours">{t('trips:businessPartners.operatingHours')}</Label>
                <Input
                  id="operatingHours"
                  value={formData.operatingHours}
                  onChange={(e) => handleInputChange('operatingHours', e.target.value)}
                  placeholder={t('trips:businessPartners.placeholders.operatingHours')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialInstructions">{t('trips:businessPartners.specialInstructions')}</Label>
              <Input
                id="specialInstructions"
                value={formData.specialInstructions}
                onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                placeholder={t('trips:businessPartners.placeholders.specialInstructions')}
              />
            </div>
          </div>

          {/* Save to Partner Option */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="saveToPartner"
                  checked={saveToPartner}
                  onCheckedChange={(checked) => setSaveToPartner(checked as boolean)}
                />
                <div className="space-y-1">
                  <Label htmlFor="saveToPartner" className="text-sm font-medium cursor-pointer">
                    {t('businessPartners:messages.saveLocationToPermanent', { partnerName })}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {saveToPartner
                      ? t('trips:businessPartners.messages.locationWillBeSaved')
                      : t('trips:businessPartners.messages.locationOnlyForThisTrip')
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel}>
              {t('common:actions.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? t('trips:businessPartners.actions.adding') : `${t('trips:businessPartners.actions.addNewLocation')} ${type === 'pickup' ? t('trips:fields.pickup') : t('trips:fields.delivery')}`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
