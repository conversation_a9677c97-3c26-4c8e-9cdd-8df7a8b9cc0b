import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { hasSubmoduleAccess } from '@/lib/rbac';
import { TripList } from '@/components/trips/trip-list';
import { TripService } from '@/lib/api/trip-service';
import { Trip, TripStatus } from '@/types/trip';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Route, Plus } from 'lucide-react';

interface TripsListContentProps {
  onTripUpdated?: () => void;
}

export const TripsListContent: React.FC<TripsListContentProps> = ({
  onTripUpdated
}) => {
  const { t } = useTranslation(['trips', 'common']);
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [trips, setTrips] = useState<Trip[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    trip: Trip | null;
  }>({ open: false, trip: null });

  // Check permissions
  const canDelete = hasSubmoduleAccess(user?.role, 'trips.delete');
  const canEdit = hasSubmoduleAccess(user?.role, 'trips.create'); // Using create permission for edit
  const canCreate = hasSubmoduleAccess(user?.role, 'trips.create');

  const fetchTrips = async () => {
    try {
      setLoading(true);
      const data = await TripService.getTrips();
      setTrips(data);
      setError('');
    } catch (err) {
      setError(t('trips:messages.failedToLoadTrips'));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrips();
  }, []);

  // Event handlers
  const handleUpdateStatus = async (tripId: string, newStatus: TripStatus): Promise<void> => {
    try {
      await TripService.updateTripStatus(tripId, newStatus);
      await fetchTrips(); // Refresh the list
      toast({
        title: t('common:success'),
        description: t('trips:messages.statusUpdatedSuccess'),
      });
      
      if (onTripUpdated) {
        onTripUpdated();
      }
    } catch (error) {
      console.error('Failed to update trip status:', error);
    }
  };

  const handleEdit = (trip: Trip) => {
    // TODO: Implement inline editing or modal-based editing
    // For now, navigate to create tab with trip data
    router.push('/trips?tab=create');
  };

  const handleView = (trip: Trip) => {
    // TODO: Implement trip details modal or expand row functionality
    // For now, just expand the row in the table
    console.log('View trip:', trip);
  };

  const handleDelete = (trip: Trip) => {
    setDeleteDialog({ open: true, trip });
  };

  const handleCreateTrip = () => {
    // Navigate to create tab
    router.push('/trips?tab=create');
  };

  const confirmDelete = async () => {
    if (!deleteDialog.trip) return;

    try {
      await TripService.deleteTrip(deleteDialog.trip.id);
      await fetchTrips(); // Refresh the list
      toast({
        title: t('common:success'),
        description: t('trips:messages.tripDeletedSuccess'),
      });
      
      if (onTripUpdated) {
        onTripUpdated();
      }
    } catch (error) {
      console.error('Failed to delete trip:', error);
      toast({
        title: t('common:error'),
        description: t('trips:messages.failedToDeleteTrip'),
        variant: 'destructive',
      });
    } finally {
      setDeleteDialog({ open: false, trip: null });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">{t('trips:messages.loadingTrips')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 text-center py-10">{error}</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Route className="h-5 w-5" />
            {t('trips:allTrips', 'All Trips')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('trips:allTripsDescription', 'Manage and track all trips in your fleet')}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {canCreate && (
            <Button onClick={handleCreateTrip} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('trips:scheduleNewTrip', 'Schedule New Trip')}
            </Button>
          )}
        </div>
      </div>

      {/* Trips List */}
      <TripList
        trips={trips}
        onUpdateStatus={handleUpdateStatus}
        onEdit={canEdit ? handleEdit : undefined}
        onView={handleView}
        onDelete={canDelete ? handleDelete : undefined}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, trip: null })}
        title={t('trips:actions.deleteTrip')}
        description={
          deleteDialog.trip
            ? `${t('trips:messages.confirmDeleteTrip')}\n\n` +
              `${t('trips:fields.route')}: ${deleteDialog.trip.startLocation} → ${deleteDialog.trip.endLocation}\n` +
              `${t('trips:fields.driver')}: ${deleteDialog.trip.driver.firstName} ${deleteDialog.trip.driver.lastName}\n` +
              `${t('trips:fields.status')}: ${deleteDialog.trip.status}\n\n` +
              `${t('common:messages.actionCannotBeUndone')}`
            : ''
        }
        confirmText={t('trips:actions.deleteTrip')}
        cancelText={t('common:actions.cancel')}
        variant="destructive"
        onConfirm={confirmDelete}
      />
    </div>
  );
};
