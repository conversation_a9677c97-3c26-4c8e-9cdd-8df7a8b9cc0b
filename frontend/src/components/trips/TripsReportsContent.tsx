import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileText, Download, Calendar, BarChart3, TrendingUp } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

export const TripsReportsContent: React.FC = () => {
  const { t } = useTranslation(['trips', 'common']);
  const [reportType, setReportType] = useState('daily');
  const [isLoading, setIsLoading] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showResults, setShowResults] = useState(false);

  // Mock data for demonstration
  const mockReportData = {
    daily: {
      summary: { totalTrips: 45, completedTrips: 42, totalDistance: 1680, avgDistance: 37.3 },
      chartData: [
        { date: '2025-06-16', trips: 8, distance: 320 },
        { date: '2025-06-17', trips: 12, distance: 450 },
        { date: '2025-06-18', trips: 6, distance: 240 },
        { date: '2025-06-19', trips: 10, distance: 380 },
        { date: '2025-06-20', trips: 9, distance: 290 },
      ]
    },
    weekly: {
      summary: { totalTrips: 312, completedTrips: 298, totalDistance: 11760, avgDistance: 37.7 },
      chartData: [
        { week: 'Week 1', trips: 78, distance: 2940 },
        { week: 'Week 2', trips: 82, distance: 3100 },
        { week: 'Week 3', trips: 75, distance: 2850 },
        { week: 'Week 4', trips: 77, distance: 2870 },
      ]
    },
    driver: {
      summary: { totalDrivers: 15, activeDrivers: 12, avgTripsPerDriver: 20.8 },
      chartData: [
        { driver: 'Jan Kowalski', trips: 28, efficiency: 95 },
        { driver: 'Anna Nowak', trips: 25, efficiency: 92 },
        { driver: 'Piotr Wiśniewski', trips: 22, efficiency: 88 },
        { driver: 'Maria Kowalczyk', trips: 20, efficiency: 90 },
        { driver: 'Tomasz Nowak', trips: 18, efficiency: 85 },
      ]
    }
  };

  const generateReport = () => {
    setIsLoading(true);
    // Simulate loading for demo
    setTimeout(() => {
      setIsLoading(false);
      setShowResults(true);
    }, 1500);
  };

  const exportReport = () => {
    // Mock export functionality
    console.log('Exporting report...');
  };

  const getCurrentReportData = () => {
    switch (reportType) {
      case 'daily':
        return mockReportData.daily;
      case 'weekly':
        return mockReportData.weekly;
      case 'driver':
        return mockReportData.driver;
      default:
        return mockReportData.daily;
    }
  };

  const renderChart = () => {
    const data = getCurrentReportData();
    
    if (reportType === 'driver') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="driver" angle={-45} textAnchor="end" height={80} />
            <YAxis />
            <Tooltip />
            <Bar dataKey="trips" fill="#3b82f6" name="Trips" />
          </BarChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data.chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={reportType === 'daily' ? 'date' : 'week'} />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip />
          <Line yAxisId="left" type="monotone" dataKey="trips" stroke="#3b82f6" strokeWidth={2} name="Trips" />
          <Line yAxisId="right" type="monotone" dataKey="distance" stroke="#10b981" strokeWidth={2} name="Distance (km)" />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('trips:reports.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('trips:reports.description')}
          </p>
        </div>
        {showResults && (
          <Button onClick={exportReport} variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            {t('trips:reports.exportReport')}
          </Button>
        )}
      </div>

      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            {t('trips:reports.generateReports')}
          </CardTitle>
          <CardDescription>
            {t('trips:reports.selectParameters')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Report Type */}
            <div className="space-y-2">
              <Label htmlFor="reportType">{t('trips:reports.reportType')}</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder={t('trips:reports.selectReportType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">{t('trips:reports.types.daily')}</SelectItem>
                  <SelectItem value="weekly">{t('trips:reports.types.weekly')}</SelectItem>
                  <SelectItem value="monthly">{t('trips:reports.types.monthly')}</SelectItem>
                  <SelectItem value="driver">{t('trips:reports.types.driver')}</SelectItem>
                  <SelectItem value="vehicle">{t('trips:reports.types.vehicle')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <Label htmlFor="startDate">{t('trips:reports.startDate')}</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">{t('trips:reports.endDate')}</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={generateReport} disabled={isLoading} className="gap-2">
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {t('trips:reports.generating')}
                </>
              ) : (
                <>
                  <TrendingUp className="h-4 w-4" />
                  {t('trips:reports.generateReport')}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Report Results */}
      {showResults && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(getCurrentReportData().summary).map(([key, value]) => (
              <Card key={key}>
                <CardContent className="pt-6">
                  <div className="text-2xl font-bold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
                  <p className="text-xs text-muted-foreground capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Report Visualization</CardTitle>
              <CardDescription>
                {reportType === 'driver' ? 'Driver performance metrics' : 'Trip trends over time'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderChart()}
            </CardContent>
          </Card>

          {/* Data Table */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Data</CardTitle>
              <CardDescription>Raw data for the selected report period</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      {Object.keys(getCurrentReportData().chartData[0] || {}).map((key) => (
                        <th key={key} className="text-left p-2 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {getCurrentReportData().chartData.map((row, index) => (
                      <tr key={index} className="border-b">
                        {Object.values(row).map((value, i) => (
                          <td key={i} className="p-2">
                            {typeof value === 'number' ? value.toLocaleString() : value}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Empty State */}
      {!showResults && !isLoading && (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('trips:reports.noReportGenerated')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('trips:reports.selectParametersToGenerate')}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
