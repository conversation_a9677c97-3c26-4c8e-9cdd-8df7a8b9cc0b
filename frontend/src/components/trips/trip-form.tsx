'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, MapPin, Truck, Package, ArrowUpDown, Wrench, Users, HelpCircle, AlertTriangle, Clock, Zap, Weight, Ruler, Thermometer, Shield, FileText } from 'lucide-react';
import { DriverService } from '@/lib/api/driver-service';
import { FleetService } from '@/lib/api/fleet-service';
import { TripService } from '@/lib/api/trip-service';
import { Driver } from '@/types/user';
import { Vehicle } from '@/types/vehicle';
import { TripType } from '@/types/trip';
import { useToast } from '@/components/ui/use-toast';
import { PartnerLocationSelector } from './partner-location-selector';
import { distanceService } from '@/lib/services/distance-service';
import { ConflictNotification } from '@/components/ui/conflict-notification';
import { localDateTimeToUTC, utcToLocalDateTime } from '@/lib/utils/timezone';
import '@/components/ui/select-fix.css';

// Trip Type Configuration - will be populated with translations inside component
const getTripTypeConfig = (t: any) => ({
  [TripType.DELIVERY]: {
    label: t('trips:types.delivery'),
    description: t('trips:typeDescriptions.delivery'),
    icon: Package,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200'
  },

  [TripType.TRANSFER]: {
    label: t('trips:types.transfer'),
    description: t('trips:typeDescriptions.transfer'),
    icon: ArrowUpDown,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200'
  },
  [TripType.MAINTENANCE]: {
    label: t('trips:types.maintenance'),
    description: t('trips:typeDescriptions.maintenance'),
    icon: Wrench,
    color: 'text-amber-600',
    bgColor: 'bg-amber-50',
    borderColor: 'border-amber-200'
  },

  [TripType.OTHER]: {
    label: t('trips:types.other'),
    description: t('trips:typeDescriptions.other'),
    icon: HelpCircle,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200'
  }
});



interface TripFormProps {
  initialData?: any;
  isEditing?: boolean;
  tripId?: string;
}

export function TripForm({ initialData, isEditing = false, tripId }: TripFormProps) {
  const { t } = useTranslation(['trips', 'forms', 'common']);
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Get translated trip type configuration
  const tripTypeConfig = getTripTypeConfig(t);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [trucks, setTrucks] = useState<Vehicle[]>([]);
  const [trailers, setTrailers] = useState<Vehicle[]>([]);
  const [activeTruckTrailerAssignments, setActiveTruckTrailerAssignments] = useState<any[]>([]);
  const [loadingDrivers, setLoadingDrivers] = useState(false);
  const [loadingVehicles, setLoadingVehicles] = useState(false);
  const [calculatingDistance, setCalculatingDistance] = useState(false);
  const [conflictWarnings, setConflictWarnings] = useState<string[]>([]);
  const [showingConflicts, setShowingConflicts] = useState(false);

  const [formData, setFormData] = useState(() => {
    // Ensure all fields have default values and handle null/undefined from initialData
    const defaultData = {
      type: 'DELIVERY',
      driverId: '',
      vehicleId: '',
      trailerId: '',
      startLocation: '',
      endLocation: '',
      startTime: '',
      endTime: '',
      purpose: '',
      notes: '',
      cargo: '',
      cargoWeight: '',
      estimatedDuration: '',
      distance: '',
      // Business Partners Integration
      pickupPartnerId: '',
      deliveryPartnerId: '',
      pickupLocationId: '',
      deliveryLocationId: '',
    };

    // If we have initialData, merge it but ensure no null/undefined values
    if (initialData) {
      const cleanedInitialData = Object.keys(defaultData).reduce((acc, key) => {
        const value = initialData[key];
        // Convert null/undefined to empty string, keep other values
        acc[key] = value == null ? '' : String(value);
        return acc;
      }, {} as any);

      return { ...defaultData, ...cleanedInitialData };
    }

    return defaultData;
  });

  // Load drivers, vehicles, and active truck-trailer assignments on component mount
  useEffect(() => {
    loadDrivers();
    loadVehicles();
    loadTrucksAndTrailers();
    loadActiveTruckTrailerAssignments();
  }, []);

  const loadDrivers = async () => {
    try {
      setLoadingDrivers(true);
      console.log('🚗 Loading drivers...');
      const driversData = await DriverService.getDrivers();
      console.log('🚗 Drivers loaded:', driversData);
      setDrivers(driversData);
    } catch (error) {
      console.error('❌ Error loading drivers:', error);
      toast({
        title: t('common:error'),
        description: t('trips:messages.loadDriversError', 'Failed to load drivers'),
        variant: 'destructive',
      });
    } finally {
      setLoadingDrivers(false);
    }
  };

  const loadVehicles = async () => {
    try {
      setLoadingVehicles(true);
      console.log('🚛 Loading vehicles...');
      const vehiclesData = await FleetService.getVehicles();
      console.log('🚛 Vehicles loaded:', vehiclesData);
      // Filter to show only available vehicles for trip assignment
      const availableVehicles = vehiclesData.filter(v =>
        v.status === 'AVAILABLE' || v.status === 'ASSIGNED'
      );
      setVehicles(availableVehicles);
    } catch (error) {
      console.error('❌ Error loading vehicles:', error);
      toast({
        title: t('common:error'),
        description: t('trips:messages.loadVehiclesError', 'Failed to load vehicles'),
        variant: 'destructive',
      });
    } finally {
      setLoadingVehicles(false);
    }
  };

  const loadTrucksAndTrailers = async () => {
    try {
      setLoadingVehicles(true);
      console.log('🚛 Loading trucks and trailers separately...');
      const [trucksData, trailersData] = await Promise.all([
        FleetService.getTrucks(),
        FleetService.getTrailers()
      ]);
      console.log('🚛 Trucks loaded:', trucksData);
      console.log('🚚 Trailers loaded:', trailersData);

      // Filter to show only available vehicles for trip assignment
      const availableTrucks = trucksData.filter(v =>
        v.status === 'AVAILABLE' || v.status === 'ASSIGNED'
      );
      const availableTrailers = trailersData.filter(v =>
        v.status === 'AVAILABLE' || v.status === 'ASSIGNED'
      );

      setTrucks(availableTrucks);
      setTrailers(availableTrailers);
    } catch (error) {
      console.error('❌ Error loading trucks and trailers:', error);
      toast({
        title: t('common:error'),
        description: t('trips:messages.failedToLoadVehicles'),
        variant: 'destructive',
      });
    } finally {
      setLoadingVehicles(false);
    }
  };



  const loadActiveTruckTrailerAssignments = async () => {
    try {
      console.log('🔗 Loading active truck-trailer assignments...');
      const assignmentsData = await TripService.getActiveTruckTrailerPairs();
      console.log('🔗 Active assignments loaded:', assignmentsData);
      setActiveTruckTrailerAssignments(assignmentsData);
    } catch (error) {
      console.error('❌ Error loading active truck-trailer assignments:', error);
      // Don't show error toast for this as it's not critical
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value ?? '', // Ensure value is never undefined
    }));
  };

  /**
   * Parse human-readable duration back to minutes for backend
   */
  const parseEstimatedDuration = (durationText: string): number => {
    if (!durationText) return 0;

    // Parse "X hours Y minutes" or "X minutes" or "X hours" format
    const hoursMatch = durationText.match(/(\d+)\s*hours?/);
    const minutesMatch = durationText.match(/(\d+)\s*minutes?/);

    const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
    const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;

    return hours * 60 + minutes; // Convert to total minutes
  };

  const handleTruckSelection = (truckId: string) => {
    handleInputChange('vehicleId', truckId);

    // Check if this truck has an assigned trailer
    const truckAssignment = activeTruckTrailerAssignments.find(
      assignment => assignment.truck?.id === truckId
    );

    if (truckAssignment && truckAssignment.trailer) {
      console.log('🔗 Auto-assigning trailer:', truckAssignment.trailer.plateNumber);
      handleInputChange('trailerId', truckAssignment.trailer.id);
      toast({
        title: t('trips:messages.trailerAutoAssigned'),
        description: t('trips:messages.trailerAutoAssignedDescription', { plateNumber: truckAssignment.trailer.plateNumber }),
      });
    } else {
      // Clear trailer selection if truck has no assigned trailer
      handleInputChange('trailerId', '');
    }
  };

  const calculateDistance = async () => {
    if (!formData.startLocation || !formData.endLocation) {
      console.log('🗺️ Cannot calculate distance - missing locations:', {
        startLocation: formData.startLocation,
        endLocation: formData.endLocation
      });
      return;
    }

    setCalculatingDistance(true);
    try {
      console.log('🗺️ Calculating distance between:', {
        start: formData.startLocation,
        end: formData.endLocation
      });

      const result = await distanceService.calculateDistance(
        formData.startLocation,
        formData.endLocation
      );

      console.log('🗺️ Distance calculation result:', result);

      if (result.success) {
        handleInputChange('distance', result.distance.toString());
        handleInputChange('estimatedDuration', result.durationText);

        toast({
          title: t('trips:messages.distanceCalculated'),
          description: t('trips:messages.distanceCalculatedDescription', { distance: result.distance, duration: result.durationText }),
          variant: 'default',
        });
      } else {
        console.error('🗺️ Distance calculation failed:', result.error);
        toast({
          title: t('trips:messages.distanceCalculationFailed'),
          description: result.error || t('trips:messages.unableToCalculateDistance'),
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Distance calculation error:', error);
      toast({
        title: t('common:error'),
        description: t('trips:messages.failedToCalculateDistance'),
        variant: 'destructive',
      });
    } finally {
      setCalculatingDistance(false);
    }
  };

  // Track if this is the initial load to avoid auto-calculation on edit mode
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [lastCalculatedLocations, setLastCalculatedLocations] = useState<{start: string, end: string} | null>(null);

  // Mark initial load as complete after form data is set
  useEffect(() => {
    if (isInitialLoad && (formData.startLocation || formData.endLocation)) {
      // Set a timeout to mark initial load as complete
      const timeoutId = setTimeout(() => {
        setIsInitialLoad(false);
        // Store the initial locations to avoid recalculating them
        if (formData.startLocation && formData.endLocation) {
          setLastCalculatedLocations({
            start: formData.startLocation,
            end: formData.endLocation
          });
        }
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [formData.startLocation, formData.endLocation, isInitialLoad]);

  // Auto-calculate distance when both locations are set (but not on initial load)
  useEffect(() => {
    // Skip auto-calculation if:
    // 1. It's the initial load (edit mode loading existing data)
    // 2. Locations are empty
    // 3. Locations haven't actually changed from last calculation
    if (isInitialLoad ||
        !formData.startLocation ||
        !formData.endLocation ||
        formData.startLocation.trim() === '' ||
        formData.endLocation.trim() === '') {
      return;
    }

    // Check if locations have actually changed
    const currentLocations = {
      start: formData.startLocation,
      end: formData.endLocation
    };

    if (lastCalculatedLocations &&
        lastCalculatedLocations.start === currentLocations.start &&
        lastCalculatedLocations.end === currentLocations.end) {
      return; // Locations haven't changed, skip calculation
    }

    console.log('🗺️ Locations changed, auto-calculating distance...');

    // Debounce the calculation to avoid too many API calls
    const timeoutId = setTimeout(() => {
      calculateDistance();
      setLastCalculatedLocations(currentLocations);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [formData.startLocation, formData.endLocation, isInitialLoad, lastCalculatedLocations]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    const missingFields = [];
    if (!formData.type) missingFields.push(t('trips:fields.type'));

    if (!formData.driverId) missingFields.push(t('trips:fields.driver'));
    if (!formData.vehicleId) missingFields.push(t('trips:fields.truck'));
    if (!formData.startLocation) missingFields.push(t('trips:fields.pickupLocation'));
    if (!formData.endLocation) missingFields.push(t('trips:fields.deliveryLocation'));
    if (!formData.startTime) missingFields.push(t('trips:fields.startTime'));

    if (missingFields.length > 0) {
      toast({
        title: t('common:errors.validationError'),
        description: t('trips:validation.fillRequiredFields', { fields: missingFields.join(', ') }),
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);

      // Prepare the trip data for submission
      const tripData = {
        type: formData.type,
        priority: 'NORMAL', // Default priority as required by backend

        driverId: formData.driverId,
        vehicleId: formData.vehicleId,
        trailerId: formData.trailerId || undefined,
        startLocation: formData.startLocation,
        endLocation: formData.endLocation,
        startTime: localDateTimeToUTC(formData.startTime), // Convert to UTC
        endTime: formData.endTime ? localDateTimeToUTC(formData.endTime) : undefined, // Convert to UTC
        purpose: formData.purpose || undefined,
        notes: formData.notes || undefined,
        cargo: formData.cargo || undefined,
        cargoWeight: formData.cargoWeight ? parseFloat(formData.cargoWeight) : undefined,
        estimatedDuration: formData.estimatedDuration ? parseEstimatedDuration(formData.estimatedDuration) : undefined,
        distance: formData.distance ? parseFloat(formData.distance) : undefined,
        // Business Partners Integration
        pickupPartnerId: formData.pickupPartnerId || undefined,
        deliveryPartnerId: formData.deliveryPartnerId || undefined,
        pickupLocationId: formData.pickupLocationId || undefined,
        deliveryLocationId: formData.deliveryLocationId || undefined,
      };

      console.log('🚀 Submitting trip data:', tripData);

      if (isEditing) {
        if (!tripId) {
          throw new Error('Trip ID is required for editing');
        }
        await TripService.updateTrip(tripId, tripData);
        console.log('✅ Trip updated successfully');

        toast({
          title: t('common:success'),
          description: t('trips:messages.tripUpdated'),
        });
      } else {
        const result = await TripService.createTrip(tripData);
        console.log('✅ Trip created successfully');

        // Check for conflict warnings
        if (result.conflictWarnings && result.conflictWarnings.length > 0) {
          // Show custom conflict notification
          setConflictWarnings(result.conflictWarnings);
          setShowingConflicts(true);

          // Also show success toast
          toast({
            title: t('common:success'),
            description: t('trips:messages.tripCreated'),
          });

          // Delay redirect to allow user to see the conflict notification
          console.log('⏱️ Delaying redirect for 5 seconds to show conflict warnings...');
          setTimeout(() => {
            console.log('⏱️ Redirect delay complete, navigating to trips list...');
            setShowingConflicts(false);
            setLoading(false);
            router.push('/trips?tab=list');
          }, 5000); // 5 seconds delay to match notification duration

          return; // Exit early to prevent immediate redirect
        } else {
          toast({
            title: t('common:success'),
            description: t('trips:messages.tripCreated'),
          });
        }
      }

      // Immediate redirect if no conflicts
      router.push('/trips?tab=list');
    } catch (error: any) {
      console.error('Error submitting trip:', error);

      toast({
        title: t('common:error'),
        description: error.response?.data?.message || (isEditing ? t('trips:messages.failedToUpdateTrip') : t('trips:messages.failedToCreateTrip')),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Conflict Notification */}
      {conflictWarnings.length > 0 && (
        <ConflictNotification
          warnings={conflictWarnings}
          onClose={() => {
            setConflictWarnings([]);
            setShowingConflicts(false);
            // If user manually closes, proceed with redirect immediately
            if (showingConflicts) {
              setLoading(false);
              router.push('/trips?tab=list');
            }
          }}
          duration={5000}
        />
      )}

      <form onSubmit={handleSubmit} className="max-w-7xl mx-auto">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {isEditing ? t('trips:editTrip') : t('trips:createTrip')}
        </h1>
        <p className="text-gray-600">
          {isEditing ? t('trips:descriptions.editTripDescription') : t('trips:descriptions.createTripDescription')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Personnel & Vehicle */}
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b border-blue-100">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-sm">1</span>
            </div>
            <h2 className="text-lg font-semibold text-gray-900">{t('trips:sections.vehicleAssignment')}</h2>
          </div>
          {/* Driver Selection */}
          <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('trips:sections.driverAssignment')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="driver">{t('trips:fields.driver')} *</Label>
            <Select value={formData.driverId} onValueChange={(value) => handleInputChange('driverId', value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('forms:placeholders.selectOption')} />
              </SelectTrigger>
              <SelectContent className="select-dropdown bg-white border border-gray-200 shadow-lg">
                {drivers.map((driver) => (
                  <SelectItem key={driver.id} value={driver.id} className="select-item hover:bg-gray-50">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {driver.firstName} {driver.lastName}
                      {driver.email && <span className="text-sm text-gray-500">({driver.email})</span>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {loadingDrivers && (
              <p className="text-sm text-gray-500 mt-1">
                {t('common:actions.loading')}
              </p>
            )}
            {!loadingDrivers && drivers.length === 0 && (
              <p className="text-sm text-gray-500 mt-1">
                {t('drivers:noDriversFound')}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {t('trips:sections.vehicleAssignment')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Truck Selection */}
            <div>
              <Label htmlFor="truck">{t('trips:fields.truck')} *</Label>
              <Select value={formData.vehicleId} onValueChange={handleTruckSelection}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('trips:placeholders.selectTruck')} />
                </SelectTrigger>
                <SelectContent className="select-dropdown bg-white border border-gray-200 shadow-lg">
                  {trucks.map((truck) => {
                    // Check if this truck has an assigned trailer
                    const hasAssignedTrailer = activeTruckTrailerAssignments.find(
                      assignment => assignment.truck?.id === truck.id
                    );

                    return (
                      <SelectItem key={truck.id} value={truck.id} className="select-item hover:bg-gray-50">
                        <div className="flex items-center gap-2">
                          <Truck className="h-4 w-4" />
                          <div className="flex flex-col">
                            <span>{truck.plateNumber} - {truck.make} {truck.model}</span>
                            {hasAssignedTrailer && (
                              <span className="text-xs text-blue-600">
                                🔗 {t('trips:messages.hasAssignedTrailer', { plateNumber: hasAssignedTrailer.trailer?.plateNumber })}
                              </span>
                            )}
                          </div>
                          <Badge variant={truck.status === 'AVAILABLE' ? 'default' : 'secondary'}>
                            {truck.status}
                          </Badge>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {loadingVehicles && (
                <p className="text-sm text-gray-500 mt-1">
                  {t('trips:messages.loadingTrucks')}
                </p>
              )}
              {!loadingVehicles && trucks.length === 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  {t('trips:messages.noTrucksAvailable')}
                </p>
              )}
            </div>

            {/* Trailer Selection */}
            <div>
              <Label htmlFor="trailer">{t('trips:fields.trailer')} ({t('common:optional')})</Label>
              <Select value={formData.trailerId || ''} onValueChange={(value) => handleInputChange('trailerId', value || undefined)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t('trips:placeholders.selectTrailer')} />
                </SelectTrigger>
                <SelectContent className="select-dropdown bg-white border border-gray-200 shadow-lg">
                  {trailers.map((trailer) => (
                    <SelectItem key={trailer.id} value={trailer.id} className="select-item hover:bg-gray-50">
                      <div className="flex items-center gap-2">
                        <Truck className="h-4 w-4" />
                        <span>{trailer.plateNumber} - {trailer.make} {trailer.model}</span>
                        <Badge variant={trailer.status === 'AVAILABLE' ? 'default' : 'secondary'}>
                          {trailer.status}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {loadingVehicles && (
                <p className="text-sm text-gray-500 mt-1">
                  {t('trips:messages.loadingTrailers')}
                </p>
              )}
              {!loadingVehicles && trailers.length === 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  {t('trips:messages.noTrailersAvailable')}
                </p>
              )}
            </div>
          </div>
        </CardContent>
          </Card>

          {/* Trip Type */}
          <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('trips:fields.type')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Trip Type Selection */}
          <div>
            <Label className="text-base font-medium">{t('trips:fields.type')} *</Label>
            <p className="text-sm text-gray-500 mb-3">{t('trips:placeholders.selectTripType')}</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {Object.entries(tripTypeConfig).map(([type, config]) => {
                const IconComponent = config.icon;
                const isSelected = formData.type === type;

                return (
                  <div
                    key={type}
                    onClick={() => handleInputChange('type', type)}
                    className={`
                      relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md
                      ${isSelected
                        ? `${config.borderColor} ${config.bgColor} shadow-sm`
                        : 'border-gray-200 bg-white hover:border-gray-300'
                      }
                    `}
                  >
                    <div className="flex items-start gap-3">
                      <IconComponent className={`h-5 w-5 mt-0.5 ${isSelected ? config.color : 'text-gray-400'}`} />
                      <div className="flex-1">
                        <h3 className={`font-medium ${isSelected ? config.color : 'text-gray-900'}`}>
                          {config.label}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="absolute top-2 right-2">
                        <div className={`w-2 h-2 rounded-full ${config.color.replace('text-', 'bg-')}`}></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>


        </CardContent>
          </Card>
        </div>

        {/* Right Column - Route, Details & Cargo */}
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b border-green-100">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 font-semibold text-sm">2</span>
            </div>
            <h2 className="text-lg font-semibold text-gray-900">{t('trips:sections.routeDetailsAndCargo')}</h2>
          </div>
          {/* Route & Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('trips:sections.routeSchedule')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <PartnerLocationSelector
                  type="pickup"
                  selectedPartnerId={formData.pickupPartnerId || undefined}
                  selectedLocationId={formData.pickupLocationId || undefined}
                  onPartnerChange={(partnerId) => handleInputChange('pickupPartnerId', partnerId || '')}
                  onLocationChange={(locationId) => handleInputChange('pickupLocationId', locationId || '')}
                  onLocationStringChange={(location) => handleInputChange('startLocation', location)}
                  locationString={formData.startLocation}
                  label={t('trips:fields.pickupLocation') + ' *'}
                />

                <PartnerLocationSelector
                  type="delivery"
                  selectedPartnerId={formData.deliveryPartnerId || undefined}
                  selectedLocationId={formData.deliveryLocationId || undefined}
                  onPartnerChange={(partnerId) => handleInputChange('deliveryPartnerId', partnerId || '')}
                  onLocationChange={(locationId) => handleInputChange('deliveryLocationId', locationId || '')}
                  onLocationStringChange={(location) => handleInputChange('endLocation', location)}
                  locationString={formData.endLocation}
                  label={t('trips:fields.deliveryLocation') + ' *'}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startTime">{t('trips:fields.startTime')} *</Label>
                  <Input
                    id="startTime"
                    type="datetime-local"
                    value={formData.startTime}
                    onChange={(e) => handleInputChange('startTime', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="endTime">{t('trips:fields.endTime')} ({t('common:optional')})</Label>
                  <Input
                    id="endTime"
                    type="datetime-local"
                    value={formData.endTime}
                    onChange={(e) => handleInputChange('endTime', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">{t('trips:fields.notes')}</Label>
                <Input
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder={t('trips:placeholders.additionalNotes')}
                />
              </div>
            </CardContent>
          </Card>



          {/* Cargo Information */}
          <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('trips:sections.cargoInformation')}
          </CardTitle>
          <p className="text-sm text-gray-500">
            {t('trips:sections.cargoInformationDescription')}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Cargo Description */}
          <div>
            <Label htmlFor="cargo" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              {t('trips:fields.cargoDescription')}
            </Label>
            <Input
              id="cargo"
              value={formData.cargo}
              onChange={(e) => handleInputChange('cargo', e.target.value)}
              placeholder={t('trips:placeholders.cargoDescription')}
              maxLength={200}
            />
            <p className="text-xs text-gray-500 mt-1">
              {(formData.cargo || '').length}/200 {t('common:characters')}
            </p>
          </div>

          {/* Weight and Dimensions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="cargoWeight" className="flex items-center gap-2">
                <Weight className="h-4 w-4" />
                {t('trips:fields.cargoWeight')} (kg)
              </Label>
              <Input
                id="cargoWeight"
                type="number"
                value={formData.cargoWeight}
                onChange={(e) => handleInputChange('cargoWeight', e.target.value)}
                placeholder={t('trips:placeholders.cargoWeight')}
                min="0"
                step="0.1"
              />
            </div>

            <div>
              <Label htmlFor="estimatedDuration" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {t('trips:fields.estimatedDuration')}
              </Label>
              <Input
                id="estimatedDuration"
                type="text"
                value={formData.estimatedDuration}
                onChange={(e) => handleInputChange('estimatedDuration', e.target.value)}
                placeholder={t('trips:placeholders.estimatedDuration')}
                readOnly
              />
            </div>
          </div>

          {/* Distance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="distance" className="flex items-center gap-2">
                <Ruler className="h-4 w-4" />
                {t('trips:fields.distance')} (km)
                {calculatingDistance && (
                  <div className="flex items-center gap-1 text-blue-600">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                    <span className="text-xs">{t('trips:actions.calculating')}</span>
                  </div>
                )}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="distance"
                  type="number"
                  value={formData.distance}
                  onChange={(e) => handleInputChange('distance', e.target.value)}
                  placeholder={calculatingDistance ? t('trips:actions.calculating') : t('trips:placeholders.enterDistance')}
                  min="0"
                  step="0.1"
                  disabled={calculatingDistance}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={calculateDistance}
                  disabled={calculatingDistance || !formData.startLocation || !formData.endLocation}
                  className="px-3"
                >
                  {calculatingDistance ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                  ) : (
                    t('trips:actions.calculate')
                  )}
                </Button>
              </div>
              {formData.startLocation && formData.endLocation && (
                <p className="text-xs text-gray-500 mt-1">
                  {t('trips:messages.distanceAutoCalculated')}
                </p>
              )}

            </div>

            {/* Vehicle Capacity Info */}
            <div className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 text-blue-700">
                <Shield className="h-4 w-4" />
                <div className="text-sm">
                  <p className="font-medium">{t('trips:fields.vehicleCapacity')}</p>
                  <p className="text-xs text-blue-600">
                    {formData.vehicleId ? t('trips:messages.checkVehicleCapacity') : t('trips:messages.selectVehicleToSeeCapacity')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Special Requirements */}
          <div>
            <Label className="text-base font-medium mb-3 block">{t('trips:fields.specialRequirements')}</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {/* Temperature Control */}
              <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center gap-2">
                  <Thermometer className="h-4 w-4 text-blue-500" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{t('trips:specialRequirements.temperatureControl')}</p>
                    <p className="text-xs text-gray-500">{t('trips:specialRequirements.refrigeratedTransport')}</p>
                  </div>
                </div>
              </div>

              {/* Fragile Handling */}
              <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-amber-500" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{t('trips:specialRequirements.fragileHandling')}</p>
                    <p className="text-xs text-gray-500">{t('trips:specialRequirements.requiresSpecialCare')}</p>
                  </div>
                </div>
              </div>

              {/* Hazardous Materials */}
              <div className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{t('trips:specialRequirements.hazardousMaterials')}</p>
                    <p className="text-xs text-gray-500">{t('trips:specialRequirements.specialPermitsRequired')}</p>
                  </div>
                </div>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {t('trips:specialRequirements.autoCheckDescription')}
            </p>
          </div>
        </CardContent>
          </Card>
        </div>
      </div>

      {/* Submit Buttons - Full Width */}
      <div className="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push('/trips?tab=list')}
          disabled={loading}
        >
          {t('common:actions.cancel')}
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {showingConflicts ? (
                t('trips:actions.reviewingConflicts')
              ) : (
                isEditing ? t('trips:actions.updating') : t('trips:actions.creating')
              )}
            </div>
          ) : (
            isEditing ? t('trips:actions.updateTrip') : t('trips:actions.createTrip')
          )}
        </Button>
      </div>
    </form>
    </>
  );
}
