import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Building2, MapPin, Plus } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/fixed-select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { getAuthHeaders } from '@/lib/auth-headers';
import { QuickLocationDialog } from './quick-location-dialog';

interface BusinessPartner {
  id: string;
  name: string;
  type: 'SHIPPER' | 'LOGISTICS_PROVIDER';
  status: string;
  locations: PartnerLocation[];
}

interface PartnerLocation {
  id: string;
  partnerId: string;
  name: string;
  type: string;
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive: boolean;
  isDefault: boolean;
}

interface PartnerLocationSelectorProps {
  type: 'pickup' | 'delivery';
  selectedPartnerId?: string;
  selectedLocationId?: string;
  onPartnerChange: (partnerId: string | undefined) => void;
  onLocationChange: (locationId: string | undefined) => void;
  onLocationStringChange: (location: string) => void;
  locationString: string;
  label: string;
}

export function PartnerLocationSelector({
  type,
  selectedPartnerId,
  selectedLocationId,
  onPartnerChange,
  onLocationChange,
  onLocationStringChange,
  locationString,
  label,
}: PartnerLocationSelectorProps) {
  const { t } = useTranslation(['trips', 'common']);
  const [partners, setPartners] = useState<BusinessPartner[]>([]);
  const [locations, setLocations] = useState<PartnerLocation[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddLocationDialog, setShowAddLocationDialog] = useState(false);
  const { toast } = useToast();

  // Fetch business partners on component mount
  useEffect(() => {
    fetchPartners();
  }, []);

  // Fetch locations when partner is selected
  useEffect(() => {
    if (selectedPartnerId) {
      fetchLocations(selectedPartnerId);
    } else {
      setLocations([]);
    }
  }, [selectedPartnerId]);

  const fetchPartners = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const response = await fetch(`${backendUrl}/api/business-partners?status=ACTIVE`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business partners');
      }

      const data = await response.json();
      setPartners(data);
    } catch (error) {
      console.error('Error fetching partners:', error);
      toast({
        title: t('common:error'),
        description: t('trips:businessPartners.messages.failedToLoadPartners'),
        variant: 'destructive',
      });
    }
  };

  const fetchLocations = async (partnerId: string) => {
    setLoading(true);
    try {
      const partner = partners.find(p => p.id === partnerId);
      if (partner && partner.locations) {
        setLocations(partner.locations);
      } else {
        // Fallback: fetch locations from API
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
        const response = await fetch(`${backendUrl}/api/business-partners/${partnerId}/locations`, {
          headers: getAuthHeaders(),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch locations');
        }

        const data = await response.json();
        setLocations(data);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      toast({
        title: t('common:error'),
        description: t('trips:businessPartners.messages.failedToLoadLocations'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePartnerChange = (partnerId: string) => {
    onPartnerChange(partnerId);
    onLocationChange(undefined);
    onLocationStringChange('');
  };

  const handleLocationChange = (value: string) => {
    if (value === 'ADD_NEW') {
      setShowAddLocationDialog(true);
      return;
    }
    
    onLocationChange(value);
    const location = locations.find(l => l.id === value);
    if (location) {
      // Include postal code in location string for better distance calculation
      const locationString = `${location.name}, ${location.address}, ${location.postalCode ? location.postalCode + ' ' : ''}${location.city}${location.state ? `, ${location.state}` : ''}`;
      onLocationStringChange(locationString);
    }
  };

  const handleAddNewLocation = async (locationData: any, saveToPartner: boolean) => {
    try {
      if (saveToPartner && selectedPartnerId) {
        // Save to partner's permanent locations
        const response = await fetch(`http://localhost:3001/business-partners/${selectedPartnerId}/locations`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(locationData),
        });

        if (!response.ok) {
          throw new Error('Failed to add location to partner');
        }

        const newLocation = await response.json();
        
        // Add to local locations list
        setLocations(prev => [...prev, newLocation]);
        
        // Select the new location
        onLocationChange(newLocation.id);
        const locationString = `${newLocation.name}, ${newLocation.address}, ${newLocation.postalCode ? newLocation.postalCode + ' ' : ''}${newLocation.city}${newLocation.state ? `, ${newLocation.state}` : ''}`;
        onLocationStringChange(locationString);

        toast({
          title: t('common:success'),
          description: t('trips:businessPartners.messages.locationAddedAndSelected'),
        });
      } else {
        // Use for this trip only
        const locationString = `${locationData.name}, ${locationData.address}, ${locationData.postalCode ? locationData.postalCode + ' ' : ''}${locationData.city}${locationData.state ? `, ${locationData.state}` : ''}`;
        onLocationStringChange(locationString);
        onLocationChange(undefined); // No permanent location ID

        toast({
          title: t('trips:businessPartners.messages.locationSet'),
          description: t('trips:businessPartners.messages.locationSetForTripOnly'),
        });
      }

      setShowAddLocationDialog(false);
    } catch (error) {
      console.error('Error adding location:', error);
      toast({
        title: t('common:error'),
        description: t('trips:businessPartners.messages.failedToAddLocation'),
        variant: 'destructive',
      });
    }
  };

  const selectedLocation = locations.find(l => l.id === selectedLocationId);

  return (
    <div className="space-y-4">
      <Label>{label}</Label>
      
      {/* Partner Selection - Always shown first */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">{t('trips:businessPartners.businessPartner')}</Label>
        <Select
          value={selectedPartnerId || ''}
          onValueChange={handlePartnerChange}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('trips:businessPartners.placeholders.chooseBusinessPartner')} />
          </SelectTrigger>
          <SelectContent>
            {partners.map((partner) => (
              <SelectItem key={partner.id} value={partner.id}>
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  {partner.name}
                  <Badge variant={partner.type === 'SHIPPER' ? 'default' : 'secondary'}>
                    {partner.type === 'SHIPPER' ? t('trips:businessPartners.types.shipper') : t('trips:businessPartners.types.logistics')}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Location Selection - Shown when partner is selected */}
      {selectedPartnerId && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">{t('trips:businessPartners.location')}</Label>
          <Select
            value={selectedLocationId || ''}
            onValueChange={handleLocationChange}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder={loading ? t('trips:businessPartners.messages.loadingLocations') : t('trips:businessPartners.placeholders.chooseLocation')} />
            </SelectTrigger>
            <SelectContent>
              {locations.map((location) => (
                <SelectItem key={location.id} value={location.id}>
                  <div className="flex items-center gap-2 py-1">
                    <MapPin className="h-4 w-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{location.name}</span>
                        {location.isDefault && (
                          <Badge variant="secondary" className="text-xs flex-shrink-0">{t('trips:businessPartners.default')}</Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {location.address}, {location.postalCode ? location.postalCode + ' ' : ''}{location.city}
                        {location.state && `, ${location.state}`}
                      </div>
                    </div>
                  </div>
                </SelectItem>
              ))}
              {/* Add New Location Option */}
              <SelectItem value="ADD_NEW">
                <div className="flex items-center gap-2 text-blue-600">
                  <Plus className="h-4 w-4" />
                  <span className="font-medium">{t('trips:businessPartners.actions.addNewLocation')}</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Selected Location Details */}
      {selectedLocation && (
        <Card>
          <CardContent className="pt-4">
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{selectedLocation.name}</span>
                <Badge variant="outline">
                  {selectedLocation.type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
              </div>
              <p className="text-muted-foreground">
                {selectedLocation.address}, {selectedLocation.postalCode ? selectedLocation.postalCode + ' ' : ''}{selectedLocation.city}
                {selectedLocation.state && `, ${selectedLocation.state}`}
              </p>
              {selectedLocation.operatingHours && (
                <p className="text-muted-foreground">
                  <span className="font-medium">{t('trips:businessPartners.operatingHours')}:</span> {selectedLocation.operatingHours}
                </p>
              )}
              {selectedLocation.specialInstructions && (
                <div className="p-2 bg-muted rounded text-xs">
                  <span className="font-medium">{t('trips:businessPartners.specialInstructions')}:</span> {selectedLocation.specialInstructions}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Location Dialog */}
      {selectedPartnerId && (
        <QuickLocationDialog
          open={showAddLocationDialog}
          onOpenChange={setShowAddLocationDialog}
          type={type}
          partnerName={partners.find(p => p.id === selectedPartnerId)?.name || 'Partner'}
          onAddLocation={handleAddNewLocation}
        />
      )}
    </div>
  );
}
