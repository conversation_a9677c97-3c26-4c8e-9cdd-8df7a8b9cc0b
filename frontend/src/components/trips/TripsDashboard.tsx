import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Route, Clock, CheckCircle, AlertTriangle, TrendingUp, TrendingDown, Users, Car, BarChart3, RefreshCw } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { TripService } from '@/lib/api/trip-service';

interface DashboardData {
  totalTrips: number;
  activeTrips: number;
  completedTrips: number;
  pendingTrips: number;
  totalDistance: number;
  averageDistance: number;
  monthlyGrowth: number;
  tripsByStatus: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  recentTrips: Array<{
    id: string;
    startLocation: string;
    endLocation: string;
    driverName: string;
    status: string;
    scheduledDate: string;
  }>;
  monthlyStats: Array<{
    month: string;
    trips: number;
    distance: number;
  }>;
}

export const TripsDashboard: React.FC = () => {
  const { t } = useTranslation(['trips', 'common']);
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // This would be replaced with actual API calls
      const mockData: DashboardData = {
        totalTrips: 1247,
        activeTrips: 23,
        completedTrips: 1156,
        pendingTrips: 68,
        totalDistance: 45680,
        averageDistance: 36.6,
        monthlyGrowth: 8.5,
        tripsByStatus: [
          { name: t('trips:status.completed'), value: 1156, color: '#22c55e' },
          { name: t('trips:status.pending'), value: 68, color: '#f59e0b' },
          { name: t('trips:status.inProgress'), value: 23, color: '#3b82f6' },
        ],
        recentTrips: [
          { id: '1', startLocation: 'Warsaw', endLocation: 'Krakow', driverName: 'Jan Kowalski', status: 'IN_PROGRESS', scheduledDate: '2025-06-22' },
          { id: '2', startLocation: 'Gdansk', endLocation: 'Poznan', driverName: 'Anna Nowak', status: 'PENDING', scheduledDate: '2025-06-22' },
          { id: '3', startLocation: 'Wroclaw', endLocation: 'Lodz', driverName: 'Piotr Wiśniewski', status: 'COMPLETED', scheduledDate: '2025-06-21' },
          { id: '4', startLocation: 'Katowice', endLocation: 'Lublin', driverName: 'Maria Kowalczyk', status: 'PENDING', scheduledDate: '2025-06-23' },
          { id: '5', startLocation: 'Szczecin', endLocation: 'Bydgoszcz', driverName: 'Tomasz Nowak', status: 'IN_PROGRESS', scheduledDate: '2025-06-22' },
        ],
        monthlyStats: [
          { month: t('common:months.jan'), trips: 180, distance: 6800 },
          { month: t('common:months.feb'), trips: 165, distance: 6200 },
          { month: t('common:months.mar'), trips: 220, distance: 8100 },
          { month: t('common:months.apr'), trips: 195, distance: 7300 },
          { month: t('common:months.may'), trips: 240, distance: 8900 },
          { month: t('common:months.jun'), trips: 247, distance: 9280 },
        ],
      };
      setData(mockData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatTrend = (trend: number) => {
    const isPositive = trend > 0;
    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span className="text-xs">{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">{t('trips:status.completed')}</Badge>;
      case 'IN_PROGRESS':
        return <Badge className="bg-blue-100 text-blue-800">{t('trips:status.inProgress')}</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800">{t('trips:status.pending')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('trips:dashboard.loadingDashboard')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('trips:dashboard.noDashboardData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">{t('trips:dashboard.overview')}</h2>
          <p className="text-sm text-muted-foreground">{t('trips:dashboard.overviewDescription')}</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {t('common:actions.refresh')}
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('trips:dashboard.totalTrips')}</CardTitle>
            <Route className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalTrips.toLocaleString()}</div>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-muted-foreground">{t('trips:dashboard.vsLastMonth')}</p>
              {formatTrend(data.monthlyGrowth)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('trips:dashboard.activeTrips')}</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeTrips}</div>
            <p className="text-xs text-muted-foreground">{t('trips:dashboard.currentlyInProgress')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('trips:dashboard.completedTrips')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.completedTrips.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{t('trips:dashboard.successfullyFinished')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('trips:dashboard.totalDistance')}</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalDistance.toLocaleString()} km</div>
            <p className="text-xs text-muted-foreground">{t('trips:dashboard.avgDistance', { distance: data.averageDistance })}</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Trip Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('trips:dashboard.tripStatusDistribution')}</CardTitle>
            <CardDescription>{t('trips:dashboard.tripStatusDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.tripsByStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {data.tripsByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>{t('trips:dashboard.monthlyActivity')}</CardTitle>
            <CardDescription>{t('trips:dashboard.monthlyActivityDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.monthlyStats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Line yAxisId="left" type="monotone" dataKey="trips" stroke="#8884d8" strokeWidth={2} name={t('trips:dashboard.trips')} />
                  <Line yAxisId="right" type="monotone" dataKey="distance" stroke="#82ca9d" strokeWidth={2} name={t('trips:dashboard.distanceKm')} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Trips */}
      <Card>
        <CardHeader>
          <CardTitle>{t('trips:dashboard.recentTrips')}</CardTitle>
          <CardDescription>{t('trips:dashboard.recentTripsDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentTrips.map((trip) => (
              <div key={trip.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Route className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{trip.startLocation} → {trip.endLocation}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Users className="h-3 w-3" />
                      <span>{trip.driverName}</span>
                      <span>•</span>
                      <span>{trip.scheduledDate}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(trip.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
