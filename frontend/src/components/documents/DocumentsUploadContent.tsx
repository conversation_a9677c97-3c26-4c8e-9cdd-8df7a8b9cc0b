import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { Upload, FileText, CheckCircle } from 'lucide-react';

interface DocumentsUploadContentProps {
  onDocumentUploaded?: () => void;
}

export const DocumentsUploadContent: React.FC<DocumentsUploadContentProps> = ({
  onDocumentUploaded
}) => {
  const router = useRouter();
  const { toast } = useToast();
  const [file, setFile] = useState<File | null>(null);
  const [documentInfo, setDocumentInfo] = useState({
    title: '',
    category: '',
    tags: '',
    description: '',
    associatedVehicle: '',
    associatedDriver: ''
  });
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
      // Auto-populate title if empty
      if (!documentInfo.title) {
        setDocumentInfo(prev => ({
          ...prev,
          title: e.target.files![0].name.replace(/\.[^/.]+$/, "")
        }));
      }
    }
  };

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string) => {
    setDocumentInfo({
      ...documentInfo,
      [field]: typeof e === 'string' ? e : e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!file) {
      toast({
        title: 'Error',
        description: 'Please select a file to upload',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);
    
    try {
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Success',
        description: 'Document uploaded successfully!',
      });
      
      // Reset form
      setFile(null);
      setDocumentInfo({
        title: '',
        category: '',
        tags: '',
        description: '',
        associatedVehicle: '',
        associatedDriver: ''
      });
      
      // Reset file input
      const fileInput = document.getElementById('document') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
      onDocumentUploaded?.();
      
      // Optionally navigate to browse tab
      setTimeout(() => {
        router.push('/documents?tab=browse');
      }, 1000);
      
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to upload document. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Document
        </h2>
        <p className="text-sm text-muted-foreground">
          Upload and categorize new documents to the system
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* File Upload */}
            <div className="space-y-2">
              <Label htmlFor="document">Upload Document *</Label>
              <Input
                id="document"
                type="file"
                required
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
              />
              {file && (
                <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-green-800">
                      {file.name}
                    </p>
                    <p className="text-xs text-green-600">
                      {(file.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Document Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Document Title *</Label>
              <Input
                id="title"
                placeholder="Enter document title"
                required
                value={documentInfo.title}
                onChange={handleInputChange('title')}
              />
            </div>

            {/* Category and Tags */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={documentInfo.category} onValueChange={handleInputChange('category')}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="registration">Vehicle Registration</SelectItem>
                    <SelectItem value="insurance">Insurance</SelectItem>
                    <SelectItem value="license">Driver License</SelectItem>
                    <SelectItem value="maintenance">Maintenance Records</SelectItem>
                    <SelectItem value="trip">Trip Documentation</SelectItem>
                    <SelectItem value="invoice">Invoices</SelectItem>
                    <SelectItem value="contract">Contracts</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  placeholder="Enter tags"
                  value={documentInfo.tags}
                  onChange={handleInputChange('tags')}
                />
                {documentInfo.tags && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {documentInfo.tags.split(',').map((tag, index) => (
                      tag.trim() && (
                        <Badge key={index} variant="outline">
                          {tag.trim()}
                        </Badge>
                      )
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter a description for this document"
                rows={3}
                value={documentInfo.description}
                onChange={handleInputChange('description')}
              />
            </div>

            {/* Associations */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="associatedVehicle">Associated Vehicle (Optional)</Label>
                <Select 
                  value={documentInfo.associatedVehicle} 
                  onValueChange={handleInputChange('associatedVehicle')}
                >
                  <SelectTrigger id="associatedVehicle">
                    <SelectValue placeholder="Select vehicle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">None</SelectItem>
                    <SelectItem value="v1">Ford Transit (ABC123)</SelectItem>
                    <SelectItem value="v2">Toyota Hiace (XYZ789)</SelectItem>
                    <SelectItem value="v3">Mercedes Sprinter (DEF456)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="associatedDriver">Associated Driver (Optional)</Label>
                <Select 
                  value={documentInfo.associatedDriver} 
                  onValueChange={handleInputChange('associatedDriver')}
                >
                  <SelectTrigger id="associatedDriver">
                    <SelectValue placeholder="Select driver" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">None</SelectItem>
                    <SelectItem value="d1">John Smith</SelectItem>
                    <SelectItem value="d2">Emma Johnson</SelectItem>
                    <SelectItem value="d3">Robert Williams</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-4 flex gap-4">
              <Button type="submit" disabled={isUploading || !file} className="gap-2">
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    Upload Document
                  </>
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline"
                onClick={() => router.push('/documents?tab=browse')}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
