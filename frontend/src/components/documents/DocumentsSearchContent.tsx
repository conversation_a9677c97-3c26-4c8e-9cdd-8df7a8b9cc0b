import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, FileText, Filter, Calendar } from 'lucide-react';

interface DocumentsSearchContentProps {
  refreshTrigger?: number;
}

export const DocumentsSearchContent: React.FC<DocumentsSearchContentProps> = ({
  refreshTrigger
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchCategory, setSearchCategory] = useState('');
  const [searchDateFrom, setSearchDateFrom] = useState('');
  const [searchDateTo, setSearchDateTo] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const categories = ['All', 'Registration', 'Insurance', 'License', 'Maintenance', 'Report', 'Invoice', 'Contract'];

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    // Simulate search delay
    setTimeout(() => {
      // Mock search results
      const mockResults = [
        {
          id: 'search1',
          title: 'Vehicle Registration - Ford Transit',
          category: 'Registration',
          date: '2025-01-15',
          fileType: 'PDF',
          size: '1.2 MB',
          snippet: 'Vehicle registration document for Ford Transit with plate number ABC123...',
          relevance: 95,
        },
        {
          id: 'search2',
          title: 'Insurance Policy - Toyota Hiace',
          category: 'Insurance',
          date: '2025-03-10',
          fileType: 'PDF',
          size: '3.5 MB',
          snippet: 'Comprehensive insurance policy covering Toyota Hiace vehicle...',
          relevance: 87,
        },
        {
          id: 'search3',
          title: 'Maintenance Record - Mercedes Sprinter',
          category: 'Maintenance',
          date: '2025-04-22',
          fileType: 'PDF',
          size: '2.3 MB',
          snippet: 'Regular maintenance service record for Mercedes Sprinter including oil change...',
          relevance: 76,
        },
      ].filter(doc => {
        const matchesQuery = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           doc.snippet.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesCategory = !searchCategory || searchCategory === 'All' || doc.category === searchCategory;
        return matchesQuery && matchesCategory;
      });

      setSearchResults(mockResults);
      setIsSearching(false);
    }, 1000);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchCategory('');
    setSearchDateFrom('');
    setSearchDateTo('');
    setSearchResults([]);
    setHasSearched(false);
  };

  const getRelevanceColor = (relevance: number) => {
    if (relevance >= 90) return 'bg-green-100 text-green-800';
    if (relevance >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <Search className="h-5 w-5" />
          Search Documents
        </h2>
        <p className="text-sm text-muted-foreground">
          Search for specific documents by name, content, or metadata
        </p>
      </div>

      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Search Criteria
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Query */}
          <div className="space-y-2">
            <Label htmlFor="searchQuery">Search Query</Label>
            <div className="flex gap-2">
              <Input
                id="searchQuery"
                placeholder="Enter keywords, document title, or content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button onClick={handleSearch} disabled={isSearching || !searchQuery.trim()}>
                {isSearching ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>

          {/* Advanced Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="searchCategory">Category</Label>
              <Select value={searchCategory} onValueChange={setSearchCategory}>
                <SelectTrigger id="searchCategory">
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="searchDateFrom">Date From</Label>
              <Input
                id="searchDateFrom"
                type="date"
                value={searchDateFrom}
                onChange={(e) => setSearchDateFrom(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="searchDateTo">Date To</Label>
              <Input
                id="searchDateTo"
                type="date"
                value={searchDateTo}
                onChange={(e) => setSearchDateTo(e.target.value)}
              />
            </div>
          </div>

          {/* Clear Button */}
          {(searchQuery || searchCategory || searchDateFrom || searchDateTo) && (
            <div className="pt-2">
              <Button variant="outline" onClick={handleClearSearch}>
                Clear All Filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search Results */}
      {hasSearched && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Search Results</span>
              {searchResults.length > 0 && (
                <Badge variant="outline">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isSearching ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Searching documents...</p>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No documents found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search criteria or using different keywords
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {searchResults.map((doc) => (
                  <div key={doc.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-gray-400" />
                        <div>
                          <h3 className="font-medium text-gray-900">{doc.title}</h3>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{doc.category}</span>
                            <span>•</span>
                            <span>{doc.date}</span>
                            <span>•</span>
                            <span>{doc.size}</span>
                          </div>
                        </div>
                      </div>
                      <Badge className={getRelevanceColor(doc.relevance)}>
                        {doc.relevance}% match
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{doc.snippet}</p>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        View Document
                      </Button>
                      <Button variant="outline" size="sm">
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Search Tips */}
      {!hasSearched && (
        <Card>
          <CardHeader>
            <CardTitle>Search Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Use specific keywords related to the document content</p>
              <p>• Search by document title, category, or associated vehicle/driver</p>
              <p>• Use date filters to narrow down results by upload or creation date</p>
              <p>• Combine multiple filters for more precise results</p>
              <p>• Search is case-insensitive and supports partial matches</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
