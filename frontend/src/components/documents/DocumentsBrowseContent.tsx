import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import { FolderOpen, Download, Eye, Plus } from 'lucide-react';

interface DocumentsBrowseContentProps {
  onDocumentUpdated?: () => void;
  refreshTrigger?: number;
}

export const DocumentsBrowseContent: React.FC<DocumentsBrowseContentProps> = ({
  onDocumentUpdated,
  refreshTrigger
}) => {
  const router = useRouter();
  const [documents, setDocuments] = useState([
    {
      id: 'doc1',
      title: 'Vehicle Registration - Ford Transit',
      category: 'Registration',
      date: '2025-01-15',
      fileType: 'PDF',
      size: '1.2 MB',
      tags: ['registration', 'ford', 'legal']
    },
    {
      id: 'doc2',
      title: 'Insurance Policy - Toyota Hiace',
      category: 'Insurance',
      date: '2025-03-10',
      fileType: 'PDF',
      size: '3.5 MB',
      tags: ['insurance', 'toyota', 'policy']
    },
    {
      id: 'doc3',
      title: 'Driver License - John Smith',
      category: 'License',
      date: '2024-11-05',
      fileType: 'PDF',
      size: '0.8 MB',
      tags: ['license', 'driver', 'legal']
    },
    {
      id: 'doc4',
      title: 'Maintenance Record - Mercedes Sprinter',
      category: 'Maintenance',
      date: '2025-04-22',
      fileType: 'PDF',
      size: '2.3 MB',
      tags: ['maintenance', 'mercedes', 'service']
    },
    {
      id: 'doc5',
      title: 'Trip Report - Q1 2025',
      category: 'Report',
      date: '2025-04-02',
      fileType: 'PDF',
      size: '4.1 MB',
      tags: ['report', 'quarterly', 'trips']
    },
  ]);
  
  const [filter, setFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('All');
  
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(filter.toLowerCase()) || 
                         doc.tags.some(tag => tag.toLowerCase().includes(filter.toLowerCase()));
    const matchesCategory = categoryFilter === 'All' || doc.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });
  
  const categories = ['All', 'Registration', 'Insurance', 'License', 'Maintenance', 'Report'];

  useEffect(() => {
    // Refresh documents when refreshTrigger changes
    if (refreshTrigger) {
      // In a real app, this would fetch from API
      console.log('Refreshing documents...');
    }
  }, [refreshTrigger]);

  const handleViewDocument = (docId: string) => {
    // In a real app, this would open the document viewer
    console.log('Viewing document:', docId);
  };

  const handleDownloadDocument = (docId: string) => {
    // In a real app, this would download the document
    console.log('Downloading document:', docId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Browse Documents
          </h2>
          <p className="text-sm text-muted-foreground">
            Access and view all stored documents
          </p>
        </div>
        <Button onClick={() => router.push('/documents?tab=upload')} className="gap-2">
          <Plus className="h-4 w-4" />
          Upload Document
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <Input
                placeholder="Search documents..."
                value={filter}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
              />
            </div>
            <div className="w-40">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <Card>
        <CardHeader>
          <CardTitle>Documents ({filteredDocuments.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {filteredDocuments.length === 0 ? (
            <div className="p-8 text-center">
              <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-muted-foreground mb-4">
                {filter || categoryFilter !== 'All' 
                  ? 'No documents match your filter criteria'
                  : 'No documents have been uploaded yet'
                }
              </p>
              <Button onClick={() => router.push('/documents?tab=upload')}>
                Upload First Document
              </Button>
            </div>
          ) : (
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Document
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <svg
                            className="flex-shrink-0 h-5 w-5 text-gray-400 mr-3"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                            />
                          </svg>
                          <div>
                            <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                            <div className="text-xs text-gray-500">{doc.size}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{doc.category}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{doc.date}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{doc.fileType}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleViewDocument(doc.id)}
                            className="gap-1"
                          >
                            <Eye className="h-3 w-3" />
                            View
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDownloadDocument(doc.id)}
                            className="gap-1"
                          >
                            <Download className="h-3 w-3" />
                            Download
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
