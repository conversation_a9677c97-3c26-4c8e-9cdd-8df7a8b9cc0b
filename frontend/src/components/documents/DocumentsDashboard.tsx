import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { FolderOpen, Upload, Search, FileText, Calendar, Tag, Users } from 'lucide-react';

export const DocumentsDashboard: React.FC = () => {
  const router = useRouter();

  const quickActions = [
    {
      title: 'Browse Documents',
      description: 'Browse and view all documents in the system',
      icon: FolderOpen,
      action: () => router.push('/documents?tab=browse'),
      color: 'bg-blue-500',
    },
    {
      title: 'Upload Document',
      description: 'Upload new documents to the system',
      icon: Upload,
      action: () => router.push('/documents?tab=upload'),
      color: 'bg-green-500',
    },
    {
      title: 'Search Documents',
      description: 'Search for specific documents by name, type, or content',
      icon: Search,
      action: () => router.push('/documents?tab=search'),
      color: 'bg-purple-500',
    },
  ];

  const stats = [
    {
      title: 'Total Documents',
      value: '247',
      icon: FileText,
      color: 'text-blue-600',
    },
    {
      title: 'Recent Uploads',
      value: '12',
      subtitle: 'This week',
      icon: Calendar,
      color: 'text-green-600',
    },
    {
      title: 'Categories',
      value: '8',
      icon: Tag,
      color: 'text-purple-600',
    },
    {
      title: 'Shared Documents',
      value: '156',
      icon: Users,
      color: 'text-orange-600',
    },
  ];

  const recentDocuments = [
    {
      id: 'doc1',
      title: 'Vehicle Registration - Ford Transit',
      category: 'Registration',
      date: '2025-01-15',
      size: '1.2 MB',
    },
    {
      id: 'doc2',
      title: 'Insurance Policy - Toyota Hiace',
      category: 'Insurance',
      date: '2025-01-14',
      size: '3.5 MB',
    },
    {
      id: 'doc3',
      title: 'Driver License - John Smith',
      category: 'License',
      date: '2025-01-13',
      size: '0.8 MB',
    },
    {
      id: 'doc4',
      title: 'Maintenance Record - Mercedes Sprinter',
      category: 'Maintenance',
      date: '2025-01-12',
      size: '2.3 MB',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Document Management Dashboard
        </h2>
        <p className="text-sm text-muted-foreground">
          Overview of your document management system
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                {stat.subtitle && (
                  <p className="text-xs text-muted-foreground">{stat.subtitle}</p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <div
                  key={action.title}
                  className="rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={action.action}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg ${action.color} text-white`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <h3 className="font-medium">{action.title}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">{action.description}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Documents */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Recent Documents</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/documents?tab=browse')}
          >
            View All
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentDocuments.map((doc) => (
              <div
                key={doc.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => router.push('/documents?tab=browse')}
              >
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">{doc.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {doc.category} • {doc.size}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">{doc.date}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Document Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Document Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-4">
            {[
              { name: 'Registration', count: 45, color: 'bg-blue-100 text-blue-800' },
              { name: 'Insurance', count: 38, color: 'bg-green-100 text-green-800' },
              { name: 'Licenses', count: 29, color: 'bg-purple-100 text-purple-800' },
              { name: 'Maintenance', count: 67, color: 'bg-orange-100 text-orange-800' },
              { name: 'Trip Reports', count: 34, color: 'bg-red-100 text-red-800' },
              { name: 'Invoices', count: 23, color: 'bg-yellow-100 text-yellow-800' },
              { name: 'Contracts', count: 8, color: 'bg-indigo-100 text-indigo-800' },
              { name: 'Other', count: 3, color: 'bg-gray-100 text-gray-800' },
            ].map((category) => (
              <div
                key={category.name}
                className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
                onClick={() => router.push(`/documents?tab=browse&category=${category.name}`)}
              >
                <span className="text-sm font-medium">{category.name}</span>
                <span className={`px-2 py-1 rounded-full text-xs ${category.color}`}>
                  {category.count}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
