{"title": "Trip Management", "addTrip": "Add Trip", "editTrip": "Edit Trip", "deleteTrip": "Delete Trip", "tripDetails": "Trip Details", "createTrip": "Create Trip", "searchTrips": "Search trips...", "noTripsFound": "No trips found", "loadingTrips": "Loading trips...", "fields": {"tripNumber": "Trip Number", "status": "Status", "type": "Type", "priority": "Priority", "driver": "Driver", "vehicle": "Vehicle", "truck": "Truck", "trailer": "Trailer", "pickupLocation": "Pickup Location", "deliveryLocation": "Delivery Location", "startTime": "Start Time", "endTime": "End Time", "estimatedDuration": "Estimated Duration", "actualDuration": "Actual Duration", "distance": "Distance", "estimatedDistance": "Estimated Distance", "notes": "Notes", "cargo": "Cargo", "cargoWeight": "Cargo Weight", "cargoValue": "Cargo Value", "cargoDescription": "Cargo Description"}, "status": {"planned": "Planned", "assigned": "Assigned", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "delayed": "Delayed"}, "types": {"standard": "Standard", "express": "Express", "pickup": "Pickup", "delivery": "Delivery", "transfer": "Transfer", "maintenance": "Maintenance", "other": "Other"}, "typeDescriptions": {"delivery": "Deliver goods to customers or destinations", "transfer": "Move goods between company locations", "maintenance": "Vehicle maintenance or service trip", "other": "Other types of trips not listed above"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}, "actions": {"viewDetails": "View Details", "editTrip": "Edit Trip", "deleteTrip": "Delete Trip", "assignDriver": "Assign Driver", "assignVehicle": "Assign Vehicle", "startTrip": "Start Trip", "completeTrip": "Complete Trip", "cancelTrip": "Cancel Trip", "duplicateTrip": "Duplicate Trip"}, "sections": {"basicInfo": "Basic Information", "routeSchedule": "Route & Schedule", "vehicleAssignment": "Vehicle Assignment", "driverAssignment": "Driver Assignment", "cargoInformation": "Cargo Information", "additionalNotes": "Additional Notes"}, "validation": {"tripNumberRequired": "Trip number is required", "driverRequired": "Driver is required", "vehicleRequired": "Vehicle is required", "pickupLocationRequired": "Pickup location is required", "deliveryLocationRequired": "Delivery location is required", "startTimeRequired": "Start time is required", "endTimeAfterStart": "End time must be after start time", "invalidDistance": "Invalid distance", "invalidWeight": "Invalid weight"}, "messages": {"tripCreated": "Trip created successfully", "tripUpdated": "Trip updated successfully", "tripDeleted": "<PERSON> deleted successfully", "tripAssigned": "Trip assigned successfully", "tripStarted": "Trip started", "tripCompleted": "Trip completed", "tripCancelled": "Trip cancelled", "confirmDelete": "Are you sure you want to delete this trip?", "confirmCancel": "Are you sure you want to cancel this trip?", "unsavedChanges": "You have unsaved changes. Do you want to continue?", "loadDriversError": "Failed to load drivers", "loadVehiclesError": "Failed to load vehicles"}, "descriptions": {"editTripDescription": "Update trip details and assignments", "createTripDescription": "Plan and schedule a new trip with vehicle and driver assignments"}, "filters": {"all": "All", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "byStatus": "By Status", "byDriver": "By Driver", "byVehicle": "By Vehicle", "dateRange": "Date Range"}, "allTrips": "All Trips", "allTripsDescription": "Manage and track all trips in your fleet", "scheduleNewTrip": "Schedule New Trip", "backToTrips": "Back to Trips", "reports": {"title": "Trip Reports", "description": "Generate and analyze reports for trips and fleet utilization", "exportReport": "Export Report", "generateReports": "Generate Reports", "selectParameters": "Configure report parameters and generate insights", "reportType": "Report Type", "selectReportType": "Select report type", "startDate": "Start Date", "endDate": "End Date", "generating": "Generating...", "generateReport": "Generate Report", "noReportGenerated": "No Report Generated", "selectParametersToGenerate": "Select report parameters above and click \"Generate Report\" to see results", "types": {"daily": "Daily Trips", "weekly": "Weekly Summary", "monthly": "Monthly Analysis", "driver": "Driver Performance", "vehicle": "Vehicle Utilization"}}, "businessPartners": {"businessPartner": "Business Partner", "location": "Location", "default": "<PERSON><PERSON><PERSON>", "operatingHours": "Operating Hours", "specialInstructions": "Special Instructions", "types": {"shipper": "Shipper", "logistics": "Logistics"}, "fields": {"locationName": "Location Name", "contactPerson": "Contact Person", "city": "City", "state": "State", "postalCode": "Postal Code"}, "placeholders": {"chooseBusinessPartner": "<PERSON>ose a business partner", "chooseLocation": "Choose a location", "locationName": "e.g., Main Warehouse, Loading Dock A", "contactName": "Contact name", "streetAddress": "Street address", "city": "City", "state": "State", "postalCode": "Postal code", "phoneNumber": "Phone number", "operatingHours": "e.g., Mon-Fri 8AM-5PM", "specialInstructions": "Any special delivery/pickup instructions"}, "actions": {"addNewLocation": "Add New Location", "adding": "Adding..."}, "messages": {"failedToLoadPartners": "Failed to load business partners", "failedToLoadLocations": "Failed to load locations", "loadingLocations": "Loading locations...", "locationAddedAndSelected": "Location added to partner and selected for trip", "locationSet": "Location Set", "locationSetForTripOnly": "Location set for this trip only", "failedToAddLocation": "Failed to add location", "locationWillBeSaved": "This location will be saved and available for future trips with this partner.", "locationOnlyForThisTrip": "This location will only be used for this trip and won't be saved permanently."}, "validation": {"fillRequiredFields": "Please fill in the required fields: Name, Address, and City"}}}