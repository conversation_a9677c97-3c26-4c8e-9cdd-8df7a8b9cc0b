{"businessPartner": "Business Partner", "location": "Location", "default": "<PERSON><PERSON><PERSON>", "operatingHours": "Operating Hours", "specialInstructions": "Special Instructions", "types": {"shipper": "Shipper", "logistics": "Logistics"}, "locationTypes": {"PICKUP_POINT": "Pickup Point", "DELIVERY_POINT": "Delivery Point", "WAREHOUSE": "Warehouse", "DISTRIBUTION_CENTER": "Distribution Center"}, "fields": {"locationName": "Location Name", "contactPerson": "Contact Person", "city": "City", "state": "State", "postalCode": "Postal Code"}, "placeholders": {"chooseBusinessPartner": "<PERSON>ose a business partner", "chooseLocation": "Choose a location", "locationName": "e.g., Main Warehouse, Loading Dock A", "contactName": "Contact name", "streetAddress": "Street address", "city": "City", "state": "State", "postalCode": "Postal code", "phoneNumber": "Phone number", "operatingHours": "e.g., Mon-Fri 8AM-5PM", "specialInstructions": "Any special delivery/pickup instructions", "searchPartners": "Search partners, contacts, streets, cities..."}, "actions": {"addNewLocation": "Add New Location", "addLocation": "Add Location", "viewDetails": "View Details", "adding": "Adding...", "search": "Search"}, "messages": {"failedToLoadPartners": "Failed to load business partners", "failedToLoadLocations": "Failed to load locations", "loadingLocations": "Loading locations...", "locationAddedAndSelected": "Location added to partner and selected for trip", "locationSet": "Location Set", "locationSetForTripOnly": "Location set for this trip only", "failedToAddLocation": "Failed to add location", "saveLocationToPermanent": "Save this location to {{partnerName}}'s permanent locations", "locationWillBeSaved": "This location will be saved and available for future trips with this partner.", "locationOnlyForThisTrip": "This location will only be used for this trip and won't be saved permanently.", "addLocationDescription": "Add a new location for <strong>{{partnerName}}</strong>. You can choose whether to save it permanently or use it only for this trip.", "locationAddedSuccess": "Location added successfully", "locationUpdatedSuccess": "Location updated successfully", "locationDeletedSuccess": "Location deleted successfully", "failedToUpdateLocation": "Failed to update location", "failedToDeleteLocation": "Failed to delete location"}, "validation": {"fillRequiredFields": "Please fill in the required fields: Name, Address, and City"}, "dashboard": {"title": "Business Partners Overview", "description": "Key metrics and insights for your partner network", "loadingDashboard": "Loading dashboard...", "noDashboardData": "No dashboard data available", "vsLastMonth": "vs last month", "pickupDeliveryPoints": "Pickup & delivery points", "shippingPartners": "Shipping partners", "logisticsProviders": "Logistics providers", "locations": "locations", "trips": "trips", "totalActivity": "Total activity", "metrics": {"totalPartners": "Total Partners", "totalLocations": "Total Locations", "activeShippers": "Active Shippers", "logisticsPartners": "Logistics Partners"}, "charts": {"partnerDistribution": {"title": "Partner Distribution", "description": "Breakdown by partner type"}, "monthlyActivity": {"title": "Monthly Activity", "description": "Trips and new partners over time"}, "trips": "Trips", "newPartners": "New Partners"}, "topPartners": {"title": "Top Partners by Activity", "description": "Most active business partners"}}, "management": {"title": "Business Partners Management", "welcomeTitle": "Welcome to Business Partners", "welcomeDescription": "Select a business partner above to view and manage their locations", "loadingPartners": "Loading business partners...", "partners": "Partners", "locations": "Locations", "totalTrips": "Total Trips", "mostActivePartners": "Most Active Partners", "trips": "trips", "contact": "Contact", "noLocations": "No locations", "noLocationsDescription": "This partner doesn't have any locations yet. Add the first location to get started.", "noLocationsFound": "No locations found", "noLocationsMatch": "No locations match your search criteria", "noLocationsYet": "This partner has no locations yet", "addFirstLocation": "Add First Location", "locationCount": "{{count}} location", "locationCount_plural": "{{count}} locations", "tripCount": "{{count}} trip", "tripCount_plural": "{{count}} trips", "tableHeaders": {"name": "Name", "address": "Address", "type": "Type", "default": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "tooltips": {"viewDetails": "View Details", "editLocation": "Edit Location", "deleteLocation": "Delete Location"}}, "shippers": {"title": "Shippers", "description": "Manage shipping partners and their pickup locations", "tableTitle": "Shipping Partners", "tableDescription": "Companies that provide goods for transportation", "noShippersFound": "No Shippers Found", "noShippersMatch": "No shippers match your search criteria.", "noShippersYet": "No shipping partners have been added yet.", "addFirstShipper": "Add First Shipper", "searchShippers": "Search shippers...", "shipperCount": "{{count}} shipper", "shipperCount_plural": "{{count}} shippers", "tableHeaders": {"company": "Company", "contact": "Contact", "locations": "Locations", "activity": "Activity", "status": "Status", "actions": "Actions"}, "loading": "Loading shippers...", "locationCount": "{{count}} location", "locationCount_plural": "{{count}} locations", "moreLocations": "+{{count}} more", "activity": {"pickups": "{{count}} pickup", "pickups_plural": "{{count}} pickups", "deliveries": "{{count}} delivery", "deliveries_plural": "{{count}} deliveries"}, "status": {"ACTIVE": "Active", "INACTIVE": "Inactive", "SUSPENDED": "Suspended"}, "confirmDelete": "Are you sure you want to delete this shipper?", "messages": {"deleteSuccess": "<PERSON><PERSON> deleted successfully", "deleteFailed": "Failed to delete shipper", "loadFailed": "Failed to load shippers"}, "tooltips": {"viewDetails": "View details", "editPartner": "Edit partner", "deletePartner": "Delete partner"}}, "addPartnerDialog": {"title": "Add Business Partner", "addShipper": "Add Shipper"}, "logistics": {"title": "Logistics Partners", "description": "Manage transportation and logistics service providers", "tableTitle": "Logistics Providers", "tableDescription": "Companies that provide transportation and delivery services", "noLogisticsFound": "No Logistics Partners Found", "noLogisticsMatch": "No logistics partners match your search criteria.", "noLogisticsYet": "No logistics partners have been added yet.", "addFirstLogistics": "Add First Logistics Partner", "searchLogistics": "Search logistics partners...", "logisticsCount": "{{count}} logistics partner", "logisticsCount_plural": "{{count}} logistics partners", "tableHeaders": {"company": "Company", "contact": "Contact", "locations": "Locations", "activity": "Activity", "status": "Status", "actions": "Actions"}, "loading": "Loading logistics partners...", "locationCount": "{{count}} location", "locationCount_plural": "{{count}} locations", "moreLocations": "+{{count}} more", "activity": {"pickups": "{{count}} pickup", "pickups_plural": "{{count}} pickups", "deliveries": "{{count}} delivery", "deliveries_plural": "{{count}} deliveries"}, "status": {"ACTIVE": "Active", "INACTIVE": "Inactive", "SUSPENDED": "Suspended"}, "confirmDelete": "Are you sure you want to delete this logistics partner?", "messages": {"deleteSuccess": "Logistics partner deleted successfully", "deleteFailed": "Failed to delete logistics partner", "loadFailed": "Failed to load logistics partners"}, "tooltips": {"viewDetails": "View details", "editPartner": "Edit partner", "deletePartner": "Delete partner"}}, "addDialog": {"title": "Add Business Partner", "description": "Add a new shipper or logistics partner to your network.", "sections": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "additionalInfo": "Additional Information"}, "fields": {"partnerName": "Partner Name", "partnerType": "Partner Type", "status": "Status", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "website": "Website", "taxId": "Tax ID", "notes": "Notes"}, "placeholders": {"partnerName": "Enter partner name", "selectPartnerType": "Select partner type", "selectStatus": "Select status", "contactName": "Primary contact name", "phoneNumber": "Phone number", "emailAddress": "Email address", "websiteUrl": "https://example.com", "taxNumber": "Tax identification number", "additionalNotes": "Additional notes about this partner"}, "partnerTypes": {"shipper": "Shipper", "logisticsPartner": "Logistics Partner"}, "statuses": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "actions": {"cancel": "Cancel", "addPartner": "Add Partner", "adding": "Adding..."}, "validation": {"partnerNameRequired": "Partner name is required", "partnerTypeRequired": "Partner type is required", "invalidEmail": "Invalid email address", "invalidWebsite": "Invalid website URL"}, "messages": {"success": "Business partner created successfully", "error": "Failed to add business partner", "createFailed": "Failed to create business partner"}}, "detailsDialog": {"title": "Business Partner Details", "description": "Business partner details and locations", "tabs": {"details": "Partner Details", "locations": "Locations"}, "sections": {"basicInfo": "Basic Information", "partnerLocations": "Partner Locations"}, "fields": {"contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "website": "Website", "taxId": "Tax ID", "notes": "Notes", "totalTrips": "Total Trips", "created": "Created", "contact": "Contact", "hours": "Hours", "specialInstructions": "Special Instructions"}, "statuses": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "noLocations": {"title": "No locations added", "description": "Add pickup and delivery locations for this partner", "addFirst": "Add First Location"}, "tripStats": "{{total}} trips ({{pickups}} pickups, {{deliveries}} deliveries)", "confirmDelete": "Are you sure you want to delete this location?"}, "locationDialog": {"title": "Location Details", "description": "Detailed information about this location", "sections": {"addressInfo": "Address Information", "contactInfo": "Contact Information", "operatingInfo": "Operating Information", "recordInfo": "Record Information"}, "fields": {"fullAddress": "Full Address", "city": "City", "stateProvince": "State/Province", "postalCode": "Postal Code", "country": "Country", "coordinates": "Coordinates", "contactPerson": "Contact Person", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "operatingHours": "Operating Hours", "specialInstructions": "Special Instructions", "created": "Created", "lastUpdated": "Last Updated"}, "statuses": {"active": "Active", "inactive": "Inactive", "default": "<PERSON><PERSON><PERSON>"}}, "addLocationDialog": {"title": "Add Location", "description": "Add a new location for the business partner", "fields": {"locationName": "Location Name", "locationType": "Location Type", "address": "Address", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "operatingHours": "Operating Hours", "specialInstructions": "Special Instructions", "isDefault": "Set as default location", "isActive": "Location is active"}, "placeholders": {"locationName": "e.g. Main warehouse, Loading dock A", "selectLocationType": "Select location type", "streetAddress": "Street address", "cityName": "City name", "stateName": "State name", "postalCode": "Postal code", "countryName": "Country name", "contactName": "Contact name", "phoneNumber": "Phone number", "emailAddress": "Email address", "operatingHours": "e.g. Mon-Fri 8:00-17:00", "specialInstructions": "Any special delivery/pickup instructions"}, "locationTypes": {"PICKUP_POINT": "Pickup Point", "DELIVERY_POINT": "Delivery Point", "WAREHOUSE": "Warehouse", "DISTRIBUTION_CENTER": "Distribution Center"}, "actions": {"cancel": "Cancel", "addLocation": "Add Location", "adding": "Adding..."}, "validation": {"locationNameRequired": "Location name is required", "locationTypeRequired": "Location type is required", "addressRequired": "Address is required", "cityRequired": "City is required", "countryRequired": "Country is required", "invalidEmail": "Invalid email address"}, "messages": {"success": "Location added successfully", "error": "Failed to add location", "createFailed": "Failed to create location"}}, "editLocationDialog": {"title": "Edit Location", "description": "Update location information", "fields": {"locationName": "Location Name", "locationType": "Location Type", "address": "Address", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "operatingHours": "Operating Hours", "specialInstructions": "Special Instructions", "isDefault": "Default location", "isActive": "Location is active"}, "placeholders": {"locationName": "e.g. Main warehouse, Loading dock A", "selectLocationType": "Select location type", "streetAddress": "Street address", "cityName": "City name", "stateName": "State name", "postalCode": "Postal code", "countryName": "Country name", "contactName": "Contact name", "phoneNumber": "Phone number", "emailAddress": "Email address", "operatingHours": "e.g. Mon-Fri 8:00-17:00", "specialInstructions": "Any special delivery/pickup instructions"}, "locationTypes": {"PICKUP_POINT": "Pickup Point", "DELIVERY_POINT": "Delivery Point", "WAREHOUSE": "Warehouse", "DISTRIBUTION_CENTER": "Distribution Center"}, "actions": {"cancel": "Cancel", "saveChanges": "Save Changes", "saving": "Saving..."}, "validation": {"nameRequired": "Name is required", "addressRequired": "Address is required", "cityRequired": "City is required", "countryRequired": "Country is required", "invalidEmail": "Invalid email address"}, "messages": {"success": "Location updated successfully", "error": "Failed to update location", "updateFailed": "Failed to update location"}}}