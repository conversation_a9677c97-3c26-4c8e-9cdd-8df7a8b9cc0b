{"buttons": {"save": "Save", "cancel": "Cancel", "submit": "Submit", "reset": "Reset", "clear": "Clear", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "close": "Close", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "upload": "Upload", "download": "Download", "print": "Print", "copy": "Copy", "duplicate": "Duplicate"}, "labels": {"required": "Required", "optional": "Optional", "selectOption": "Select option", "selectMultiple": "Select multiple", "noOptions": "No options", "loading": "Loading...", "searching": "Searching...", "noResults": "No results", "selectAll": "Select all", "deselectAll": "Deselect all", "showMore": "Show more", "showLess": "Show less", "expand": "Expand", "collapse": "Collapse"}, "placeholders": {"enterText": "Enter text", "enterEmail": "Enter email", "enterPhone": "Enter phone", "password": "Password", "enterPassword": "Enter password", "confirmPassword": "Confirm password", "enterNumber": "Enter number", "enterDate": "Select date", "enterTime": "Select time", "selectOption": "Select option", "searchHere": "Search here...", "typeToSearch": "Type to search...", "enterAddress": "Enter address", "enterCity": "Enter city", "enterPostalCode": "Enter postal code", "enterNotes": "Enter notes..."}, "validation": {"required": "This field is required", "email": "Enter a valid email address", "phone": "Enter a valid phone number", "number": "Enter a valid number", "positiveNumber": "Number must be greater than zero", "integer": "Enter a whole number", "decimal": "Enter a decimal number", "minLength": "Minimum {{min}} characters", "maxLength": "Maximum {{max}} characters", "minValue": "Minimum value: {{min}}", "maxValue": "Maximum value: {{max}}", "pattern": "Invalid format", "passwordMatch": "Passwords must match", "passwordStrength": "Password must contain at least 8 characters, one uppercase letter, one lowercase letter, and one number", "dateInvalid": "Invalid date", "dateInFuture": "Date must be in the future", "dateInPast": "Date must be in the past", "timeInvalid": "Invalid time", "urlInvalid": "Invalid URL", "fileSize": "File is too large (maximum {{max}})", "fileType": "Invalid file type", "uniqueValue": "This value already exists"}, "messages": {"saveSuccess": "Saved successfully", "saveError": "Error saving", "deleteSuccess": "Deleted successfully", "deleteError": "Error deleting", "updateSuccess": "Updated successfully", "updateError": "Error updating", "createSuccess": "Created successfully", "createError": "Error creating", "loadError": "Error loading data", "networkError": "Network connection error", "serverError": "Server error", "validationError": "Validation errors", "unsavedChanges": "You have unsaved changes", "confirmLeave": "Are you sure you want to leave this page?", "confirmDelete": "Are you sure you want to delete?", "confirmReset": "Are you sure you want to reset the form?", "processing": "Processing...", "uploading": "Uploading...", "downloading": "Downloading...", "noChanges": "No changes to save"}, "dateTime": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year", "selectDate": "Select date", "selectTime": "Select time", "selectDateTime": "Select date and time", "dateFormat": "MM/DD/YYYY", "timeFormat": "HH:mm", "dateTimeFormat": "MM/DD/YYYY HH:mm"}, "file": {"selectFile": "Select file", "selectFiles": "Select files", "dropFiles": "Drop files here", "uploadFile": "Upload file", "uploadFiles": "Upload files", "removeFile": "Remove file", "downloadFile": "Download file", "previewFile": "Preview file", "fileSelected": "File selected", "filesSelected": "{{count}} files selected", "maxFileSize": "Maximum file size: {{size}}", "allowedTypes": "Allowed types: {{types}}", "uploadProgress": "Upload progress: {{progress}}%", "uploadComplete": "Upload complete", "uploadFailed": "Upload failed"}}