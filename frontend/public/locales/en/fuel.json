{"title": "Fuel Management", "addRecord": "Add Record", "editRecord": "Edit Record", "deleteRecord": "Delete Record", "fuelRecord": "Fuel Record", "createRecord": "Create Record", "searchRecords": "Search records...", "noRecordsFound": "No records found", "loadingRecords": "Loading records...", "tabs": {"records": "Records", "prices": "Prices", "analytics": "Analytics", "reports": "Reports"}, "fields": {"date": "Date", "vehicle": "Vehicle", "driver": "Driver", "fuelType": "Fuel Type", "quantity": "Quantity", "pricePerLiter": "Price per Liter", "totalCost": "Total Cost", "odometer": "Odometer", "location": "Location", "station": "Station", "receiptNumber": "Receipt Number", "notes": "Notes", "efficiency": "Efficiency", "consumption": "Consumption"}, "fuelTypes": {"diesel": "Diesel", "petrol": "Petrol", "lpg": "LPG", "cng": "CNG", "electric": "Electric"}, "actions": {"viewDetails": "View Details", "editRecord": "Edit Record", "deleteRecord": "Delete Record", "uploadReceipt": "Upload Receipt", "downloadReport": "Download Report", "exportData": "Export Data", "refreshPrices": "Refresh Prices"}, "sections": {"basicInfo": "Basic Information", "fuelDetails": "Fuel Details", "vehicleInfo": "Vehicle Information", "costBreakdown": "Cost Breakdown", "receipt": "Receipt"}, "validation": {"dateRequired": "Date is required", "vehicleRequired": "Vehicle is required", "driverRequired": "Driver is required", "quantityRequired": "Quantity is required", "quantityPositive": "Quantity must be greater than zero", "quantityMin": "Quantity must be at least 0.1 liters", "quantityMax": "Quantity cannot exceed 1000 liters", "priceRequired": "Price is required", "pricePositive": "Price must be greater than zero", "totalCostMin": "Total cost must be at least 0.01", "totalCostMax": "Total cost cannot exceed 10000", "locationRequired": "Location is required", "fuelingDateRequired": "Fueling date is required", "odometerRequired": "Odometer reading is required", "odometerPositive": "Odometer reading must be greater than zero", "odometerMax": "Odometer reading seems unreasonably high", "enteredByRequired": "Entered by is required", "invalidDate": "Invalid date", "futureDate": "Date cannot be in the future", "invalidFileType": "Invalid file type", "fileTypeDescription": "Please upload a JPEG, PNG, or PDF file.", "fileTooLarge": "File too large", "fileSizeDescription": "Please upload a file smaller than 5MB."}, "messages": {"recordCreated": "Fuel record created successfully", "recordUpdated": "Fuel record updated successfully", "recordDeleted": "Fuel record deleted successfully", "pricesUpdated": "Fuel prices updated", "confirmDelete": "Are you sure you want to delete this record?", "unsavedChanges": "You have unsaved changes. Do you want to continue?", "receiptUploaded": "Receipt uploaded successfully", "reportGenerated": "Report generated successfully", "createSuccess": "Fuel record created successfully.", "updateSuccess": "Fuel record updated successfully.", "createError": "Failed to create fuel record.", "updateError": "Failed to update fuel record."}, "form": {"title": "Fuel Record", "editTitle": "Edit Fuel Record", "createTitle": "Create Fuel Record", "description": "Enter fueling details", "editDescription": "Update fueling details", "vehicleAndDriver": "Vehicle and Driver", "fuelDetails": "Fuel Details", "additionalInfo": "Additional Information", "selectVehicle": "Select vehicle", "selectDriver": "Select driver", "fuelingDate": "Fueling Date", "quantityLiters": "Quantity (liters)", "totalCostPLN": "Total Cost (PLN)", "pricePerLiter": "Price per liter", "calculatedAutomatically": "Calculated automatically", "odometerReading": "Odometer Reading", "receiptNumber": "Receipt Number", "receiptNumberOptional": "optional", "uploadReceipt": "Upload Receipt", "chooseFile": "Choose file", "noFileChosen": "No file chosen", "supportedFormats": "Supported formats: JPEG, PNG, PDF (max 5MB)", "enteredBy": "Entered by", "currentUser": "Current user", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "update": "Update", "updating": "Updating..."}, "filters": {"all": "All", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "lastMonth": "Last Month", "byVehicle": "By Vehicle", "byDriver": "By Driver", "byFuelType": "By Fuel Type", "dateRange": "Date Range", "allVehicles": "All Vehicles", "allDrivers": "All Drivers"}, "stats": {"totalRecords": "Total Records", "totalCost": "Total Cost", "totalQuantity": "Total Quantity", "averagePrice": "Average Price", "averageConsumption": "Average Consumption", "mostEfficientVehicle": "Most Efficient Vehicle", "monthlySpending": "Monthly Spending", "fuelEfficiency": "Fuel Efficiency", "fleetEfficiency": "Fleet Efficiency", "totalVolume": "Total Volume", "activeFleet": "Active Fleet"}, "dashboard": {"loadingError": "Failed to load dashboard data.", "averageConsumptionDesc": "Average consumption", "thisMonth": "This month", "activeDrivers": "{{count}} active driver", "activeDrivers_plural": "{{count}} active drivers", "monthlyBudget": "Monthly Budget", "budgetDescription": "Fuel spending vs. allocated budget for this month", "spent": "Spent: {{amount}}", "budget": "Budget: {{amount}}", "used": "{{percentage}}% used", "remaining": "{{amount}} remaining", "mostEfficientVehicles": "Most Efficient Vehicles", "mostEfficientVehiclesDesc": "Vehicles with the best fuel efficiency this month", "mostEfficientDrivers": "Most Efficient Drivers", "mostEfficientDriversDesc": "Drivers with the best fuel efficiency this month", "spentAmount": "{{amount}} spent"}, "charts": {"priceHistory": "Price History", "consumptionTrend": "Consumption Trend", "costByVehicle": "Cost by Vehicle", "efficiencyComparison": "Efficiency Comparison", "monthlySpending": "Monthly Spending"}, "units": {"liters": "liters", "kilometers": "kilometers", "kmPerLiter": "km/l", "litersPer100km": "l/100km", "pln": "PLN", "plnPerLiter": "PLN/l"}, "recordsDescription": "Manage and view all fuel consumption records", "pagination": {"showing": "Showing", "to": "to", "of": "of", "records": "records"}, "recordsMessages": {"loadError": "Failed to load fuel records", "deleteError": "Failed to delete fuel record"}, "prices": {"currentPriceGross": "Current Price (Gross)", "currentPriceNet": "Current Price (Net)", "priceTrend": "Price Trend", "scrapingStatus": "Scraping Status", "netPrice": "Net", "vatIncluded": "+23% VAT", "active": "Active", "issues": "Issues", "lastUpdate": "Last update", "noData": "No data", "vsYesterday": "vs yesterday", "priceScrapingIssues": "Price Scraping Issues", "scrapingIssuesDesc": "Fuel price scraping is experiencing issues.", "retryAttempt": "Retry attempt", "priceHistory30Days": "Price History (30 Days)", "orlenPricesDesc": "Orlen.pl diesel prices with 23% VAT included", "manualFetch": "Manual Fetch", "fetching": "Fetching...", "loadingPriceHistory": "Loading price history...", "noPriceHistoryData": "No price history data available", "currentPriceDetails": "Current Price Details", "latestFuelPriceInfo": "Latest fuel price information from Orlen.pl", "effectiveDate": "Effective Date", "netPriceLabel": "Net Price", "vatLabel": "VAT (23%)", "grossPriceLabel": "Gross Price", "source": "Source", "lastScraped": "Last Scraped", "vatRate": "VAT Rate", "noCurrentPriceData": "No current price data available.", "date": "Date", "grossPrice": "Gross Price", "messages": {"fetchSuccess": "Fuel price fetched successfully.", "fetchError": "Failed to fetch fuel price manually."}}, "analytics": {"loadingAnalytics": "Loading analytics...", "noAnalyticsData": "No analytics data available", "refresh": "Refresh", "timeRanges": {"last7Days": "Last 7 days", "last30Days": "Last 30 days", "last3Months": "Last 3 months", "lastYear": "Last year"}, "metrics": {"totalFuelCost": "Total Fuel Cost", "totalVolume": "Total Volume", "avgEfficiency": "Avg Efficiency", "totalRecords": "Total Records", "vsPreviousPeriod": "vs previous period", "fleetAverage": "Fleet average", "fuelRecords": "Fuel records"}, "charts": {"monthlyFuelConsumption": "Monthly Fuel Consumption", "costAndVolumeTrends": "Cost and volume trends over time", "costBreakdownByVehicleType": "Cost Breakdown by Vehicle Type", "distributionOfFuelCosts": "Distribution of fuel costs"}, "topPerformers": {"topVehiclesByCost": "Top Vehicles by Cost", "highestFuelConsumptionVehicles": "Highest fuel consumption vehicles", "topDriversByEfficiency": "Top Drivers by Efficiency", "mostFuelEfficientDrivers": "Most fuel-efficient drivers"}, "vehicleTypes": {"trucks": "Trucks", "deliveryVehicles": "Delivery Vehicles", "serviceVehicles": "Service Vehicles"}, "months": {"jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec"}, "chartLabels": {"cost": "Cost", "volume": "Volume", "month": "Month", "liters": "Liters", "pln": "PLN", "efficiency": "Efficiency", "l100km": "L/100km"}}}