{"vehicles": {"title": "Vehicles", "addVehicle": "Add Vehicle", "editVehicle": "Edit Vehicle", "deleteVehicle": "Delete Vehicle", "vehicleDetails": "Vehicle Details", "plateNumber": "Plate Number", "make": "Make", "model": "Model", "year": "Year", "status": "Status", "color": "Color", "fuelType": "Fuel Type", "mileage": "Mileage", "vin": "VIN Number", "purchaseDate": "Purchase Date", "lastMaintenance": "Last Maintenance", "nextMaintenance": "Next Maintenance", "assignedDriver": "Assigned Driver", "vehicleType": "Vehicle Type", "truck": "Truck", "trailer": "Trailer", "van": "<PERSON>", "description": "Manage vehicles and truck-trailer assignments", "loading": "Loading vehicles...", "loadError": "Failed to load vehicles. Please try again.", "allVehicles": "All Vehicles", "trucks": "Trucks", "trailers": "Trailers", "assignments": "Assignments", "onHold": "On Hold", "delayed": "Delayed", "truckTrailerAssignments": "Truck-Trailer Assignments", "assignmentsDescription": "Manage truck and trailer pairings for operations", "details": "Details", "serviceHistory": "Service History", "serviceHistoryDescription": "Maintenance records and service logs for this vehicle.", "insurance": "Insurance", "reviewsInspections": "Reviews & Inspections", "driverAssignments": "Driver Assignments", "basicInformation": "Basic Information", "basicInformationDescription": "General vehicle details and specifications.", "truckSpecifications": "Truck Specifications", "trailerSpecifications": "Trailer Specifications", "truckSpecificationsDescription": "Engine, transmission and truck-specific details.", "trailerSpecificationsDescription": "Cargo capacity, dimensions and trailer-specific details.", "notAvailable": "Not Available", "notSpecified": "Not Specified", "engineType": "Engine Type", "transmission": "Transmission", "fuelCapacity": "Fuel Capacity", "axleConfiguration": "Axle Configuration", "cabConfiguration": "Cab Configuration", "trailerType": "Trailer Type", "cargoCapacity": "Cargo Capacity", "maxWeight": "Max Weight", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "dimensions": "Dimensions", "hasRefrigeration": "Has Refrigeration", "yes": "Yes", "no": "No", "diesel": "Diesel", "gasoline": "Gasoline", "electric": "Electric", "hybrid": "Hybrid", "other": "Other", "dryvan": "Dry Van", "refrigerated": "Refrigerated", "flatbed": "Flatbed", "tanker": "Tanker", "lowboy": "Lowboy", "stepdeck": "Step Deck", "containerchasis": "Container <PERSON><PERSON><PERSON>", "containerchassis": "Container <PERSON><PERSON><PERSON>", "available": "Available", "assigned": "Assigned", "maintenance": "Maintenance", "outofservice": "Out of Service"}, "categories": {"engine": "Engine", "transmission": "Transmission", "brakes": "<PERSON>rakes", "electrical": "Electrical", "tires": "Tires", "coolingSystem": "Cooling System", "fuelSystem": "Fuel System", "exhaustSystem": "Exhaust System", "suspensionAxles": "Suspension & Axles", "cargoArea": "Cargo Area", "refrigerationUnit": "Refrigeration Unit", "hydraulicSystems": "Hydraulic Systems", "lightingSystem": "Lighting System", "other": "Other"}, "table": {"actions": "Actions", "edit": "Edit", "delete": "Delete", "view": "View", "active": "Active", "inactive": "Inactive", "expired": "Expired"}, "service": {"title": "Service Records", "addService": "Add Service Record", "editService": "Edit Service", "serviceDate": "Service", "serviceType": "Service Type", "description": "Track maintenance and service history for all fleet vehicles", "cost": "Cost", "provider": "Provider", "nextServiceDate": "Next Service Date", "mileageAtService": "Mileage at Service", "maintenance": "Maintenance", "repair": "Repair", "inspection": "Inspection", "preventive": "Preventive", "scheduled": "Scheduled", "inprogress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "status": "Status", "loadError": "Failed to load service records. Please try again later.", "noRecordsFound": "No Service Records Found", "noRecordsDescription": "No service records found. Add your first service record to get started.", "updateSuccess": "Service record updated successfully", "createSuccess": "Service record created successfully", "recordDetails": "Service Record Details", "serviceInformation": "Service Information", "vehicleInformation": "Vehicle Information", "serviceDescription": "Service Description", "backToRecords": "Back to Service Records", "record": "Record", "addFirstRecord": "Add First Service Record", "selectVehicle": "Select vehicle", "descriptionPlaceholder": "Describe the service performed", "technician": "Technician", "technicianPlaceholder": "Technician name", "updateRecord": "Update Record", "createRecord": "Create Record", "confirmDelete": "Are you sure you want to delete this service record?", "deleteSuccess": "Service record deleted successfully", "deleteError": "Failed to delete service record", "vehicle": "Vehicle", "serviceDetails": "Service Details", "dates": "Dates", "plate": "Plate", "noNotes": "No additional notes", "nextService": "Next", "loadRecordError": "Failed to load service record", "saveError": "Failed to save service record", "partsCost": "Parts Cost", "laborCost": "Labor Cost", "scheduledDate": "Scheduled Date", "additionalNotes": "Additional Notes", "descriptionRequired": "Description is required", "scheduledDateRequired": "Scheduled date is required", "completedDateRequired": "Service date is required when status is completed", "serviceDateHelp": "Leave empty for scheduled maintenance, fill when service is completed"}, "insurance": {"title": "Insurance Policies", "addInsurance": "Add Insurance", "editInsurance": "Edit Insurance", "policyNumber": "Policy Number", "provider": "Provider", "startDate": "Start Date", "endDate": "End Date", "premium": "Premium", "coverage": "Coverage", "deductible": "Deductible", "status": "Status", "selectVehicle": "Select Vehicle", "description": "Manage vehicle insurance policies and renewals", "addPolicy": "Add Insurance Policy", "attentionRequired": "Insurance Attention Required", "policies": "policies", "policy": "policy", "expiredOr": "expired or", "expiringSoon": "expiring soon", "loading": "Loading insurance policies...", "noPoliciesFound": "No Insurance Policies Found", "noPoliciesDescription": "No insurance policies found. Add your first insurance policy to get started.", "addFirstPolicy": "Add First Policy", "policyType": "Policy Type", "annualPremium": "Annual Premium", "premiumPlaceholder": "0.00", "coverageDetails": "Coverage Details", "coveragePlaceholder": "Describe coverage details", "updatePolicy": "Update Policy", "createPolicy": "Create Policy", "updating": "Updating...", "creating": "Creating...", "comprehensive": "Comprehensive", "liability": "Liability", "thirdParty": "Third Party", "fireTheft": "Fire & Theft", "renewaldue": "Renewal Due", "active": "Active", "expired": "Expired", "cancelled": "Cancelled", "policyNumberPlaceholder": "Enter policy number", "providerPlaceholder": "Enter insurance provider", "confirmDelete": "Are you sure you want to delete this insurance policy?", "expiresInDays": "Expires in {{days}} days", "coveragePeriod": "Coverage Period"}, "reviews": {"title": "Vehicle Reviews & Inspections", "addReview": "Add Review", "editReview": "Edit Review", "reviewDate": "Review Date", "reviewType": "Review Type", "result": "Result", "expiryDate": "Expiry Date", "inspector": "Inspector", "passed": "Passed", "failed": "Failed", "pending": "Pending", "description": "Schedule and track vehicle reviews and inspections", "scheduleReview": "Schedule Review", "loading": "Loading vehicle reviews...", "noReviewsFound": "No Reviews Found", "noReviewsDescription": "No vehicle reviews found. Schedule your first review to get started.", "scheduleFirstReview": "Schedule First Review", "vehicleReviews": "Vehicle Reviews", "overdue": "Overdue", "upcoming": "Upcoming", "location": "Location", "locationPlaceholder": "Inspection location", "findings": "Findings", "findingsPlaceholder": "Inspection findings and observations", "recommendations": "Recommendations", "recommendationsPlaceholder": "Recommendations for future maintenance", "confirmDeleteReview": "Are you sure you want to delete this review?", "reviewDeletedSuccess": "Review deleted successfully", "reviewUpdatedSuccess": "Review updated successfully", "reviewScheduledSuccess": "Review scheduled successfully", "failedToDeleteReview": "Failed to delete review", "failedToSaveReview": "Failed to save review", "updating": "Updating...", "scheduling": "Scheduling...", "updateReview": "Update Review", "annualSafetyInspection": "Annual Safety Inspection", "quarterlyMaintenanceReview": "Quarterly Maintenance Review", "dotInspection": "DOT Inspection", "emissionsTest": "Emissions Test", "preTripInspection": "Pre-Trip Inspection", "scheduled": "Scheduled", "completed": "Completed", "nextReviewDate": "Next Review Date", "completedDate": "Completed Date", "scheduledDate": "Scheduled Date"}, "assignments": {"title": "Driver Assignments", "description": "Manage driver assignments for this vehicle.", "loading": "Loading vehicle assignments...", "noAssignmentsFound": "No Assignments Found", "noAssignmentsDescription": "No vehicle assignments found. Create your first assignment to get started.", "createFirstAssignment": "Create First Assignment", "createAssignment": "Create Assignment", "driver": "Driver", "assignmentPeriod": "Assignment Period", "type": "Type", "priority": "Priority", "status": "Status", "start": "Start", "end": "End", "expired": "Expired", "expiringSoon": "Expiring Soon", "complete": "Complete", "cancel": "Cancel", "regular": "Regular", "temporary": "Temporary", "emergency": "Emergency", "maintenance": "Maintenance", "training": "Training", "low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>", "active": "Active", "completed": "Completed", "cancelled": "Cancelled", "selectDriver": "Select Driver", "loadingDrivers": "Loading drivers...", "assignmentType": "Assignment Type", "startDate": "Start Date", "endDate": "End Date", "notes": "Notes", "notesPlaceholder": "Additional notes about this assignment", "confirmComplete": "Are you sure you want to complete this assignment?", "confirmCancel": "Are you sure you want to cancel this assignment?", "assignmentCompletedSuccess": "Assignment completed successfully", "assignmentCancelledSuccess": "Assignment cancelled successfully", "assignmentCreatedSuccess": "Assignment created successfully", "failedToCompleteAssignment": "Failed to complete assignment", "failedToCancelAssignment": "Failed to cancel assignment", "failedToSaveAssignment": "Failed to save assignment", "updateNotSupported": "Assignment updates are not currently supported", "updating": "Updating...", "creating": "Creating...", "updateAssignment": "Update Assignment", "editAssignment": "Edit Assignment"}, "tabs": {"vehicles": "Vehicles", "service": "Service", "insurance": "Insurance", "reviews": "Reviews"}, "dashboard": {"totalVehicles": "Total Vehicles", "availableVehicles": "Available Vehicles", "inMaintenance": "In Maintenance", "upcomingMaintenance": "Upcoming Maintenance", "vsLastMonth": "vs last month", "readyForAssignment": "Ready for assignment", "currentlyServiced": "Currently serviced", "dueThisMonth": "Due this month", "noDataAvailable": "No dashboard data available", "inProgress": "In Progress", "scheduled": "Scheduled", "vehicleStatusDistribution": "Vehicle Status Distribution", "currentStatusDescription": "Current status of all fleet vehicles", "fleetComposition": "Fleet Composition", "breakdownByType": "Breakdown by vehicle type", "maintenanceActivity": "Maintenance Activity", "monthlyMaintenanceDescription": "Monthly maintenance completed vs scheduled", "recentActivity": "Recent Fleet Activity", "latestUpdates": "Latest updates and activities across your fleet"}}