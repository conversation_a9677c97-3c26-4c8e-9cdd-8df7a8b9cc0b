{"title": "Business Partners", "addPartner": "Add Partner", "editPartner": "Edit Partner", "deletePartner": "Delete Partner", "partnerDetails": "Partner Details", "createPartner": "Create Partner", "searchPartners": "Search partners...", "noPartnersFound": "No partners found", "loadingPartners": "Loading partners...", "types": {"shipper": "Shipper", "logisticsPartner": "Logistics Partner", "consignee": "Consignee", "supplier": "Supplier", "customer": "Customer", "broker": "Broker", "carrier": "Carrier"}, "fields": {"name": "Name", "type": "Type", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "postalCode": "Postal Code", "country": "Country", "taxId": "Tax ID", "website": "Website", "notes": "Notes", "status": "Status", "contractNumber": "Contract Number", "contractExpiry": "Contract Expiry", "paymentTerms": "Payment Terms", "creditLimit": "Credit Limit"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "suspended": "Suspended", "blacklisted": "Blacklisted"}, "actions": {"viewDetails": "View Details", "editPartner": "Edit Partner", "deletePartner": "Delete Partner", "addLocation": "Add Location", "viewLocations": "View Locations", "sendEmail": "Send Email", "downloadContract": "Download Contract", "viewTransactions": "View Transactions"}, "locations": {"title": "Locations", "addLocation": "Add Location", "editLocation": "Edit Location", "deleteLocation": "Delete Location", "noLocationsFound": "No locations found", "pickupLocations": "Pickup Locations", "deliveryLocations": "Delivery Locations", "fields": {"name": "Location Name", "address": "Address", "city": "City", "postalCode": "Postal Code", "country": "Country", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "workingHours": "Working Hours", "specialInstructions": "Special Instructions", "isPickup": "Pickup Location", "isDelivery": "Delivery Location", "isActive": "Active"}}, "sections": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "addressInfo": "Address Information", "businessInfo": "Business Information", "contractInfo": "Contract Information", "additionalInfo": "Additional Information"}, "validation": {"nameRequired": "Name is required", "typeRequired": "Type is required", "emailInvalid": "Invalid email format", "phoneRequired": "Phone is required", "addressRequired": "Address is required", "cityRequired": "City is required", "postalCodeRequired": "Postal code is required", "taxIdInvalid": "Invalid tax ID", "websiteInvalid": "Invalid website URL"}, "messages": {"partnerCreated": "Partner created successfully", "partnerUpdated": "Partner updated successfully", "partnerDeleted": "Partner deleted successfully", "locationCreated": "Location created successfully", "locationUpdated": "Location updated successfully", "locationDeleted": "Location deleted successfully", "confirmDelete": "Are you sure you want to delete this partner?", "confirmDeleteLocation": "Are you sure you want to delete this location?", "unsavedChanges": "You have unsaved changes. Do you want to continue?", "contractExpiringSoon": "Contract expiring soon", "contractExpired": "Contract expired"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "byType": "By Type", "contractExpiring": "Contract Expiring", "recentlyAdded": "Recently Added"}, "stats": {"totalPartners": "Total Partners", "activePartners": "Active Partners", "shippers": "Shippers", "logisticsPartners": "Logistics Partners", "contractsExpiring": "Contracts Expiring", "totalLocations": "Total Locations"}}