{"title": "Zarządzan<PERSON>", "addTrip": "<PERSON><PERSON><PERSON>", "editTrip": "<PERSON><PERSON><PERSON><PERSON>", "deleteTrip": "<PERSON><PERSON><PERSON>", "tripDetails": "Szczegóły Trasy", "createTrip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchTrips": "S<PERSON>j tras...", "noTripsFound": "Nie znaleziono tras", "loadingTrips": "Ładowanie tras...", "fields": {"tripNumber": "<PERSON><PERSON><PERSON>", "status": "Status", "type": "<PERSON><PERSON>", "priority": "Priorytet", "driver": "Kierowca", "vehicle": "Pojazd", "truck": "Ciężarówka", "trailer": "Naczepa", "pickupLocation": "Miejsce Odbioru", "deliveryLocation": "<PERSON><PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON>", "endTime": "<PERSON>zas Zak<PERSON>ńcz<PERSON>", "estimatedDuration": "Szacowany Czas", "actualDuration": "Rzeczywisty Czas", "distance": "<PERSON><PERSON><PERSON><PERSON>", "estimatedDistance": "Szacowany Dystans", "notes": "<PERSON><PERSON><PERSON>", "cargo": "Ładunek", "cargoWeight": "Waga Ładunk<PERSON>", "cargoValue": "<PERSON><PERSON><PERSON><PERSON>", "cargoDescription": "Op<PERSON>", "purpose": "<PERSON>l", "route": "<PERSON><PERSON><PERSON>", "stops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrived": "Przyjazd", "vehicleCapacity": "Pojemność pojazdu", "specialRequirements": "Wymagania specjalne", "pickup": "Odbiór", "delivery": "Dostawa", "stopLocation": "Lokalizacja przystanku {{number}}"}, "status": {"planned": "Zaplanowana", "assigned": "Przypisana", "inProgress": "W Trakcie", "completed": "Zakończona", "cancelled": "Anulowana", "delayed": "Opóźniona", "scheduled": "Zaplanowana", "pending": "Oczekująca"}, "types": {"standard": "Standardowa", "express": "Ekspresowa", "pickup": "Odbiór", "delivery": "Dostawa", "transfer": "Transfer", "maintenance": "Ko<PERSON>r<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "placeholders": {"selectDriver": "<PERSON><PERSON><PERSON><PERSON>", "selectVehicle": "Wybierz pojazd", "enterStartLocation": "Wprowadź miejsce rozpoczęcia", "enterEndLocation": "Wprowadź miejsce zakończenia", "enterCargo": "Wprowadź opis ładunku", "enterNotes": "Wprowadź dodatkowe uwagi", "selectTruck": "Wybierz ciężarówkę", "selectTrailer": "<PERSON><PERSON><PERSON><PERSON> (opcjonalnie)", "selectTripType": "W<PERSON>bierz typ trasy, k<PERSON><PERSON><PERSON><PERSON>u<PERSON>", "enterDistance": "Wprowadź dystans w kilometrach", "estimatedDuration": "np. 2 godziny 30 minut", "additionalNotes": "Dodatkowe uwagi", "cargoDescription": "Opisz przewożony ładunek", "cargoWeight": "Waga w kilogramach", "selectType": "W<PERSON>bierz typ", "selectPriority": "Wybierz priorytet", "purpose": "<PERSON>l tej trasy", "enterStopLocation": "Wprowadź lokalizację przystanku", "stopPurpose": "Cel tego przystanku"}, "typeDescriptions": {"delivery": "Dostawa towarów do klientów lub miejsc docelowych", "transfer": "Przeniesienie towarów między lokalizacjami firmy", "maintenance": "Po<PERSON><PERSON>ż serwisowa lub konserwacyjna pojazdu", "other": "Inne rodzaje podróży nie wymienione powyżej"}, "priority": {"low": "<PERSON><PERSON>", "normal": "Normalna", "high": "Wysoka", "urgent": "Pilna"}, "actions": {"viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "editTrip": "<PERSON><PERSON><PERSON><PERSON>", "deleteTrip": "Usuń", "assignDriver": "Przypisz Kierow<PERSON>ę", "assignVehicle": "Przypisz Pojazd", "startTrip": "Roz<PERSON>cz<PERSON><PERSON>", "completeTrip": "Zakończ Trasę", "cancelTrip": "<PERSON><PERSON><PERSON>", "duplicateTrip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createTrip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateTrip": "<PERSON>kt<PERSON><PERSON><PERSON><PERSON>", "calculate": "<PERSON><PERSON><PERSON><PERSON>", "calculating": "Obliczanie...", "creating": "Tworzenie...", "updating": "Aktualizowanie...", "reviewingConflicts": "Sprawdzanie konfliktów...", "newTrip": "Nowa Trasa", "addStop": "<PERSON><PERSON><PERSON>"}, "sections": {"basicInfo": "Podstawowe Informacje", "routeSchedule": "Trasa i Harmonogram", "vehicleAssignment": "Przypisanie Pojazdu", "driverAssignment": "Przypisanie Kierowcy", "cargoInformation": "Informacje o Ładunku", "additionalNotes": "Dodatkowe Uwagi", "tripDetails": "Szczegóły Trasy", "routeDetailsAndCargo": "Trasa, Szczegóły i Informacje o Ładunku", "cargoInformationDescription": "Określ szczegóły ładunku, wagę i wymagania specjalne"}, "validation": {"tripNumberRequired": "Numer trasy jest wymagany", "driverRequired": "<PERSON><PERSON><PERSON><PERSON> jest wymagany", "vehicleRequired": "<PERSON><PERSON><PERSON><PERSON> jest wymagany", "pickupLocationRequired": "Mi<PERSON><PERSON><PERSON> odbioru jest wymagane", "deliveryLocationRequired": "<PERSON><PERSON><PERSON><PERSON> dostawy jest wymagane", "startTimeRequired": "<PERSON><PERSON> rozpoczęcia jest wymagany", "endTimeAfterStart": "<PERSON>zas zakończenia musi być po czasie rozpoczęcia", "invalidDistance": "Nieprawidłowy dystans", "invalidWeight": "Nieprawidłowa waga", "fillRequiredFields": "<PERSON><PERSON><PERSON> wypełnić wszystkie wymagane pola: {{fields}}"}, "messages": {"tripCreated": "Trasa została utworzona pomyślnie", "tripUpdated": "Trasa została zaktualizowana pomyślnie", "tripDeleted": "Trasa została usunięta pomyślnie", "tripAssigned": "Trasa została przypisana pomyślnie", "tripStarted": "Trasa została rozpoczęta", "tripCompleted": "Trasa została zakończona", "tripCancelled": "Trasa została anulowana", "confirmDelete": "<PERSON>zy na pewno chcesz usunąć tę trasę?", "confirmCancel": "<PERSON>zy na pewno chcesz anulować tę trasę?", "unsavedChanges": "<PERSON>sz niezapisane zmiany. <PERSON><PERSON>z k<PERSON>ynuować?", "loadDriversError": "Nie udało się załadować kierowców", "loadVehiclesError": "Nie udało się załadować pojazdów", "failedToLoadTrips": "<PERSON>e udało się załadować tras", "loadingTrips": "Ładowanie tras...", "statusUpdatedSuccess": "Status trasy został zaktualizowany pomyślnie", "tripDeletedSuccess": "Trasa została usunięta pomyślnie", "failedToDeleteTrip": "Nie udało się usunąć trasy. Spróbuj ponownie.", "confirmDeleteTrip": "<PERSON>zy na pewno chcesz usunąć tę trasę?", "failedToLoadVehicles": "Nie udało się załadować ciężarówek i naczep", "trailerAutoAssigned": "Naczepa przypisana automatycznie", "trailerAutoAssignedDescription": "Naczepa {{plateNumber}} została automatycznie przypisana do tej ciężarówki.", "distanceCalculated": "Dystans ob<PERSON>", "distanceCalculatedDescription": "Dystans: {{distance}} km, Szacowany czas: {{duration}}", "distanceCalculationFailed": "Obliczanie dystansu nie powiodło się", "unableToCalculateDistance": "Nie można automatycznie obliczyć dystansu", "failedToCalculateDistance": "<PERSON><PERSON> udało się obliczyć dystansu", "failedToCreateTrip": "Nie udało się utworzyć trasy", "failedToUpdateTrip": "Nie udało się zaktualizować trasy", "hasAssignedTrailer": "Ma przypisaną naczepę: {{plateNumber}}", "loadingTrucks": "Ładowanie ciężarówek...", "noTrucksAvailable": "Brak dostępnych ciężarówek", "loadingTrailers": "Ładowanie naczep...", "noTrailersAvailable": "Brak dostępnych naczep", "checkVehicleCapacity": "Sprawdź pojemność wybranego pojazdu", "selectVehicleToSeeCapacity": "<PERSON><PERSON><PERSON><PERSON> pojaz<PERSON>, a<PERSON> z<PERSON><PERSON><PERSON><PERSON> poje<PERSON>ć", "distanceAutoCalculated": "Dystans zostanie obliczony automatycznie po wybraniu obu lokalizacji"}, "descriptions": {"editTripDescription": "Zaktualizuj szczegóły trasy i przypisania", "createTripDescription": "Zaplanuj i zarezerwuj nową trasę z przypisaniami pojazdu i kierowcy", "createTripFormDescription": "Wypeł<PERSON><PERSON> formularz, aby utwo<PERSON><PERSON><PERSON> nową trasę ze szczegółami takimi jak typ, priorytet, kierowca, pojazd, harmonogram i przystanki."}, "filters": {"all": "Wszystkie", "today": "<PERSON><PERSON><PERSON><PERSON>", "thisWeek": "Ten Tydzień", "thisMonth": "<PERSON>", "byStatus": "Według Statusu", "byDriver": "Według Kierowcy", "byVehicle": "Według Pojazdu", "dateRange": "<PERSON><PERSON><PERSON>"}, "allTrips": "Wszystkie Trasy", "allTripsDescription": "Zarządzaj i śledź wszystkie trasy w swojej flocie", "scheduleNewTrip": "Zaplanuj <PERSON>", "backToTrips": "Powrót do Tras", "dashboard": {"overview": "Przegląd zarządzania trasami", "overviewDescription": "Kluczowe metryki i spostrzeżenia dotyczące operacji floty", "loadingDashboard": "Ładowanie <PERSON>u...", "noDashboardData": "Brak danych na pulpicie", "totalTrips": "Łączna liczba tras", "activeTrips": "Aktywne trasy", "completedTrips": "Ukończone trasy", "totalDistance": "Łączny dystans", "vsLastMonth": "w porównaniu z poprzednim miesiącem", "currentlyInProgress": "Obecnie w trakcie", "successfullyFinished": "Pomyślnie zakończone", "avgDistance": "Śr.: {{distance}} km/trasa", "tripStatusDistribution": "Rozkład statusu tras", "tripStatusDescription": "Podział tras według aktualnego statusu", "monthlyActivity": "Miesięczna aktywność", "monthlyActivityDescription": "Trasy i dystans w czasie", "trips": "<PERSON><PERSON>y", "distanceKm": "Dystans (km)", "recentTrips": "Ostatnie trasy", "recentTripsDescription": "Najnowsze aktywności tras i aktualizacje statusu"}, "specialRequirements": {"temperatureControl": "Kontrola temperatury", "refrigeratedTransport": "Transport chłodniczy", "fragileHandling": "Obsługa delikatnych przedmiotów", "requiresSpecialCare": "Wymaga szczególnej ostrożności", "hazardousMaterials": "Materiały niebezpieczne", "specialPermitsRequired": "Wymagane specjalne pozwolenia", "autoCheckDescription": "Wymagania specjalne będą automatycznie sprawdzane na podstawie możliwości wybranego pojazdu"}, "warnings": {"noDriversAvailable": "Brak dostępnych kierowców. Proszę dodać kierowców przed utworzeniem trasy.", "noVehiclesAvailable": "Brak dostępnych pojazdów. Proszę dodać pojazdy przed utworzeniem trasy."}, "units": {"hours": "god<PERSON>y"}, "reports": {"title": "<PERSON><PERSON><PERSON>", "description": "Generuj i analizuj raporty dotyczące tras i wykorzystania floty", "exportReport": "Eksportuj Raport", "generateReports": "<PERSON><PERSON><PERSON>", "selectParameters": "Skonfiguruj parametry raportu i generuj analizy", "reportType": "<PERSON><PERSON>", "selectReportType": "<PERSON><PERSON><PERSON><PERSON> typ raportu", "startDate": "<PERSON>", "endDate": "Data Zakończenia", "generating": "Generowanie...", "generateReport": "<PERSON><PERSON><PERSON>", "noReportGenerated": "<PERSON><PERSON>", "selectParametersToGenerate": "<PERSON><PERSON><PERSON>rz parametry raportu powyżej i kliknij \"Generuj Raport\", aby zobaczyć wyniki", "types": {"daily": "<PERSON><PERSON><PERSON>", "weekly": "Podsumowanie Tygodniowe", "monthly": "<PERSON><PERSON><PERSON>", "driver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vehicle": "Wykorzystanie Pojazdu"}}, "businessPartners": {"businessPartner": "Partner Biznesowy", "location": "Lokalizacja", "default": "Domyślna", "operatingHours": "<PERSON><PERSON><PERSON> pracy", "specialInstructions": "Instrukcje specjalne", "types": {"shipper": "Nadawca", "logistics": "Logistyka"}, "fields": {"locationName": "Nazwa <PERSON>ji", "contactPerson": "Osoba k<PERSON>aktowa", "city": "<PERSON><PERSON>", "state": "Województwo", "postalCode": "<PERSON><PERSON>"}, "placeholders": {"chooseBusinessPartner": "<PERSON><PERSON><PERSON>rz partnera <PERSON>ego", "chooseLocation": "<PERSON><PERSON><PERSON><PERSON>", "locationName": "np. Gł<PERSON>ny magazyn, Rampa załadunkowa A", "contactName": "Nazwa kontak<PERSON>", "streetAddress": "<PERSON><PERSON> ul<PERSON>", "city": "<PERSON><PERSON>", "state": "Województwo", "postalCode": "<PERSON><PERSON>", "phoneNumber": "Numer telefonu", "operatingHours": "np. Pon-Pt 8:00-17:00", "specialInstructions": "Wszelkie specjalne instrukcje dostawy/odbioru"}, "actions": {"addNewLocation": "Dodaj nową lokalizację", "adding": "Dodawanie..."}, "messages": {"failedToLoadPartners": "Nie udało się załadować partnerów biznesowych", "failedToLoadLocations": "Nie udało się załadować lokalizacji", "loadingLocations": "Ładowanie lokalizacji...", "locationAddedAndSelected": "Lokalizacja dodana do partnera i wybrana dla trasy", "locationSet": "Lokalizacja ustawiona", "locationSetForTripOnly": "Lokalizacja ustawiona tylko dla tej trasy", "failedToAddLocation": "<PERSON>e udało się dodać lokalizacji", "locationWillBeSaved": "Ta lokalizacja zostanie zapisana i będzie dostępna dla przyszłych tras z tym partnerem.", "locationOnlyForThisTrip": "Ta lokalizacja będzie używana tylko dla tej trasy i nie zostanie zapisana na stałe."}, "validation": {"fillRequiredFields": "<PERSON><PERSON>ę wypełnić wymagane pola: <PERSON><PERSON><PERSON>, Adres i Miasto"}}}