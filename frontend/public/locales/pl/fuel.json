{"title": "Zarządzanie Paliwem", "addRecord": "<PERSON><PERSON><PERSON>", "editRecord": "<PERSON><PERSON><PERSON><PERSON>", "deleteRecord": "<PERSON><PERSON><PERSON>kord", "fuelRecord": "Re<PERSON><PERSON> Paliwa", "createRecord": "Utw<PERSON>rz <PERSON>", "searchRecords": "Szukaj rekordów...", "noRecordsFound": "Nie znaleziono rekordów", "loadingRecords": "Ładowanie rekordów...", "tabs": {"records": "Rekordy", "prices": "<PERSON><PERSON>", "analytics": "Analityka", "reports": "<PERSON><PERSON><PERSON>"}, "fields": {"date": "Data", "vehicle": "Pojazd", "driver": "Kierowca", "fuelType": "Ty<PERSON>", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "pricePerLiter": "Cena za Litr", "totalCost": "Koszt Całkowity", "odometer": "Licznik", "location": "Lokalizacja", "station": "<PERSON><PERSON><PERSON>", "receiptNumber": "<PERSON><PERSON>r <PERSON>", "notes": "<PERSON><PERSON><PERSON>", "efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consumption": "Zużycie"}, "fuelTypes": {"diesel": "Diesel", "petrol": "Benzyna", "lpg": "LPG", "cng": "CNG", "electric": "Elektryczny"}, "actions": {"viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "editRecord": "<PERSON><PERSON><PERSON><PERSON>", "deleteRecord": "<PERSON><PERSON><PERSON>kord", "uploadReceipt": "Prześlij <PERSON>gon", "downloadReport": "<PERSON><PERSON><PERSON>", "exportData": "Eksportuj <PERSON>", "refreshPrices": "Odś<PERSON><PERSON>ż Ceny"}, "sections": {"basicInfo": "Podstawowe Informacje", "fuelDetails": "Szczegóły Paliwa", "vehicleInfo": "Informacje o Pojeździe", "costBreakdown": "Podział <PERSON>", "receipt": "Paragon"}, "validation": {"dateRequired": "Data jest wymagana", "vehicleRequired": "<PERSON><PERSON><PERSON><PERSON> jest wymagany", "driverRequired": "<PERSON><PERSON><PERSON><PERSON> jest wymagany", "quantityRequired": "<PERSON><PERSON><PERSON><PERSON> jest wymagana", "quantityPositive": "<PERSON><PERSON><PERSON>ć musi być większa od zera", "quantityMin": "Ilość musi wynosić co najmniej 0,1 litra", "quantityMax": "Ilość nie może przekraczać 1000 litrów", "priceRequired": "<PERSON><PERSON> jest wymagana", "pricePositive": "Cena musi być większa od zera", "totalCostMin": "Koszt całkowity musi wynosić co najmniej 0,01", "totalCostMax": "Koszt całkowity nie może przekraczać 10000", "locationRequired": "Lokalizacja jest wymagana", "fuelingDateRequired": "Data tankowania jest wymagana", "odometerRequired": "Odczyt licznika jest wymagany", "odometerPositive": "Odczyt licznika musi być większy od zera", "odometerMax": "Odczyt licznika wydaje się nierealistycznie wysoki", "enteredByRequired": "Pole 'Wprowadzone przez' jest wymagane", "invalidDate": "Nieprawidłowa data", "futureDate": "Data nie może być w przyszłości", "invalidFileType": "Nieprawidłowy typ pliku", "fileTypeDescription": "Proszę przes<PERSON>ć plik JPEG, PNG lub PDF.", "fileTooLarge": "Plik zbyt duży", "fileSizeDescription": "Proszę przesłać plik mniejszy niż 5MB."}, "messages": {"recordCreated": "Rekord paliwa został utworzony pomyślnie", "recordUpdated": "Rekord paliwa został zaktualizowany pomyślnie", "recordDeleted": "Rekord paliwa został usunięty pomyślnie", "pricesUpdated": "Ceny paliwa zostały zaktualizowane", "confirmDelete": "<PERSON>zy na pewno chcesz usunąć ten rekord?", "unsavedChanges": "<PERSON>sz niezapisane zmiany. <PERSON><PERSON>z k<PERSON>ynuować?", "receiptUploaded": "Paragon został przesłany pomyślnie", "reportGenerated": "Raport został wygenerowany pomyślnie", "createSuccess": "Rekord paliwa został utworzony pomyślnie.", "updateSuccess": "Rekord paliwa został zaktualizowany pomyślnie.", "createError": "Nie udało się utworzyć rekordu paliwa.", "updateError": "Nie udało się zaktualizować rekordu paliwa."}, "form": {"title": "Re<PERSON><PERSON> Paliwa", "editTitle": "<PERSON><PERSON><PERSON><PERSON>", "createTitle": "Utwórz Rekord Paliwa", "description": "Wprowadź szczegóły tankowania", "editDescription": "Zaktualizuj szczegóły tankowania", "vehicleAndDriver": "Pojazd i Kierowca", "fuelDetails": "Szczegóły Paliwa", "additionalInfo": "Dodatkowe Informacje", "selectVehicle": "Wybierz pojazd", "selectDriver": "<PERSON><PERSON><PERSON><PERSON>", "fuelingDate": "Data Tankowania", "quantityLiters": "<PERSON><PERSON><PERSON><PERSON> (litry)", "totalCostPLN": "Koszt Całkowity (PLN)", "pricePerLiter": "<PERSON>na za litr", "calculatedAutomatically": "Obliczane automatycznie", "odometerReading": "Odczyt Licznika", "receiptNumber": "<PERSON><PERSON>r <PERSON>", "receiptNumberOptional": "opcjonalny", "uploadReceipt": "Prześlij <PERSON>gon", "chooseFile": "<PERSON><PERSON><PERSON>rz plik", "noFileChosen": "<PERSON><PERSON> wybrano pliku", "supportedFormats": "Obsługiwane formaty: JPEG, PNG, PDF (maks. 5MB)", "enteredBy": "Wprowadzone przez", "currentUser": "Aktualny użytkownik", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Zapisywanie...", "update": "Aktualizuj", "updating": "Aktualizowanie..."}, "filters": {"all": "Wszystkie", "today": "<PERSON><PERSON><PERSON><PERSON>", "thisWeek": "Ten Tydzień", "thisMonth": "<PERSON>", "lastMonth": "Ostatni <PERSON>", "byVehicle": "Według Pojazdu", "byDriver": "Według Kierowcy", "byFuelType": "Według Typu Paliwa", "dateRange": "<PERSON><PERSON><PERSON>", "allVehicles": "Wszystkie Pojazdy", "allDrivers": "Wszyscy Kierowcy"}, "stats": {"totalRecords": "Łączna Liczba Rekordów", "totalCost": "Koszt Całkowity", "totalQuantity": "Łączna Iloś<PERSON>", "averagePrice": "Średnia Cena", "averageConsumption": "Średnie Zużycie", "mostEfficientVehicle": "Najbardziej Efektywny Pojazd", "monthlySpending": "Miesięczne Wydatki", "fuelEfficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fleetEfficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lot<PERSON>", "totalVolume": "Łączna Objętość", "activeFleet": "Aktywna Flota"}, "dashboard": {"loadingError": "<PERSON>e udało się załadować danych pulpitu.", "averageConsumptionDesc": "Średnie zużycie", "thisMonth": "<PERSON> mi<PERSON>", "activeDrivers": "{{count}} aktywny kierowca", "activeDrivers_plural": "{{count}} aktywnych kierowców", "monthlyBudget": "Miesięczny Budżet", "budgetDescription": "Wydatki na paliwo vs. przydzielony budżet na ten miesiąc", "spent": "Wydano: {{amount}}", "budget": "Budżet: {{amount}}", "used": "{{percentage}}% wykorzystane", "remaining": "{{amount}} pozostało", "mostEfficientVehicles": "Najbardziej Efektywne Pojazdy", "mostEfficientVehiclesDesc": "Pojazdy o najlepszej efektywności paliwowej w tym mi<PERSON>cu", "mostEfficientDrivers": "Najbardziej Efektywni Kierowcy", "mostEfficientDriversDesc": "Kierowcy o najlepszej efektywności paliwowej w tym mi<PERSON>cu", "spentAmount": "{{amount}} wyd<PERSON>"}, "charts": {"priceHistory": "Historia Cen", "consumptionTrend": "Trend Zużycia", "costByVehicle": "Koszt według Pojazdu", "efficiencyComparison": "Porównanie Efektywności", "monthlySpending": "Miesięczne Wydatki"}, "units": {"liters": "litry", "kilometers": "kilometry", "kmPerLiter": "km/l", "litersPer100km": "l/100km", "pln": "PLN", "plnPerLiter": "PLN/l"}, "recordsDescription": "Zarządzaj i przeglądaj wszystkie rekordy zużycia paliwa", "pagination": {"showing": "Pokazuje", "to": "do", "of": "z", "records": "rekordów"}, "recordsMessages": {"loadError": "Nie udało się załadować rekordów paliwa", "deleteError": "<PERSON>e udało się usunąć rekordu paliwa"}, "prices": {"currentPriceGross": "<PERSON><PERSON><PERSON><PERSON> (Brutto)", "currentPriceNet": "<PERSON><PERSON><PERSON><PERSON> (Netto)", "priceTrend": "Trend Cen", "scrapingStatus": "Status Pobierania", "netPrice": "Net<PERSON>", "vatIncluded": "+23% VAT", "active": "Aktywny", "issues": "<PERSON><PERSON>", "lastUpdate": "Ostatnia aktualizacja", "noData": "<PERSON><PERSON> da<PERSON>", "vsYesterday": "vs wczoraj", "priceScrapingIssues": "Problemy z Pobieraniem Cen", "scrapingIssuesDesc": "Pobieranie cen paliwa napotyka problemy.", "retryAttempt": "Próba p<PERSON>", "priceHistory30Days": "Historia Cen (30 Dni)", "orlenPricesDesc": "Ceny oleju napędowego Orlen.pl z 23% VAT", "manualFetch": "<PERSON><PERSON><PERSON>", "fetching": "Pobieranie...", "loadingPriceHistory": "Ładowanie historii cen...", "noPriceHistoryData": "Brak danych historii cen", "currentPriceDetails": "Szczegóły Aktualnej Ceny", "latestFuelPriceInfo": "Najnowsze informacje o cenach paliwa z Orlen.pl", "effectiveDate": "Data Obowiązywania", "netPriceLabel": "<PERSON><PERSON>", "vatLabel": "VAT (23%)", "grossPriceLabel": "<PERSON><PERSON>", "source": "Źródło", "lastScraped": "Ostatnie Pobieranie", "vatRate": "Stawka VAT", "noCurrentPriceData": "Brak danych o aktualnej cenie.", "date": "Data", "grossPrice": "<PERSON><PERSON>", "messages": {"fetchSuccess": "Cena paliwa została pobrana pomyślnie.", "fetchError": "<PERSON>e udało się ręcznie pobrać ceny paliwa."}}, "analytics": {"loadingAnalytics": "Ładowanie analityki...", "noAnalyticsData": "<PERSON><PERSON> danych analitycznych", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeRanges": {"last7Days": "Ostatnie 7 dni", "last30Days": "Ostatnie 30 dni", "last3Months": "Ostatnie 3 miesiące", "lastYear": "Ostatni rok"}, "metrics": {"totalFuelCost": "Całkowity Koszt Paliwa", "totalVolume": "Całkowita Ob<PERSON>ść", "avgEfficiency": "Średnia Efektywność", "totalRecords": "Łączna Liczba Rekordów", "vsPreviousPeriod": "vs poprzedni okres", "fleetAverage": "Średnia floty", "fuelRecords": "Rekordy paliwa"}, "charts": {"monthlyFuelConsumption": "Miesięczne Zużycie Paliwa", "costAndVolumeTrends": "Trendy kosztów i objętości w czasie", "costBreakdownByVehicleType": "Podział Kosztów według Typu Pojazdu", "distributionOfFuelCosts": "Rozkład kosztów paliwa"}, "topPerformers": {"topVehiclesByCost": "Najdroższe Pojazdy", "highestFuelConsumptionVehicles": "Pojazdy o najwyższym zużyciu paliwa", "topDriversByEfficiency": "Najbardziej Efektywni Kierowcy", "mostFuelEfficientDrivers": "Najbardziej oszczędni kierowcy"}, "vehicleTypes": {"trucks": "Ciężarówki", "deliveryVehicles": "Pojazdy <PERSON>e", "serviceVehicles": "<PERSON><PERSON><PERSON><PERSON>"}, "months": {"jan": "Sty", "feb": "Lut", "mar": "Mar", "apr": "K<PERSON>", "may": "Maj", "jun": "<PERSON><PERSON>", "jul": "Lip", "aug": "<PERSON><PERSON>", "sep": "Wrz", "oct": "<PERSON><PERSON>", "nov": "<PERSON><PERSON>", "dec": "Gru"}, "chartLabels": {"cost": "Koszt", "volume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON><PERSON>", "liters": "Litry", "pln": "PLN", "efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l100km": "L/100km"}}}