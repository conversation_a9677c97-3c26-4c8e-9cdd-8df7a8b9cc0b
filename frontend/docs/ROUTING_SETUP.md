# Routing Distance Calculation Setup

This guide explains how to set up routed distance calculation instead of direct (straight-line) distances.

## Current Status

- ✅ **Direct Distance**: Currently using Haversine formula (straight-line distance)
- 🔄 **Routed Distance**: Ready to implement with API keys

## API Options

### 1. OpenRouteService (Recommended)

**Pros:**
- ✅ Free tier: 2,000 requests/day
- ✅ Truck-specific routing (HGV profile)
- ✅ Avoid tolls, weight restrictions
- ✅ Perfect for fleet management

**Cons:**
- ❌ Limited free requests
- ❌ Requires registration

**Setup:**
1. Go to [OpenRouteService](https://openrouteservice.org/dev/#/signup)
2. Create free account
3. Get API key
4. Add to `.env.local`: `NEXT_PUBLIC_OPENROUTESERVICE_API_KEY=your_key_here`

### 2. Google Maps Directions API

**Pros:**
- ✅ Most accurate routing
- ✅ Real-time traffic data
- ✅ Comprehensive road database
- ✅ Reliable service

**Cons:**
- ❌ Paid: $5 per 1,000 requests
- ❌ Requires billing account

**Setup:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create project and enable Directions API
3. Create API key
4. Add to `.env.local`: `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_key_here`

### 3. MapBox Directions API

**Pros:**
- ✅ Free tier: 100,000 requests/month
- ✅ Good accuracy
- ✅ Professional service

**Cons:**
- ❌ Not yet implemented (can be added)

## Implementation Steps

### Step 1: Get API Key

Choose your preferred service and get an API key (see options above).

### Step 2: Configure Environment

Add your API key to `frontend/.env.local`:

```bash
# For OpenRouteService
NEXT_PUBLIC_OPENROUTESERVICE_API_KEY=your_ors_key_here

# OR for Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_key_here
```

### Step 3: Restart Application

```bash
cd frontend
npm run dev
```

### Step 4: Test

1. Go to trip creation form
2. Select pickup and delivery locations
3. Check console for routing logs
4. Verify distances are calculated via routing API

## Configuration Options

### Preferred API

In `distance-service.ts`, change the preferred API:

```typescript
private readonly PREFERRED_API = 'openroute'; // or 'google'
```

### Routing Profile

For OpenRouteService, you can customize the routing profile:

```typescript
profile: 'driving-hgv', // Heavy goods vehicle (trucks)
// or 'driving-car' for regular vehicles
```

### Avoid Features

Configure what to avoid in routes:

```typescript
avoid_features: ['tolls', 'highways', 'ferries']
```

## Fallback Strategy

The system uses a smart fallback strategy:

1. **Primary**: Preferred routing API (OpenRouteService or Google)
2. **Secondary**: Alternative routing API (if available)
3. **Fallback**: Direct distance calculation (Haversine)

## Cost Estimation

### OpenRouteService (Free Tier)
- **Limit**: 2,000 requests/day
- **Cost**: Free
- **Estimated usage**: ~65 trips/day (assuming 30 distance calculations per trip)

### Google Maps
- **Cost**: $5 per 1,000 requests
- **Monthly estimate**: $150 for 30,000 requests (~1,000 trips)

## Monitoring

Check the browser console for routing logs:

```
🛣️ Using OpenRouteService for routing calculation
🛣️ Routing result: { distance: 294.2, duration: 3.2, route: {...} }
```

## Troubleshooting

### No API Key
- **Symptom**: Falls back to direct distance
- **Solution**: Add API key to environment file

### API Quota Exceeded
- **Symptom**: Routing fails, falls back to direct distance
- **Solution**: Upgrade plan or use alternative API

### Invalid API Key
- **Symptom**: API errors in console
- **Solution**: Verify API key is correct and has proper permissions

## Benefits of Routed Distance

- ✅ **Accurate distances**: Real road distances vs straight-line
- ✅ **Better time estimates**: Actual driving time
- ✅ **Route optimization**: Considers traffic, road types
- ✅ **Truck-specific**: Weight limits, height restrictions
- ✅ **Cost accuracy**: Better fuel and time estimates
