/** @type {import('next-i18next').UserConfig} */
module.exports = {
  i18n: {
    defaultLocale: 'pl',
    locales: ['pl', 'en'],
    localeDetection: false,
  },
  fallbackLng: {
    default: ['pl'],
    en: ['en'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',

  // Namespace configuration
  ns: ['common', 'navigation', 'forms', 'errors', 'fleet', 'drivers', 'trips', 'fuel', 'businessPartners', 'partners'],
  defaultNS: 'common',

  // Load path for translation files
  localePath: './public/locales',

  // Interpolation settings
  interpolation: {
    escapeValue: false, // React already escapes values
  },

  // React specific settings
  react: {
    useSuspense: false,
  },

  // Server-side settings
  serializeConfig: false,
  use: [],
}
