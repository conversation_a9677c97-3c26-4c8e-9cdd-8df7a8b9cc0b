{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "1.2.14", "@types/js-cookie": "3.0.6", "class-variance-authority": "0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "i18next": "^25.2.1", "js-cookie": "3.0.5", "lucide-react": "0.511.0", "next": "14.1.0", "next-i18next": "^15.4.2", "node-fetch": "^3.3.2", "react": "18.3.1", "react-day-picker": "^9.7.0", "react-dom": "18.3.1", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.3", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "zod": "^3.25.46"}, "devDependencies": {"@types/node": "20.11.0", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "autoprefixer": "10.4.21", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "postcss": "8.5.4", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7", "typescript": "5.3.3"}}