const { PrismaClient } = require('@prisma/client');

async function testAssignmentsQuery() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Testing assignments query...');
    
    const rawAssignments = await prisma.vehicleAssignment.findMany({
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
          },
        },
      },
    });
    
    console.log(`Query found ${rawAssignments.length} assignments`);
    console.log('Assignments:', JSON.stringify(rawAssignments, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAssignmentsQuery();
