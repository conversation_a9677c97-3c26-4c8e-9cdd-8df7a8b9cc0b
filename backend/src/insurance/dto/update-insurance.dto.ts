import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsDateString, <PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { PolicyStatus, InsuranceType } from '@prisma/client';

export class UpdateInsuranceDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Vehicle ID must not exceed 100 characters' })
  vehicleId?: string;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Policy number must not exceed 200 characters' })
  policyNumber?: string;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Provider name must not exceed 200 characters' })
  provider?: string;

  @IsOptional()
  @IsEnum(InsuranceType, { message: 'Type must be a valid insurance type' })
  type?: InsuranceType;

  @IsOptional()
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  endDate?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Premium must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Premium must be non-negative' })
  premium?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Coverage must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Coverage must be non-negative' })
  coverage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Deductible must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Deductible must be non-negative' })
  deductible?: number;

  @IsOptional()
  @IsEnum(PolicyStatus, { message: 'Status must be a valid policy status' })
  status?: PolicyStatus;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Notes must not exceed 1000 characters' })
  notes?: string;
}
