import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>num, <PERSON><PERSON><PERSON>ber, IsDate<PERSON>tring, <PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { PolicyStatus, InsuranceType } from '@prisma/client';

export class CreateInsuranceDto {
  @IsString()
  @MaxLength(100, { message: 'Vehicle ID must not exceed 100 characters' })
  vehicleId: string;

  @IsString()
  @MaxLength(200, { message: 'Policy number must not exceed 200 characters' })
  policyNumber: string;

  @IsString()
  @MaxLength(200, { message: 'Provider name must not exceed 200 characters' })
  provider: string;

  @IsEnum(InsuranceType, { message: 'Type must be a valid insurance type' })
  type: InsuranceType;

  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  startDate: string;

  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  endDate: string;

  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Premium must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Premium must be non-negative' })
  premium: number;

  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Coverage must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Coverage must be non-negative' })
  coverage: number;

  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Deductible must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Deductible must be non-negative' })
  deductible: number;

  @IsOptional()
  @IsEnum(PolicyStatus, { message: 'Status must be a valid policy status' })
  status?: PolicyStatus = PolicyStatus.ACTIVE;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Notes must not exceed 1000 characters' })
  notes?: string;
}
