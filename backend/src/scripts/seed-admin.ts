import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function seedAdmin() {
  const hashedPassword = await bcrypt.hash('admin123', 10); // Replace 'admin123' with your desired password

  try {
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        passwordHash: hashedPassword, // Updated field name from 'password' to 'passwordHash'
        role: 'ADMIN',
      },
    });

    console.log('Admin user seeded:', admin);
  } catch (error) {
    console.error('Error seeding admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedAdmin();
