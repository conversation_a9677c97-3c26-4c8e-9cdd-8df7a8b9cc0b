import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards,
  BadRequestException 
} from '@nestjs/common';
import { BusinessPartnersService, CreateBusinessPartnerDto, UpdateBusinessPartnerDto, CreatePartnerLocationDto, UpdatePartnerLocationDto } from './business-partners.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BusinessPartner, PartnerLocation, PartnerType, PartnerStatus } from '@prisma/client';

@Controller('business-partners')
@UseGuards(JwtAuthGuard)
export class BusinessPartnersController {
  constructor(private readonly businessPartnersService: BusinessPartnersService) {}

  // Business Partner endpoints
  @Get()
  async findAllPartners(
    @Query('type') type?: PartnerType,
    @Query('status') status?: PartnerStatus,
    @Query('search') search?: string
  ): Promise<BusinessPartner[]> {
    return this.businessPartnersService.findAllPartners({
      type,
      status,
      search
    });
  }

  @Get('shippers')
  async findShippers(): Promise<BusinessPartner[]> {
    return this.businessPartnersService.findAllPartners({
      type: PartnerType.SHIPPER,
      status: PartnerStatus.ACTIVE
    });
  }

  @Get('logistics-partners')
  async findLogisticsPartners(): Promise<BusinessPartner[]> {
    return this.businessPartnersService.findAllPartners({
      type: PartnerType.LOGISTICS_PARTNER,
      status: PartnerStatus.ACTIVE
    });
  }

  @Get(':id')
  async findPartnerById(@Param('id') id: string): Promise<BusinessPartner> {
    return this.businessPartnersService.findPartnerById(id);
  }

  @Post()
  async createPartner(@Body() createPartnerDto: CreateBusinessPartnerDto): Promise<BusinessPartner> {
    return this.businessPartnersService.createPartner(createPartnerDto);
  }

  @Put(':id')
  async updatePartner(
    @Param('id') id: string,
    @Body() updatePartnerDto: UpdateBusinessPartnerDto
  ): Promise<BusinessPartner> {
    return this.businessPartnersService.updatePartner(id, updatePartnerDto);
  }

  @Delete(':id')
  async deletePartner(@Param('id') id: string): Promise<BusinessPartner> {
    return this.businessPartnersService.deletePartner(id);
  }

  // Partner Location endpoints
  @Get(':partnerId/locations')
  async findLocationsByPartner(@Param('partnerId') partnerId: string): Promise<PartnerLocation[]> {
    return this.businessPartnersService.findLocationsByPartner(partnerId);
  }

  @Get('locations/:id')
  async findLocationById(@Param('id') id: string): Promise<PartnerLocation> {
    return this.businessPartnersService.findLocationById(id);
  }

  @Post(':partnerId/locations')
  async createLocation(
    @Param('partnerId') partnerId: string,
    @Body() createLocationDto: Omit<CreatePartnerLocationDto, 'partnerId'>
  ): Promise<PartnerLocation> {
    return this.businessPartnersService.createLocation({
      ...createLocationDto,
      partnerId
    });
  }

  @Put('locations/:id')
  async updateLocation(
    @Param('id') id: string,
    @Body() updateLocationDto: UpdatePartnerLocationDto
  ): Promise<PartnerLocation> {
    return this.businessPartnersService.updateLocation(id, updateLocationDto);
  }

  @Delete('locations/:id')
  async deleteLocation(@Param('id') id: string): Promise<PartnerLocation> {
    return this.businessPartnersService.deleteLocation(id);
  }

  // Utility endpoints for trip creation
  @Get('locations/pickup/all')
  async findAllPickupLocations(@Query('partnerId') partnerId?: string): Promise<PartnerLocation[]> {
    return this.businessPartnersService.findPickupLocations(partnerId);
  }

  @Get('locations/delivery/all')
  async findAllDeliveryLocations(@Query('partnerId') partnerId?: string): Promise<PartnerLocation[]> {
    return this.businessPartnersService.findDeliveryLocations(partnerId);
  }
}
