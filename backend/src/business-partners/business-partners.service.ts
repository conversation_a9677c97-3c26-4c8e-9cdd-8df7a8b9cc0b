import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { BusinessPartner, PartnerLocation, PartnerType, PartnerStatus, LocationType, Prisma } from '@prisma/client';

export interface CreateBusinessPartnerDto {
  name: string;
  type: PartnerType;
  status?: PartnerStatus;
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
}

export interface UpdateBusinessPartnerDto {
  name?: string;
  type?: PartnerType;
  status?: PartnerStatus;
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  taxId?: string;
  notes?: string;
}

export interface CreatePartnerLocationDto {
  partnerId: string;
  name: string;
  type: LocationType;
  address: string;
  city: string;
  state?: string;
  postalCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

export interface UpdatePartnerLocationDto {
  name?: string;
  type?: LocationType;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  contactPerson?: string;
  phone?: string;
  email?: string;
  operatingHours?: string;
  specialInstructions?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

@Injectable()
export class BusinessPartnersService {
  constructor(private prisma: PrismaService) {}

  // Business Partner CRUD Operations
  async findAllPartners(filters?: {
    type?: PartnerType;
    status?: PartnerStatus;
    search?: string;
  }): Promise<BusinessPartner[]> {
    const where: Prisma.BusinessPartnerWhereInput = {};

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { contactPerson: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    return this.prisma.businessPartner.findMany({
      where,
      include: {
        locations: {
          where: { isActive: true },
          orderBy: [
            { isDefault: 'desc' },
            { name: 'asc' }
          ]
        },
        _count: {
          select: {
            tripPickups: true,
            tripDeliveries: true,
          }
        }
      },
      orderBy: [
        { status: 'asc' },
        { name: 'asc' }
      ]
    });
  }

  async findPartnerById(id: string): Promise<BusinessPartner> {
    const partner = await this.prisma.businessPartner.findUnique({
      where: { id },
      include: {
        locations: {
          orderBy: [
            { isDefault: 'desc' },
            { name: 'asc' }
          ]
        },
        tripPickups: {
          take: 10,
          orderBy: { startTime: 'desc' },
          include: {
            driver: { select: { firstName: true, lastName: true } },
            vehicle: { select: { plateNumber: true, make: true, model: true } }
          }
        },
        tripDeliveries: {
          take: 10,
          orderBy: { startTime: 'desc' },
          include: {
            driver: { select: { firstName: true, lastName: true } },
            vehicle: { select: { plateNumber: true, make: true, model: true } }
          }
        }
      }
    });

    if (!partner) {
      throw new NotFoundException('Business partner not found');
    }

    return partner;
  }

  async createPartner(data: CreateBusinessPartnerDto): Promise<BusinessPartner> {
    try {
      return await this.prisma.businessPartner.create({
        data: {
          ...data,
          status: data.status || PartnerStatus.ACTIVE,
        },
        include: {
          locations: true
        }
      });
    } catch (error) {
      throw new BadRequestException('Failed to create business partner: ' + error.message);
    }
  }

  async updatePartner(id: string, data: UpdateBusinessPartnerDto): Promise<BusinessPartner> {
    try {
      return await this.prisma.businessPartner.update({
        where: { id },
        data,
        include: {
          locations: true
        }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Business partner not found');
      }
      throw new BadRequestException('Failed to update business partner: ' + error.message);
    }
  }

  async deletePartner(id: string): Promise<BusinessPartner> {
    try {
      return await this.prisma.businessPartner.delete({
        where: { id }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Business partner not found');
      }
      throw new BadRequestException('Failed to delete business partner: ' + error.message);
    }
  }

  // Partner Location CRUD Operations
  async findLocationsByPartner(partnerId: string): Promise<PartnerLocation[]> {
    return this.prisma.partnerLocation.findMany({
      where: { partnerId },
      orderBy: [
        { isDefault: 'desc' },
        { name: 'asc' }
      ]
    });
  }

  async findLocationById(id: string): Promise<PartnerLocation> {
    const location = await this.prisma.partnerLocation.findUnique({
      where: { id },
      include: {
        partner: true
      }
    });

    if (!location) {
      throw new NotFoundException('Partner location not found');
    }

    return location;
  }

  async createLocation(data: CreatePartnerLocationDto): Promise<PartnerLocation> {
    try {
      // If this is set as default, unset other defaults for the same partner
      if (data.isDefault) {
        await this.prisma.partnerLocation.updateMany({
          where: { 
            partnerId: data.partnerId,
            type: data.type 
          },
          data: { isDefault: false }
        });
      }

      return await this.prisma.partnerLocation.create({
        data: {
          ...data,
          country: data.country || 'Poland',
          isActive: data.isActive !== undefined ? data.isActive : true,
          isDefault: data.isDefault || false,
        },
        include: {
          partner: true
        }
      });
    } catch (error) {
      throw new BadRequestException('Failed to create partner location: ' + error.message);
    }
  }

  async updateLocation(id: string, data: UpdatePartnerLocationDto): Promise<PartnerLocation> {
    try {
      // If this is being set as default, unset other defaults for the same partner and type
      if (data.isDefault) {
        const location = await this.prisma.partnerLocation.findUnique({
          where: { id }
        });
        
        if (location) {
          await this.prisma.partnerLocation.updateMany({
            where: { 
              partnerId: location.partnerId,
              type: data.type || location.type,
              id: { not: id }
            },
            data: { isDefault: false }
          });
        }
      }

      return await this.prisma.partnerLocation.update({
        where: { id },
        data,
        include: {
          partner: true
        }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Partner location not found');
      }
      throw new BadRequestException('Failed to update partner location: ' + error.message);
    }
  }

  async deleteLocation(id: string): Promise<PartnerLocation> {
    try {
      return await this.prisma.partnerLocation.delete({
        where: { id }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Partner location not found');
      }
      throw new BadRequestException('Failed to delete partner location: ' + error.message);
    }
  }

  // Utility methods for trip creation
  async findPickupLocations(partnerId?: string): Promise<PartnerLocation[]> {
    const where: Prisma.PartnerLocationWhereInput = {
      isActive: true,
      type: { in: ['PICKUP_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER'] }
    };

    if (partnerId) {
      where.partnerId = partnerId;
    }

    return this.prisma.partnerLocation.findMany({
      where,
      include: {
        partner: { select: { name: true, type: true } }
      },
      orderBy: [
        { isDefault: 'desc' },
        { partner: { name: 'asc' } },
        { name: 'asc' }
      ]
    });
  }

  async findDeliveryLocations(partnerId?: string): Promise<PartnerLocation[]> {
    const where: Prisma.PartnerLocationWhereInput = {
      isActive: true,
      type: { in: ['DELIVERY_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER'] }
    };

    if (partnerId) {
      where.partnerId = partnerId;
    }

    return this.prisma.partnerLocation.findMany({
      where,
      include: {
        partner: { select: { name: true, type: true } }
      },
      orderBy: [
        { isDefault: 'desc' },
        { partner: { name: 'asc' } },
        { name: 'asc' }
      ]
    });
  }
}
