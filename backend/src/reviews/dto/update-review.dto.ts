import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsDateString, <PERSON><PERSON><PERSON>th, <PERSON>Arra<PERSON> } from 'class-validator';
import { ReviewType, ReviewStatus } from '@prisma/client';

export class UpdateReviewDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Vehicle ID must not exceed 100 characters' })
  vehicleId?: string;

  @IsOptional()
  @IsEnum(ReviewType, { message: 'Review type must be a valid review type' })
  reviewType?: ReviewType;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Reviewer name must not exceed 200 characters' })
  reviewBy?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Scheduled date must be a valid ISO date string' })
  scheduledDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Completed date must be a valid ISO date string' })
  completedDate?: string;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Location must not exceed 200 characters' })
  location?: string;

  @IsOptional()
  @IsEnum(ReviewStatus, { message: 'Status must be a valid review status' })
  status?: ReviewStatus;

  @IsOptional()
  @IsString()
  @MaxLength(2000, { message: 'Findings must not exceed 2000 characters' })
  findings?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2000, { message: 'Recommendations must not exceed 2000 characters' })
  recommendations?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Next review date must be a valid ISO date string' })
  nextReviewDate?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  documents?: string[];
}
