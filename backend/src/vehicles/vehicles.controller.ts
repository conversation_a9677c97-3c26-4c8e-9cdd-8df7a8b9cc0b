import {
  Controller,
  Get,
  Post,
  Patch,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Query
} from '@nestjs/common';
import { VehiclesService } from './vehicles.service';
import { VehicleAssignmentService } from './vehicle-assignment.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { CreateAssignmentDto } from './dto/create-assignment.dto';
import {
  Vehicle,
  VehicleType,
  MaintenanceLog,
  MaintenanceType,
  MaintenanceCategory,
  MaintenanceStatus,
  VehicleStatus,
  VehicleAssignment
} from '@prisma/client';

@Controller('vehicles')
@UseGuards(JwtAuthGuard)
export class VehiclesController {
  constructor(
    private readonly vehiclesService: VehiclesService,
    private readonly assignmentService: VehicleAssignmentService,
    private readonly prisma: PrismaService
  ) {}

  @Get()
  findAll(@Query('type') type?: string): Promise<Vehicle[]> {
    if (type === 'TRUCK') {
      return this.vehiclesService.findTrucks();
    }
    if (type === 'TRAILER') {
      return this.vehiclesService.findTrailers();
    }
    return this.vehiclesService.findAll();
  }

  @Get('trucks')
  findTrucks(): Promise<Vehicle[]> {
    return this.vehiclesService.findTrucks();
  }

  @Get('trailers')
  findTrailers(): Promise<Vehicle[]> {
    return this.vehiclesService.findTrailers();
  }

  @Get('trucks/available')
  findAvailableTrucks(): Promise<Vehicle[]> {
    return this.vehiclesService.findAvailableTrucks();
  }

  @Get('trailers/available')
  findAvailableTrailers(): Promise<Vehicle[]> {
    return this.vehiclesService.findAvailableTrailers();
  }

  // Put all specific routes before parameterized routes to avoid conflicts
  @Get('assignments')
  async findAllAssignments(): Promise<VehicleAssignment[]> {
    console.log('Backend: Vehicle assignments endpoint called');
    try {
      console.log('Backend: Starting Prisma query for assignments...');
      const rawAssignments = await this.prisma.vehicleAssignment.findMany({
        include: {
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          vehicle: {
            select: {
              id: true,
              plateNumber: true,
              make: true,
              model: true,
              year: true,
              status: true,
            },
          },
        },
      });
      
      console.log(`Backend: Direct query found ${rawAssignments.length} assignments`);
      if (rawAssignments.length > 0) {
        console.log('Backend: Sample assignment:', JSON.stringify(rawAssignments[0], null, 2));
      } else {
        console.log('Backend: No assignments found in database');
      }
      
      console.log('Backend: About to return assignments data');
      return rawAssignments;
    } catch (error) {
      console.error('Backend: Error fetching assignments:', error);
      console.error('Backend: Error stack:', error.stack);
      throw error;
    }
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<any | null> {
    return this.vehiclesService.findOne(id);
  }

  @Post()
  create(@Body() createVehicleDto: CreateVehicleDto): Promise<Vehicle> {
    // Format the purchaseDate properly if it's provided
    const formattedDto = {
      ...createVehicleDto,
      purchaseDate: createVehicleDto.purchaseDate 
        ? new Date(createVehicleDto.purchaseDate).toISOString() 
        : undefined
    };
    return this.vehiclesService.create(formattedDto);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateVehicleDto: UpdateVehicleDto,
  ): Promise<Vehicle> {
    // Format date fields properly if they're provided
    const formattedDto = {
      ...updateVehicleDto,
      lastMaintenance: updateVehicleDto.lastMaintenance 
        ? new Date(updateVehicleDto.lastMaintenance)
        : undefined
    };
    return this.vehiclesService.update(id, formattedDto);
  }

  @Put(':id')
  fullUpdate(
    @Param('id') id: string,
    @Body() updateVehicleDto: UpdateVehicleDto,
  ): Promise<Vehicle> {
    // Format date fields properly if they're provided
    const formattedDto = {
      ...updateVehicleDto,
      lastMaintenance: updateVehicleDto.lastMaintenance 
        ? new Date(updateVehicleDto.lastMaintenance)
        : undefined
    };
    return this.vehiclesService.update(id, formattedDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<Vehicle> {
    return this.vehiclesService.delete(id);
  }

  @Get(':id/assignments')
  findAssignments(@Param('id') id: string): Promise<VehicleAssignment[]> {
    return this.assignmentService.findAllByVehicle(id);
  }

  @Post(':id/assignments')
  createAssignment(
    @Param('id') id: string,
    @Body() createAssignmentDto: {
      driverId: string;
      startDate: string;
      endDate?: string;
    },
  ): Promise<VehicleAssignment> {
    return this.assignmentService.create({
      vehicleId: id,
      driverId: createAssignmentDto.driverId,
      startDate: new Date(createAssignmentDto.startDate),
      endDate: createAssignmentDto.endDate ? new Date(createAssignmentDto.endDate) : undefined,
    });
  }

  @Patch('assignments/:id/complete')
  completeAssignment(@Param('id') id: string): Promise<VehicleAssignment> {
    return this.assignmentService.complete(id);
  }

  @Patch('assignments/:id/cancel')
  cancelAssignment(@Param('id') id: string): Promise<VehicleAssignment> {
    return this.assignmentService.cancel(id);
  }

  @Get(':id/maintenance-logs')
  async getMaintenanceLogs(@Param('id') id: string): Promise<MaintenanceLog[]> {
    return this.vehiclesService.findMaintenanceLogs(id);
  }

  @Post(':id/maintenance-logs')
  async createMaintenanceLog(
    @Param('id') id: string,
    @Body() data: {
      type: MaintenanceType;
      category: MaintenanceCategory;
      description: string;
      status: MaintenanceStatus;
      date?: string;
      scheduledDate: string;
      mileage?: number;
      partsCost?: number;
      laborCost?: number;
      technician?: string;
      notes?: string;
      nextMaintenanceDate?: string;
      nextMaintenanceMileage?: number;
    }
  ): Promise<MaintenanceLog> {
    return this.vehiclesService.createMaintenanceLog(id, data);
  }

  @Put(':id/maintenance-logs/:logId')
  async updateMaintenanceLog(
    @Param('logId') logId: string,
    @Body() data: {
      type?: MaintenanceType;
      category?: MaintenanceCategory;
      description?: string;
      status?: MaintenanceStatus;
      date?: string;
      scheduledDate?: string;
      mileage?: number;
      partsCost?: number;
      laborCost?: number;
      technician?: string;
      notes?: string;
      nextMaintenanceDate?: string;
      nextMaintenanceMileage?: number;
    }
  ): Promise<MaintenanceLog> {
    return this.vehiclesService.updateMaintenanceLog(logId, data);
  }

  @Delete(':id/maintenance-logs/:logId')
  async deleteMaintenanceLog(
    @Param('logId') logId: string
  ): Promise<MaintenanceLog> {
    return this.vehiclesService.deleteMaintenanceLog(logId);
  }

  @Get('maintenance/upcoming')
  async getUpcomingMaintenance(): Promise<MaintenanceLog[]> {
    return this.vehiclesService.getUpcomingMaintenance();
  }
}
