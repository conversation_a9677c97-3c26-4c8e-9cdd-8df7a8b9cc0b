import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma, VehicleAssignment, VehicleStatus } from '@prisma/client';
import {
  BusinessLogicException,
  ResourceNotFoundException,
  ConflictException,
  DatabaseException
} from '../common/exceptions/custom-exceptions';

/**
 * Service responsible for managing vehicle-driver assignments
 *
 * This service handles the complex business logic of assigning vehicles to drivers,
 * ensuring data consistency through database transactions, and managing the lifecycle
 * of assignments from creation to completion or cancellation.
 *
 * Key responsibilities:
 * - Validate vehicle and driver availability before assignment
 * - Ensure no conflicting assignments exist
 * - Update both vehicle and driver status atomically
 * - Handle assignment completion and cancellation
 * - Provide comprehensive error handling and logging
 */
@Injectable()
export class VehicleAssignmentService {
  constructor(private prisma: PrismaService) {}

  /**
   * Creates a new vehicle assignment
   *
   * This method performs comprehensive validation and creates an assignment
   * using a database transaction to ensure data consistency.
   *
   * @param data Assignment data including vehicle ID, driver ID, and dates
   * @returns Promise<VehicleAssignment> The created assignment with related data
   *
   * @throws BusinessLogicException When date validation fails
   * @throws ResourceNotFoundException When vehicle or driver doesn't exist
   * @throws ConflictException When vehicle or driver is not available
   * @throws DatabaseException When database operation fails
   */
  async create(data: {
    vehicleId: string;
    driverId: string;
    startDate: Date;
    endDate?: Date;
  }): Promise<VehicleAssignment> {
    // Validate date logic - end date must be after start date
    if (data.endDate && data.startDate >= data.endDate) {
      throw new BusinessLogicException('End date must be after start date');
    }

    // Validate vehicle and driver availability using dedicated methods
    await this.validateVehicleAvailability(data.vehicleId);
    await this.validateDriverAvailability(data.driverId);

    // Create assignment and update both vehicle and driver status in a transaction
    try {
      const [assignment] = await this.prisma.$transaction([
        this.prisma.vehicleAssignment.create({
          data: {
            ...data,
            status: 'ACTIVE',
          },
          include: {
            driver: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
                status: true,
              },
            },
            vehicle: {
              select: {
                plateNumber: true,
                make: true,
                model: true,
                status: true,
              },
            },
          },
        }),
        this.prisma.vehicle.update({
          where: { id: data.vehicleId },
          data: { status: VehicleStatus.ASSIGNED },
        }),
        // Update driver status to indicate they are now assigned
        this.prisma.user.update({
          where: { id: data.driverId },
          data: { status: 'Assigned' },
        }),
      ]);

      return assignment;
    } catch (error) {
      // Handle potential database errors with more specific error messages
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Handle specific Prisma error codes
        switch (error.code) {
          case 'P2002':
            throw new ConflictException('Assignment already exists or violates unique constraint');
          case 'P2025':
            throw new ResourceNotFoundException('Vehicle or Driver', 'referenced in assignment');
          default:
            throw new DatabaseException('Failed to create assignment', error);
        }
      }
      throw error;
    }
  }

  async findAllByVehicle(vehicleId: string): Promise<VehicleAssignment[]> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
    });

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    return this.prisma.vehicleAssignment.findMany({
      where: { vehicleId },
      include: {
        driver: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        vehicle: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
          },
        },
      },
      orderBy: { startDate: 'desc' },
    });
  }

  async findAll(): Promise<VehicleAssignment[]> {
    return this.prisma.vehicleAssignment.findMany({
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
          },
        },
      },
      orderBy: { startDate: 'desc' },
    });
  }

  async complete(id: string): Promise<VehicleAssignment> {
    const assignment = await this.prisma.vehicleAssignment.findUnique({
      where: { id },
      select: { vehicleId: true, driverId: true, status: true },
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    if (assignment.status !== 'ACTIVE') {
      throw new BusinessLogicException('Only active assignments can be completed');
    }

    try {
      const [updatedAssignment] = await this.prisma.$transaction([
        this.prisma.vehicleAssignment.update({
          where: { id },
          data: {
            status: 'COMPLETED',
            endDate: new Date(),
          },
          include: {
            driver: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            vehicle: {
              select: {
                plateNumber: true,
                make: true,
                model: true,
              },
            },
          },
        }),
        this.prisma.vehicle.update({
          where: { id: assignment.vehicleId },
          data: { status: VehicleStatus.AVAILABLE },
        }),
        // Update driver status back to available
        this.prisma.user.update({
          where: { id: assignment.driverId },
          data: { status: 'Active' },
        }),
      ]);

      return updatedAssignment;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        throw new DatabaseException('Failed to complete assignment', error);
      }
      throw error;
    }
  }

  async cancel(id: string): Promise<VehicleAssignment> {
    const assignment = await this.prisma.vehicleAssignment.findUnique({
      where: { id },
      select: { vehicleId: true, driverId: true, status: true },
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    if (assignment.status !== 'ACTIVE') {
      throw new BusinessLogicException('Only active assignments can be cancelled');
    }

    try {
      const [updatedAssignment] = await this.prisma.$transaction([
        this.prisma.vehicleAssignment.update({
          where: { id },
          data: {
            status: 'CANCELLED',
            endDate: new Date(),
          },
          include: {
            driver: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            vehicle: {
              select: {
                plateNumber: true,
                make: true,
                model: true,
              },
            },
          },
        }),
        this.prisma.vehicle.update({
          where: { id: assignment.vehicleId },
          data: { status: VehicleStatus.AVAILABLE },
        }),
        // Update driver status back to available
        this.prisma.user.update({
          where: { id: assignment.driverId },
          data: { status: 'Active' },
        }),
      ]);

      return updatedAssignment;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        throw new DatabaseException('Failed to cancel assignment', error);
      }
      throw error;
    }
  }

  /**
   * Comprehensive driver availability validation
   *
   * Performs a thorough check to ensure a driver can be assigned to a vehicle.
   * This includes verifying the driver exists, has the correct role, and is
   * currently available for assignment.
   *
   * @param driverId The unique identifier of the driver to validate
   * @returns Promise<any> Driver data if validation passes
   *
   * @throws ResourceNotFoundException When driver doesn't exist
   * @throws BusinessLogicException When user is not a driver
   * @throws ConflictException When driver is already assigned or unavailable
   *
   * Business Rules:
   * - Driver must exist in the system
   * - User must have DRIVER role
   * - Driver cannot have any active assignments
   * - Driver status must be 'Active' or 'Available'
   */
  private async validateDriverAvailability(driverId: string): Promise<any> {
    const driver = await this.prisma.user.findUnique({
      where: { id: driverId },
      include: {
        assignments: {
          where: {
            status: 'ACTIVE',
          },
        },
      },
    });

    if (!driver) {
      throw new ResourceNotFoundException('Driver', driverId);
    }

    if (driver.role !== 'DRIVER') {
      throw new BusinessLogicException('User is not a driver');
    }

    // Check if driver has any active assignments
    if (driver.assignments.length > 0) {
      throw new ConflictException('Driver already has an active assignment');
    }

    // Additional check for driver availability based on status
    if (driver.status && driver.status !== 'Active' && driver.status !== 'Available') {
      throw new ConflictException(`Driver is currently ${driver.status.toLowerCase()} and cannot be assigned`);
    }

    return driver;
  }

  /**
   * Comprehensive vehicle availability validation
   *
   * Performs a thorough check to ensure a vehicle can be assigned to a driver.
   * This includes verifying the vehicle exists, is in the correct status, and
   * has no conflicting assignments.
   *
   * @param vehicleId The unique identifier of the vehicle to validate
   * @returns Promise<any> Vehicle data if validation passes
   *
   * @throws ResourceNotFoundException When vehicle doesn't exist
   * @throws ConflictException When vehicle is not available or already assigned
   *
   * Business Rules:
   * - Vehicle must exist in the system
   * - Vehicle status must be 'AVAILABLE'
   * - Vehicle cannot have any active assignments
   * - Vehicle must be in operational condition
   */
  private async validateVehicleAvailability(vehicleId: string): Promise<any> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      include: {
        assignments: {
          where: {
            status: 'ACTIVE',
          },
        },
      },
    });

    if (!vehicle) {
      throw new ResourceNotFoundException('Vehicle', vehicleId);
    }

    if (vehicle.status !== VehicleStatus.AVAILABLE) {
      throw new ConflictException(`Vehicle is currently ${vehicle.status.toLowerCase()} and cannot be assigned`);
    }

    // Check if vehicle has any active assignments
    if (vehicle.assignments.length > 0) {
      throw new ConflictException('Vehicle already has an active assignment');
    }

    return vehicle;
  }
}
