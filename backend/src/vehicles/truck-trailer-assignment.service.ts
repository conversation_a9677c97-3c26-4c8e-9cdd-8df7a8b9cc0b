import { Injectable, BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TruckTrailerAssignment, AssignmentStatus, VehicleType } from '@prisma/client';
import { CreateTruckTrailerAssignmentDto } from './dto/create-truck-trailer-assignment.dto';
import { UpdateTruckTrailerAssignmentDto } from './dto/update-truck-trailer-assignment.dto';

@Injectable()
export class TruckTrailerAssignmentService {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<TruckTrailerAssignment[]> {
    return this.prisma.truckTrailerAssignment.findMany({
      include: {
        truck: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
            vehicleType: true,
          }
        },
        trailer: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
            vehicleType: true,
            trailerType: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async findActive(): Promise<TruckTrailerAssignment[]> {
    return this.prisma.truckTrailerAssignment.findMany({
      where: {
        status: AssignmentStatus.ACTIVE,
        endDate: null
      },
      include: {
        truck: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
            vehicleType: true,
          }
        },
        trailer: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
            vehicleType: true,
            trailerType: true,
          }
        }
      }
    });
  }

  async findOne(id: string): Promise<TruckTrailerAssignment | null> {
    return this.prisma.truckTrailerAssignment.findUnique({
      where: { id },
      include: {
        truck: true,
        trailer: true
      }
    });
  }

  async findByTruck(truckId: string): Promise<TruckTrailerAssignment[]> {
    return this.prisma.truckTrailerAssignment.findMany({
      where: { truckId },
      include: {
        truck: true,
        trailer: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async findByTrailer(trailerId: string): Promise<TruckTrailerAssignment[]> {
    return this.prisma.truckTrailerAssignment.findMany({
      where: { trailerId },
      include: {
        truck: true,
        trailer: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async create(data: CreateTruckTrailerAssignmentDto): Promise<TruckTrailerAssignment> {
    // Validate that truck and trailer exist and are correct types
    await this.validateVehicles(data.truckId, data.trailerId);

    // Check for existing active assignments
    await this.checkForConflicts(data.truckId, data.trailerId, data.startDate);

    // Ensure assignedBy is provided (should be set by controller)
    if (!data.assignedBy) {
      throw new BadRequestException('Assigned by is required');
    }

    return this.prisma.truckTrailerAssignment.create({
      data: {
        truckId: data.truckId,
        trailerId: data.trailerId,
        startDate: new Date(data.startDate),
        endDate: data.endDate ? new Date(data.endDate) : null,
        status: AssignmentStatus.ACTIVE,
        notes: data.notes,
        assignedBy: data.assignedBy
      },
      include: {
        truck: true,
        trailer: true
      }
    });
  }

  async update(id: string, data: UpdateTruckTrailerAssignmentDto): Promise<TruckTrailerAssignment> {
    const assignment = await this.findOne(id);
    if (!assignment) {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }

    // If updating truck or trailer, validate them
    if (data.truckId || data.trailerId) {
      await this.validateVehicles(
        data.truckId || assignment.truckId,
        data.trailerId || assignment.trailerId
      );
    }

    const updateData: any = { ...data };
    if (updateData.startDate) {
      updateData.startDate = new Date(updateData.startDate);
    }
    if (updateData.endDate) {
      updateData.endDate = new Date(updateData.endDate);
    }

    return this.prisma.truckTrailerAssignment.update({
      where: { id },
      data: updateData,
      include: {
        truck: true,
        trailer: true
      }
    });
  }

  async complete(id: string): Promise<TruckTrailerAssignment> {
    const assignment = await this.findOne(id);
    if (!assignment) {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }

    if (assignment.status !== AssignmentStatus.ACTIVE) {
      throw new BadRequestException('Only active assignments can be completed');
    }

    return this.prisma.truckTrailerAssignment.update({
      where: { id },
      data: {
        status: AssignmentStatus.COMPLETED,
        endDate: new Date()
      },
      include: {
        truck: true,
        trailer: true
      }
    });
  }

  async cancel(id: string): Promise<TruckTrailerAssignment> {
    const assignment = await this.findOne(id);
    if (!assignment) {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }

    if (assignment.status === AssignmentStatus.COMPLETED) {
      throw new BadRequestException('Completed assignments cannot be cancelled');
    }

    return this.prisma.truckTrailerAssignment.update({
      where: { id },
      data: {
        status: AssignmentStatus.CANCELLED,
        endDate: new Date()
      },
      include: {
        truck: true,
        trailer: true
      }
    });
  }

  async delete(id: string): Promise<TruckTrailerAssignment> {
    const assignment = await this.findOne(id);
    if (!assignment) {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }

    return this.prisma.truckTrailerAssignment.delete({
      where: { id }
    });
  }

  private async validateVehicles(truckId: string, trailerId: string): Promise<void> {
    // Check if truck exists and is actually a truck
    const truck = await this.prisma.vehicle.findUnique({
      where: { id: truckId },
      select: { id: true, vehicleType: true, plateNumber: true }
    });

    if (!truck) {
      throw new NotFoundException(`Truck with ID ${truckId} not found`);
    }

    if (truck.vehicleType !== VehicleType.TRUCK) {
      throw new BadRequestException(`Vehicle ${truck.plateNumber} is not a truck`);
    }

    // Check if trailer exists and is actually a trailer
    const trailer = await this.prisma.vehicle.findUnique({
      where: { id: trailerId },
      select: { id: true, vehicleType: true, plateNumber: true }
    });

    if (!trailer) {
      throw new NotFoundException(`Trailer with ID ${trailerId} not found`);
    }

    if (trailer.vehicleType !== VehicleType.TRAILER) {
      throw new BadRequestException(`Vehicle ${trailer.plateNumber} is not a trailer`);
    }
  }

  private async checkForConflicts(truckId: string, trailerId: string, startDate: string): Promise<void> {
    const startDateTime = new Date(startDate);

    // Check if truck has active assignment
    const truckConflict = await this.prisma.truckTrailerAssignment.findFirst({
      where: {
        truckId,
        status: AssignmentStatus.ACTIVE,
        OR: [
          { endDate: null },
          { endDate: { gte: startDateTime } }
        ]
      }
    });

    if (truckConflict) {
      throw new ConflictException('Truck already has an active assignment');
    }

    // Check if trailer has active assignment
    const trailerConflict = await this.prisma.truckTrailerAssignment.findFirst({
      where: {
        trailerId,
        status: AssignmentStatus.ACTIVE,
        OR: [
          { endDate: null },
          { endDate: { gte: startDateTime } }
        ]
      }
    });

    if (trailerConflict) {
      throw new ConflictException('Trailer already has an active assignment');
    }
  }
}
