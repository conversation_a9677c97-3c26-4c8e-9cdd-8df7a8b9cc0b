import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  Vehicle,
  VehicleStatus,
  VehicleType,
  TrailerType,
  MaintenanceLog,
  MaintenanceType,
  MaintenanceCategory,
  MaintenanceStatus,
  Prisma
} from '@prisma/client';

@Injectable()
export class VehiclesService {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<any[]> {
    return this.prisma.vehicle.findMany({
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
        year: true,
        status: true,
        vehicleType: true,
        color: true,
        mileage: true,
        lastMaintenance: true,
        createdAt: true,
        updatedAt: true,
        // Only include active assignments with minimal driver data
        assignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
            startDate: true,
            endDate: true,
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        // Only get the most recent maintenance log
        maintenanceLogs: {
          select: {
            id: true,
            date: true,
            type: true,
            description: true,
          },
          orderBy: {
            date: 'desc',
          },
          take: 1,
        },
        // Optimized truck assignments
        truckAssignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
            startDate: true,
            endDate: true,
            trailer: {
              select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                trailerType: true,
              }
            }
          }
        },
        // Optimized trailer assignments
        trailerAssignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
            startDate: true,
            endDate: true,
            truck: {
              select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
              }
            }
          }
        },
      },
      orderBy: [
        { status: 'asc' }, // Available vehicles first
        { plateNumber: 'asc' }
      ],
    });
  }

  async findByType(vehicleType: VehicleType): Promise<any[]> {
    return this.prisma.vehicle.findMany({
      where: { vehicleType },
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
        year: true,
        status: true,
        vehicleType: true,
        color: true,
        mileage: true,
        lastMaintenance: true,
        // Vehicle type specific fields
        ...(vehicleType === 'TRUCK' ? {
          engineType: true,
          transmission: true,
          fuelCapacity: true,
          axleConfiguration: true,
          cabConfiguration: true,
        } : {
          trailerType: true,
          cargoCapacity: true,
          maxWeight: true,
          length: true,
          width: true,
          height: true,
          hasRefrigeration: true,
        }),
        // Only active assignments
        assignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
            startDate: true,
            endDate: true,
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        // Conditional includes based on vehicle type
        ...(vehicleType === 'TRUCK' ? {
          truckAssignments: {
            where: {
              status: 'ACTIVE',
            },
            select: {
              id: true,
              status: true,
              trailer: {
                select: {
                  id: true,
                  plateNumber: true,
                  trailerType: true,
                }
              }
            }
          }
        } : {
          trailerAssignments: {
            where: {
              status: 'ACTIVE',
            },
            select: {
              id: true,
              status: true,
              truck: {
                select: {
                  id: true,
                  plateNumber: true,
                  make: true,
                  model: true,
                }
              }
            }
          }
        }),
      },
      orderBy: [
        { status: 'asc' },
        { plateNumber: 'asc' }
      ],
    });
  }

  async findTrucks(): Promise<any[]> {
    return this.findByType(VehicleType.TRUCK);
  }

  async findTrailers(): Promise<any[]> {
    return this.findByType(VehicleType.TRAILER);
  }

  /**
   * Find vehicles with pagination and filtering
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 20,
    filters?: {
      vehicleType?: VehicleType;
      status?: string;
      search?: string;
    }
  ) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (filters?.vehicleType) {
      where.vehicleType = filters.vehicleType;
    }
    if (filters?.status) {
      where.status = filters.status;
    }
    if (filters?.search) {
      where.OR = [
        { plateNumber: { contains: filters.search, mode: 'insensitive' } },
        { make: { contains: filters.search, mode: 'insensitive' } },
        { model: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    const [vehicles, total] = await Promise.all([
      this.prisma.vehicle.findMany({
        where,
        select: {
          id: true,
          plateNumber: true,
          make: true,
          model: true,
          year: true,
          status: true,
          vehicleType: true,
          mileage: true,
          lastMaintenance: true,
          assignments: {
            where: {
              status: 'ACTIVE',
            },
            select: {
              id: true,
              driver: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: [
          { status: 'asc' },
          { plateNumber: 'asc' }
        ],
        skip,
        take: limit,
      }),
      this.prisma.vehicle.count({ where }),
    ]);

    return {
      vehicles,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findAvailableTrucks(): Promise<Vehicle[]> {
    return this.prisma.vehicle.findMany({
      where: {
        vehicleType: VehicleType.TRUCK,
        status: VehicleStatus.AVAILABLE,
        truckAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      },
      orderBy: {
        plateNumber: 'asc',
      },
    });
  }

  async findAvailableTrailers(): Promise<Vehicle[]> {
    return this.prisma.vehicle.findMany({
      where: {
        vehicleType: VehicleType.TRAILER,
        status: VehicleStatus.AVAILABLE,
        trailerAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      },
      orderBy: {
        plateNumber: 'asc',
      },
    });
  }

  async findOne(id: string): Promise<any | null> {
    return this.prisma.vehicle.findUnique({
      where: { id },
      include: {
        assignments: {
          include: {
            driver: true,
          },
        },
        maintenanceLogs: {
          orderBy: {
            date: 'desc',
          },
        },
      },
    });
  }

  async create(data: {
    plateNumber: string;
    make: string;
    model: string;
    year: number;
    vehicleType: VehicleType;
    vin?: string;
    color?: string;
    mileage?: number;
    fuelType?: string;
    purchaseDate?: string;
    // Truck-specific fields
    engineType?: string;
    transmission?: string;
    fuelCapacity?: number;
    axleConfiguration?: string;
    cabConfiguration?: string;
    // Trailer-specific fields
    trailerType?: TrailerType;
    cargoCapacity?: number;
    maxWeight?: number;
    length?: number;
    width?: number;
    height?: number;
    hasRefrigeration?: boolean;
  }): Promise<Vehicle> {
    // Create vehicle with properly formatted data
    return this.prisma.vehicle.create({
      data: {
        ...data,
        status: VehicleStatus.AVAILABLE,
        // Ensure purchaseDate is properly converted to a Date object if provided
        ...(data.purchaseDate && { purchaseDate: new Date(data.purchaseDate) }),
      },
    });
  }

  async update(id: string, data: {
    plateNumber?: string;
    make?: string;
    model?: string;
    year?: number;
    vehicleType?: VehicleType;
    status?: VehicleStatus;
    lastMaintenance?: Date;
    vin?: string;
    color?: string;
    mileage?: number;
    fuelType?: string;
    purchaseDate?: string;
    // Truck-specific fields
    engineType?: string;
    transmission?: string;
    fuelCapacity?: number;
    axleConfiguration?: string;
    cabConfiguration?: string;
    // Trailer-specific fields
    trailerType?: TrailerType;
    cargoCapacity?: number;
    maxWeight?: number;
    length?: number;
    width?: number;
    height?: number;
    hasRefrigeration?: boolean;
  }): Promise<Vehicle> {
    // Create a new object for the update data
    const updateData: any = { ...data };

    // Convert the purchaseDate string to a Date object if it exists
    if (updateData.purchaseDate) {
      updateData.purchaseDate = new Date(updateData.purchaseDate);
    }

    return this.prisma.vehicle.update({
      where: { id },
      data: updateData,
    });
  }

  async delete(id: string): Promise<Vehicle> {
    return this.prisma.vehicle.delete({
      where: { id },
    });
  }

  async findMaintenanceLogs(vehicleId: string): Promise<MaintenanceLog[]> {
    return this.prisma.maintenanceLog.findMany({
      where: { vehicleId },
      orderBy: [
        { scheduledDate: 'asc' },
        { date: 'desc' },
      ],
    });
  }
  
  async findAllMaintenanceLogs(): Promise<MaintenanceLog[]> {
    return this.prisma.maintenanceLog.findMany({
      include: {
        vehicle: true,
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { date: 'desc' },
      ],
    });
  }
  
  async findOneMaintenanceLog(id: string): Promise<MaintenanceLog | null> {
    return this.prisma.maintenanceLog.findUnique({
      where: { id },
      include: {
        vehicle: true,
      },
    });
  }

  async createMaintenanceLog(vehicleId: string, data: {
    type: MaintenanceType;
    category: MaintenanceCategory;
    description: string;
    status: MaintenanceStatus;
    date?: Date | string;
    scheduledDate: Date | string;
    mileage?: number;
    partsCost?: number;
    laborCost?: number;
    technician?: string;
    notes?: string;
    nextMaintenanceDate?: Date | string;
    nextMaintenanceMileage?: number;
  }): Promise<MaintenanceLog> {
    const cost = (data.partsCost || 0) + (data.laborCost || 0);
    
    const maintenanceData: Prisma.MaintenanceLogCreateInput = {
      type: data.type,
      category: data.category,
      description: data.description,
      status: data.status,
      date: data.date ? new Date(data.date) : new Date(),
      scheduledDate: new Date(data.scheduledDate),
      mileage: data.mileage ?? null,
      cost: cost > 0 ? cost : null,
      partsCost: data.partsCost ?? null,
      laborCost: data.laborCost ?? null,
      technician: data.technician ?? null,
      notes: data.notes ?? null,
      nextMaintenanceDate: data.nextMaintenanceDate ? new Date(data.nextMaintenanceDate) : null,
      nextMaintenanceMileage: data.nextMaintenanceMileage ?? null,
      vehicle: {
        connect: { id: vehicleId }
      }
    };

    const log = await this.prisma.maintenanceLog.create({
      data: maintenanceData,
    });

    // Update vehicle's lastMaintenance date if this is a completed maintenance
    if (data.status === MaintenanceStatus.COMPLETED && data.date) {
      await this.prisma.vehicle.update({
        where: { id: vehicleId },
        data: { 
          lastMaintenance: new Date(data.date),
          status: VehicleStatus.AVAILABLE,
        },
      });
    }

    // If maintenance is scheduled or in progress, update vehicle status
    if (data.status === MaintenanceStatus.SCHEDULED || data.status === MaintenanceStatus.IN_PROGRESS) {
      await this.prisma.vehicle.update({
        where: { id: vehicleId },
        data: { status: VehicleStatus.MAINTENANCE },
      });
    }

    return log;
  }

  async updateMaintenanceLog(id: string, data: {
    type?: MaintenanceType;
    category?: MaintenanceCategory;
    description?: string;
    status?: MaintenanceStatus;
    date?: Date | string;
    scheduledDate?: Date | string;
    mileage?: number;
    partsCost?: number;
    laborCost?: number;
    technician?: string;
    notes?: string;
    nextMaintenanceDate?: Date | string;
    nextMaintenanceMileage?: number;
  }): Promise<MaintenanceLog> {
    // Calculate total cost if parts or labor cost is provided
    const currentLog = await this.prisma.maintenanceLog.findUnique({
      where: { id },
      select: { partsCost: true, laborCost: true },
    });
    
    const updateData: Prisma.MaintenanceLogUpdateInput = {
      ...(data.type && { type: data.type }),
      ...(data.category && { category: data.category }),
      ...(data.description && { description: data.description }),
      ...(data.status && { status: data.status }),
      ...(data.date && { date: new Date(data.date) }),
      ...(data.scheduledDate && { scheduledDate: new Date(data.scheduledDate) }),
      ...(data.mileage !== undefined && { mileage: data.mileage }),
      ...(data.technician !== undefined && { technician: data.technician }),
      ...(data.notes !== undefined && { notes: data.notes }),
      ...(data.nextMaintenanceDate && { nextMaintenanceDate: new Date(data.nextMaintenanceDate) }),
      ...(data.nextMaintenanceMileage !== undefined && { nextMaintenanceMileage: data.nextMaintenanceMileage }),
    };

    if (data.partsCost !== undefined || data.laborCost !== undefined) {
      const newPartsCost = data.partsCost ?? currentLog?.partsCost ?? 0;
      const newLaborCost = data.laborCost ?? currentLog?.laborCost ?? 0;
      const totalCost = newPartsCost + newLaborCost;
      
      updateData.partsCost = data.partsCost;
      updateData.laborCost = data.laborCost;
      updateData.cost = totalCost > 0 ? totalCost : undefined;
    }

    const log = await this.prisma.maintenanceLog.update({
      where: { id },
      data: updateData,
    });

    // Update vehicle status based on maintenance status
    if (data.status) {
      const vehicle = await this.prisma.maintenanceLog.findUnique({
        where: { id },
        select: { vehicleId: true },
      });

      if (vehicle) {
        if (data.status === MaintenanceStatus.COMPLETED) {
          await this.prisma.vehicle.update({
            where: { id: vehicle.vehicleId },
            data: { 
              status: VehicleStatus.AVAILABLE,
              lastMaintenance: data.date ? new Date(data.date) : new Date(),
            },
          });
        } else if (data.status === MaintenanceStatus.SCHEDULED || data.status === MaintenanceStatus.IN_PROGRESS) {
          await this.prisma.vehicle.update({
            where: { id: vehicle.vehicleId },
            data: { status: VehicleStatus.MAINTENANCE },
          });
        }
      }
    }

    return log;
  }

  async deleteMaintenanceLog(id: string): Promise<MaintenanceLog> {
    const log = await this.prisma.maintenanceLog.findUnique({
      where: { id },
      include: { vehicle: true },
    });

    if (log && (log.status === MaintenanceStatus.SCHEDULED || log.status === MaintenanceStatus.IN_PROGRESS)) {
      // Check if there are other active maintenance logs for this vehicle
      const otherActiveLogs = await this.prisma.maintenanceLog.count({
        where: {
          vehicleId: log.vehicleId,
          id: { not: id },
          status: {
            in: [MaintenanceStatus.SCHEDULED, MaintenanceStatus.IN_PROGRESS],
          },
        },
      });

      // If this was the last active maintenance log, set vehicle status back to AVAILABLE
      if (otherActiveLogs === 0) {
        await this.prisma.vehicle.update({
          where: { id: log.vehicleId },
          data: { status: VehicleStatus.AVAILABLE },
        });
      }
    }

    return this.prisma.maintenanceLog.delete({
      where: { id },
    });
  }

  async getUpcomingMaintenance(): Promise<MaintenanceLog[]> {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return this.prisma.maintenanceLog.findMany({
      where: {
        OR: [
          {
            status: MaintenanceStatus.SCHEDULED,
            scheduledDate: {
              lte: thirtyDaysFromNow,
            },
          },
          {
            status: MaintenanceStatus.COMPLETED,
            nextMaintenanceDate: {
              lte: thirtyDaysFromNow,
            },
          },
        ],
      },
      include: {
        vehicle: true,
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { nextMaintenanceDate: 'asc' },
      ],
    });
  }
}
