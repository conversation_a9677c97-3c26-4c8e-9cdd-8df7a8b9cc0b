import { Injectable } from '@nestjs/common';
import { VehicleReview, Prisma } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class ReviewsService {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<VehicleReview[]> {
    return this.prisma.vehicleReview.findMany({
      orderBy: { scheduledDate: 'asc' },
    });
  }

  async findOne(id: string): Promise<VehicleReview | null> {
    return this.prisma.vehicleReview.findUnique({
      where: { id },
    });
  }

  async findAllByVehicle(vehicleId: string): Promise<VehicleReview[]> {
    return this.prisma.vehicleReview.findMany({
      where: { vehicleId },
      orderBy: { scheduledDate: 'asc' },
    });
  }

  async create(data: Prisma.VehicleReviewCreateInput): Promise<VehicleReview> {
    return this.prisma.vehicleReview.create({
      data,
    });
  }

  async update(id: string, data: Prisma.VehicleReviewUpdateInput): Promise<VehicleReview> {
    return this.prisma.vehicleReview.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<VehicleReview> {
    return this.prisma.vehicleReview.delete({
      where: { id },
    });
  }

  async getUpcomingInspections(days = 14): Promise<VehicleReview[]> {
    const today = new Date();
    const threshold = new Date();
    threshold.setDate(today.getDate() + days);
    
    return this.prisma.vehicleReview.findMany({
      where: {
        scheduledDate: {
          gte: today,
          lte: threshold,
        },
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS'],
        },
      },
      orderBy: {
        scheduledDate: 'asc',
      },
    });
  }
}
