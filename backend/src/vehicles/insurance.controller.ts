import { Controller, Get, Post, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { InsuranceService } from './insurance.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateInsuranceDto } from '../insurance/dto/create-insurance.dto';
import { UpdateInsuranceDto } from '../insurance/dto/update-insurance.dto';
import { InsurancePolicy, InsuranceType, PolicyStatus } from '@prisma/client';

@Controller('insurance')
@UseGuards(JwtAuthGuard)
export class InsuranceController {
  constructor(private readonly insuranceService: InsuranceService) {}

  @Get()
  findAll(@Query('vehicleId') vehicleId?: string): Promise<InsurancePolicy[]> {
    if (vehicleId) {
      return this.insuranceService.findAllByVehicle(vehicleId);
    }
    return this.insuranceService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<InsurancePolicy | null> {
    return this.insuranceService.findOne(id);
  }

  @Post()
  create(@Body() createInsuranceDto: CreateInsuranceDto): Promise<InsurancePolicy> {
    // Format dates as ISO-8601 DateTime strings
    const formattedData = {
      vehicle: {
        connect: { id: createInsuranceDto.vehicleId }
      },
      policyNumber: createInsuranceDto.policyNumber,
      provider: createInsuranceDto.provider,
      type: createInsuranceDto.type,
      // Convert date strings to ISO DateTime format
      startDate: new Date(createInsuranceDto.startDate).toISOString(),
      endDate: new Date(createInsuranceDto.endDate).toISOString(),
      premium: createInsuranceDto.premium,
      coverage: createInsuranceDto.coverage,
      deductible: createInsuranceDto.deductible,
      status: createInsuranceDto.status,
      notes: createInsuranceDto.notes,
    };
    
    return this.insuranceService.create(formattedData);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateInsuranceDto: UpdateInsuranceDto,
  ): Promise<InsurancePolicy> {
    // Format the data and convert dates to ISO format
    const formattedData: any = { ...updateInsuranceDto };
    
    if (updateInsuranceDto.startDate) {
      formattedData.startDate = new Date(updateInsuranceDto.startDate).toISOString();
    }
    
    if (updateInsuranceDto.endDate) {
      formattedData.endDate = new Date(updateInsuranceDto.endDate).toISOString();
    }
    
    return this.insuranceService.update(id, formattedData);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<InsurancePolicy> {
    return this.insuranceService.delete(id);
  }

  @Get('upcoming/renewals')
  getUpcomingRenewals(): Promise<InsurancePolicy[]> {
    return this.insuranceService.getUpcomingRenewals();
  }
}
