import { Injectable } from '@nestjs/common';
import { InsurancePolicy, Prisma } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class InsuranceService {
  constructor(private prisma: PrismaService) {}

  async findAll(): Promise<InsurancePolicy[]> {
    return this.prisma.insurancePolicy.findMany({
      orderBy: { endDate: 'asc' },
    });
  }

  async findOne(id: string): Promise<InsurancePolicy | null> {
    return this.prisma.insurancePolicy.findUnique({
      where: { id },
    });
  }

  async findAllByVehicle(vehicleId: string): Promise<InsurancePolicy[]> {
    return this.prisma.insurancePolicy.findMany({
      where: { vehicleId },
      orderBy: { endDate: 'asc' },
    });
  }

  async create(data: Prisma.InsurancePolicyCreateInput): Promise<InsurancePolicy> {
    return this.prisma.insurancePolicy.create({
      data,
    });
  }

  async update(id: string, data: Prisma.InsurancePolicyUpdateInput): Promise<InsurancePolicy> {
    return this.prisma.insurancePolicy.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<InsurancePolicy> {
    return this.prisma.insurancePolicy.delete({
      where: { id },
    });
  }

  async getUpcomingRenewals(days = 30): Promise<InsurancePolicy[]> {
    const today = new Date();
    const threshold = new Date();
    threshold.setDate(today.getDate() + days);
    
    return this.prisma.insurancePolicy.findMany({
      where: {
        endDate: {
          gte: today,
          lte: threshold,
        },
        status: 'ACTIVE',
      },
      orderBy: {
        endDate: 'asc',
      },
    });
  }
}
