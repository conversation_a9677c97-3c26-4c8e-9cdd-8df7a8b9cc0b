import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  MaintenanceLog, 
  MaintenanceType, 
  MaintenanceCategory, 
  MaintenanceStatus, 
  VehicleType,
  TrailerType 
} from '@prisma/client';

interface CreateMaintenanceInput {
  vehicleId: string;
  type: MaintenanceType;
  category: MaintenanceCategory;
  description: string;
  status?: MaintenanceStatus;
  date?: string;
  scheduledDate: string;
  mileage?: number;
  partsCost?: number;
  laborCost?: number;
  technician?: string;
  notes?: string;
  nextMaintenanceDate?: string;
  nextMaintenanceMileage?: number;
}

@Injectable()
export class EnhancedMaintenanceService {
  constructor(private prisma: PrismaService) {}

  // Get maintenance categories appropriate for vehicle type
  getMaintenanceCategoriesForVehicleType(vehicleType: VehicleType): MaintenanceCategory[] {
    const commonCategories: MaintenanceCategory[] = [
      'OTHER',
      'BRAKES',
      'TIRES',
      'ELECTRICAL'
    ];

    if (vehicleType === VehicleType.TRUCK) {
      return [
        ...commonCategories,
        'ENGINE',
        'TRANSMISSION',
        'COOLING_SYSTEM',
        'FUEL_SYSTEM',
        'EXHAUST_SYSTEM'
      ];
    } else if (vehicleType === VehicleType.TRAILER) {
      return [
        ...commonCategories,
        'SUSPENSION_AXLES',
        'CARGO_AREA',
        'REFRIGERATION_UNIT',
        'HYDRAULIC_SYSTEMS',
        'LIGHTING_SYSTEM'
      ];
    }

    return commonCategories;
  }

  // Get maintenance recommendations based on vehicle type and mileage
  async getMaintenanceRecommendations(vehicleId: string): Promise<any[]> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      include: {
        maintenanceLogs: {
          orderBy: { date: 'desc' },
          take: 10
        }
      }
    });

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    const recommendations: any[] = [];
    const currentMileage = vehicle.mileage || 0;

    if (vehicle.vehicleType === VehicleType.TRUCK) {
      // Truck-specific recommendations
      const truckRecommendations = this.getTruckMaintenanceRecommendations(currentMileage, vehicle.maintenanceLogs);
      recommendations.push(...truckRecommendations);
    } else if (vehicle.vehicleType === VehicleType.TRAILER) {
      // Trailer-specific recommendations
      const trailerRecommendations = this.getTrailerMaintenanceRecommendations(currentMileage, vehicle.maintenanceLogs);
      recommendations.push(...trailerRecommendations);
    }

    return recommendations;
  }

  private getTruckMaintenanceRecommendations(mileage: number, logs: MaintenanceLog[]): any[] {
    const recommendations: any[] = [];

    // Oil change every 10,000 miles
    const lastOilChange = logs.find(log => 
      log.category === 'ENGINE' && log.description.toLowerCase().includes('oil')
    );
    if (!lastOilChange || (mileage - (lastOilChange.mileage || 0)) > 10000) {
      recommendations.push({
        type: 'PREVENTIVE_MAINTENANCE',
        category: 'ENGINE',
        description: 'Oil and filter change',
        priority: 'HIGH',
        estimatedCost: 150,
        dueAtMileage: Math.ceil(mileage / 10000) * 10000
      });
    }

    // Transmission service every 50,000 miles
    const lastTransmissionService = logs.find(log => log.category === 'TRANSMISSION');
    if (!lastTransmissionService || (mileage - (lastTransmissionService.mileage || 0)) > 50000) {
      recommendations.push({
        type: 'PREVENTIVE_MAINTENANCE',
        category: 'TRANSMISSION',
        description: 'Transmission fluid and filter service',
        priority: 'MEDIUM',
        estimatedCost: 300,
        dueAtMileage: Math.ceil(mileage / 50000) * 50000
      });
    }

    // Brake inspection every 25,000 miles
    const lastBrakeInspection = logs.find(log => log.category === 'BRAKES');
    if (!lastBrakeInspection || (mileage - (lastBrakeInspection.mileage || 0)) > 25000) {
      recommendations.push({
        type: 'INSPECTION',
        category: 'BRAKES',
        description: 'Brake system inspection and service',
        priority: 'HIGH',
        estimatedCost: 200,
        dueAtMileage: Math.ceil(mileage / 25000) * 25000
      });
    }

    return recommendations;
  }

  private getTrailerMaintenanceRecommendations(mileage: number, logs: MaintenanceLog[]): any[] {
    const recommendations: any[] = [];

    // Brake inspection every 30,000 miles
    const lastBrakeInspection = logs.find(log => log.category === 'BRAKES');
    if (!lastBrakeInspection || (mileage - (lastBrakeInspection.mileage || 0)) > 30000) {
      recommendations.push({
        type: 'INSPECTION',
        category: 'BRAKES',
        description: 'Trailer brake system inspection',
        priority: 'HIGH',
        estimatedCost: 150,
        dueAtMileage: Math.ceil(mileage / 30000) * 30000
      });
    }

    // Suspension/axle inspection every 40,000 miles
    const lastSuspensionCheck = logs.find(log => log.category === 'SUSPENSION_AXLES');
    if (!lastSuspensionCheck || (mileage - (lastSuspensionCheck.mileage || 0)) > 40000) {
      recommendations.push({
        type: 'INSPECTION',
        category: 'SUSPENSION_AXLES',
        description: 'Suspension and axle inspection',
        priority: 'MEDIUM',
        estimatedCost: 100,
        dueAtMileage: Math.ceil(mileage / 40000) * 40000
      });
    }

    // Refrigeration unit service (if applicable)
    // This would need to check if the trailer has refrigeration
    recommendations.push({
      type: 'PREVENTIVE_MAINTENANCE',
      category: 'REFRIGERATION_UNIT',
      description: 'Refrigeration unit service and inspection',
      priority: 'MEDIUM',
      estimatedCost: 250,
      note: 'Only applicable for refrigerated trailers'
    });

    return recommendations;
  }

  // Get maintenance history by vehicle type
  async getMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]> {
    return this.prisma.maintenanceLog.findMany({
      where: {
        vehicle: {
          vehicleType: vehicleType
        }
      },
      include: {
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
            trailerType: true,
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });
  }

  // Get maintenance statistics by vehicle type
  async getMaintenanceStatsByVehicleType(): Promise<any> {
    const truckStats = await this.prisma.maintenanceLog.aggregate({
      where: {
        vehicle: {
          vehicleType: VehicleType.TRUCK
        }
      },
      _avg: {
        cost: true,
        partsCost: true,
        laborCost: true,
      },
      _sum: {
        cost: true,
        partsCost: true,
        laborCost: true,
      },
      _count: {
        id: true,
      }
    });

    const trailerStats = await this.prisma.maintenanceLog.aggregate({
      where: {
        vehicle: {
          vehicleType: VehicleType.TRAILER
        }
      },
      _avg: {
        cost: true,
        partsCost: true,
        laborCost: true,
      },
      _sum: {
        cost: true,
        partsCost: true,
        laborCost: true,
      },
      _count: {
        id: true,
      }
    });

    return {
      trucks: {
        totalMaintenance: truckStats._count.id,
        averageCost: truckStats._avg.cost,
        totalCost: truckStats._sum.cost,
        averagePartsCost: truckStats._avg.partsCost,
        averageLaborCost: truckStats._avg.laborCost,
      },
      trailers: {
        totalMaintenance: trailerStats._count.id,
        averageCost: trailerStats._avg.cost,
        totalCost: trailerStats._sum.cost,
        averagePartsCost: trailerStats._avg.partsCost,
        averageLaborCost: trailerStats._avg.laborCost,
      }
    };
  }

  // Create maintenance with vehicle type validation
  async createMaintenance(data: CreateMaintenanceInput): Promise<MaintenanceLog> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: data.vehicleId }
    });

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    // Validate category is appropriate for vehicle type
    const validCategories = this.getMaintenanceCategoriesForVehicleType(vehicle.vehicleType);
    if (!validCategories.includes(data.category)) {
      throw new BadRequestException(
        `Category ${data.category} is not valid for ${vehicle.vehicleType} vehicles. Valid categories: ${validCategories.join(', ')}`
      );
    }

    // Calculate total cost
    const totalCost = (data.partsCost || 0) + (data.laborCost || 0);

    return this.prisma.maintenanceLog.create({
      data: {
        vehicleId: data.vehicleId,
        type: data.type,
        category: data.category,
        description: data.description,
        status: data.status || MaintenanceStatus.SCHEDULED,
        date: data.date ? new Date(data.date) : null,
        scheduledDate: new Date(data.scheduledDate),
        mileage: data.mileage,
        partsCost: data.partsCost,
        laborCost: data.laborCost,
        cost: totalCost,
        technician: data.technician,
        notes: data.notes,
        nextMaintenanceDate: data.nextMaintenanceDate ? new Date(data.nextMaintenanceDate) : null,
        nextMaintenanceMileage: data.nextMaintenanceMileage,
      },
      include: {
        vehicle: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        }
      }
    });
  }

  // Get upcoming maintenance by vehicle type
  async getUpcomingMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]> {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return this.prisma.maintenanceLog.findMany({
      where: {
        vehicle: {
          vehicleType: vehicleType
        },
        status: {
          in: [MaintenanceStatus.SCHEDULED, MaintenanceStatus.IN_PROGRESS]
        },
        scheduledDate: {
          lte: thirtyDaysFromNow
        }
      },
      include: {
        vehicle: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        }
      },
      orderBy: {
        scheduledDate: 'asc'
      }
    });
  }
}
