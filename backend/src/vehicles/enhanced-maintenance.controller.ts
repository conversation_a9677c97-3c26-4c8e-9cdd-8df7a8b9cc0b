import { Controller, Get, Post, Body, Param, UseGuards, Query } from '@nestjs/common';
import { EnhancedMaintenanceService } from './enhanced-maintenance.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MaintenanceLog, VehicleType, MaintenanceCategory } from '@prisma/client';

@Controller('enhanced-maintenance')
@UseGuards(JwtAuthGuard)
export class EnhancedMaintenanceController {
  constructor(private readonly enhancedMaintenanceService: EnhancedMaintenanceService) {}

  @Get('categories/:vehicleType')
  getMaintenanceCategoriesForVehicleType(
    @Param('vehicleType') vehicleType: VehicleType
  ): MaintenanceCategory[] {
    return this.enhancedMaintenanceService.getMaintenanceCategoriesForVehicleType(vehicleType);
  }

  @Get('recommendations/:vehicleId')
  getMaintenanceRecommendations(@Param('vehicleId') vehicleId: string): Promise<any[]> {
    return this.enhancedMaintenanceService.getMaintenanceRecommendations(vehicleId);
  }

  @Get('by-vehicle-type')
  getMaintenanceByVehicleType(
    @Query('type') vehicleType: VehicleType
  ): Promise<MaintenanceLog[]> {
    return this.enhancedMaintenanceService.getMaintenanceByVehicleType(vehicleType);
  }

  @Get('stats/by-vehicle-type')
  getMaintenanceStatsByVehicleType(): Promise<any> {
    return this.enhancedMaintenanceService.getMaintenanceStatsByVehicleType();
  }

  @Get('upcoming/trucks')
  getUpcomingTruckMaintenance(): Promise<MaintenanceLog[]> {
    return this.enhancedMaintenanceService.getUpcomingMaintenanceByVehicleType(VehicleType.TRUCK);
  }

  @Get('upcoming/trailers')
  getUpcomingTrailerMaintenance(): Promise<MaintenanceLog[]> {
    return this.enhancedMaintenanceService.getUpcomingMaintenanceByVehicleType(VehicleType.TRAILER);
  }

  @Post()
  createMaintenance(@Body() createMaintenanceDto: any): Promise<MaintenanceLog> {
    return this.enhancedMaintenanceService.createMaintenance(createMaintenanceDto);
  }
}
