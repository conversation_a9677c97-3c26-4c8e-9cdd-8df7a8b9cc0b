import { Module } from '@nestjs/common';
import { VehiclesService } from './vehicles.service';
import { VehicleAssignmentService } from './vehicle-assignment.service';
import { TruckTrailerAssignmentService } from './truck-trailer-assignment.service';
import { EnhancedMaintenanceService } from './enhanced-maintenance.service';
import { InsuranceService } from './insurance.service';
import { ReviewsService } from './reviews.service';
import { VehiclesController } from './vehicles.controller';
import { TruckTrailerAssignmentController } from './truck-trailer-assignment.controller';
import { EnhancedMaintenanceController } from './enhanced-maintenance.controller';
import { InsuranceController } from './insurance.controller';
import { ReviewsController } from './reviews.controller';
import { MaintenanceController } from './maintenance.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [
    VehiclesController,
    TruckTrailerAssignmentController,
    EnhancedMaintenanceController,
    InsuranceController,
    ReviewsController,
    MaintenanceController
  ],
  providers: [
    VehiclesService,
    VehicleAssignmentService,
    TruckTrailerAssignmentService,
    EnhancedMaintenanceService,
    InsuranceService,
    ReviewsService
  ],
  exports: [
    VehiclesService,
    VehicleAssignmentService,
    TruckTrailerAssignmentService,
    EnhancedMaintenanceService,
    InsuranceService,
    ReviewsService
  ],
})
export class VehiclesModule {}
