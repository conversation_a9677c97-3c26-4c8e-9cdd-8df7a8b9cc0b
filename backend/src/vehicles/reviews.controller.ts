import { Controller, Get, Post, Patch, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateReviewDto } from '../reviews/dto/create-review.dto';
import { UpdateReviewDto } from '../reviews/dto/update-review.dto';
import { VehicleReview, ReviewType, ReviewStatus } from '@prisma/client';

@Controller('reviews')
@UseGuards(JwtAuthGuard)
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Get()
  findAll(@Query('vehicleId') vehicleId?: string): Promise<VehicleReview[]> {
    if (vehicleId) {
      return this.reviewsService.findAllByVehicle(vehicleId);
    }
    return this.reviewsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<VehicleReview | null> {
    return this.reviewsService.findOne(id);
  }

  @Post()
  create(@Body() createReviewDto: CreateReviewDto): Promise<VehicleReview> {
    // Format dates as ISO-8601 DateTime strings
    const formattedData = {
      vehicle: {
        connect: { id: createReviewDto.vehicleId }
      },
      reviewType: createReviewDto.reviewType,
      reviewBy: createReviewDto.reviewBy,
      scheduledDate: new Date(createReviewDto.scheduledDate).toISOString(),
      completedDate: createReviewDto.completedDate ? new Date(createReviewDto.completedDate).toISOString() : undefined,
      location: createReviewDto.location,
      status: createReviewDto.status,
      findings: createReviewDto.findings,
      recommendations: createReviewDto.recommendations,
      nextReviewDate: createReviewDto.nextReviewDate ? new Date(createReviewDto.nextReviewDate).toISOString() : undefined,
      documents: createReviewDto.documents || [],
    };
    
    return this.reviewsService.create(formattedData);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateReviewDto: UpdateReviewDto,
  ): Promise<VehicleReview> {
    // Format the data and convert dates to ISO format
    const formattedData: any = { ...updateReviewDto };
    
    if (updateReviewDto.scheduledDate) {
      formattedData.scheduledDate = new Date(updateReviewDto.scheduledDate).toISOString();
    }
    
    if (updateReviewDto.completedDate) {
      formattedData.completedDate = new Date(updateReviewDto.completedDate).toISOString();
    }
    
    if (updateReviewDto.nextReviewDate) {
      formattedData.nextReviewDate = new Date(updateReviewDto.nextReviewDate).toISOString();
    }
    
    return this.reviewsService.update(id, formattedData);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<VehicleReview> {
    return this.reviewsService.delete(id);
  }

  @Get('upcoming/inspections')
  getUpcomingInspections(): Promise<VehicleReview[]> {
    return this.reviewsService.getUpcomingInspections();
  }
}
