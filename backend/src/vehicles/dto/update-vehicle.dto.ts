import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsDateString, Length } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { CreateVehicleDto } from './create-vehicle.dto';
import { VehicleStatus } from '@prisma/client';

export class UpdateVehicleDto extends PartialType(CreateVehicleDto) {
  @IsOptional()
  @IsEnum(VehicleStatus, {
    message: 'Status must be one of: AVAILABLE, ASSIGNED, MAINTENANCE, OUT_OF_SERVICE'
  })
  status?: VehicleStatus;

  @IsOptional()
  @IsDateString({}, { message: 'Last maintenance must be a valid date string' })
  lastMaintenance?: string;
}
