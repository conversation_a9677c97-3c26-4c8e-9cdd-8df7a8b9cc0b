import { <PERSON>S<PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsEnum, IsDateString, <PERSON>, Min, IsNotEmpty } from 'class-validator';
import { VehicleStatus } from '@prisma/client';
import { IsNotPastDate, IsAfterStartDate } from '../../common/validators/custom-validators';
import { Transform } from 'class-transformer';

export class CreateAssignmentDto {
  @IsNotEmpty({ message: 'Driver ID is required' })
  @IsString({ message: 'Driver ID must be a string' })
  @Length(25, 25, { message: 'Driver ID must be a valid CUID' })
  driverId: string;

  @IsNotEmpty({ message: 'Vehicle ID is required' })
  @IsString({ message: 'Vehicle ID must be a string' })
  @Length(25, 25, { message: 'Vehicle ID must be a valid CUID' })
  vehicleId: string;

  @IsNotEmpty({ message: 'Start date is required' })
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  @IsNotPastDate({ message: 'Start date cannot be in the past' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  startDate: string;

  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  @IsAfterStartDate('startDate', { message: 'End date must be after start date' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  endDate?: string;

  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @Length(0, 1000, { message: 'Notes cannot exceed 1000 characters' })
  @Transform(({ value }) => value?.trim())
  notes?: string;

  @IsOptional()
  @IsEnum(['REGULAR', 'TEMPORARY', 'EMERGENCY', 'MAINTENANCE', 'TRAINING'], {
    message: 'Type must be one of: REGULAR, TEMPORARY, EMERGENCY, MAINTENANCE, TRAINING'
  })
  type?: string;

  @IsOptional()
  @IsEnum(['LOW', 'NORMAL', 'HIGH', 'URGENT'], {
    message: 'Priority must be one of: LOW, NORMAL, HIGH, URGENT'
  })
  priority?: string;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Mileage must be a valid number' })
  @Min(0, { message: 'Mileage cannot be negative' })
  @Transform(({ value }) => value ? Number(value) : value)
  mileage?: number;
}
