import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, Is<PERSON><PERSON>al, Is<PERSON>num, <PERSON>, <PERSON>, IsDateString, Length, IsBoolean, ValidateIf, IsNotEmpty, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { VehicleType, TrailerType } from '@prisma/client';
import { IsValidPlateNumber } from '../../common/validators/custom-validators';

export class CreateVehicleDto {
  @IsNotEmpty({ message: 'Plate number is required' })
  @IsString({ message: 'Plate number must be a string' })
  @Length(1, 20, { message: 'Plate number must be between 1 and 20 characters' })
  @IsValidPlateNumber({ message: 'Plate number format is invalid' })
  @Transform(({ value }) => value?.trim().toUpperCase())
  plateNumber: string;

  @IsNotEmpty({ message: 'Make is required' })
  @IsString({ message: 'Make must be a string' })
  @Length(1, 50, { message: 'Make must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  make: string;

  @IsNotEmpty({ message: 'Model is required' })
  @IsString({ message: 'Model must be a string' })
  @Length(1, 50, { message: 'Model must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  model: string;

  @IsNotEmpty({ message: 'Year is required' })
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Year must be a valid number' })
  @Min(1900, { message: 'Year must be 1900 or later' })
  @Max(new Date().getFullYear() + 1, { message: 'Year cannot be in the future' })
  @Transform(({ value }) => value ? Number(value) : value)
  year: number;

  // Vehicle Type Classification
  @IsNotEmpty({ message: 'Vehicle type is required' })
  @IsEnum(VehicleType, {
    message: 'Vehicle type must be either TRUCK or TRAILER'
  })
  vehicleType: VehicleType;

  @IsOptional()
  @IsString({ message: 'VIN must be a string' })
  @Length(17, 17, { message: 'VIN must be exactly 17 characters' })
  @Matches(/^[A-HJ-NPR-Z0-9]{17}$/i, { message: 'VIN contains invalid characters (I, O, Q not allowed)' })
  @Transform(({ value }) => value?.trim().toUpperCase())
  vin?: string;

  @IsOptional()
  @IsString({ message: 'Color must be a string' })
  @Length(1, 30, { message: 'Color must be between 1 and 30 characters' })
  @Transform(({ value }) => value?.trim())
  color?: string;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Mileage must be a valid number' })
  @Min(0, { message: 'Mileage cannot be negative' })
  @Max(9999999, { message: 'Mileage seems unreasonably high' })
  @Transform(({ value }) => value ? Number(value) : value)
  mileage?: number;

  @IsOptional()
  @IsEnum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID', 'OTHER'], {
    message: 'Fuel type must be one of: GASOLINE, DIESEL, ELECTRIC, HYBRID, OTHER'
  })
  fuelType?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Purchase date must be a valid ISO date string' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  purchaseDate?: string;

  // Truck-specific fields
  @ValidateIf(o => o.vehicleType === 'TRUCK')
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Engine type must be between 1 and 50 characters' })
  engineType?: string;

  @ValidateIf(o => o.vehicleType === 'TRUCK')
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Transmission must be between 1 and 50 characters' })
  transmission?: string;

  @ValidateIf(o => o.vehicleType === 'TRUCK')
  @IsOptional()
  @IsNumber({}, { message: 'Fuel capacity must be a valid number' })
  @Min(0, { message: 'Fuel capacity cannot be negative' })
  fuelCapacity?: number;

  @ValidateIf(o => o.vehicleType === 'TRUCK')
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Axle configuration must be between 1 and 50 characters' })
  axleConfiguration?: string;

  @ValidateIf(o => o.vehicleType === 'TRUCK')
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Cab configuration must be between 1 and 50 characters' })
  cabConfiguration?: string;

  // Trailer-specific fields
  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsEnum(TrailerType, {
    message: 'Trailer type must be one of: DRY_VAN, REFRIGERATED, FLATBED, TANKER, LOWBOY, STEP_DECK, CONTAINER_CHASSIS'
  })
  trailerType?: TrailerType;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsNumber({}, { message: 'Cargo capacity must be a valid number' })
  @Min(0, { message: 'Cargo capacity cannot be negative' })
  cargoCapacity?: number;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsNumber({}, { message: 'Max weight must be a valid number' })
  @Min(0, { message: 'Max weight cannot be negative' })
  maxWeight?: number;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsNumber({}, { message: 'Length must be a valid number' })
  @Min(0, { message: 'Length cannot be negative' })
  length?: number;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsNumber({}, { message: 'Width must be a valid number' })
  @Min(0, { message: 'Width cannot be negative' })
  width?: number;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsNumber({}, { message: 'Height must be a valid number' })
  @Min(0, { message: 'Height cannot be negative' })
  height?: number;

  @ValidateIf(o => o.vehicleType === 'TRAILER')
  @IsOptional()
  @IsBoolean({ message: 'Has refrigeration must be a boolean value' })
  hasRefrigeration?: boolean;
}
