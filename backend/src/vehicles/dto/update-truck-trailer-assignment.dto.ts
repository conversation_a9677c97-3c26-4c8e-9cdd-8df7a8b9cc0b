import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum } from 'class-validator';
import { CreateTruckTrailerAssignmentDto } from './create-truck-trailer-assignment.dto';
import { AssignmentStatus } from '@prisma/client';

export class UpdateTruckTrailerAssignmentDto extends PartialType(CreateTruckTrailerAssignmentDto) {
  @IsOptional()
  @IsEnum(AssignmentStatus, {
    message: 'Status must be one of: ACTIVE, COMPLETED, CANCELLED, PENDING, ON_HOLD, DELAYED'
  })
  status?: AssignmentStatus;
}
