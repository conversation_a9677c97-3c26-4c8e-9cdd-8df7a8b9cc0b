import { IsString, IsOptional, IsDateString, Length, IsNotEmpty } from 'class-validator';
import { IsNotPastDate, IsAfterStartDate } from '../../common/validators/custom-validators';
import { Transform } from 'class-transformer';

export class CreateTruckTrailerAssignmentDto {
  @IsNotEmpty({ message: 'Truck ID is required' })
  @IsString({ message: 'Truck ID must be a string' })
  @Length(25, 25, { message: 'Truck ID must be a valid CUID' })
  truckId: string;

  @IsNotEmpty({ message: 'Trailer ID is required' })
  @IsString({ message: 'Trailer ID must be a string' })
  @Length(25, 25, { message: 'Trailer ID must be a valid CUID' })
  trailerId: string;

  @IsNotEmpty({ message: 'Start date is required' })
  @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
  @IsNotPastDate({ message: 'Start date cannot be in the past' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  startDate: string;

  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid ISO date string' })
  @IsAfterStartDate('startDate', { message: 'End date must be after start date' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  endDate?: string;

  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @Length(0, 1000, { message: 'Notes cannot exceed 1000 characters' })
  @Transform(({ value }) => value?.trim())
  notes?: string;

  @IsOptional()
  @IsString({ message: 'Assigned by must be a string' })
  @Length(25, 25, { message: 'Assigned by must be a valid CUID' })
  assignedBy?: string;
}
