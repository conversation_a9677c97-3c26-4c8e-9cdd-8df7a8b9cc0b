import { Controller, Get, Post, Patch, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { VehiclesService } from './vehicles.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateMaintenanceDto } from '../maintenance/dto/create-maintenance.dto';
import { UpdateMaintenanceDto } from '../maintenance/dto/update-maintenance.dto';
import { 
  MaintenanceLog, 
  MaintenanceType,
  MaintenanceCategory,
  MaintenanceStatus,
} from '@prisma/client';

@Controller('maintenance')
@UseGuards(JwtAuthGuard)
export class MaintenanceController {
  constructor(private readonly vehiclesService: VehiclesService) {}

  @Get()
  findAll(): Promise<MaintenanceLog[]> {
    return this.vehiclesService.findAllMaintenanceLogs();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<MaintenanceLog | null> {
    return this.vehiclesService.findOneMaintenanceLog(id);
  }

  @Post()
  create(@Body() createMaintenanceDto: CreateMaintenanceDto): Promise<MaintenanceLog> {
    // Format dates properly for the service
    const formattedData = {
      type: createMaintenanceDto.type,
      category: createMaintenanceDto.category || 'OTHER' as MaintenanceCategory,
      description: createMaintenanceDto.description,
      status: createMaintenanceDto.status || 'SCHEDULED' as MaintenanceStatus,
      date: createMaintenanceDto.date ? createMaintenanceDto.date : undefined,
      scheduledDate: createMaintenanceDto.scheduledDate || new Date().toISOString(),
      mileage: createMaintenanceDto.mileage,
      partsCost: createMaintenanceDto.partsCost,
      laborCost: createMaintenanceDto.laborCost,
      technician: createMaintenanceDto.technician,
      notes: createMaintenanceDto.notes,
      nextMaintenanceDate: createMaintenanceDto.nextMaintenanceDate,
      nextMaintenanceMileage: createMaintenanceDto.nextMaintenanceMileage,
    };

    return this.vehiclesService.createMaintenanceLog(createMaintenanceDto.vehicleId, formattedData);
  }  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateMaintenanceDto: UpdateMaintenanceDto,
  ): Promise<MaintenanceLog> {
    // Extract only the fields that are present in the DTO
    const updateData: any = {};
    
    if (updateMaintenanceDto.type !== undefined) updateData.type = updateMaintenanceDto.type;
    if (updateMaintenanceDto.category !== undefined) updateData.category = updateMaintenanceDto.category;
    if (updateMaintenanceDto.description !== undefined) updateData.description = updateMaintenanceDto.description;
    if (updateMaintenanceDto.status !== undefined) updateData.status = updateMaintenanceDto.status;
    if (updateMaintenanceDto.date !== undefined) updateData.date = updateMaintenanceDto.date;
    if (updateMaintenanceDto.scheduledDate !== undefined) updateData.scheduledDate = updateMaintenanceDto.scheduledDate;
    if (updateMaintenanceDto.mileage !== undefined) updateData.mileage = updateMaintenanceDto.mileage;
    if (updateMaintenanceDto.partsCost !== undefined) updateData.partsCost = updateMaintenanceDto.partsCost;
    if (updateMaintenanceDto.laborCost !== undefined) updateData.laborCost = updateMaintenanceDto.laborCost;
    if (updateMaintenanceDto.technician !== undefined) updateData.technician = updateMaintenanceDto.technician;
    if (updateMaintenanceDto.notes !== undefined) updateData.notes = updateMaintenanceDto.notes;
    if (updateMaintenanceDto.nextMaintenanceDate !== undefined) updateData.nextMaintenanceDate = updateMaintenanceDto.nextMaintenanceDate;
    if (updateMaintenanceDto.nextMaintenanceMileage !== undefined) updateData.nextMaintenanceMileage = updateMaintenanceDto.nextMaintenanceMileage;
    
    return this.vehiclesService.updateMaintenanceLog(id, updateData);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<MaintenanceLog> {
    return this.vehiclesService.deleteMaintenanceLog(id);
  }
}
