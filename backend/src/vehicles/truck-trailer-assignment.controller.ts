import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
  Query,
  Request
} from '@nestjs/common';
import { TruckTrailerAssignmentService } from './truck-trailer-assignment.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateTruckTrailerAssignmentDto } from './dto/create-truck-trailer-assignment.dto';
import { UpdateTruckTrailerAssignmentDto } from './dto/update-truck-trailer-assignment.dto';
import { TruckTrailerAssignment } from '@prisma/client';

@Controller('truck-trailer-assignments')
@UseGuards(JwtAuthGuard)
export class TruckTrailerAssignmentController {
  constructor(
    private readonly assignmentService: TruckTrailerAssignmentService
  ) {}

  @Get()
  async findAll(@Query('active') active?: string): Promise<TruckTrailerAssignment[]> {
    if (active === 'true') {
      return this.assignmentService.findActive();
    }
    return this.assignmentService.findAll();
  }

  @Get('active')
  async findActive(): Promise<TruckTrailerAssignment[]> {
    return this.assignmentService.findActive();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<TruckTrailerAssignment | null> {
    return this.assignmentService.findOne(id);
  }

  @Get('truck/:truckId')
  async findByTruck(@Param('truckId') truckId: string): Promise<TruckTrailerAssignment[]> {
    return this.assignmentService.findByTruck(truckId);
  }

  @Get('trailer/:trailerId')
  async findByTrailer(@Param('trailerId') trailerId: string): Promise<TruckTrailerAssignment[]> {
    return this.assignmentService.findByTrailer(trailerId);
  }

  @Post()
  async create(
    @Body() createAssignmentDto: CreateTruckTrailerAssignmentDto,
    @Request() req: any
  ): Promise<TruckTrailerAssignment> {
    // Set assignedBy to the current user's ID if not provided
    if (!createAssignmentDto.assignedBy) {
      createAssignmentDto.assignedBy = req.user.id;
    }
    return this.assignmentService.create(createAssignmentDto);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAssignmentDto: UpdateTruckTrailerAssignmentDto
  ): Promise<TruckTrailerAssignment> {
    return this.assignmentService.update(id, updateAssignmentDto);
  }

  @Patch(':id/complete')
  async complete(@Param('id') id: string): Promise<TruckTrailerAssignment> {
    return this.assignmentService.complete(id);
  }

  @Patch(':id/cancel')
  async cancel(@Param('id') id: string): Promise<TruckTrailerAssignment> {
    return this.assignmentService.cancel(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<TruckTrailerAssignment> {
    return this.assignmentService.delete(id);
  }
}
