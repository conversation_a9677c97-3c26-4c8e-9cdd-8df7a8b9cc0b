import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AlertType, AlertSeverity, SystemAlert } from '@prisma/client';

export interface CreateAlertDto {
  type: AlertType;
  message: string;
  severity: AlertSeverity;
}

export interface AlertFilters {
  type?: AlertType;
  severity?: AlertSeverity;
  resolved?: boolean;
  limit?: number;
}

@Injectable()
export class SystemAlertsService {
  private readonly logger = new Logger(SystemAlertsService.name);

  constructor(private prisma: PrismaService) {}

  async createAlert(data: CreateAlertDto): Promise<SystemAlert> {
    try {
      const alert = await this.prisma.systemAlert.create({
        data: {
          type: data.type,
          message: data.message,
          severity: data.severity,
        }
      });

      this.logger.log(`🚨 System alert created: ${data.type} - ${data.severity}`);
      
      return alert;
    } catch (error) {
      this.logger.error(`Failed to create system alert: ${error.message}`);
      throw error;
    }
  }

  async getAlerts(filters: AlertFilters = {}): Promise<SystemAlert[]> {
    const where: any = {};

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.severity) {
      where.severity = filters.severity;
    }

    if (filters.resolved !== undefined) {
      where.resolved = filters.resolved;
    }

    return this.prisma.systemAlert.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters.limit || 50,
    });
  }

  async getUnresolvedAlerts(): Promise<SystemAlert[]> {
    return this.getAlerts({ resolved: false });
  }

  async getHighPriorityAlerts(): Promise<SystemAlert[]> {
    return this.getAlerts({ 
      resolved: false, 
      severity: 'HIGH' 
    });
  }

  async resolveAlert(id: string, resolvedBy: string): Promise<SystemAlert> {
    const alert = await this.prisma.systemAlert.update({
      where: { id },
      data: {
        resolved: true,
        resolvedAt: new Date(),
        resolvedBy,
      }
    });

    this.logger.log(`✅ System alert resolved: ${id} by ${resolvedBy}`);

    return alert;
  }

  async deleteAlert(id: string): Promise<void> {
    await this.prisma.systemAlert.delete({
      where: { id }
    });

    this.logger.log(`🗑️ System alert deleted: ${id}`);
  }

  async getAlertStats(): Promise<{
    total: number;
    unresolved: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
  }> {
    const [total, unresolved, byType, bySeverity] = await Promise.all([
      this.prisma.systemAlert.count(),
      this.prisma.systemAlert.count({ where: { resolved: false } }),
      this.prisma.systemAlert.groupBy({
        by: ['type'],
        _count: { type: true },
        where: { resolved: false }
      }),
      this.prisma.systemAlert.groupBy({
        by: ['severity'],
        _count: { severity: true },
        where: { resolved: false }
      })
    ]);

    const typeStats: Record<string, number> = {};
    byType.forEach(item => {
      typeStats[item.type] = item._count.type;
    });

    const severityStats: Record<string, number> = {};
    bySeverity.forEach(item => {
      severityStats[item.severity] = item._count.severity;
    });

    return {
      total,
      unresolved,
      byType: typeStats,
      bySeverity: severityStats,
    };
  }
}
