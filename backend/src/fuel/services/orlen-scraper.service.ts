import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PrismaService } from '../../prisma/prisma.service';
import * as puppeteer from 'puppeteer';

export interface ScrapingResult {
  success: boolean;
  effectiveDate: Date;
  priceNet: number;
  priceGross: number;
  isCurrentDate: boolean;
  errorMessage?: string;
}

@Injectable()
export class OrlenScraperService {
  private readonly logger = new Logger(OrlenScraperService.name);
  private readonly MAX_RETRIES = 96; // 24 hours with 15-minute intervals
  private readonly RETRY_INTERVAL = 15 * 60 * 1000; // 15 minutes in ms
  private readonly VAT_RATE = 0.23; // 23% Polish VAT
  private readonly ORLEN_URL = 'https://www.orlen.pl/pl/dla-biznesu/hurtowe-ceny-paliw';

  constructor(private prisma: PrismaService) {}

  @Cron('0 8 * * *') // Every day at 8:00 AM
  async scheduledPriceFetch(): Promise<void> {
    this.logger.log('🕐 Starting scheduled fuel price fetch at 8:00 AM');
    await this.fetchDailyPricesWithRetry();
  }

  async fetchDailyPricesWithRetry(retryCount: number = 0): Promise<void> {
    try {
      const scrapingResult = await this.scrapeFuelPrice();
      
      if (scrapingResult.success) {
        await this.saveFuelPrice(scrapingResult);
        await this.logScrapingAttempt(scrapingResult, true, retryCount);
        this.logger.log(`✅ Fuel price scraped successfully: ${scrapingResult.priceNet}zł (net), ${scrapingResult.priceGross}zł (gross)`);
      } else {
        await this.handleScrapingFailure(scrapingResult, retryCount);
      }
    } catch (error) {
      await this.handleScrapingError(error, retryCount);
    }
  }

  private async scrapeFuelPrice(): Promise<ScrapingResult> {
    let browser: puppeteer.Browser | null = null;
    
    try {
      this.logger.log('🌐 Launching browser for Orlen price scraping');
      
      browser = await puppeteer.launch({ 
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
      
      const page = await browser.newPage();
      
      // Set user agent to avoid bot detection
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      this.logger.log(`📄 Navigating to ${this.ORLEN_URL}`);
      
      await page.goto(this.ORLEN_URL, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // Extract effective date and diesel price
      const effectiveDate = await this.extractEffectiveDate(page);
      const dieselPriceNet = await this.extractDieselPrice(page);
      
      const today = new Date();

      // Orlen shows prices effective for the current date
      // So if today is June 21st, we expect to find prices effective for June 21st
      const isCurrentDate = this.isSameDate(effectiveDate, today);

      const priceGross = dieselPriceNet * (1 + this.VAT_RATE);

      this.logger.log(`📅 Found effective date: ${effectiveDate.toDateString()}`);
      this.logger.log(`💰 Found diesel price: ${dieselPriceNet}zł (net), ${priceGross.toFixed(2)}zł (gross)`);
      this.logger.log(`🗓️ Expected date: ${today.toDateString()} (today's date for current prices)`);
      this.logger.log(`🗓️ Date match: ${isCurrentDate ? 'YES' : 'NO'} (today: ${today.toDateString()})`);

      return {
        success: isCurrentDate,
        effectiveDate, // Keep the original date from Orlen (e.g., June 21st)
        priceNet: dieselPriceNet,
        priceGross,
        isCurrentDate,
        errorMessage: isCurrentDate ? undefined : `Date mismatch: found ${effectiveDate.toDateString()}, expected ${today.toDateString()} (today's date for current prices)`
      };
      
    } catch (error) {
      this.logger.error(`❌ Error during scraping: ${error.message}`);
      throw error;
    } finally {
      if (browser) {
        await browser.close();
        this.logger.log('🔒 Browser closed');
      }
    }
  }

  private async extractEffectiveDate(page: puppeteer.Page): Promise<Date> {
    try {
      // Look for text containing "obowiązujące od dnia" followed by a date
      const dateText = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        for (const element of elements) {
          const text = element.textContent || '';
          if (text.includes('obowiązujące od dnia')) {
            return text;
          }
        }
        return null;
      });
      
      if (!dateText) {
        throw new Error('Could not find effective date text on page');
      }
      
      this.logger.log(`📝 Found date text: "${dateText}"`);
      
      // Extract date using regex: dd-mm-yyyy or dd.mm.yyyy
      const dateMatch = dateText.match(/(\d{1,2})[-.](\d{1,2})[-.](\d{4})/);
      if (!dateMatch) {
        throw new Error(`Could not extract date from text: "${dateText}"`);
      }
      
      const [, day, month, year] = dateMatch;
      // Create date at noon in Polish timezone to avoid timezone issues
      const extractedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0);

      this.logger.log(`📅 Extracted date: ${extractedDate.toDateString()}`);

      return extractedDate;
    } catch (error) {
      this.logger.error(`❌ Failed to extract effective date: ${error.message}`);
      throw new Error(`Failed to extract effective date: ${error.message}`);
    }
  }

  private async extractDieselPrice(page: puppeteer.Page): Promise<number> {
    try {
      // Look for diesel price in the table - specifically "Olej Napędowy Ekodiesel"
      const priceData = await page.evaluate(() => {
        // Get the table content
        const table = document.querySelector('table');
        if (!table) {
          return null;
        }

        const tableText = table.innerText;

        // Look for "Olej Napędowy Ekodiesel" line and extract the price
        const lines = tableText.split('\n');
        for (const line of lines) {
          if (line.includes('Olej Napędowy Ekodiesel')) {
            // Extract price from the line - format: "Olej Napędowy Ekodiesel 4 892"
            const priceMatch = line.match(/(\d+\s+\d+)$/);
            if (priceMatch) {
              return priceMatch[1];
            }
          }
        }

        // Fallback: look for any diesel-related price
        for (const line of lines) {
          if (line.toLowerCase().includes('olej napędowy') && !line.toLowerCase().includes('grzewczy')) {
            const priceMatch = line.match(/(\d+\s+\d+)$/);
            if (priceMatch) {
              return priceMatch[1];
            }
          }
        }

        return null;
      });

      if (!priceData) {
        throw new Error('Could not find diesel price on page');
      }

      this.logger.log(`💰 Found price text: "${priceData}"`);

      // Convert price text to number
      // Format: "4 892" -> 4892 (remove space)
      const priceString = priceData.replace(/\s+/g, '');
      const pricePerM3 = parseFloat(priceString);

      if (isNaN(pricePerM3) || pricePerM3 <= 0) {
        throw new Error(`Invalid price extracted: ${priceData} -> ${pricePerM3}`);
      }

      // Convert from PLN/m³ to PLN/liter (1 m³ = 1000 liters)
      const pricePerLiter = pricePerM3 / 1000;

      this.logger.log(`💰 Parsed price: ${pricePerM3} PLN/m³ = ${pricePerLiter.toFixed(3)} PLN/L`);

      return pricePerLiter;
    } catch (error) {
      this.logger.error(`❌ Failed to extract diesel price: ${error.message}`);
      throw new Error(`Failed to extract diesel price: ${error.message}`);
    }
  }

  private isSameDate(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  private async saveFuelPrice(result: ScrapingResult): Promise<void> {
    try {
      await this.prisma.fuelPrice.upsert({
        where: {
          effectiveDate: result.effectiveDate
        },
        update: {
          dieselPriceNet: result.priceNet,
          dieselPriceGross: result.priceGross,
          vatRate: this.VAT_RATE,
          scrapedAt: new Date(),
        },
        create: {
          effectiveDate: result.effectiveDate,
          dieselPriceNet: result.priceNet,
          dieselPriceGross: result.priceGross,
          vatRate: this.VAT_RATE,
          source: 'orlen.pl',
        }
      });
      
      this.logger.log(`💾 Fuel price saved to database`);
    } catch (error) {
      this.logger.error(`❌ Failed to save fuel price: ${error.message}`);
      throw error;
    }
  }

  private async logScrapingAttempt(result: ScrapingResult, success: boolean, retryCount: number): Promise<void> {
    try {
      await this.prisma.fuelPriceScrapingLog.create({
        data: {
          success,
          effectiveDate: result.effectiveDate,
          priceFound: result.priceNet,
          errorMessage: result.errorMessage,
          retryCount,
        }
      });
    } catch (error) {
      this.logger.error(`❌ Failed to log scraping attempt: ${error.message}`);
    }
  }

  private async handleScrapingFailure(result: ScrapingResult, retryCount: number): Promise<void> {
    if (retryCount < this.MAX_RETRIES) {
      const nextRetry = new Date(Date.now() + this.RETRY_INTERVAL);
      
      await this.logScrapingAttempt(result, false, retryCount + 1);
      
      this.logger.warn(`⏰ Price date mismatch, scheduling retry ${retryCount + 1}/${this.MAX_RETRIES} at ${nextRetry.toLocaleString()}`);
      
      // Schedule retry
      setTimeout(() => {
        this.fetchDailyPricesWithRetry(retryCount + 1);
      }, this.RETRY_INTERVAL);
      
    } else {
      // Max retries reached, send alert
      await this.sendAdminAlert(`Fuel price scraping failed after 24 hours of retries. Last error: ${result.errorMessage}`);
      this.logger.error('❌ Max retries reached for fuel price scraping');
    }
  }

  private async handleScrapingError(error: any, retryCount: number): Promise<void> {
    const errorMessage = `Scraping error: ${error.message}`;
    
    await this.prisma.fuelPriceScrapingLog.create({
      data: {
        success: false,
        errorMessage,
        retryCount,
      }
    });
    
    if (retryCount < this.MAX_RETRIES) {
      const nextRetry = new Date(Date.now() + this.RETRY_INTERVAL);
      
      this.logger.warn(`⏰ Scraping error, scheduling retry ${retryCount + 1}/${this.MAX_RETRIES} at ${nextRetry.toLocaleString()}`);
      
      setTimeout(() => {
        this.fetchDailyPricesWithRetry(retryCount + 1);
      }, this.RETRY_INTERVAL);
    } else {
      await this.sendAdminAlert(`Fuel price scraping failed after 24 hours of retries due to errors. Last error: ${errorMessage}`);
      this.logger.error('❌ Max retries reached for fuel price scraping due to errors');
    }
  }

  private async sendAdminAlert(message: string): Promise<void> {
    try {
      this.logger.error(`🚨 ADMIN ALERT: ${message}`);
      
      // Store alert in database for admin dashboard
      await this.prisma.systemAlert.create({
        data: {
          type: 'FUEL_PRICE_SCRAPING_FAILURE',
          message,
          severity: 'HIGH',
        }
      });
      
      // TODO: Implement additional notification methods:
      // - Email notification
      // - Slack/Discord webhook
      // - SMS notification
      // - In-app notification
      
    } catch (error) {
      this.logger.error(`❌ Failed to send admin alert: ${error.message}`);
    }
  }

  // Manual trigger for testing
  async manualPriceFetch(): Promise<ScrapingResult> {
    this.logger.log('🔧 Manual fuel price fetch triggered');
    const result = await this.scrapeFuelPrice();
    
    if (result.success) {
      await this.saveFuelPrice(result);
    }
    
    await this.logScrapingAttempt(result, result.success, 0);
    
    return result;
  }

  // Get latest fuel price
  async getLatestPrice(): Promise<{ priceNet: number; priceGross: number; effectiveDate: Date } | null> {
    const latestPrice = await this.prisma.fuelPrice.findFirst({
      orderBy: { effectiveDate: 'desc' }
    });

    if (!latestPrice) {
      return null;
    }

    return {
      priceNet: latestPrice.dieselPriceNet,
      priceGross: latestPrice.dieselPriceGross,
      effectiveDate: latestPrice.effectiveDate,
    };
  }

  // Get scraping status
  async getScrapingStatus(): Promise<{ success: boolean; lastAttempt: string; nextRetry?: string; errorMessage?: string; retryCount: number }> {
    const latestLog = await this.prisma.fuelPriceScrapingLog.findFirst({
      orderBy: { attemptDate: 'desc' }
    });

    if (!latestLog) {
      return {
        success: false,
        lastAttempt: new Date().toISOString(),
        retryCount: 0,
        errorMessage: 'No scraping attempts found'
      };
    }

    return {
      success: latestLog.success,
      lastAttempt: latestLog.attemptDate.toISOString(),
      retryCount: latestLog.retryCount,
      errorMessage: latestLog.errorMessage || undefined,
    };
  }

  // Get price history
  async getPriceHistory(days: number = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    return this.prisma.fuelPrice.findMany({
      where: {
        effectiveDate: {
          gte: startDate
        }
      },
      orderBy: { effectiveDate: 'desc' }
    });
  }
}
