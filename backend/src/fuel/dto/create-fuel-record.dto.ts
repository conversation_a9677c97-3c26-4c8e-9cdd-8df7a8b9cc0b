import { IsNotEmpty, <PERSON>String, <PERSON><PERSON><PERSON>ber, IsDateString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsInt } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateFuelRecordDto {
  @IsNotEmpty({ message: 'Vehicle ID is required' })
  @IsString({ message: 'Vehicle ID must be a string' })
  vehicleId: string;

  @IsNotEmpty({ message: 'Driver ID is required' })
  @IsString({ message: 'Driver ID must be a string' })
  driverId: string;

  @IsNotEmpty({ message: 'Quantity is required' })
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Quantity must be a valid number' })
  @Min(0.1, { message: 'Quantity must be at least 0.1 liters' })
  @Max(1000, { message: 'Quantity cannot exceed 1000 liters' })
  @Transform(({ value }) => value ? Number(value) : value)
  quantity: number;

  @IsNotEmpty({ message: 'Total cost is required' })
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Total cost must be a valid number' })
  @Min(0.01, { message: 'Total cost must be at least 0.01' })
  @Max(10000, { message: 'Total cost cannot exceed 10000' })
  @Transform(({ value }) => value ? Number(value) : value)
  totalCost: number;

  @IsNotEmpty({ message: 'Location is required' })
  @IsString({ message: 'Location must be a string' })
  @Transform(({ value }) => value?.trim())
  location: string;

  @IsNotEmpty({ message: 'Fueling date is required' })
  @IsDateString({}, { message: 'Fueling date must be a valid date string' })
  fuelingDate: string;

  @IsNotEmpty({ message: 'Odometer reading is required' })
  @IsInt({ message: 'Odometer reading must be an integer' })
  @Min(0, { message: 'Odometer reading cannot be negative' })
  @Max(9999999, { message: 'Odometer reading seems unreasonably high' })
  @Transform(({ value }) => value ? Number(value) : value)
  odometerReading: number;

  @IsOptional()
  @IsString({ message: 'Receipt number must be a string' })
  @Transform(({ value }) => value?.trim())
  receiptNumber?: string;

  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @Transform(({ value }) => value?.trim())
  notes?: string;

  @IsNotEmpty({ message: 'Entered by is required' })
  @IsString({ message: 'Entered by must be a string' })
  @Transform(({ value }) => value?.trim())
  enteredBy: string;

  // Receipt file will be handled separately in the controller
  // receiptFile?: Express.Multer.File;
}
