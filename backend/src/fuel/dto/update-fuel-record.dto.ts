import { PartialType } from '@nestjs/mapped-types';
import { CreateFuelRecordDto } from './create-fuel-record.dto';
import { IsOptional, IsString, IsNumber, IsDateString, IsInt, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateFuelRecordDto extends PartialType(CreateFuelRecordDto) {
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Quantity must be a valid number' })
  @Min(0.1, { message: 'Quantity must be at least 0.1 liters' })
  @Max(1000, { message: 'Quantity cannot exceed 1000 liters' })
  @Transform(({ value }) => value ? Number(value) : value)
  quantity?: number;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Total cost must be a valid number' })
  @Min(0.01, { message: 'Total cost must be at least 0.01' })
  @Max(10000, { message: 'Total cost cannot exceed 10000' })
  @Transform(({ value }) => value ? Number(value) : value)
  totalCost?: number;

  @IsOptional()
  @IsDateString({}, { message: 'Fueling date must be a valid date string' })
  fuelingDate?: string;
}
