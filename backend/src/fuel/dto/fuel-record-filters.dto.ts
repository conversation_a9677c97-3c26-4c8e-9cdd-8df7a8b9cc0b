import { <PERSON><PERSON>ptional, Is<PERSON><PERSON>, <PERSON><PERSON>ate<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

export class FuelRecordFiltersDto {
  @IsOptional()
  @IsString({ message: 'Vehicle ID must be a string' })
  vehicleId?: string;

  @IsOptional()
  @IsString({ message: 'Driver ID must be a string' })
  driverId?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid date string' })
  endDate?: string;

  @IsOptional()
  @IsString({ message: 'Location must be a string' })
  @Transform(({ value }) => value?.trim())
  location?: string;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Page must be a valid number' })
  @Min(1, { message: 'Page must be at least 1' })
  @Transform(({ value }) => value ? Number(value) : 1)
  page?: number = 1;

  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Limit must be a valid number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  @Transform(({ value }) => value ? Number(value) : 20)
  limit?: number = 20;

  @IsOptional()
  @IsString({ message: 'Sort by must be a string' })
  sortBy?: string = 'fuelingDate';

  @IsOptional()
  @IsString({ message: 'Sort order must be a string' })
  sortOrder?: 'asc' | 'desc' = 'desc';
}
