import { Controller, Get, Post, Put, Param, Query, Body, Logger } from '@nestjs/common';
import { OrlenScraperService } from './services/orlen-scraper.service';
import { SystemAlertsService, AlertFilters } from './services/system-alerts.service';

@Controller('fuel-prices')
export class FuelPriceController {
  private readonly logger = new Logger(FuelPriceController.name);

  constructor(
    private readonly orlenScraperService: OrlenScraperService,
    private readonly systemAlertsService: SystemAlertsService,
  ) {}

  @Get('current')
  async getCurrentPrice() {
    try {
      const price = await this.orlenScraperService.getLatestPrice();
      
      return {
        success: true,
        message: 'Current fuel price retrieved successfully',
        data: price,
      };
    } catch (error) {
      this.logger.error(`Failed to get current price: ${error.message}`);
      throw error;
    }
  }

  @Get('history')
  async getPriceHistory(@Query('days') days?: string) {
    try {
      const daysNumber = days ? parseInt(days) : 30;
      const history = await this.orlenScraperService.getPriceHistory(daysNumber);
      
      return {
        success: true,
        message: 'Price history retrieved successfully',
        data: history,
      };
    } catch (error) {
      this.logger.error(`Failed to get price history: ${error.message}`);
      throw error;
    }
  }

  @Post('manual-fetch')
  async manualFetch() {
    try {
      const result = await this.orlenScraperService.manualPriceFetch();

      return {
        success: true,
        message: 'Manual price fetch completed',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Manual price fetch failed: ${error.message}`);
      throw error;
    }
  }

  @Get('status')
  async getScrapingStatus() {
    try {
      const status = await this.orlenScraperService.getScrapingStatus();

      return {
        success: true,
        message: 'Scraping status retrieved successfully',
        data: status,
      };
    } catch (error) {
      this.logger.error(`Failed to get scraping status: ${error.message}`);
      throw error;
    }
  }

  @Get('alerts')
  async getSystemAlerts(@Query() filters: AlertFilters) {
    try {
      const alerts = await this.systemAlertsService.getAlerts(filters);
      
      return {
        success: true,
        message: 'System alerts retrieved successfully',
        data: alerts,
      };
    } catch (error) {
      this.logger.error(`Failed to get system alerts: ${error.message}`);
      throw error;
    }
  }

  @Get('alerts/unresolved')
  async getUnresolvedAlerts() {
    try {
      const alerts = await this.systemAlertsService.getUnresolvedAlerts();
      
      return {
        success: true,
        message: 'Unresolved alerts retrieved successfully',
        data: alerts,
      };
    } catch (error) {
      this.logger.error(`Failed to get unresolved alerts: ${error.message}`);
      throw error;
    }
  }

  @Get('alerts/stats')
  async getAlertStats() {
    try {
      const stats = await this.systemAlertsService.getAlertStats();
      
      return {
        success: true,
        message: 'Alert statistics retrieved successfully',
        data: stats,
      };
    } catch (error) {
      this.logger.error(`Failed to get alert stats: ${error.message}`);
      throw error;
    }
  }

  @Put('alerts/:id/resolve')
  async resolveAlert(
    @Param('id') id: string,
    @Body('resolvedBy') resolvedBy: string,
  ) {
    try {
      const alert = await this.systemAlertsService.resolveAlert(id, resolvedBy);
      
      return {
        success: true,
        message: 'Alert resolved successfully',
        data: alert,
      };
    } catch (error) {
      this.logger.error(`Failed to resolve alert ${id}: ${error.message}`);
      throw error;
    }
  }
}
