import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { FuelRecord, VehicleFuelStats, Prisma } from '@prisma/client';
import { CreateFuelRecordDto } from './dto/create-fuel-record.dto';
import { UpdateFuelRecordDto } from './dto/update-fuel-record.dto';
import { FuelRecordFiltersDto } from './dto/fuel-record-filters.dto';

export interface FuelRecordWithRelations extends FuelRecord {
  vehicle: {
    id: string;
    plateNumber: string;
    make: string;
    model: string;
    vehicleType: string;
  };
  driver: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface FuelEfficiencyMetrics {
  fuelEfficiency: number; // L/100km
  distanceTraveled: number;
  fuelConsumed: number;
  period: {
    start: Date;
    end: Date;
  };
}

export interface VehicleFuelSummary {
  vehicleId: string;
  totalCost: number;
  totalLiters: number;
  averageEfficiency: number;
  recordCount: number;
  period: {
    start: Date;
    end: Date;
  };
  marketComparison?: {
    avgMarketPrice: number;
    avgPaidPrice: number;
    difference: number;
    percentageDifference: number;
  };
}

@Injectable()
export class FuelService {
  private readonly logger = new Logger(FuelService.name);

  constructor(private prisma: PrismaService) {}

  async recordFueling(data: CreateFuelRecordDto): Promise<FuelRecordWithRelations> {
    // Validate vehicle exists and is a truck (only trucks consume fuel)
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: data.vehicleId },
      select: { id: true, vehicleType: true, plateNumber: true, make: true, model: true }
    });

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    if (vehicle.vehicleType !== 'TRUCK') {
      throw new BadRequestException('Only trucks can have fuel records');
    }

    // Validate driver exists
    const driver = await this.prisma.user.findUnique({
      where: { id: data.driverId },
      select: { id: true, firstName: true, lastName: true, email: true }
    });

    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Calculate price per liter
    const pricePerLiter = data.totalCost / data.quantity;

    // Validate odometer reading (should be higher than previous reading)
    const lastRecord = await this.getLastFuelRecord(data.vehicleId);
    if (lastRecord && data.odometerReading <= lastRecord.odometerReading) {
      this.logger.warn(`Odometer reading ${data.odometerReading} is not higher than previous reading ${lastRecord.odometerReading} for vehicle ${data.vehicleId}`);
      // Don't throw error, just log warning for now (trust-based system)
    }

    try {
      const fuelRecord = await this.prisma.fuelRecord.create({
        data: {
          vehicleId: data.vehicleId,
          driverId: data.driverId,
          quantity: data.quantity,
          totalCost: data.totalCost,
          pricePerLiter,
          location: data.location,
          fuelingDate: new Date(data.fuelingDate),
          odometerReading: data.odometerReading,
          receiptNumber: data.receiptNumber,
          notes: data.notes,
          enteredBy: data.enteredBy,
        },
        include: {
          vehicle: {
            select: {
              id: true,
              plateNumber: true,
              make: true,
              model: true,
              vehicleType: true,
            }
          },
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      });

      // Update vehicle mileage if this is the latest record
      if (!lastRecord || data.odometerReading > (lastRecord.odometerReading || 0)) {
        await this.prisma.vehicle.update({
          where: { id: data.vehicleId },
          data: { mileage: data.odometerReading }
        });
      }

      // Trigger monthly stats recalculation for this vehicle/driver
      const fuelingDate = new Date(data.fuelingDate);
      await this.recalculateMonthlyStats(
        data.vehicleId,
        data.driverId,
        fuelingDate.getMonth() + 1,
        fuelingDate.getFullYear()
      );

      this.logger.log(`Fuel record created for vehicle ${vehicle.plateNumber} by ${driver.firstName} ${driver.lastName}`);

      return fuelRecord;
    } catch (error) {
      this.logger.error(`Failed to create fuel record: ${error.message}`);
      throw new BadRequestException('Failed to create fuel record');
    }
  }

  async getFuelRecords(filters: FuelRecordFiltersDto): Promise<{
    records: FuelRecordWithRelations[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const where: Prisma.FuelRecordWhereInput = {};

    // Apply filters
    if (filters.vehicleId) {
      where.vehicleId = filters.vehicleId;
    }

    if (filters.driverId) {
      where.driverId = filters.driverId;
    }

    if (filters.startDate || filters.endDate) {
      where.fuelingDate = {};
      if (filters.startDate) {
        where.fuelingDate.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.fuelingDate.lte = new Date(filters.endDate);
      }
    }

    if (filters.location) {
      where.location = {
        contains: filters.location,
        mode: 'insensitive'
      };
    }

    // Count total records
    const total = await this.prisma.fuelRecord.count({ where });

    // Calculate pagination
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;
    const totalPages = Math.ceil(total / limit);

    // Fetch records
    const records = await this.prisma.fuelRecord.findMany({
      where,
      include: {
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      },
      orderBy: {
        [filters.sortBy || 'fuelingDate']: filters.sortOrder || 'desc'
      },
      skip,
      take: limit,
    });

    return {
      records,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async getFuelRecordById(id: string): Promise<FuelRecordWithRelations> {
    const record = await this.prisma.fuelRecord.findUnique({
      where: { id },
      include: {
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    });

    if (!record) {
      throw new NotFoundException('Fuel record not found');
    }

    return record;
  }

  async updateFuelRecord(id: string, data: UpdateFuelRecordDto): Promise<FuelRecordWithRelations> {
    const existingRecord = await this.getFuelRecordById(id);

    // Calculate new price per liter if quantity or total cost changed
    let pricePerLiter = existingRecord.pricePerLiter;
    if (data.quantity || data.totalCost) {
      const quantity = data.quantity || existingRecord.quantity;
      const totalCost = data.totalCost || existingRecord.totalCost;
      pricePerLiter = totalCost / quantity;
    }

    const updatedRecord = await this.prisma.fuelRecord.update({
      where: { id },
      data: {
        ...data,
        pricePerLiter,
        fuelingDate: data.fuelingDate ? new Date(data.fuelingDate) : undefined,
      },
      include: {
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    });

    // Recalculate monthly stats if the record was updated
    const fuelingDate = new Date(updatedRecord.fuelingDate);
    await this.recalculateMonthlyStats(
      updatedRecord.vehicleId,
      updatedRecord.driverId,
      fuelingDate.getMonth() + 1,
      fuelingDate.getFullYear()
    );

    this.logger.log(`Fuel record ${id} updated`);

    return updatedRecord;
  }

  async deleteFuelRecord(id: string): Promise<void> {
    const record = await this.getFuelRecordById(id);

    await this.prisma.fuelRecord.delete({
      where: { id }
    });

    // Recalculate monthly stats after deletion
    const fuelingDate = new Date(record.fuelingDate);
    await this.recalculateMonthlyStats(
      record.vehicleId,
      record.driverId,
      fuelingDate.getMonth() + 1,
      fuelingDate.getFullYear()
    );

    this.logger.log(`Fuel record ${id} deleted`);
  }

  private async getLastFuelRecord(vehicleId: string): Promise<FuelRecord | null> {
    return this.prisma.fuelRecord.findFirst({
      where: { vehicleId },
      orderBy: { fuelingDate: 'desc' }
    });
  }

  private async recalculateMonthlyStats(vehicleId: string, driverId: string, month: number, year: number): Promise<void> {
    try {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59);

      // Get all fuel records for this vehicle/driver/month
      const records = await this.prisma.fuelRecord.findMany({
        where: {
          vehicleId,
          driverId,
          fuelingDate: {
            gte: startDate,
            lte: endDate,
          }
        },
        orderBy: { fuelingDate: 'asc' }
      });

      if (records.length === 0) {
        // Delete existing stats if no records
        await this.prisma.vehicleFuelStats.deleteMany({
          where: { vehicleId, driverId, month, year }
        });
        return;
      }

      // Calculate metrics
      const totalFuelCost = records.reduce((sum, record) => sum + record.totalCost, 0);
      const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
      const avgPricePerLiter = totalFuelCost / totalLiters;
      const fuelingCount = records.length;

      // Calculate distance traveled (from first to last odometer reading)
      const firstRecord = records[0];
      const lastRecord = records[records.length - 1];
      const totalDistance = lastRecord.odometerReading - firstRecord.odometerReading;

      // Calculate fuel efficiency (L/100km)
      const fuelEfficiency = totalDistance > 0 ? (totalLiters / totalDistance) * 100 : 0;
      const costPerKm = totalDistance > 0 ? totalFuelCost / totalDistance : 0;

      // Get market price comparison (if available)
      const marketPrices = await this.prisma.fuelPrice.findMany({
        where: {
          effectiveDate: {
            gte: startDate,
            lte: endDate,
          }
        }
      });

      let avgMarketPrice: number | null = null;
      let priceDifference: number | null = null;

      if (marketPrices.length > 0) {
        avgMarketPrice = marketPrices.reduce((sum, price) => sum + price.dieselPriceGross, 0) / marketPrices.length;
        priceDifference = avgPricePerLiter - avgMarketPrice;
      }

      // Upsert the stats record
      await this.prisma.vehicleFuelStats.upsert({
        where: {
          vehicleId_driverId_month_year: {
            vehicleId,
            driverId,
            month,
            year,
          }
        },
        update: {
          totalFuelCost,
          totalLiters,
          totalDistance,
          fuelEfficiency,
          costPerKm,
          avgPricePerLiter,
          fuelingCount,
          avgMarketPrice,
          priceDifference,
          calculatedAt: new Date(),
        },
        create: {
          vehicleId,
          driverId,
          month,
          year,
          totalFuelCost,
          totalLiters,
          totalDistance,
          fuelEfficiency,
          costPerKm,
          avgPricePerLiter,
          fuelingCount,
          avgMarketPrice,
          priceDifference,
        }
      });

      this.logger.log(`Monthly stats recalculated for vehicle ${vehicleId}, driver ${driverId}, ${month}/${year}`);
    } catch (error) {
      this.logger.error(`Failed to recalculate monthly stats: ${error.message}`);
    }
  }

  async calculateFuelEfficiency(vehicleId: string, period?: { start: Date; end: Date }): Promise<FuelEfficiencyMetrics | null> {
    const where: Prisma.FuelRecordWhereInput = { vehicleId };

    if (period) {
      where.fuelingDate = {
        gte: period.start,
        lte: period.end,
      };
    }

    const records = await this.prisma.fuelRecord.findMany({
      where,
      orderBy: { fuelingDate: 'asc' }
    });

    if (records.length < 2) {
      return null; // Need at least 2 records to calculate efficiency
    }

    const firstRecord = records[0];
    const lastRecord = records[records.length - 1];
    const distanceTraveled = lastRecord.odometerReading - firstRecord.odometerReading;
    const fuelConsumed = records.reduce((sum, record) => sum + record.quantity, 0);

    if (distanceTraveled <= 0) {
      return null;
    }

    const fuelEfficiency = (fuelConsumed / distanceTraveled) * 100; // L/100km

    return {
      fuelEfficiency,
      distanceTraveled,
      fuelConsumed,
      period: {
        start: period?.start || firstRecord.fuelingDate,
        end: period?.end || lastRecord.fuelingDate,
      }
    };
  }

  async getVehicleFuelSummary(vehicleId: string, period?: { start: Date; end: Date }): Promise<VehicleFuelSummary> {
    const where: Prisma.FuelRecordWhereInput = { vehicleId };

    if (period) {
      where.fuelingDate = {
        gte: period.start,
        lte: period.end,
      };
    }

    const records = await this.prisma.fuelRecord.findMany({
      where,
      orderBy: { fuelingDate: 'asc' }
    });

    const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
    const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
    const recordCount = records.length;

    // Calculate average efficiency
    const efficiencyMetrics = await this.calculateFuelEfficiency(vehicleId, period);
    const averageEfficiency = efficiencyMetrics?.fuelEfficiency || 0;

    // Market comparison
    let marketComparison: VehicleFuelSummary['marketComparison'] | undefined;

    if (period && records.length > 0) {
      const marketPrices = await this.prisma.fuelPrice.findMany({
        where: {
          effectiveDate: {
            gte: period.start,
            lte: period.end,
          }
        }
      });

      if (marketPrices.length > 0) {
        const avgMarketPrice = marketPrices.reduce((sum, price) => sum + price.dieselPriceGross, 0) / marketPrices.length;
        const avgPaidPrice = totalCost / totalLiters;
        const difference = avgPaidPrice - avgMarketPrice;
        const percentageDifference = (difference / avgMarketPrice) * 100;

        marketComparison = {
          avgMarketPrice,
          avgPaidPrice,
          difference,
          percentageDifference,
        };
      }
    }

    return {
      vehicleId,
      totalCost,
      totalLiters,
      averageEfficiency,
      recordCount,
      period: {
        start: period?.start || (records[0]?.fuelingDate || new Date()),
        end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
      },
      marketComparison,
    };
  }

  async getDriverFuelSummary(driverId: string, period?: { start: Date; end: Date }): Promise<any> {
    const where: Prisma.FuelRecordWhereInput = { driverId };

    if (period) {
      where.fuelingDate = {
        gte: period.start,
        lte: period.end,
      };
    }

    const records = await this.prisma.fuelRecord.findMany({
      where,
      include: {
        vehicle: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
          }
        }
      },
      orderBy: { fuelingDate: 'asc' }
    });

    const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
    const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
    const recordCount = records.length;

    // Group by vehicle for efficiency comparison
    const vehicleStats = records.reduce((acc, record) => {
      const vehicleId = record.vehicleId;
      if (!acc[vehicleId]) {
        acc[vehicleId] = {
          vehicle: record.vehicle,
          totalCost: 0,
          totalLiters: 0,
          records: [],
        };
      }
      acc[vehicleId].totalCost += record.totalCost;
      acc[vehicleId].totalLiters += record.quantity;
      acc[vehicleId].records.push(record);
      return acc;
    }, {} as any);

    // Calculate efficiency for each vehicle
    const vehicleEfficiencies = await Promise.all(
      Object.entries(vehicleStats).map(async ([vehicleId, stats]: [string, any]) => {
        const efficiency = await this.calculateFuelEfficiency(vehicleId, period);
        return {
          vehicleId,
          vehicle: stats.vehicle,
          totalCost: stats.totalCost,
          totalLiters: stats.totalLiters,
          efficiency: efficiency?.fuelEfficiency || 0,
          recordCount: stats.records.length,
        };
      })
    );

    const averageEfficiency = vehicleEfficiencies.length > 0
      ? vehicleEfficiencies.reduce((sum, v) => sum + v.efficiency, 0) / vehicleEfficiencies.length
      : 0;

    return {
      driverId,
      totalCost,
      totalLiters,
      averageEfficiency,
      recordCount,
      vehicleBreakdown: vehicleEfficiencies,
      period: {
        start: period?.start || (records[0]?.fuelingDate || new Date()),
        end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
      },
    };
  }

  async getFleetFuelReport(period?: { start: Date; end: Date }): Promise<any> {
    const where: Prisma.FuelRecordWhereInput = {};

    if (period) {
      where.fuelingDate = {
        gte: period.start,
        lte: period.end,
      };
    }

    const [records, vehicles, drivers] = await Promise.all([
      this.prisma.fuelRecord.findMany({
        where,
        include: {
          vehicle: {
            select: {
              plateNumber: true,
              make: true,
              model: true,
              vehicleType: true,
            }
          },
          driver: {
            select: {
              firstName: true,
              lastName: true,
            }
          }
        },
        orderBy: { fuelingDate: 'asc' }
      }),
      this.prisma.vehicle.findMany({
        where: { vehicleType: 'TRUCK' },
        select: {
          id: true,
          plateNumber: true,
          make: true,
          model: true,
        }
      }),
      this.prisma.user.findMany({
        where: { role: 'DRIVER' },
        select: {
          id: true,
          firstName: true,
          lastName: true,
        }
      })
    ]);

    const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
    const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
    const averagePricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;

    // Vehicle performance ranking
    const vehiclePerformance = await Promise.all(
      vehicles.map(async (vehicle) => {
        const vehicleRecords = records.filter(r => r.vehicleId === vehicle.id);
        const vehicleCost = vehicleRecords.reduce((sum, r) => sum + r.totalCost, 0);
        const vehicleLiters = vehicleRecords.reduce((sum, r) => sum + r.quantity, 0);
        const efficiency = await this.calculateFuelEfficiency(vehicle.id, period);

        return {
          vehicle,
          totalCost: vehicleCost,
          totalLiters: vehicleLiters,
          efficiency: efficiency?.fuelEfficiency || 0,
          recordCount: vehicleRecords.length,
        };
      })
    );

    // Driver performance ranking
    const driverPerformance = await Promise.all(
      drivers.map(async (driver) => {
        const driverSummary = await this.getDriverFuelSummary(driver.id, period);
        return {
          driver,
          ...driverSummary,
        };
      })
    );

    // Sort by efficiency (lower is better)
    vehiclePerformance.sort((a, b) => a.efficiency - b.efficiency);
    driverPerformance.sort((a, b) => a.averageEfficiency - b.averageEfficiency);

    // Monthly trends (if period spans multiple months)
    const monthlyTrends = this.calculateMonthlyTrends(records);

    return {
      summary: {
        totalCost,
        totalLiters,
        averagePricePerLiter,
        recordCount: records.length,
        activeVehicles: vehiclePerformance.filter(v => v.recordCount > 0).length,
        activeDrivers: driverPerformance.filter(d => d.recordCount > 0).length,
      },
      vehiclePerformance: vehiclePerformance.slice(0, 10), // Top 10
      driverPerformance: driverPerformance.slice(0, 10), // Top 10
      monthlyTrends,
      period: {
        start: period?.start || (records[0]?.fuelingDate || new Date()),
        end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
      },
    };
  }

  private calculateMonthlyTrends(records: any[]): any[] {
    const monthlyData = records.reduce((acc, record) => {
      const date = new Date(record.fuelingDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!acc[monthKey]) {
        acc[monthKey] = {
          month: monthKey,
          totalCost: 0,
          totalLiters: 0,
          recordCount: 0,
        };
      }

      acc[monthKey].totalCost += record.totalCost;
      acc[monthKey].totalLiters += record.quantity;
      acc[monthKey].recordCount += 1;

      return acc;
    }, {} as any);

    return Object.values(monthlyData).map((data: any) => ({
      ...data,
      averagePricePerLiter: data.totalLiters > 0 ? data.totalCost / data.totalLiters : 0,
    }));
  }

  async getDashboardStats(): Promise<any> {
    const currentDate = new Date();
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const lastMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0, 23, 59, 59);

    const [
      currentMonthRecords,
      lastMonthRecords,
      totalStats,
      vehicleCount,
      activeDrivers,
    ] = await Promise.all([
      this.prisma.fuelRecord.findMany({
        where: {
          fuelingDate: {
            gte: currentMonth,
          }
        }
      }),
      this.prisma.fuelRecord.findMany({
        where: {
          fuelingDate: {
            gte: lastMonth,
            lte: lastMonthEnd,
          }
        }
      }),
      this.prisma.fuelRecord.aggregate({
        _sum: {
          totalCost: true,
          quantity: true,
        },
        _count: {
          id: true,
        }
      }),
      this.prisma.vehicle.count({
        where: { vehicleType: 'TRUCK' }
      }),
      this.prisma.fuelRecord.findMany({
        where: {
          fuelingDate: {
            gte: currentMonth,
          }
        },
        select: { driverId: true },
        distinct: ['driverId']
      })
    ]);

    const currentMonthCost = currentMonthRecords.reduce((sum, r) => sum + r.totalCost, 0);
    const lastMonthCost = lastMonthRecords.reduce((sum, r) => sum + r.totalCost, 0);
    const currentMonthLiters = currentMonthRecords.reduce((sum, r) => sum + r.quantity, 0);
    const lastMonthLiters = lastMonthRecords.reduce((sum, r) => sum + r.quantity, 0);

    // Calculate trends
    const costTrend = lastMonthCost > 0 ? ((currentMonthCost - lastMonthCost) / lastMonthCost) * 100 : 0;
    const volumeTrend = lastMonthLiters > 0 ? ((currentMonthLiters - lastMonthLiters) / lastMonthLiters) * 100 : 0;

    // Get top performers for current month
    const topVehicles = await this.getTopPerformingVehicles(3, { start: currentMonth, end: currentDate });
    const topDrivers = await this.getTopPerformingDrivers(3, { start: currentMonth, end: currentDate });

    return {
      totalFuelCost: totalStats._sum.totalCost || 0,
      totalLiters: totalStats._sum.quantity || 0,
      averageEfficiency: 28.5, // TODO: Calculate actual fleet average
      totalVehicles: vehicleCount,
      activeDrivers: activeDrivers.length,
      monthlyBudget: 15000, // TODO: Make this configurable
      monthlySpent: currentMonthCost,
      topPerformers: {
        vehicles: topVehicles,
        drivers: topDrivers,
      },
      recentTrends: {
        costTrend,
        efficiencyTrend: -2.1, // TODO: Calculate actual efficiency trend
        volumeTrend,
      },
    };
  }

  private async getTopPerformingVehicles(limit: number, period: { start: Date; end: Date }): Promise<any[]> {
    const vehicles = await this.prisma.vehicle.findMany({
      where: { vehicleType: 'TRUCK' },
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
      }
    });

    const vehiclePerformance = await Promise.all(
      vehicles.map(async (vehicle) => {
        const summary = await this.getVehicleFuelSummary(vehicle.id, period);
        const efficiency = await this.calculateFuelEfficiency(vehicle.id, period);

        return {
          id: vehicle.id,
          plateNumber: vehicle.plateNumber,
          efficiency: efficiency?.fuelEfficiency || 0,
          totalCost: summary.totalCost,
        };
      })
    );

    return vehiclePerformance
      .filter(v => v.totalCost > 0)
      .sort((a, b) => a.efficiency - b.efficiency)
      .slice(0, limit);
  }

  private async getTopPerformingDrivers(limit: number, period: { start: Date; end: Date }): Promise<any[]> {
    const drivers = await this.prisma.user.findMany({
      where: { role: 'DRIVER' },
      select: {
        id: true,
        firstName: true,
        lastName: true,
      }
    });

    const driverPerformance = await Promise.all(
      drivers.map(async (driver) => {
        const summary = await this.getDriverFuelSummary(driver.id, period);

        return {
          id: driver.id,
          name: `${driver.firstName} ${driver.lastName}`,
          efficiency: summary.averageEfficiency,
          totalCost: summary.totalCost,
        };
      })
    );

    return driverPerformance
      .filter(d => d.totalCost > 0)
      .sort((a, b) => a.efficiency - b.efficiency)
      .slice(0, limit);
  }

  async getBasicStats(): Promise<any> {
    const currentDate = new Date();
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const lastMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

    // Get total records count
    const totalRecords = await this.prisma.fuelRecord.count();

    // Get total cost and liters
    const totalStats = await this.prisma.fuelRecord.aggregate({
      _sum: {
        totalCost: true,
        quantity: true,
      }
    });

    // Get current month cost
    const currentMonthStats = await this.prisma.fuelRecord.aggregate({
      where: {
        fuelingDate: {
          gte: currentMonth,
          lte: currentDate,
        }
      },
      _sum: {
        totalCost: true,
      }
    });

    // Get last month cost
    const lastMonthStats = await this.prisma.fuelRecord.aggregate({
      where: {
        fuelingDate: {
          gte: lastMonth,
          lte: lastMonthEnd,
        }
      },
      _sum: {
        totalCost: true,
      }
    });

    // Calculate average efficiency (simplified)
    const averageEfficiency = 28.5; // TODO: Calculate actual fleet average

    return {
      totalRecords,
      totalCost: totalStats._sum.totalCost || 0,
      totalLiters: totalStats._sum.quantity || 0,
      averageEfficiency,
      currentMonthCost: currentMonthStats._sum.totalCost || 0,
      lastMonthCost: lastMonthStats._sum.totalCost || 0,
    };
  }
}
