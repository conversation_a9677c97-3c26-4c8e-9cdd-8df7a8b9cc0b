import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FuelService } from './fuel.service';
import { CreateFuelRecordDto } from './dto/create-fuel-record.dto';
import { UpdateFuelRecordDto } from './dto/update-fuel-record.dto';
import { FuelRecordFiltersDto } from './dto/fuel-record-filters.dto';

@Controller('fuel')
export class FuelController {
  private readonly logger = new Logger(FuelController.name);

  constructor(private readonly fuelService: FuelService) {}

  @Post('records')
  @UseInterceptors(FileInterceptor('receiptFile'))
  async createFuelRecord(
    @Body() createFuelRecordDto: CreateFuelRecordDto,
    @UploadedFile() receiptFile?: any,
  ) {
    try {
      // TODO: Handle receipt file upload (save to storage and get URL)
      if (receiptFile) {
        // For now, we'll just log that a file was uploaded
        this.logger.log(`Receipt file uploaded: ${receiptFile.originalname}, size: ${receiptFile.size} bytes`);
        // In a real implementation, you would:
        // 1. Validate file type (image/pdf)
        // 2. Save to cloud storage (AWS S3, etc.)
        // 3. Get the URL and add it to the DTO
        // createFuelRecordDto.receiptUrl = await this.uploadFile(receiptFile);
      }

      const fuelRecord = await this.fuelService.recordFueling(createFuelRecordDto);
      
      return {
        success: true,
        message: 'Fuel record created successfully',
        data: fuelRecord,
      };
    } catch (error) {
      this.logger.error(`Failed to create fuel record: ${error.message}`);
      throw error;
    }
  }

  @Get('records')
  async getFuelRecords(@Query() filters: FuelRecordFiltersDto) {
    try {
      const result = await this.fuelService.getFuelRecords(filters);
      
      return {
        success: true,
        message: 'Fuel records retrieved successfully',
        data: result.records,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get fuel records: ${error.message}`);
      throw error;
    }
  }

  @Get('records/:id')
  async getFuelRecordById(@Param('id') id: string) {
    try {
      const fuelRecord = await this.fuelService.getFuelRecordById(id);
      
      return {
        success: true,
        message: 'Fuel record retrieved successfully',
        data: fuelRecord,
      };
    } catch (error) {
      this.logger.error(`Failed to get fuel record ${id}: ${error.message}`);
      throw error;
    }
  }

  @Put('records/:id')
  @UseInterceptors(FileInterceptor('receiptFile'))
  async updateFuelRecord(
    @Param('id') id: string,
    @Body() updateFuelRecordDto: UpdateFuelRecordDto,
    @UploadedFile() receiptFile?: any,
  ) {
    try {
      // TODO: Handle receipt file upload if provided
      if (receiptFile) {
        this.logger.log(`Receipt file uploaded for update: ${receiptFile.originalname}`);
        // updateFuelRecordDto.receiptUrl = await this.uploadFile(receiptFile);
      }

      const fuelRecord = await this.fuelService.updateFuelRecord(id, updateFuelRecordDto);
      
      return {
        success: true,
        message: 'Fuel record updated successfully',
        data: fuelRecord,
      };
    } catch (error) {
      this.logger.error(`Failed to update fuel record ${id}: ${error.message}`);
      throw error;
    }
  }

  @Delete('records/:id')
  async deleteFuelRecord(@Param('id') id: string) {
    try {
      await this.fuelService.deleteFuelRecord(id);
      
      return {
        success: true,
        message: 'Fuel record deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to delete fuel record ${id}: ${error.message}`);
      throw error;
    }
  }

  @Get('vehicles/:vehicleId/summary')
  async getVehicleFuelSummary(
    @Param('vehicleId') vehicleId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      let period: { start: Date; end: Date } | undefined;
      
      if (startDate && endDate) {
        period = {
          start: new Date(startDate),
          end: new Date(endDate),
        };
      }

      const summary = await this.fuelService.getVehicleFuelSummary(vehicleId, period);
      
      return {
        success: true,
        message: 'Vehicle fuel summary retrieved successfully',
        data: summary,
      };
    } catch (error) {
      this.logger.error(`Failed to get vehicle fuel summary for ${vehicleId}: ${error.message}`);
      throw error;
    }
  }

  @Get('vehicles/:vehicleId/efficiency')
  async getVehicleFuelEfficiency(
    @Param('vehicleId') vehicleId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      let period: { start: Date; end: Date } | undefined;
      
      if (startDate && endDate) {
        period = {
          start: new Date(startDate),
          end: new Date(endDate),
        };
      }

      const efficiency = await this.fuelService.calculateFuelEfficiency(vehicleId, period);
      
      return {
        success: true,
        message: 'Vehicle fuel efficiency retrieved successfully',
        data: efficiency,
      };
    } catch (error) {
      this.logger.error(`Failed to get vehicle fuel efficiency for ${vehicleId}: ${error.message}`);
      throw error;
    }
  }

  @Get('drivers/:driverId/summary')
  async getDriverFuelSummary(
    @Param('driverId') driverId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      let period: { start: Date; end: Date } | undefined;

      if (startDate && endDate) {
        period = {
          start: new Date(startDate),
          end: new Date(endDate),
        };
      }

      const summary = await this.fuelService.getDriverFuelSummary(driverId, period);

      return {
        success: true,
        message: 'Driver fuel summary retrieved successfully',
        data: summary,
      };
    } catch (error) {
      this.logger.error(`Failed to get driver fuel summary for ${driverId}: ${error.message}`);
      throw error;
    }
  }

  @Get('fleet/report')
  async getFleetFuelReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      let period: { start: Date; end: Date } | undefined;

      if (startDate && endDate) {
        period = {
          start: new Date(startDate),
          end: new Date(endDate),
        };
      }

      const report = await this.fuelService.getFleetFuelReport(period);

      return {
        success: true,
        message: 'Fleet fuel report retrieved successfully',
        data: report,
      };
    } catch (error) {
      this.logger.error(`Failed to get fleet fuel report: ${error.message}`);
      throw error;
    }
  }

  @Get('dashboard/stats')
  async getDashboardStats() {
    try {
      const stats = await this.fuelService.getDashboardStats();

      return {
        success: true,
        message: 'Dashboard statistics retrieved successfully',
        data: stats,
      };
    } catch (error) {
      this.logger.error(`Failed to get dashboard stats: ${error.message}`);
      throw error;
    }
  }

  @Get('stats')
  async getBasicStats() {
    try {
      // Get basic fuel statistics for the main page
      const stats = await this.fuelService.getBasicStats();

      return {
        success: true,
        message: 'Basic fuel stats retrieved successfully',
        data: stats,
      };
    } catch (error) {
      this.logger.error(`Failed to get basic stats: ${error.message}`);
      throw error;
    }
  }
}
