import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { FuelController } from './fuel.controller';
import { FuelPriceController } from './fuel-price.controller';
import { FuelService } from './fuel.service';
import { OrlenScraperService } from './services/orlen-scraper.service';
import { SystemAlertsService } from './services/system-alerts.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    ScheduleModule.forRoot(), // Enable scheduling for cron jobs
  ],
  controllers: [
    FuelController,
    FuelPriceController,
  ],
  providers: [
    FuelService,
    OrlenScraperService,
    SystemAlertsService,
  ],
  exports: [
    FuelService,
    OrlenScraperService,
    SystemAlertsService,
  ],
})
export class FuelModule {}
