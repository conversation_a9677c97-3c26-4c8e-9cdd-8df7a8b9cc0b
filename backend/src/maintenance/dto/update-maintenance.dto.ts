import { IsString, <PERSON><PERSON>ption<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsDateString, <PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { MaintenanceType, MaintenanceCategory, MaintenanceStatus } from '@prisma/client';

export class UpdateMaintenanceDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Vehicle ID must not exceed 100 characters' })
  vehicleId?: string;

  @IsOptional()
  @IsEnum(MaintenanceType, { message: 'Type must be a valid maintenance type' })
  type?: MaintenanceType;

  @IsOptional()
  @IsEnum(MaintenanceCategory, { message: 'Category must be a valid maintenance category' })
  category?: MaintenanceCategory;

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsEnum(MaintenanceStatus, { message: 'Status must be a valid maintenance status' })
  status?: MaintenanceStatus;

  @IsOptional()
  @IsDateString({}, { message: 'Date must be a valid ISO date string' })
  date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Scheduled date must be a valid ISO date string' })
  scheduledDate?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Mileage must be a valid number' })
  @Min(0, { message: 'Mileage must be non-negative' })
  mileage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Parts cost must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Parts cost must be non-negative' })
  partsCost?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Labor cost must be a valid decimal with up to 2 decimal places' })
  @Min(0, { message: 'Labor cost must be non-negative' })
  laborCost?: number;

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Technician name must not exceed 200 characters' })
  technician?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Notes must not exceed 1000 characters' })
  notes?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Next maintenance date must be a valid ISO date string' })
  nextMaintenanceDate?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Next maintenance mileage must be a valid number' })
  @Min(0, { message: 'Next maintenance mileage must be non-negative' })
  nextMaintenanceMileage?: number;
}
