import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, <PERSON><PERSON> } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: ['query', 'info', 'warn', 'error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log('Successfully connected to database');

      // Warm up the connection pool
      await this.$queryRaw`SELECT 1`;
      this.logger.log('Database connection pool warmed up');

      // Set up custom logging for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.log('Database logging enabled for development environment');
      }
    } catch (error) {
      this.logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.logger.log('Disconnected from database');
    } catch (error) {
      this.logger.error('Error disconnecting from database:', error);
    }
  }

  /**
   * Execute a transaction with retry logic for deadlock handling
   */
  async executeTransaction<T>(
    operations: (prisma: PrismaClient) => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.$transaction(operations, {
          timeout: 10000, // 10 second timeout
          isolationLevel: 'ReadCommitted',
        });
      } catch (error: any) {
        lastError = error;

        // Check if it's a deadlock or serialization failure
        if (
          error.code === 'P2034' || // Transaction conflict
          error.code === 'P2028' || // Transaction timeout
          (error.message && error.message.includes('deadlock'))
        ) {
          if (attempt < maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff
            this.logger.warn(`Transaction failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        // If it's not a retryable error or we've exhausted retries, throw
        throw error;
      }
    }

    throw lastError;
  }

  /**
   * Health check for database connection
   */
  async healthCheck(): Promise<{ status: string; timestamp: Date }> {
    try {
      await this.$queryRaw`SELECT 1`;
      return {
        status: 'healthy',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date(),
      };
    }
  }

  /**
   * Log query performance for monitoring
   */
  logQuery(query: string, duration: number, params?: any) {
    if (duration > 1000) {
      this.logger.warn(`Slow query detected: ${duration}ms - ${query.substring(0, 100)}...`);
    } else if (process.env.NODE_ENV === 'development') {
      this.logger.debug(`Query: ${duration}ms - ${query.substring(0, 100)}...`);
    }
  }

  /**
   * Wrapper for queries with performance monitoring
   */
  async monitoredQuery<T>(queryFn: () => Promise<T>, queryName?: string): Promise<T> {
    const start = Date.now();
    try {
      const result = await queryFn();
      const duration = Date.now() - start;

      if (queryName && duration > 100) { // Log queries taking more than 100ms
        this.logger.debug(`${queryName} completed in ${duration}ms`);
      }

      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.logger.error(`Query failed after ${duration}ms: ${queryName || 'Unknown'}`, error);
      throw error;
    }
  }
}
