import { Controller, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { DistanceService, DistanceResult } from './distance.service';

export interface CalculateDistanceDto {
  startLatitude: number;
  startLongitude: number;
  endLatitude: number;
  endLongitude: number;
  profile?: string;
}

@Controller('distance')
export class DistanceController {
  constructor(private readonly distanceService: DistanceService) {}

  @Post('calculate')
  async calculateDistance(@Body() dto: CalculateDistanceDto): Promise<DistanceResult> {
    try {
      const result = await this.distanceService.calculateRoutedDistance(
        { latitude: dto.startLatitude, longitude: dto.startLongitude },
        { latitude: dto.endLatitude, longitude: dto.endLongitude },
        dto.profile || 'driving-car'
      );
      return result;
    } catch (error) {
      throw new HttpException(
        `Distance calculation failed: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
