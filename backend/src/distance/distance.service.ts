import { Injectable, HttpException, HttpStatus } from '@nestjs/common';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface DistanceResult {
  distance: number;
  duration: number;
  durationText: string;
  success: boolean;
  error?: string;
}

@Injectable()
export class DistanceService {
  private readonly ORS_API_KEY = process.env.OPENROUTESERVICE_API_KEY;
  private readonly ORS_BASE_URL = 'https://api.openrouteservice.org/v2';

  async calculateRoutedDistance(
    start: LocationCoordinates,
    end: LocationCoordinates,
    profile: string = 'driving-car'
  ): Promise<DistanceResult> {
    if (!this.ORS_API_KEY) {
      throw new HttpException(
        'OpenRouteService API key not configured',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    try {
      console.log('🛣️ Backend: Calculating routed distance');
      console.log('📍 Start:', start);
      console.log('📍 End:', end);
      console.log('🚗 Profile:', profile);

      const coordinates = [[start.longitude, start.latitude], [end.longitude, end.latitude]];

      const requestBody = {
        coordinates: coordinates,
        instructions: false,
        geometry: true,
        elevation: false
      };

      // Add profile-specific options
      if (profile === 'driving-hgv') {
        requestBody['options'] = {
          vehicle_type: 'hgv',
          avoid_features: []
        };
      }

      console.log('📦 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(`${this.ORS_BASE_URL}/directions/${profile}/json`, {
        method: 'POST',
        headers: {
          'Authorization': this.ORS_API_KEY,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🛣️ OpenRouteService API error:', errorText);
        throw new HttpException(
          `OpenRouteService API error: ${response.status} - ${errorText}`,
          HttpStatus.BAD_REQUEST
        );
      }

      const data = await response.json();
      console.log('📊 Response data keys:', Object.keys(data));

      if (!data.routes || data.routes.length === 0) {
        throw new HttpException(
          'No routes found in OpenRouteService response',
          HttpStatus.BAD_REQUEST
        );
      }

      const route = data.routes[0];
      const summary = route.summary;

      // OpenRouteService returns distance in meters, duration in seconds
      const distanceKm = summary.distance / 1000; // Convert meters to kilometers
      const durationHours = summary.duration / 3600; // Convert seconds to hours

      console.log('🛣️ Route summary:', {
        distance: distanceKm,
        duration: durationHours
      });

      return {
        distance: Math.round(distanceKm * 10) / 10,
        duration: Math.round(durationHours * 10) / 10,
        durationText: this.formatDuration(durationHours),
        success: true
      };

    } catch (error) {
      console.error('🛣️ Distance calculation error:', error);
      console.log('🔄 Falling back to direct distance calculation');

      // Fallback to direct distance calculation
      return this.calculateDirectDistance(start, end);
    }
  }

  /**
   * Calculate direct distance using Haversine formula
   */
  private calculateDirectDistance(
    start: LocationCoordinates,
    end: LocationCoordinates
  ): DistanceResult {
    console.log('📏 Using direct distance calculation (Haversine)');

    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(end.latitude - start.latitude);
    const dLon = this.toRadians(end.longitude - start.longitude);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(start.latitude)) * Math.cos(this.toRadians(end.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    // Estimate duration based on average speed (assume 60 km/h for direct distance)
    const duration = distance / 60;

    console.log('📏 Direct distance result:', {
      distance: Math.round(distance * 10) / 10,
      duration: Math.round(duration * 10) / 10
    });

    return {
      distance: Math.round(distance * 10) / 10,
      duration: Math.round(duration * 10) / 10,
      durationText: this.formatDuration(duration),
      success: true
    };
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Format duration in hours to human-readable text
   */
  private formatDuration(durationHours: number): string {
    const totalMinutes = Math.round(durationHours * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours === 0) {
      return `${minutes} minutes`;
    } else if (minutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minutes`;
    }
  }
}
