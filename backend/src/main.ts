import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { SecurityMiddleware } from './common/middleware/security.middleware';
import { RateLimitGuard } from './common/guards/rate-limit.guard';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { LoggerService } from './common/services/logger.service';
import { RedisService } from './common/services/redis.service';
import { Reflector } from '@nestjs/core';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Get services
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);

  // Set global API prefix
  app.setGlobalPrefix('api');

  // Setup Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Fleet Fusion API')
    .setDescription('Comprehensive Fleet Management System API')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('auth', 'Authentication endpoints')
    .addTag('vehicles', 'Vehicle management')
    .addTag('maintenance', 'Maintenance management')
    .addTag('insurance', 'Insurance policy management')
    .addTag('reviews', 'Vehicle review management')
    .addTag('trips', 'Trip management')
    .addTag('users', 'User and driver management')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // Configure CORS with development-friendly settings
  const isDevelopment = process.env.NODE_ENV !== 'production';
  const allowedOrigins = configService.get<string>('ALLOWED_ORIGINS')?.split(',') || [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    'http://127.0.0.1:3002',
    'http://************:3000', // Add the network IP
    'http://************:3001',
    'http://************:3002',
    'https://fleet-fusion.yourdomain.com'
  ];
  
  app.enableCors({
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);
      
      // In development, be more permissive for local network access
      if (isDevelopment) {
        // Check if it's a local development origin
        if (origin.includes('localhost') || 
            origin.includes('127.0.0.1') || 
            origin.includes('************') ||
            allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
      } else {
        // In production, only allow specific origins
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
      }
      
      logger.logSecurityEvent('CORS_VIOLATION', { origin, isDevelopment, timestamp: new Date().toISOString() });
      return callback(new Error('CORS policy violation'), false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID', 'X-API-Key'],
    exposedHeaders: ['X-Total-Count', 'X-Request-ID'],
    maxAge: 86400 // 24 hours
  });

  // Apply security middleware (create instance with logger dependency)
  const securityMiddleware = new SecurityMiddleware(logger);
  app.use(securityMiddleware.use.bind(securityMiddleware));

  // Global validation pipe with enhanced configuration
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
    disableErrorMessages: process.env.NODE_ENV === 'production',
    transformOptions: {
      enableImplicitConversion: true,
    },
    exceptionFactory: (errors) => {
      const messages = errors.map(error => 
        Object.values(error.constraints || {}).join(', ')
      );
      logger.logBusinessEvent('VALIDATION_ERROR', { errors: messages });
      return new Error(`Validation failed: ${messages.join('; ')}`);
    }
  }));

  // Global exception filter with logger dependency
  app.useGlobalFilters(new GlobalExceptionFilter(logger));

  // Global rate limiting (create instance with dependencies)
  const reflector = app.get(Reflector);
  const redisService = app.get(RedisService);
  app.useGlobalGuards(new RateLimitGuard(reflector, logger, redisService));

  // Global logging interceptor
  app.useGlobalInterceptors(new LoggingInterceptor(logger));

  // API Documentation placeholder - add Swagger later if needed
  if (process.env.NODE_ENV !== 'production') {
    logger.log('API Documentation: Will be available at /api/docs (Swagger pending)');
  }

  // Health check endpoint (add /api prefix manually since global prefix doesn't apply to manual routes)
  app.getHttpAdapter().get('/api/health', (req, res) => {
    const healthcheck = {
      uptime: process.uptime(),
      message: 'Fleet Fusion API is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      memory: process.memoryUsage(),
    };

    res.status(200).json(healthcheck);
  });

  // Graceful shutdown handling
  process.on('SIGINT', async () => {
    logger.log('Received SIGINT, shutting down gracefully...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    logger.log('Received SIGTERM, shutting down gracefully...');
    await app.close();
    process.exit(0);
  });

  const port = configService.get<number>('app.port') || 3001;

  await app.listen(port);
  
  // Enhanced startup logging
  logger.log(`🚀 Fleet Fusion API started successfully`);
  logger.log(`📍 Server running on: http://localhost:${port}`);
  logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.log(`📊 Process ID: ${process.pid}`);

  if (process.env.NODE_ENV !== 'production') {
    logger.log(`📚 API Documentation: Will be available at http://localhost:${port}/api/docs (Swagger pending)`);
    logger.log(`❤️  Health Check: http://localhost:${port}/api/health`);
  }
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start the application:', error);
  process.exit(1);
});
