import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
import * as bcrypt from 'bcrypt';

interface FindAllParams {
  role?: 'ADMIN' | 'MANAGER' | 'DRIVER';
  status?: 'AVAILABLE' | 'ASSIGNED' | 'INACTIVE';
}

interface CreateDriverDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findAll(params?: FindAllParams): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>[]> {
    const { role } = params || {};
    
    const where: any = {};
    if (role) {
      where.role = role;
    }

    return this.prisma.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
      },
    });
  }
  
  async findAllDrivers(): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role' | 'phone' | 'licenseNumber' | 'status'>[]> {
    return this.prisma.user.findMany({
      where: { role: 'DRIVER' },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        phone: true,
        licenseNumber: true,
        status: true,
      },
    });
  }
  
  async findDriverById(id: string): Promise<any> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });
    
    if (!user || user.role !== 'DRIVER') {
      throw new Error('Driver not found');
    }
    
    // Remove sensitive data
    const { passwordHash, ...driverData } = user;
    
    return driverData;
  }

  async createDriver(data: CreateDriverDto): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>> {
    // Check if user with this email already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      throw new Error('A user with this email already exists');
    }
    
    const hashedPassword = await bcrypt.hash(data.password, 10);
    
    const user = await this.prisma.user.create({
      data: {
        email: data.email,
        passwordHash: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        role: 'DRIVER',
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
      },
    });

    return user;
  }
  
  async getDriverAssignments(driverId: string): Promise<any[]> {
    // Verify driver exists
    const driver = await this.prisma.user.findUnique({
      where: { id: driverId },
    });
    
    if (!driver) {
      throw new Error('Driver not found');
    }
    
    return this.prisma.vehicleAssignment.findMany({
      where: { driverId },
      include: {
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            year: true,
            status: true,
          },
        }
      },
      orderBy: {
        startDate: 'desc',
      },
    });
  }
  
  async updateDriver(id: string, updateData: any): Promise<any> {
    console.log('⭐ Update driver called with ID:', id);
    console.log('⭐ Update data received:', JSON.stringify(updateData, null, 2));
    
    // Check if driver exists
    const driver = await this.prisma.user.findUnique({
      where: { id },
    });
    
    if (!driver) {
      throw new Error('Driver not found');
    }
    
    // If email is being changed, check if the new email is already in use
    if (updateData.email && updateData.email !== driver.email) {
      const existingUser = await this.prisma.user.findUnique({
        where: { email: updateData.email },
      });
      
      if (existingUser) {
        throw new Error('A user with this email already exists');
      }
    }
    
    // Prepare data for update - filter out any fields that shouldn't be updated
    const { passwordHash, role, createdAt, updatedAt, ...rawUpdateData } = updateData;
    
    // Handle date fields properly
    const safeUpdateData = { ...rawUpdateData };
    
    // Convert date strings to Date objects if they exist
    if (safeUpdateData.licenseExpiry) {
      try {
        safeUpdateData.licenseExpiry = new Date(safeUpdateData.licenseExpiry);
      } catch (error) {
        console.error('Invalid license expiry date:', safeUpdateData.licenseExpiry);
        delete safeUpdateData.licenseExpiry;
      }
    }
    
    if (safeUpdateData.hireDate) {
      try {
        safeUpdateData.hireDate = new Date(safeUpdateData.hireDate);
      } catch (error) {
        console.error('Invalid hire date:', safeUpdateData.hireDate);
        delete safeUpdateData.hireDate;
      }
    }
    
    console.log('⭐ Processed update data:', safeUpdateData);
    
    // Update the driver - don't use select to avoid field mismatch issues
    const updatedDriver = await this.prisma.user.update({
      where: { id },
      data: safeUpdateData,
    });
    
    // Remove sensitive data before returning
    const { passwordHash: _, ...driverData } = updatedDriver;
    
    console.log('⭐ Driver successfully updated:', driverData);
    
    return driverData;
  }
  
  async deleteDriver(id: string): Promise<any> {
    console.log(`Deleting driver with ID: ${id}`);
    
    // Check if driver exists
    const driver = await this.prisma.user.findUnique({
      where: { id },
    });
    
    if (!driver || driver.role !== 'DRIVER') {
      throw new Error('Driver not found');
    }
    
    // Check if driver has active assignments before deletion
    const activeAssignments = await this.prisma.vehicleAssignment.findMany({
      where: {
        driverId: id,
        status: 'ACTIVE',
      },
    });
    
    if (activeAssignments.length > 0) {
      throw new Error('Cannot delete driver with active vehicle assignments');
    }
    
    // Delete the driver
    const deletedDriver = await this.prisma.user.delete({
      where: { id },
    });
    
    // Remove sensitive data before returning
    const { passwordHash: _, ...driverData } = deletedDriver;
    
    console.log(`Driver successfully deleted:`, driverData);
    
    return driverData;
  }
}
