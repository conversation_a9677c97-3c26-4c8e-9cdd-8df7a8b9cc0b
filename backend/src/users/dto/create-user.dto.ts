import { 
  <PERSON>S<PERSON>, 
  IsEmail, 
  IsOptional, 
  IsEnum, 
  IsDateString, 
  Length, 
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
  Matches
} from 'class-validator';
import { Transform } from 'class-transformer';
import { UserRole } from '@prisma/client';
import { IsValidPhoneNumber, IsValidLicenseNumber } from '../../common/validators/custom-validators';

export class CreateUserDto {
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @MaxLength(100, { message: 'Email cannot exceed 100 characters' })
  @Transform(({ value }) => value?.trim().toLowerCase())
  email: string;

  @IsNotEmpty({ message: 'Password is required' })
  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(128, { message: 'Password cannot exceed 128 characters' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
  })
  password: string;

  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  @Length(1, 50, { message: 'First name must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  firstName: string;

  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  @Length(1, 50, { message: 'Last name must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  lastName: string;

  @IsOptional()
  @IsEnum(UserRole, {
    message: 'Role must be one of: ADMIN, MANAGER, DRIVER'
  })
  role?: UserRole;

  @IsOptional()
  @IsString({ message: 'Phone must be a string' })
  @IsValidPhoneNumber({ message: 'Phone number format is invalid' })
  @Transform(({ value }) => value?.trim())
  phone?: string;

  @IsOptional()
  @IsString({ message: 'License number must be a string' })
  @IsValidLicenseNumber({ message: 'License number format is invalid' })
  @Transform(({ value }) => value?.trim().toUpperCase())
  licenseNumber?: string;

  @IsOptional()
  @IsString({ message: 'License type must be a string' })
  @Length(1, 20, { message: 'License type must be between 1 and 20 characters' })
  @Transform(({ value }) => value?.trim())
  licenseType?: string;

  @IsOptional()
  @IsDateString({}, { message: 'License expiry must be a valid ISO date string' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  licenseExpiry?: string;

  @IsOptional()
  @IsString({ message: 'License restrictions must be a string' })
  @MaxLength(500, { message: 'License restrictions cannot exceed 500 characters' })
  @Transform(({ value }) => value?.trim())
  licenseRestrictions?: string;

  @IsOptional()
  @IsString({ message: 'Address must be a string' })
  @MaxLength(200, { message: 'Address cannot exceed 200 characters' })
  @Transform(({ value }) => value?.trim())
  address?: string;

  @IsOptional()
  @IsString({ message: 'Emergency contact name must be a string' })
  @MaxLength(100, { message: 'Emergency contact name cannot exceed 100 characters' })
  @Transform(({ value }) => value?.trim())
  emergencyContactName?: string;

  @IsOptional()
  @IsString({ message: 'Emergency contact phone must be a string' })
  @IsValidPhoneNumber({ message: 'Emergency contact phone number format is invalid' })
  @Transform(({ value }) => value?.trim())
  emergencyContactPhone?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Hire date must be a valid ISO date string' })
  @Transform(({ value }) => value ? new Date(value).toISOString() : value)
  hireDate?: string;

  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
  @Transform(({ value }) => value?.trim())
  notes?: string;

  @IsOptional()
  @IsString({ message: 'Status must be a string' })
  @IsEnum(['Active', 'Inactive', 'Suspended', 'On Leave'], {
    message: 'Status must be one of: Active, Inactive, Suspended, On Leave'
  })
  status?: string;
}

export class CreateDriverDto {
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @MaxLength(100, { message: 'Email cannot exceed 100 characters' })
  @Transform(({ value }) => value?.trim().toLowerCase())
  email: string;

  @IsNotEmpty({ message: 'Password is required' })
  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(128, { message: 'Password cannot exceed 128 characters' })
  password: string;

  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  @Length(1, 50, { message: 'First name must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  firstName: string;

  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  @Length(1, 50, { message: 'Last name must be between 1 and 50 characters' })
  @Transform(({ value }) => value?.trim())
  lastName: string;

  @IsOptional()
  @IsString({ message: 'Phone must be a string' })
  @IsValidPhoneNumber({ message: 'Phone number format is invalid' })
  @Transform(({ value }) => value?.trim())
  phone?: string;

  @IsOptional()
  @IsString({ message: 'License number must be a string' })
  @IsValidLicenseNumber({ message: 'License number format is invalid' })
  @Transform(({ value }) => value?.trim().toUpperCase())
  licenseNumber?: string;
}
