import { Controller, Get, Post, Body, Query, UseGuards, BadRequestException, Param, Patch, Delete, NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '@prisma/client';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  findAll(
    @Query('role') role?: 'ADMIN' | 'MANAGER' | 'DRIVER'
  ): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>[]> {
    return this.usersService.findAll({ role });
  }
  
  @Get('drivers')
  findAllDrivers(): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role' | 'phone' | 'licenseNumber' | 'status'>[]> {
    return this.usersService.findAllDrivers();
  }
  
  @Get('drivers/:id')
  async findOneDriver(@Param('id') id: string): Promise<any> {
    return this.usersService.findDriverById(id);
  }
  
  @Get('drivers/:id/assignments')
  async getDriverAssignments(@Param('id') id: string): Promise<any[]> {
    return this.usersService.getDriverAssignments(id);
  }
  
  @Post('driver')
  async createDriver(
    @Body() createDriverDto: { email: string; password: string; firstName: string; lastName: string; }
  ): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>> {
    try {
      return await this.usersService.createDriver(createDriverDto);
    } catch (error) {
      // Convert to proper HTTP exception
      if (error.message.includes('email already exists')) {
        throw new BadRequestException('A user with this email already exists');
      }
      throw new BadRequestException(`Failed to create driver: ${error.message}`);
    }
  }
  
  @Patch('drivers/:id')
  async updateDriver(
    @Param('id') id: string,
    @Body() updateDriverDto: any
  ): Promise<any> {
    try {
      console.log(`Updating driver ${id} with data:`, updateDriverDto);
      const result = await this.usersService.updateDriver(id, updateDriverDto);
      console.log(`Driver update successful:`, result);
      return result;
    } catch (error) {
      console.error(`Error updating driver ${id}:`, error);
      if (error.message?.includes('not found')) {
        throw new BadRequestException('Driver not found');
      }
      if (error.message?.includes('email already exists')) {
        throw new BadRequestException('A user with this email already exists');
      }
      throw new BadRequestException(`Failed to update driver: ${error.message || 'Unknown error'}`);
    }
  }
  
  @Delete('drivers/:id')
  async deleteDriver(@Param('id') id: string): Promise<any> {
    try {
      return await this.usersService.deleteDriver(id);
    } catch (error) {
      console.error(`Error deleting driver ${id}:`, error);
      if (error.message?.includes('not found')) {
        throw new NotFoundException('Driver not found');
      }
      throw new BadRequestException(`Failed to delete driver: ${error.message || 'Unknown error'}`);
    }
  }
}
