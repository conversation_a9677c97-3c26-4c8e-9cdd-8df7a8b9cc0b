import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req } from '@nestjs/common';
import { TripsService } from './trips.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateTripDto } from './dto/create-trip.dto';
import { UpdateTripDto } from './dto/update-trip.dto';
import { Trip, TripStatus, User } from '@prisma/client';

interface RequestWithUser extends Request {
  user: User;
}

@Controller('trips')
@UseGuards(JwtAuthGuard)
export class TripsController {
  constructor(private readonly tripsService: TripsService) {}

  @Get()
  async findAll(@Req() req: RequestWithUser): Promise<Trip[]> {
    if (req.user.role === 'DRIVER') {
      return this.tripsService.findByDriver(req.user.id);
    }
    return this.tripsService.findAll();
  }

  @Get('truck-trailer-pairs/active')
  findActiveTruckTrailerPairs(): Promise<any[]> {
    return this.tripsService.findActiveTruckTrailerPairs();
  }

  @Get('truck-trailer-pairs/available')
  findAvailableTruckTrailerPairs(): Promise<any[]> {
    return this.tripsService.findAvailableTruckTrailerPairs();
  }

  @Get('vehicle/:vehicleId')
  findByVehicle(@Param('vehicleId') vehicleId: string): Promise<Trip[]> {
    return this.tripsService.findByVehicle(vehicleId);
  }

  @Get('trailer/:trailerId')
  findByTrailer(@Param('trailerId') trailerId: string): Promise<Trip[]> {
    return this.tripsService.findByTrailer(trailerId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Trip> {
    return this.tripsService.findOne(id);
  }

  @Post()
  async create(@Body() createTripDto: CreateTripDto): Promise<Trip> {
    console.log('Creating trip with data:', JSON.stringify(createTripDto, null, 2));
    
    // Format the data and convert dates to ISO format
    const formattedData = {
      ...createTripDto,
      startTime: new Date(createTripDto.startTime).toISOString(),
      endTime: createTripDto.endTime ? new Date(createTripDto.endTime).toISOString() : undefined,
    };
    
    return this.tripsService.create(formattedData);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateTripDto: UpdateTripDto,
  ): Promise<Trip> {
    // Format the data and convert dates to ISO format
    const formattedData: any = { ...updateTripDto };
    
    if (updateTripDto.startTime) {
      formattedData.startTime = new Date(updateTripDto.startTime).toISOString();
    }
    
    if (updateTripDto.endTime) {
      formattedData.endTime = new Date(updateTripDto.endTime).toISOString();
    }
    
    return this.tripsService.update(id, formattedData);
  }

  @Patch(':id/status')
  updateStatus(
    @Param('id') id: string,
    @Body('status') status: TripStatus,
  ): Promise<Trip> {
    return this.tripsService.updateStatus(id, status);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<Trip> {
    return this.tripsService.delete(id);
  }
}
