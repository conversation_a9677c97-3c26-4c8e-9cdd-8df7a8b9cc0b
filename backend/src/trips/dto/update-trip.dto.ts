import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON>E<PERSON>, IsDateString, Length, Min } from 'class-validator';
import { TripType, TripPriority, TripStatus } from '@prisma/client';

export class UpdateTripDto {
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Driver ID must be valid' })
  driverId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Vehicle ID must be valid' })
  vehicleId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Trailer ID must be valid' })
  trailerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Assignment ID must be valid' })
  assignmentId?: string;

  @IsOptional()
  @IsEnum(TripType, { message: 'Trip type must be valid' })
  type?: TripType;

  @IsOptional()
  @IsEnum(TripPriority, { message: 'Trip priority must be valid' })
  priority?: TripPriority;

  @IsOptional()
  @IsString()
  @Length(1, 200, { message: 'Start location must not exceed 200 characters' })
  startLocation?: string;

  @IsOptional()
  @IsString()
  @Length(1, 200, { message: 'End location must not exceed 200 characters' })
  endLocation?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Start time must be a valid date string' })
  startTime?: string;

  @IsOptional()
  @IsDateString({}, { message: 'End time must be a valid date string' })
  endTime?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Estimated duration must be a valid number' })
  @Min(0, { message: 'Estimated duration cannot be negative' })
  estimatedDuration?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Distance must be a valid number' })
  @Min(0, { message: 'Distance cannot be negative' })
  distance?: number;

  @IsOptional()
  @IsString()
  @Length(0, 1000, { message: 'Notes cannot exceed 1000 characters' })
  notes?: string;

  @IsOptional()
  @IsString()
  @Length(0, 200, { message: 'Purpose cannot exceed 200 characters' })
  purpose?: string;

  @IsOptional()
  @IsString()
  @Length(0, 200, { message: 'Cargo description cannot exceed 200 characters' })
  cargo?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Cargo weight must be a valid number' })
  @Min(0, { message: 'Cargo weight cannot be negative' })
  cargoWeight?: number;

  @IsOptional()
  @IsEnum(TripStatus, { message: 'Status must be a valid trip status' })
  status?: TripStatus;

  // Business Partners Integration
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Pickup partner ID must be valid' })
  pickupPartnerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Delivery partner ID must be valid' })
  deliveryPartnerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Pickup location ID must be valid' })
  pickupLocationId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Delivery location ID must be valid' })
  deliveryLocationId?: string;
}
