import { IsString, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, IsDateString, Length, Min } from 'class-validator';
import { TripType, TripPriority } from '@prisma/client';

export class CreateTripDto {
  @IsString()
  @Length(1, 50, { message: 'Driver ID is required' })
  driverId: string;

  @IsString()
  @Length(1, 50, { message: 'Vehicle ID is required' })
  vehicleId: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Trailer ID must be valid' })
  trailerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Truck-trailer assignment ID must be valid' })
  truckTrailerAssignmentId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Assignment ID must be valid' })
  assignmentId?: string;

  @IsEnum(TripType, { message: 'Trip type must be valid' })
  type: TripType;

  @IsEnum(TripPriority, { message: 'Trip priority must be valid' })
  priority: TripPriority;

  @IsString()
  @Length(1, 200, { message: 'Start location is required and must not exceed 200 characters' })
  startLocation: string;

  @IsString()
  @Length(1, 200, { message: 'End location is required and must not exceed 200 characters' })
  endLocation: string;

  @IsDateString({}, { message: 'Start time must be a valid date string' })
  startTime: string;

  @IsOptional()
  @IsDateString({}, { message: 'End time must be a valid date string' })
  endTime?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Estimated duration must be a valid number' })
  @Min(0, { message: 'Estimated duration cannot be negative' })
  estimatedDuration?: number;

  @IsOptional()
  @IsNumber({}, { message: 'Distance must be a valid number' })
  @Min(0, { message: 'Distance cannot be negative' })
  distance?: number;

  @IsOptional()
  @IsString()
  @Length(0, 1000, { message: 'Notes cannot exceed 1000 characters' })
  notes?: string;

  @IsOptional()
  @IsString()
  @Length(0, 200, { message: 'Purpose cannot exceed 200 characters' })
  purpose?: string;

  @IsOptional()
  @IsString()
  @Length(0, 200, { message: 'Cargo description cannot exceed 200 characters' })
  cargo?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Cargo weight must be a valid number' })
  @Min(0, { message: 'Cargo weight cannot be negative' })
  cargoWeight?: number;

  // Business Partners Integration
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Pickup partner ID must be valid' })
  pickupPartnerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Delivery partner ID must be valid' })
  deliveryPartnerId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Pickup location ID must be valid' })
  pickupLocationId?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Delivery location ID must be valid' })
  deliveryLocationId?: string;
}
