import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Trip, TripStatus, TripType, TripPriority, Prisma } from '@prisma/client';
import { OptimisticLockingService } from '../common/services/optimistic-locking.service';
import { RealtimeGateway } from '../common/gateways/realtime.gateway';
import { RedisService } from '../common/services/redis.service';

interface CreateTripInput {
  driverId: string;
  vehicleId: string;
  trailerId?: string;
  truckTrailerAssignmentId?: string;
  assignmentId?: string;
  type: TripType;
  priority: TripPriority;
  startLocation: string;
  endLocation: string;
  startTime: Date | string;
  endTime?: Date | string;
  estimatedDuration?: number;
  distance?: number;
  notes?: string;
  purpose?: string;
  cargo?: string;
  cargoWeight?: number;
  // Business Partners Integration
  pickupPartnerId?: string;
  deliveryPartnerId?: string;
  pickupLocationId?: string;
  deliveryLocationId?: string;
}

@Injectable()
export class TripsService {
  constructor(
    private prisma: PrismaService,
    private optimisticLocking: OptimisticLockingService,
    private realtimeGateway: RealtimeGateway,
    private redisService: RedisService,
  ) {}

  private getTripIncludes() {
    return {
      driver: true,
      vehicle: {
        include: {
          truckAssignments: {
            where: {
              status: 'ACTIVE',
              endDate: null,
            },
            include: {
              trailer: {
                select: {
                  id: true,
                  plateNumber: true,
                  make: true,
                  model: true,
                  trailerType: true,
                  cargoCapacity: true,
                  maxWeight: true,
                }
              }
            }
          }
        }
      },
      trailer: true,
      assignment: true,
      stops: {
        orderBy: {
          sequence: 'asc',
        },
      },
      expenses: true,
      // Business Partners Integration
      pickupPartner: {
        select: {
          id: true,
          name: true,
          type: true,
          contactPerson: true,
          phone: true,
          email: true,
        }
      },
      deliveryPartner: {
        select: {
          id: true,
          name: true,
          type: true,
          contactPerson: true,
          phone: true,
          email: true,
        }
      },
      pickupLocation: {
        select: {
          id: true,
          name: true,
          address: true,
          city: true,
          state: true,
          contactPerson: true,
          phone: true,
          operatingHours: true,
          specialInstructions: true,
        }
      },
      deliveryLocation: {
        select: {
          id: true,
          name: true,
          address: true,
          city: true,
          state: true,
          contactPerson: true,
          phone: true,
          operatingHours: true,
          specialInstructions: true,
        }
      },
    } as const;
  }

  async findAll(): Promise<Trip[]> {
    const cacheKey = 'trips:all';

    // Try to get from cache first
    const cached = await this.redisService.get<Trip[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Get from database
    const trips = await this.prisma.trip.findMany({
      include: this.getTripIncludes(),
      orderBy: {
        startTime: 'desc',
      },
    });

    // Cache for 5 minutes
    await this.redisService.set(cacheKey, trips, 300);

    return trips;
  }

  async findByDriver(driverId: string): Promise<Trip[]> {
    return this.prisma.trip.findMany({
      where: { driverId },
      include: this.getTripIncludes(),
      orderBy: {
        startTime: 'desc',
      },
    });
  }

  async findByVehicle(vehicleId: string): Promise<Trip[]> {
    return this.prisma.trip.findMany({
      where: { vehicleId },
      include: this.getTripIncludes(),
      orderBy: {
        startTime: 'desc',
      },
    });
  }

  async findOne(id: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id },
      include: this.getTripIncludes(),
    });

    if (!trip) {
      throw new BadRequestException('Trip not found');
    }

    return trip;
  }

  async findByTrailer(trailerId: string): Promise<Trip[]> {
    return this.prisma.trip.findMany({
      where: { trailerId },
      include: this.getTripIncludes(),
      orderBy: {
        startTime: 'desc',
      },
    });
  }

  async findActiveTruckTrailerPairs(): Promise<any[]> {
    return this.prisma.truckTrailerAssignment.findMany({
      where: {
        status: 'ACTIVE',
        endDate: null,
      },
      include: {
        truck: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            status: true,
            vehicleType: true,
            engineType: true,
            fuelCapacity: true,
          }
        },
        trailer: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            status: true,
            vehicleType: true,
            trailerType: true,
            cargoCapacity: true,
            maxWeight: true,
          }
        }
      }
    });
  }

  async findAvailableTruckTrailerPairs(): Promise<any[]> {
    // Get active assignments where both truck and trailer are available
    const activePairs = await this.prisma.truckTrailerAssignment.findMany({
      where: {
        status: 'ACTIVE',
        endDate: null,
        truck: {
          status: 'AVAILABLE',
        },
        trailer: {
          status: 'AVAILABLE',
        },
        // Check that neither truck nor trailer has an active trip
        AND: [
          {
            truck: {
              trips: {
                none: {
                  status: {
                    in: ['SCHEDULED', 'IN_PROGRESS']
                  }
                }
              }
            }
          },
          {
            trailer: {
              trailerTrips: {
                none: {
                  status: {
                    in: ['SCHEDULED', 'IN_PROGRESS']
                  }
                }
              }
            }
          }
        ]
      },
      include: {
        truck: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            status: true,
            vehicleType: true,
            engineType: true,
            fuelCapacity: true,
          }
        },
        trailer: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
            status: true,
            vehicleType: true,
            trailerType: true,
            cargoCapacity: true,
            maxWeight: true,
          }
        }
      }
    });

    return activePairs;
  }

  /**
   * Check for time-based conflicts with existing trips
   */
  private async checkTimeConflicts(
    vehicleId: string,
    trailerId: string | undefined,
    driverId: string,
    startTime: Date | string,
    endTime: Date | string | undefined
  ): Promise<{ hasConflicts: boolean; conflicts: any[] }> {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : null;

    const conflicts: any[] = [];

    // Check vehicle conflicts
    const vehicleConflicts = await this.prisma.trip.findMany({
      where: {
        vehicleId,
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS']
        },
        OR: [
          // New trip starts during existing trip
          {
            AND: [
              { startTime: { lte: start } },
              end ? { endTime: { gte: start } } : { endTime: null }
            ]
          },
          // New trip ends during existing trip
          end ? {
            AND: [
              { startTime: { lte: end } },
              { endTime: { gte: end } }
            ]
          } : {},
          // New trip completely contains existing trip
          end ? {
            AND: [
              { startTime: { gte: start } },
              { endTime: { lte: end } }
            ]
          } : {},
          // Existing trip completely contains new trip
          {
            AND: [
              { startTime: { lte: start } },
              end ? { endTime: { gte: end } } : { endTime: null }
            ]
          }
        ]
      },
      include: {
        vehicle: { select: { plateNumber: true } }
      }
    });

    if (vehicleConflicts.length > 0) {
      conflicts.push(...vehicleConflicts.map(trip => ({
        type: 'vehicle',
        vehicle: trip.vehicle.plateNumber,
        tripId: trip.id,
        startTime: trip.startTime,
        endTime: trip.endTime,
        status: trip.status
      })));
    }

    // Check trailer conflicts if trailer is specified
    if (trailerId) {
      const trailerConflicts = await this.prisma.trip.findMany({
        where: {
          trailerId,
          status: {
            in: ['SCHEDULED', 'IN_PROGRESS']
          },
          OR: [
            // Same logic as vehicle conflicts
            {
              AND: [
                { startTime: { lte: start } },
                end ? { endTime: { gte: start } } : { endTime: null }
              ]
            },
            end ? {
              AND: [
                { startTime: { lte: end } },
                { endTime: { gte: end } }
              ]
            } : {},
            end ? {
              AND: [
                { startTime: { gte: start } },
                { endTime: { lte: end } }
              ]
            } : {},
            {
              AND: [
                { startTime: { lte: start } },
                end ? { endTime: { gte: end } } : { endTime: null }
              ]
            }
          ]
        },
        include: {
          trailer: { select: { plateNumber: true } }
        }
      });

      if (trailerConflicts.length > 0) {
        conflicts.push(...trailerConflicts.map(trip => ({
          type: 'trailer',
          trailer: trip.trailer?.plateNumber,
          tripId: trip.id,
          startTime: trip.startTime,
          endTime: trip.endTime,
          status: trip.status
        })));
      }
    }

    // Check driver conflicts
    const driverConflicts = await this.prisma.trip.findMany({
      where: {
        driverId,
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS']
        },
        OR: [
          // Same logic as vehicle conflicts
          {
            AND: [
              { startTime: { lte: start } },
              end ? { endTime: { gte: start } } : { endTime: null }
            ]
          },
          end ? {
            AND: [
              { startTime: { lte: end } },
              { endTime: { gte: end } }
            ]
          } : {},
          end ? {
            AND: [
              { startTime: { gte: start } },
              { endTime: { lte: end } }
            ]
          } : {},
          {
            AND: [
              { startTime: { lte: start } },
              end ? { endTime: { gte: end } } : { endTime: null }
            ]
          }
        ]
      },
      include: {
        driver: { select: { firstName: true, lastName: true } }
      }
    });

    if (driverConflicts.length > 0) {
      conflicts.push(...driverConflicts.map(trip => ({
        type: 'driver',
        driver: `${trip.driver.firstName} ${trip.driver.lastName}`,
        tripId: trip.id,
        startTime: trip.startTime,
        endTime: trip.endTime,
        status: trip.status
      })));
    }

    return {
      hasConflicts: conflicts.length > 0,
      conflicts
    };
  }

  async create(data: CreateTripInput): Promise<Trip & { conflictWarnings?: string[] }> {
    // Validate truck-trailer assignment if provided
    if (data.truckTrailerAssignmentId) {
      const assignment = await this.prisma.truckTrailerAssignment.findUnique({
        where: { id: data.truckTrailerAssignmentId },
        include: {
          truck: true,
          trailer: true,
        }
      });

      if (!assignment) {
        throw new BadRequestException('Truck-trailer assignment not found');
      }

      if (assignment.status !== 'ACTIVE') {
        throw new BadRequestException('Truck-trailer assignment is not active');
      }

      // Ensure the vehicleId matches the truck in the assignment
      if (data.vehicleId !== assignment.truckId) {
        throw new BadRequestException('Vehicle ID must match the truck in the assignment');
      }

      // Set trailerId from the assignment
      data.trailerId = assignment.trailerId;
    }

    // Check if vehicle exists
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: data.vehicleId },
    });

    if (!vehicle) {
      throw new BadRequestException('Vehicle not found');
    }

    if (vehicle.status !== 'AVAILABLE' && vehicle.status !== 'ASSIGNED') {
      throw new BadRequestException('Vehicle is not available');
    }

    // If trailer is specified, check if it exists
    if (data.trailerId) {
      const trailer = await this.prisma.vehicle.findUnique({
        where: { id: data.trailerId },
      });

      if (!trailer) {
        throw new BadRequestException('Trailer not found');
      }

      if (trailer.vehicleType !== 'TRAILER') {
        throw new BadRequestException('Specified trailer ID is not a trailer');
      }

      if (trailer.status !== 'AVAILABLE' && trailer.status !== 'ASSIGNED') {
        throw new BadRequestException('Trailer is not available');
      }
    }

    // Check if driver exists
    const driver = await this.prisma.user.findUnique({
      where: { id: data.driverId },
    });

    if (!driver) {
      throw new BadRequestException('Driver not found');
    }

    // Check for time-based conflicts (but don't block creation)
    const conflictCheck = await this.checkTimeConflicts(
      data.vehicleId,
      data.trailerId,
      data.driverId,
      data.startTime,
      data.endTime
    );

    let conflictWarnings: string[] = [];
    if (conflictCheck.hasConflicts) {
      conflictWarnings = conflictCheck.conflicts.map(conflict => {
        const timeRange = `${new Date(conflict.startTime).toLocaleString()} - ${
          conflict.endTime ? new Date(conflict.endTime).toLocaleString() : 'Open-ended'
        }`;

        switch (conflict.type) {
          case 'vehicle':
            return `Vehicle ${conflict.vehicle} has a conflicting trip (${conflict.status}) from ${timeRange}`;
          case 'trailer':
            return `Trailer ${conflict.trailer} has a conflicting trip (${conflict.status}) from ${timeRange}`;
          case 'driver':
            return `Driver ${conflict.driver} has a conflicting trip (${conflict.status}) from ${timeRange}`;
          default:
            return `Conflict detected from ${timeRange}`;
        }
      });

      // Log the conflicts but don't throw an error
      console.log('⚠️ Time conflicts detected but allowing trip creation:', conflictWarnings);
    }

    try {
      // Create the base trip
      const trip = await this.prisma.trip.create({
        data: {
          driverId: data.driverId,
          vehicleId: data.vehicleId,
          trailerId: data.trailerId,
          assignmentId: data.assignmentId,
          type: data.type,
          priority: data.priority,
          startLocation: data.startLocation,
          endLocation: data.endLocation,
          startTime: data.startTime,
          endTime: data.endTime,
          estimatedDuration: data.estimatedDuration,
          distance: data.distance,
          notes: data.notes,
          purpose: data.purpose,
          cargo: data.cargo,
          cargoWeight: data.cargoWeight,
          status: TripStatus.SCHEDULED,
          // Business Partners Integration
          pickupPartnerId: data.pickupPartnerId,
          deliveryPartnerId: data.deliveryPartnerId,
          pickupLocationId: data.pickupLocationId,
          deliveryLocationId: data.deliveryLocationId,
        },
      });

      const createdTrip = await this.findOne(trip.id);

      // Return trip with conflict warnings if any
      const result: any = {
        ...createdTrip,
      };

      if (conflictWarnings.length > 0) {
        result.conflictWarnings = conflictWarnings;
      }

      return result;
    } catch (error) {
      throw new BadRequestException('Failed to create trip: ' + error.message);
    }
  }

  async update(id: string, data: Prisma.TripUpdateInput & { version?: number }, userId?: string): Promise<Trip> {
    try {
      let updatedTrip: Trip;

      if (data.version) {
        // Use optimistic locking
        const { version, ...updateData } = data;
        updatedTrip = await this.optimisticLocking.updateTrip(id, updateData, version, userId);
      } else {
        // Fallback to regular update
        updatedTrip = await this.prisma.trip.update({
          where: { id },
          data,
          include: this.getTripIncludes(),
        });
      }

      // Invalidate cache
      await this.invalidateTripCaches(id);

      // Broadcast real-time update
      this.realtimeGateway.broadcastTripUpdate(id, {
        action: 'updated',
        trip: updatedTrip,
        updatedBy: userId,
      });

      return updatedTrip;
    } catch (error) {
      throw new BadRequestException('Failed to update trip: ' + error.message);
    }
  }

  async updateStatus(id: string, status: TripStatus, version?: number, userId?: string): Promise<Trip> {
    try {
      let updatedTrip: Trip;

      if (version) {
        // Use optimistic locking
        updatedTrip = await this.optimisticLocking.updateTrip(id, { status }, version, userId);
      } else {
        // Fallback to regular update
        updatedTrip = await this.prisma.trip.update({
          where: { id },
          data: { status },
          include: this.getTripIncludes(),
        });
      }

      // Invalidate cache
      await this.invalidateTripCaches(id);

      // Broadcast real-time update
      this.realtimeGateway.broadcastTripUpdate(id, {
        action: 'status_updated',
        status,
        trip: updatedTrip,
        updatedBy: userId,
      });

      return updatedTrip;
    } catch (error) {
      throw new BadRequestException('Failed to update trip status: ' + error.message);
    }
  }

  async delete(id: string, userId?: string): Promise<Trip> {
    const deletedTrip = await this.prisma.trip.delete({
      where: { id },
      include: this.getTripIncludes(),
    });

    // Invalidate cache
    await this.invalidateTripCaches(id);

    // Broadcast real-time update
    this.realtimeGateway.broadcastTripUpdate(id, {
      action: 'deleted',
      deletedBy: userId,
    });

    return deletedTrip;
  }

  /**
   * Invalidate trip-related caches
   */
  private async invalidateTripCaches(tripId?: string): Promise<void> {
    try {
      // Invalidate general trip caches
      await this.redisService.delPattern('trips:*');

      // Invalidate specific trip cache if ID provided
      if (tripId) {
        await this.redisService.del(`trip:${tripId}`);
      }
    } catch (error) {
      // Log error but don't fail the operation
      console.warn('Failed to invalidate trip caches:', error);
    }
  }
}
