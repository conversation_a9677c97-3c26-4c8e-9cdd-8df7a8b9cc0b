import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../common/services/redis.service';
import { RealtimeGateway } from '../common/gateways/realtime.gateway';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    websocket: ServiceHealth;
  };
}

interface ServiceHealth {
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  details?: any;
}

interface ApplicationMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  database: {
    connections: number;
    queries: number;
  };
  websocket: {
    connectedUsers: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
  };
}

@Injectable()
export class HealthService {
  private startTime = Date.now();
  private queryCount = 0;
  private cacheHits = 0;
  private cacheMisses = 0;

  constructor(
    private readonly prisma: PrismaService,
    private readonly redisService: RedisService,
    private readonly realtimeGateway: RealtimeGateway,
  ) {}

  async getHealthStatus(): Promise<HealthStatus> {
    const [databaseHealth, redisHealth, websocketHealth] = await Promise.all([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
      this.checkWebSocketHealth(),
    ]);

    const overallStatus = this.determineOverallStatus([
      databaseHealth.status,
      redisHealth.status,
      websocketHealth.status,
    ]);

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: databaseHealth,
        redis: redisHealth,
        websocket: websocketHealth,
      },
    };
  }

  async getDetailedHealthStatus(): Promise<any> {
    const basicHealth = await this.getHealthStatus();
    const metrics = await this.getMetrics();

    return {
      ...basicHealth,
      metrics,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
      },
    };
  }

  async getMetrics(): Promise<ApplicationMetrics> {
    const memoryUsage = process.memoryUsage();
    
    return {
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
      },
      cpu: {
        usage: process.cpuUsage().user / 1000000, // Convert to seconds
      },
      database: {
        connections: await this.getDatabaseConnections(),
        queries: this.queryCount,
      },
      websocket: {
        connectedUsers: this.realtimeGateway.getConnectedUsersCount(),
      },
      cache: {
        hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) * 100 || 0,
        missRate: this.cacheMisses / (this.cacheHits + this.cacheMisses) * 100 || 0,
      },
    };
  }

  private async checkDatabaseHealth(): Promise<ServiceHealth> {
    try {
      const start = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        responseTime,
        details: {
          message: 'Database connection successful',
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }

  private async checkRedisHealth(): Promise<ServiceHealth> {
    try {
      const start = Date.now();
      const health = await this.redisService.healthCheck();
      const responseTime = Date.now() - start;

      return {
        status: health.connected ? 'healthy' : 'unhealthy',
        responseTime,
        details: health,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }

  private async checkWebSocketHealth(): Promise<ServiceHealth> {
    try {
      const connectedUsers = this.realtimeGateway.getConnectedUsersCount();
      
      return {
        status: 'healthy',
        details: {
          connectedUsers,
          message: 'WebSocket gateway operational',
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }

  private async getDatabaseConnections(): Promise<number> {
    try {
      // This is a simplified way to get connection count
      // In a real implementation, you might want to use Prisma metrics
      return 1; // Placeholder
    } catch (error) {
      return 0;
    }
  }

  private determineOverallStatus(statuses: string[]): 'healthy' | 'degraded' | 'unhealthy' {
    const unhealthyCount = statuses.filter(status => status === 'unhealthy').length;
    
    if (unhealthyCount === 0) {
      return 'healthy';
    } else if (unhealthyCount < statuses.length) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }

  // Metrics tracking methods
  incrementQueryCount(): void {
    this.queryCount++;
  }

  incrementCacheHit(): void {
    this.cacheHits++;
  }

  incrementCacheMiss(): void {
    this.cacheMisses++;
  }

  resetMetrics(): void {
    this.queryCount = 0;
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }
}
