import { Controller, Get } from '@nestjs/common';
import { HealthService } from './health.service';

@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  async getHealth(): Promise<any> {
    return this.healthService.getHealthStatus();
  }

  @Get('detailed')
  async getDetailedHealth(): Promise<any> {
    return this.healthService.getDetailedHealthStatus();
  }

  @Get('metrics')
  async getMetrics(): Promise<any> {
    return this.healthService.getMetrics();
  }
}
