import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { PrismaModule } from './prisma/prisma.module';
import { VehiclesModule } from './vehicles/vehicles.module';
import { TripsModule } from './trips/trips.module';
import { UsersModule } from './users/users.module';
import { BusinessPartnersModule } from './business-partners/business-partners.module';
import { DistanceModule } from './distance/distance.module';
import { FuelModule } from './fuel/fuel.module';
import { CommonModule } from './common/common.module';
import { HealthModule } from './health/health.module';
import appConfig from './config/app.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig],
    }),
    CommonModule, // Global common services
    PrismaModule,
    AuthModule,
    VehiclesModule,
    TripsModule,
    UsersModule,
    BusinessPartnersModule,
    DistanceModule,
    FuelModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
