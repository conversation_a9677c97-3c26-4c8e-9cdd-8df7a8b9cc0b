import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { RedisService } from '../services/redis.service';
import { CACHE_KEY, CacheOptions, generateCacheKey } from '../decorators/cache.decorator';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON>and<PERSON>,
  ): Promise<Observable<any>> {
    const cacheOptions = this.reflector.get<CacheOptions>(
      CACHE_KEY,
      context.getHandler(),
    );

    if (!cacheOptions) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const className = context.getClass().name;
    const methodName = context.getHandler().name;
    const args = [request.params, request.query, request.body].filter(Boolean);

    const cacheKey = cacheOptions.key || 
      generateCacheKey(className, methodName, args, cacheOptions.keyGenerator);

    // Try to get from cache
    try {
      const cachedResult = await this.redisService.get(cacheKey);
      if (cachedResult !== null) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return of(cachedResult);
      }
    } catch (error) {
      this.logger.warn(`Cache get error for key ${cacheKey}:`, error);
    }

    // Cache miss - execute method and cache result
    return next.handle().pipe(
      tap(async (result) => {
        try {
          const ttl = cacheOptions.ttl || 300; // Default 5 minutes
          await this.redisService.set(cacheKey, result, ttl);
          this.logger.debug(`Cached result for key: ${cacheKey} (TTL: ${ttl}s)`);
        } catch (error) {
          this.logger.warn(`Cache set error for key ${cacheKey}:`, error);
        }
      }),
    );
  }
}
