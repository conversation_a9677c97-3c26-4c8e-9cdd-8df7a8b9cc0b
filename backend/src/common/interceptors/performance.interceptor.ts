import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { HealthService } from '../../health/health.service';

@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);

  constructor(private readonly healthService: HealthService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const method = request.method;
    const url = request.url;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip || request.connection.remoteAddress;
    const userId = request.user?.id;

    const startTime = Date.now();

    return next.handle().pipe(
      tap({
        next: (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const statusCode = response.statusCode;

          // Log performance metrics
          this.logPerformanceMetrics({
            method,
            url,
            statusCode,
            duration,
            userAgent,
            ip,
            userId,
            success: true,
          });

          // Track metrics
          this.healthService.incrementQueryCount();

          // Log slow requests (> 1 second)
          if (duration > 1000) {
            this.logger.warn(`Slow request detected: ${method} ${url} took ${duration}ms`, {
              method,
              url,
              duration,
              userId,
              ip,
            });
          }
        },
        error: (error) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const statusCode = error.status || 500;

          // Log error metrics
          this.logPerformanceMetrics({
            method,
            url,
            statusCode,
            duration,
            userAgent,
            ip,
            userId,
            success: false,
            error: error.message,
          });

          // Log error
          this.logger.error(`Request failed: ${method} ${url} (${duration}ms)`, {
            method,
            url,
            duration,
            statusCode,
            error: error.message,
            userId,
            ip,
          });
        },
      }),
    );
  }

  private logPerformanceMetrics(metrics: {
    method: string;
    url: string;
    statusCode: number;
    duration: number;
    userAgent: string;
    ip: string;
    userId?: string;
    success: boolean;
    error?: string;
  }) {
    const {
      method,
      url,
      statusCode,
      duration,
      userAgent,
      ip,
      userId,
      success,
      error,
    } = metrics;

    // Create structured log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: 'performance',
      method,
      url,
      statusCode,
      duration,
      userAgent,
      ip,
      userId,
      success,
      error,
    };

    // Log based on performance thresholds
    if (duration < 100) {
      this.logger.debug(`Fast request: ${method} ${url} (${duration}ms)`, logEntry);
    } else if (duration < 500) {
      this.logger.log(`Normal request: ${method} ${url} (${duration}ms)`, logEntry);
    } else if (duration < 1000) {
      this.logger.warn(`Slow request: ${method} ${url} (${duration}ms)`, logEntry);
    } else {
      this.logger.error(`Very slow request: ${method} ${url} (${duration}ms)`, logEntry);
    }

    // Store metrics for health monitoring
    this.storeMetrics(metrics);
  }

  private storeMetrics(metrics: any) {
    // In a production environment, you might want to:
    // 1. Store metrics in a time-series database (e.g., InfluxDB, Prometheus)
    // 2. Send metrics to monitoring services (e.g., DataDog, New Relic)
    // 3. Aggregate metrics for dashboards
    
    // For now, we'll just track basic counters
    // This could be enhanced to store detailed metrics
  }
}
