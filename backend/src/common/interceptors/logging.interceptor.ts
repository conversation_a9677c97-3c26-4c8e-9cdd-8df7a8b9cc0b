import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LoggerService } from '../services/logger.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = request.user?.id;

    const startTime = Date.now();

    // Log the incoming request
    this.logger.logApiRequest(method, url, userId, ip, userAgent);

    return next.handle().pipe(
      tap({
        next: (data) => {
          const duration = Date.now() - startTime;
          const statusCode = response.statusCode;

          // Log successful response
          this.logger.logApiResponse(method, url, statusCode, duration, userId);

          // Log performance metrics for slow requests
          if (duration > 1000) {
            this.logger.logPerformanceMetric('slow_request', duration, 'ms', {
              method,
              url,
              userId,
              statusCode,
            });
          }
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          const statusCode = error.status || 500;

          // Log error response
          this.logger.error('API Error', error.stack, {
            method,
            url,
            statusCode,
            duration,
            userId,
            ip,
            errorMessage: error.message,
            type: 'api_error',
          });
        },
      }),
    );
  }
}
