import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HealthModule } from '../health/health.module';

// Services
import { LoggerService } from './services/logger.service';
import { RedisService } from './services/redis.service';
import { OptimisticLockingService } from './services/optimistic-locking.service';

// Guards
import { RateLimitGuard } from './guards/rate-limit.guard';

// Interceptors
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { CacheInterceptor } from './interceptors/cache.interceptor';
import { PerformanceInterceptor } from './interceptors/performance.interceptor';

// Gateways
import { RealtimeGateway } from './gateways/realtime.gateway';

// Filters
import { GlobalExceptionFilter } from './filters/global-exception.filter';

@Global()
@Module({
  imports: [ConfigModule, HealthModule],
  providers: [
    // Services
    LoggerService,
    RedisService,
    OptimisticLockingService,
    
    // Guards
    RateLimitGuard,
    
    // Interceptors
    LoggingInterceptor,
    CacheInterceptor,
    PerformanceInterceptor,
    
    // Gateways
    RealtimeGateway,
    
    // Filters
    GlobalExceptionFilter,
  ],
  exports: [
    // Services
    LoggerService,
    RedisService,
    OptimisticLockingService,
    
    // Guards
    RateLimitGuard,
    
    // Interceptors
    LoggingInterceptor,
    CacheInterceptor,
    PerformanceInterceptor,
    
    // Gateways
    RealtimeGateway,
    
    // Filters
    GlobalExceptionFilter,
  ],
})
export class CommonModule {}
