import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { LoggerService } from '../services/logger.service';
import { RedisService } from '../services/redis.service';

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (request: any) => string; // Custom key generator
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitData {
  count: number;
  resetTime: number;
}

// Fallback in-memory store when Redis is unavailable
const requestCounts = new Map<string, RateLimitData>();

export const RATE_LIMIT_KEY = 'rateLimit';

// Decorator for setting rate limit options
export const RateLimit = (options: RateLimitOptions) => 
  SetMetadata(RATE_LIMIT_KEY, options);

@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly logger: LoggerService,
    private readonly redisService: RedisService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const handler = context.getHandler();
    const rateLimitOptions = this.reflector.get<RateLimitOptions>(
      RATE_LIMIT_KEY,
      handler,
    );

    if (!rateLimitOptions) {
      return true; // No rate limiting configured
    }

    const key = this.generateKey(request, rateLimitOptions);
    const now = Date.now();
    const windowMs = rateLimitOptions.windowMs;
    const maxRequests = rateLimitOptions.maxRequests;

    try {
      // Try Redis first
      const rateLimitData = await this.getRateLimitData(key, windowMs);

      if (rateLimitData.count >= maxRequests) {
        const remainingTime = Math.ceil((rateLimitData.resetTime - now) / 1000);

        // Log rate limit violation
        this.logger.logSecurityEvent('rate_limit_exceeded', {
          key,
          count: rateLimitData.count,
          maxRequests,
          remainingTime,
          windowMs,
        }, request.user?.id, request.ip);

        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: 'Rate limit exceeded',
            error: 'Too Many Requests',
            retryAfter: remainingTime,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // Increment the count
      await this.incrementRateLimit(key, windowMs);
      return true;
    } catch (error) {
      // Fallback to in-memory if Redis fails
      this.logger.warn('Redis rate limiting failed, falling back to in-memory', error);
      return this.fallbackRateLimit(request, rateLimitOptions);
    }
  }

  private async getRateLimitData(key: string, windowMs: number): Promise<RateLimitData> {
    const redisKey = `rate_limit:${key}`;
    const now = Date.now();

    try {
      const data = await this.redisService.get<RateLimitData>(redisKey);

      if (!data || data.resetTime <= now) {
        // Initialize new rate limit window
        const newData: RateLimitData = {
          count: 0,
          resetTime: now + windowMs,
        };
        await this.redisService.set(redisKey, newData, Math.ceil(windowMs / 1000));
        return newData;
      }

      return data;
    } catch (error) {
      throw error;
    }
  }

  private async incrementRateLimit(key: string, windowMs: number): Promise<void> {
    const redisKey = `rate_limit:${key}`;
    const now = Date.now();

    try {
      const data = await this.redisService.get<RateLimitData>(redisKey);

      if (data && data.resetTime > now) {
        data.count++;
        await this.redisService.set(redisKey, data, Math.ceil((data.resetTime - now) / 1000));
      }
    } catch (error) {
      throw error;
    }
  }

  private fallbackRateLimit(request: any, rateLimitOptions: RateLimitOptions): boolean {
    const key = this.generateKey(request, rateLimitOptions);
    const now = Date.now();
    const windowStart = now - rateLimitOptions.windowMs;

    // Clean up expired entries
    this.cleanupExpiredEntries(windowStart);

    let rateLimitData = requestCounts.get(key);

    if (!rateLimitData || rateLimitData.resetTime <= now) {
      rateLimitData = {
        count: 1,
        resetTime: now + rateLimitOptions.windowMs,
      };
      requestCounts.set(key, rateLimitData);
      return true;
    }

    if (rateLimitData.count >= rateLimitOptions.maxRequests) {
      const remainingTime = Math.ceil((rateLimitData.resetTime - now) / 1000);

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Rate limit exceeded',
          error: 'Too Many Requests',
          retryAfter: remainingTime,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    rateLimitData.count++;
    requestCounts.set(key, rateLimitData);
    return true;
  }

  private generateKey(request: any, options: RateLimitOptions): string {
    if (options.keyGenerator) {
      return options.keyGenerator(request);
    }

    // Default key generation: IP + User ID (if authenticated)
    const ip = request.ip || request.connection.remoteAddress;
    const userId = request.user?.id || 'anonymous';
    return `${ip}:${userId}`;
  }

  private cleanupExpiredEntries(windowStart: number): void {
    const now = Date.now();
    for (const [key, data] of requestCounts.entries()) {
      if (data.resetTime <= now) {
        requestCounts.delete(key);
      }
    }
  }

  // Static method to get remaining requests for a key
  static getRemainingRequests(key: string, maxRequests: number): number {
    const data = requestCounts.get(key);
    if (!data || data.resetTime <= Date.now()) {
      return maxRequests;
    }
    return Math.max(0, maxRequests - data.count);
  }

  // Static method to get reset time for a key
  static getResetTime(key: string): number | null {
    const data = requestCounts.get(key);
    if (!data || data.resetTime <= Date.now()) {
      return null;
    }
    return data.resetTime;
  }
}
