import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Custom validator to check if a date is not in the past
 */
export function IsNotPastDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isNotPastDate',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Allow empty values, use @IsNotEmpty if required
          const date = new Date(value);
          const now = new Date();
          now.setHours(0, 0, 0, 0); // Reset time to start of day
          return date >= now;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} cannot be in the past`;
        },
      },
    });
  };
}

/**
 * Custom validator to check if end date is after start date
 */
export function IsAfterStartDate(startDateProperty: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isAfterStartDate',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [startDateProperty],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Allow empty values
          const [startDatePropertyName] = args.constraints;
          const startDate = (args.object as any)[startDatePropertyName];
          if (!startDate) return true; // Can't validate if start date is missing
          
          const endDate = new Date(value);
          const startDateObj = new Date(startDate);
          return endDate > startDateObj;
        },
        defaultMessage(args: ValidationArguments) {
          const [startDatePropertyName] = args.constraints;
          return `${args.property} must be after ${startDatePropertyName}`;
        },
      },
    });
  };
}

/**
 * Custom validator to check if a plate number format is valid
 */
export function IsValidPlateNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidPlateNumber',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true;

          // Polish license plate validation - supports multiple formats:
          // Standard: ABC 1234, AB 12345, A 123456 (old format)
          // New format: WGM 1776M, ABC 123D, etc.
          // Special: ABC-1234 (with dash)
          const cleanValue = value.replace(/[\s-]/g, ''); // Remove spaces and dashes

          // Polish plate formats:
          // 1. Old format: 1-3 letters + 3-6 digits (ABC123, AB1234, A12345)
          // 2. New format: 2-3 letters + 3-4 digits + 1 letter (WGM1776M, ABC123D)
          // 3. Special vehicles: Various formats
          const plateRegexes = [
            /^[A-Z]{1,3}\d{3,6}$/i,           // Old format: ABC123, AB1234, A12345
            /^[A-Z]{2,3}\d{3,4}[A-Z]$/i,      // New format: WGM1776M, ABC123D
            /^[A-Z]{1,4}\d{1,6}[A-Z]?$/i,     // Flexible format for special cases
          ];

          return plateRegexes.some(regex => regex.test(cleanValue));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid plate number format`;
        },
      },
    });
  };
}

/**
 * Custom validator to check if a license number format is valid
 */
export function IsValidLicenseNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidLicenseNumber',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true;
          // Basic license number validation - adjust based on your requirements
          // This example requires at least 6 alphanumeric characters
          const licenseRegex = /^[A-Z0-9]{6,}$/i;
          return licenseRegex.test(value.replace(/\s/g, ''));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid license number format`;
        },
      },
    });
  };
}

/**
 * Custom validator to check if a phone number format is valid
 */
export function IsValidPhoneNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidPhoneNumber',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true;
          // Basic phone number validation - supports various formats
          const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
          const cleanPhone = value.replace(/[\s\-\(\)]/g, '');
          return phoneRegex.test(cleanPhone) && cleanPhone.length >= 7;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid phone number`;
        },
      },
    });
  };
}

/**
 * Injectable service for database-dependent validations
 */
@Injectable()
export class DatabaseValidatorService {
  constructor(private prisma: PrismaService) {}

  /**
   * Check if a vehicle exists and is of the correct type
   */
  async validateVehicleExists(vehicleId: string, expectedType?: string): Promise<boolean> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: { id: true, vehicleType: true },
    });

    if (!vehicle) return false;
    if (expectedType && vehicle.vehicleType !== expectedType) return false;
    return true;
  }

  /**
   * Check if a user exists and has the correct role
   */
  async validateUserExists(userId: string, expectedRole?: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true },
    });

    if (!user) return false;
    if (expectedRole && user.role !== expectedRole) return false;
    return true;
  }

  /**
   * Check if an email is unique (excluding a specific user ID)
   */
  async validateEmailUnique(email: string, excludeUserId?: string): Promise<boolean> {
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
      select: { id: true },
    });

    if (!existingUser) return true;
    if (excludeUserId && existingUser.id === excludeUserId) return true;
    return false;
  }

  /**
   * Check if a plate number is unique (excluding a specific vehicle ID)
   */
  async validatePlateNumberUnique(plateNumber: string, excludeVehicleId?: string): Promise<boolean> {
    const existingVehicle = await this.prisma.vehicle.findUnique({
      where: { plateNumber },
      select: { id: true },
    });

    if (!existingVehicle) return true;
    if (excludeVehicleId && existingVehicle.id === excludeVehicleId) return true;
    return false;
  }
}

/**
 * Custom validator that uses database validation service
 */
export function IsUniqueEmail(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isUniqueEmail',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      async: true,
      validator: {
        async validate(value: any, args: ValidationArguments) {
          // This would need to be implemented with dependency injection
          // For now, return true and handle uniqueness in service layer
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be unique`;
        },
      },
    });
  };
}
