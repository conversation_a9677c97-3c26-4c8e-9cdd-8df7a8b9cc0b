import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Request, Response } from 'express';
import { Prisma } from '@prisma/client';
import { LoggerService } from '../services/logger.service';
import {
  BusinessLogicException,
  ValidationException,
  ConflictException,
  ResourceNotFoundException,
  DatabaseException
} from '../exceptions/custom-exceptions';

interface PrismaErrorResult {
  message: string;
  errors?: string[];
}

@Injectable()
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(private readonly logger: LoggerService) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const requestId = this.generateRequestId();
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errors: string[] = [];
    let details: string | undefined;

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object') {
        const resp = exceptionResponse as any;
        message = resp.message || message;
        errors = Array.isArray(resp.message) ? resp.message : resp.errors || [];
      }
    } else if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      status = HttpStatus.BAD_REQUEST;
      const result = this.handlePrismaError(exception);
      message = result.message;
      errors = result.errors || [];
    } else if (exception instanceof Prisma.PrismaClientValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Invalid data provided';
      errors = ['Validation failed for the provided data'];
    } else if (exception instanceof BusinessLogicException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof ValidationException) {
      status = exception.getStatus();
      const response = exception.getResponse() as any;
      message = response.message || exception.message;
      details = response.details;
    } else if (exception instanceof ConflictException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof ResourceNotFoundException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof DatabaseException) {
      status = exception.getStatus();
      const response = exception.getResponse() as any;
      message = response.message || 'Database operation failed';
      details = process.env.NODE_ENV === 'production' ? undefined : response.details;
    } else if (exception instanceof Error) {
      message = process.env.NODE_ENV === 'production' ? 'Internal server error' : exception.message;
      details = exception.message;
    } else {
      message = 'Unknown error occurred';
      details = String(exception);
    }

    // Log the error with context
    this.logger.error(
      `HTTP ${status} Error: ${message}`,
      exception instanceof Error ? exception.stack : 'Unknown error',
      {
        requestId,
        method: request.method,
        url: request.url,
        userAgent: request.get('user-agent'),
        ip: request.ip,
        body: request.body,
        query: request.query,
        params: request.params
      }
    );

    // Log security-related errors
    if (status === HttpStatus.UNAUTHORIZED || status === HttpStatus.FORBIDDEN) {
      this.logger.logSecurityEvent('AUTHENTICATION_FAILURE', {
        ip: request.ip,
        userAgent: request.get('user-agent'),
        url: request.url,
        method: request.method
      });
    }

    // Send standardized error response
    const errorResponse = {
      success: false,
      statusCode: status,
      message,
      ...(details && { details }),
      ...(errors.length > 0 && { errors }),
      requestId,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method
    };

    // Remove sensitive information in production
    if (process.env.NODE_ENV === 'production') {
      delete errorResponse.details;
    }

    response.status(status).json(errorResponse);
  }

  private handlePrismaError(exception: Prisma.PrismaClientKnownRequestError): PrismaErrorResult {
    switch (exception.code) {
      case 'P2002':
        return {
          message: 'A record with this information already exists',
          errors: [`Unique constraint violation on field: ${exception.meta?.target}`]
        };
      case 'P2014':
        return {
          message: 'The change you are trying to make would violate a required relationship',
          errors: ['Foreign key constraint violation']
        };
      case 'P2003':
        return {
          message: 'Foreign key constraint failed',
          errors: [`Foreign key constraint failed on field: ${exception.meta?.field_name}`]
        };
      case 'P2025':
        return {
          message: 'Record not found',
          errors: ['The requested record does not exist']
        };
      case 'P2000':
        return {
          message: 'Value too long for column',
          errors: [`Value provided is too long for column: ${exception.meta?.column_name}`]
        };
      case 'P2001':
        return {
          message: 'Record does not exist',
          errors: ['The record searched for does not exist']
        };
      case 'P2004':
        return {
          message: 'Constraint failed',
          errors: [`A constraint failed on the database: ${exception.meta?.database_error}`]
        };
      case 'P2015':
        return {
          message: 'Related record not found',
          errors: ['A related record could not be found']
        };
      case 'P2016':
        return {
          message: 'Query interpretation error',
          errors: ['There was an error interpreting the query']
        };
      case 'P2017':
        return {
          message: 'Records not connected',
          errors: ['The records for relation are not connected']
        };
      case 'P2018':
        return {
          message: 'Required connected records not found',
          errors: ['The required connected records were not found']
        };
      case 'P2019':
        return {
          message: 'Input error',
          errors: ['Input error in the provided data']
        };
      case 'P2020':
        return {
          message: 'Value out of range',
          errors: ['Value out of range for the type']
        };
      default:
        return {
          message: 'Database operation failed',
          errors: [`Database error: ${exception.code}`]
        };
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
