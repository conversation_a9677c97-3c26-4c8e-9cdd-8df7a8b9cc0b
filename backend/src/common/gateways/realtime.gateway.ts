import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
    role: string;
  };
}

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/realtime',
})
export class RealtimeGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(RealtimeGateway.name);
  private connectedUsers = new Map<string, AuthenticatedSocket>();

  handleConnection(client: AuthenticatedSocket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    
    // Remove from connected users if authenticated
    if (client.user) {
      this.connectedUsers.delete(client.user.id);
      this.logger.log(`User ${client.user.email} disconnected`);
    }
  }

  @SubscribeMessage('authenticate')
  async handleAuthentication(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { token: string },
  ) {
    try {
      // Here you would verify the JWT token
      // For now, we'll simulate authentication
      // In a real implementation, you'd use the JWT service to verify the token
      
      // Mock user data - replace with actual JWT verification
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'MANAGER',
      };

      client.user = user;
      this.connectedUsers.set(user.id, client);
      
      client.emit('authenticated', { success: true, user });
      this.logger.log(`User ${user.email} authenticated via WebSocket`);
    } catch (error) {
      this.logger.error('WebSocket authentication failed:', error);
      client.emit('authenticated', { success: false, error: 'Authentication failed' });
    }
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ) {
    if (!client.user) {
      client.emit('error', { message: 'Not authenticated' });
      return;
    }

    client.join(data.room);
    this.logger.log(`User ${client.user.email} joined room: ${data.room}`);
    client.emit('joined-room', { room: data.room });
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ) {
    client.leave(data.room);
    this.logger.log(`Client ${client.id} left room: ${data.room}`);
    client.emit('left-room', { room: data.room });
  }

  // Real-time update methods

  /**
   * Broadcast trip status update to all connected clients
   */
  broadcastTripUpdate(tripId: string, update: any) {
    this.server.emit('trip-updated', {
      tripId,
      ...update,
      timestamp: new Date().toISOString(),
    });
    this.logger.debug(`Broadcasted trip update for trip ${tripId}`);
  }

  /**
   * Broadcast vehicle status update
   */
  broadcastVehicleUpdate(vehicleId: string, update: any) {
    this.server.emit('vehicle-updated', {
      vehicleId,
      ...update,
      timestamp: new Date().toISOString(),
    });
    this.logger.debug(`Broadcasted vehicle update for vehicle ${vehicleId}`);
  }

  /**
   * Broadcast driver assignment update
   */
  broadcastDriverUpdate(driverId: string, update: any) {
    this.server.emit('driver-updated', {
      driverId,
      ...update,
      timestamp: new Date().toISOString(),
    });
    this.logger.debug(`Broadcasted driver update for driver ${driverId}`);
  }

  /**
   * Send notification to specific user
   */
  sendNotificationToUser(userId: string, notification: any) {
    const userSocket = this.connectedUsers.get(userId);
    if (userSocket) {
      userSocket.emit('notification', {
        ...notification,
        timestamp: new Date().toISOString(),
      });
      this.logger.debug(`Sent notification to user ${userId}`);
    }
  }

  /**
   * Send notification to users with specific role
   */
  sendNotificationToRole(role: string, notification: any) {
    this.connectedUsers.forEach((socket, userId) => {
      if (socket.user && socket.user.role === role) {
        socket.emit('notification', {
          ...notification,
          timestamp: new Date().toISOString(),
        });
      }
    });
    this.logger.debug(`Sent notification to users with role ${role}`);
  }

  /**
   * Broadcast system alert
   */
  broadcastSystemAlert(alert: any) {
    this.server.emit('system-alert', {
      ...alert,
      timestamp: new Date().toISOString(),
    });
    this.logger.debug('Broadcasted system alert');
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get connected users by role
   */
  getConnectedUsersByRole(role: string): number {
    let count = 0;
    this.connectedUsers.forEach((socket) => {
      if (socket.user && socket.user.role === role) {
        count++;
      }
    });
    return count;
  }
}
