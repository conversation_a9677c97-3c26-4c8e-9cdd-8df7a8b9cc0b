export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
  timestamp: string;
  requestId?: string;
}

export class ResponseFormatter {
  /**
   * Format successful response
   */
  static success<T>(
    data: T,
    message?: string,
    meta?: ApiResponse<T>['meta'],
    requestId?: string,
  ): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      meta,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format error response
   */
  static error(
    message: string,
    errors?: string[],
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message,
      errors,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format paginated response
   */
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    message?: string,
    requestId?: string,
  ): ApiResponse<T[]> {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      success: true,
      data,
      message,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format validation error response
   */
  static validationError(
    errors: string[],
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message: 'Validation failed',
      errors,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format not found response
   */
  static notFound(
    resource: string,
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message: `${resource} not found`,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format unauthorized response
   */
  static unauthorized(
    message: string = 'Unauthorized access',
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format forbidden response
   */
  static forbidden(
    message: string = 'Access forbidden',
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format created response
   */
  static created<T>(
    data: T,
    message: string = 'Resource created successfully',
    requestId?: string,
  ): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format updated response
   */
  static updated<T>(
    data: T,
    message: string = 'Resource updated successfully',
    requestId?: string,
  ): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }

  /**
   * Format deleted response
   */
  static deleted(
    message: string = 'Resource deleted successfully',
    requestId?: string,
  ): ApiResponse<null> {
    return {
      success: true,
      data: null,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    };
  }
}
