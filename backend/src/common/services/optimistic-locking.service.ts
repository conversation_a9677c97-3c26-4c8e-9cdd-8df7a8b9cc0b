import { Injectable, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface OptimisticLockingData {
  version: number;
  lastModifiedBy?: string;
}

export interface UpdateWithOptimisticLocking<T> extends OptimisticLockingData {
  data: T;
}

@Injectable()
export class OptimisticLockingService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Update a Trip with optimistic locking
   */
  async updateTrip(
    id: string,
    updateData: any,
    currentVersion: number,
    userId?: string,
  ): Promise<any> {
    try {
      return await this.prisma.trip.update({
        where: {
          id,
          version: currentVersion, // This ensures the version hasn't changed
        },
        data: {
          ...updateData,
          version: {
            increment: 1, // Increment version
          },
          lastModifiedBy: userId,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        // Record not found or version mismatch
        throw new ConflictException(
          'The record has been modified by another user. Please refresh and try again.',
        );
      }
      throw error;
    }
  }

  /**
   * Update a Vehicle with optimistic locking
   */
  async updateVehicle(
    id: string,
    updateData: any,
    currentVersion: number,
    userId?: string,
  ): Promise<any> {
    try {
      return await this.prisma.vehicle.update({
        where: {
          id,
          // Note: Vehicle model would need version field added
        },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new ConflictException(
          'The vehicle has been modified by another user. Please refresh and try again.',
        );
      }
      throw error;
    }
  }

  /**
   * Generic optimistic locking update
   */
  async updateWithOptimisticLocking<T>(
    model: string,
    id: string,
    updateData: any,
    currentVersion: number,
    userId?: string,
  ): Promise<T> {
    try {
      const modelDelegate = (this.prisma as any)[model];
      
      if (!modelDelegate) {
        throw new Error(`Model ${model} not found`);
      }

      return await modelDelegate.update({
        where: {
          id,
          version: currentVersion,
        },
        data: {
          ...updateData,
          version: {
            increment: 1,
          },
          lastModifiedBy: userId,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new ConflictException(
          'The record has been modified by another user. Please refresh and try again.',
        );
      }
      throw error;
    }
  }

  /**
   * Check if a record has been modified since a given version
   */
  async checkVersion(model: string, id: string, expectedVersion: number): Promise<boolean> {
    try {
      const modelDelegate = (this.prisma as any)[model];
      
      if (!modelDelegate) {
        throw new Error(`Model ${model} not found`);
      }

      const record = await modelDelegate.findUnique({
        where: { id },
        select: { version: true },
      });

      return record && record.version === expectedVersion;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current version of a record
   */
  async getCurrentVersion(model: string, id: string): Promise<number | null> {
    try {
      const modelDelegate = (this.prisma as any)[model];
      
      if (!modelDelegate) {
        throw new Error(`Model ${model} not found`);
      }

      const record = await modelDelegate.findUnique({
        where: { id },
        select: { version: true },
      });

      return record ? record.version : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Validate optimistic locking data
   */
  validateOptimisticLockingData(data: any): OptimisticLockingData {
    if (typeof data.version !== 'number') {
      throw new Error('Version field is required for optimistic locking');
    }

    return {
      version: data.version,
      lastModifiedBy: data.lastModifiedBy,
    };
  }
}
