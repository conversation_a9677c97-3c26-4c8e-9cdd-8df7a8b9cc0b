import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  VERBOSE = 4,
}

export interface LogContext {
  userId?: string;
  vehicleId?: string;
  tripId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  duration?: number;
  [key: string]: any;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private logLevel: LogLevel;
  private serviceName: string;

  constructor(private readonly configService: ConfigService) {
    this.logLevel = this.getLogLevelFromConfig();
    this.serviceName = this.configService.get<string>('app.serviceName', 'FleetFusion');
  }

  private getLogLevelFromConfig(): LogLevel {
    const level = this.configService.get<string>('LOG_LEVEL', 'INFO').toUpperCase();
    return LogLevel[level as keyof typeof LogLevel] ?? LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private formatMessage(level: string, message: any, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const pid = process.pid;
    
    const baseLog = {
      timestamp,
      level,
      service: this.serviceName,
      pid,
      message: typeof message === 'object' ? JSON.stringify(message) : message,
    };

    if (context) {
      Object.assign(baseLog, context);
    }

    return JSON.stringify(baseLog);
  }

  log(message: any, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message, context));
    }
  }

  error(message: any, trace?: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const errorLog = {
        ...context,
        trace,
        error: true,
      };
      console.error(this.formatMessage('ERROR', message, errorLog));
    }
  }

  warn(message: any, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message, context));
    }
  }

  debug(message: any, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.debug(this.formatMessage('DEBUG', message, context));
    }
  }

  verbose(message: any, context?: LogContext): void {
    if (this.shouldLog(LogLevel.VERBOSE)) {
      console.log(this.formatMessage('VERBOSE', message, context));
    }
  }

  // Business logic specific logging methods
  logApiRequest(method: string, url: string, userId?: string, ip?: string, userAgent?: string): void {
    this.log('API Request', {
      method,
      url,
      userId,
      ip,
      userAgent,
      type: 'api_request',
    });
  }

  logApiResponse(method: string, url: string, statusCode: number, duration: number, userId?: string): void {
    this.log('API Response', {
      method,
      url,
      statusCode,
      duration,
      userId,
      type: 'api_response',
    });
  }

  logBusinessEvent(event: string, data: any, userId?: string): void {
    this.log('Business Event', {
      event,
      data,
      userId,
      type: 'business_event',
    });
  }

  logSecurityEvent(event: string, details: any, userId?: string, ip?: string): void {
    this.warn('Security Event', {
      event,
      details,
      userId,
      ip,
      type: 'security_event',
    });
  }

  logPerformanceMetric(metric: string, value: number, unit: string, context?: LogContext): void {
    this.log('Performance Metric', {
      metric,
      value,
      unit,
      ...context,
      type: 'performance_metric',
    });
  }

  logDatabaseQuery(query: string, duration: number, affectedRows?: number): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.debug('Database Query', {
        query: query.replace(/\s+/g, ' ').trim(),
        duration,
        affectedRows,
        type: 'database_query',
      });
    }
  }

  logVehicleEvent(event: string, vehicleId: string, data: any, userId?: string): void {
    this.logBusinessEvent(`vehicle_${event}`, {
      vehicleId,
      ...data,
    }, userId);
  }

  logTripEvent(event: string, tripId: string, data: any, userId?: string): void {
    this.logBusinessEvent(`trip_${event}`, {
      tripId,
      ...data,
    }, userId);
  }

  logMaintenanceEvent(event: string, maintenanceId: string, data: any, userId?: string): void {
    this.logBusinessEvent(`maintenance_${event}`, {
      maintenanceId,
      ...data,
    }, userId);
  }
}
