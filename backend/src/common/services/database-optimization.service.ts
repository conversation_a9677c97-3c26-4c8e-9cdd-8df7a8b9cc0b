import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { VehicleType, VehicleStatus } from '@prisma/client';

/**
 * Service for optimized database operations and query patterns
 */
@Injectable()
export class DatabaseOptimizationService {
  constructor(private prisma: PrismaService) {}

  /**
   * Optimized query for finding vehicles with minimal data for dropdowns/lists
   */
  async findVehiclesMinimal(vehicleType?: VehicleType) {
    const where = vehicleType ? { vehicleType } : {};

    return this.prisma.vehicle.findMany({
      where,
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
        status: true,
        vehicleType: true,
      },
      orderBy: [
        { status: 'asc' }, // Available vehicles first
        { plateNumber: 'asc' }
      ],
    });
  }

  /**
   * Optimized query for finding available vehicles with assignment status
   */
  async findAvailableVehicles(vehicleType?: VehicleType) {
    const where: any = {
      status: VehicleStatus.AVAILABLE,
    };

    if (vehicleType) {
      where.vehicleType = vehicleType;
    }

    return this.prisma.vehicle.findMany({
      where,
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
        year: true,
        status: true,
        vehicleType: true,
        // Only get active assignments to check availability
        assignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
          },
        },
      },
      orderBy: {
        plateNumber: 'asc',
      },
    });
  }

  /**
   * Optimized query for finding drivers with minimal data
   */
  async findDriversMinimal() {
    return this.prisma.user.findMany({
      where: {
        role: 'DRIVER',
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        status: true,
        licenseNumber: true,
      },
      orderBy: [
        { status: 'asc' }, // Active drivers first
        { firstName: 'asc' },
        { lastName: 'asc' }
      ],
    });
  }

  /**
   * Optimized query for finding available drivers
   */
  async findAvailableDrivers() {
    return this.prisma.user.findMany({
      where: {
        role: 'DRIVER',
        OR: [
          { status: 'Active' },
          { status: 'Available' },
          { status: null },
        ],
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        status: true,
        licenseNumber: true,
        // Only get active assignments to check availability
        assignments: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            status: true,
          },
        },
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' }
      ],
    });
  }

  /**
   * Batch operation for updating multiple vehicle statuses
   */
  async batchUpdateVehicleStatus(updates: Array<{ id: string; status: VehicleStatus }>) {
    const operations = updates.map(({ id, status }) =>
      this.prisma.vehicle.update({
        where: { id },
        data: { status },
      })
    );

    return this.prisma.$transaction(operations);
  }

  /**
   * Batch operation for updating multiple user statuses
   */
  async batchUpdateUserStatus(updates: Array<{ id: string; status: string }>) {
    const operations = updates.map(({ id, status }) =>
      this.prisma.user.update({
        where: { id },
        data: { status },
      })
    );

    return this.prisma.$transaction(operations);
  }

  /**
   * Optimized query for trip dashboard with pagination
   */
  async findTripsForDashboard(page: number = 1, limit: number = 20, filters?: any) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }
    if (filters?.driverId) {
      where.driverId = filters.driverId;
    }
    if (filters?.vehicleId) {
      where.vehicleId = filters.vehicleId;
    }

    const [trips, total] = await Promise.all([
      this.prisma.trip.findMany({
        where,
        select: {
          id: true,
          startLocation: true,
          endLocation: true,
          startTime: true,
          endTime: true,
          status: true,
          distance: true,
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          vehicle: {
            select: {
              id: true,
              plateNumber: true,
              make: true,
              model: true,
            },
          },
        },
        orderBy: {
          startTime: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.trip.count({ where }),
    ]);

    return {
      trips,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Optimized query for assignment statistics
   */
  async getAssignmentStatistics() {
    const [
      totalAssignments,
      activeAssignments,
      completedAssignments,
      vehicleUtilization,
      driverUtilization,
    ] = await Promise.all([
      this.prisma.vehicleAssignment.count(),
      this.prisma.vehicleAssignment.count({
        where: { status: 'ACTIVE' },
      }),
      this.prisma.vehicleAssignment.count({
        where: { status: 'COMPLETED' },
      }),
      this.prisma.vehicle.count({
        where: { status: 'ASSIGNED' },
      }),
      this.prisma.user.count({
        where: {
          role: 'DRIVER',
          status: 'Assigned',
        },
      }),
    ]);

    const totalVehicles = await this.prisma.vehicle.count();
    const totalDrivers = await this.prisma.user.count({
      where: { role: 'DRIVER' },
    });

    return {
      totalAssignments,
      activeAssignments,
      completedAssignments,
      vehicleUtilizationRate: totalVehicles > 0 ? (vehicleUtilization / totalVehicles) * 100 : 0,
      driverUtilizationRate: totalDrivers > 0 ? (driverUtilization / totalDrivers) * 100 : 0,
    };
  }

  /**
   * Optimized query for checking resource conflicts
   */
  async checkResourceConflicts(vehicleId: string, driverId: string, startDate: Date, endDate?: Date) {
    const endDateFilter = endDate ? { lte: endDate } : undefined;
    
    const [vehicleConflicts, driverConflicts] = await Promise.all([
      this.prisma.vehicleAssignment.findMany({
        where: {
          vehicleId,
          status: 'ACTIVE',
          startDate: { lte: endDate || new Date('2099-12-31') },
          OR: [
            { endDate: null },
            { endDate: { gte: startDate } },
          ],
        },
        select: {
          id: true,
          startDate: true,
          endDate: true,
        },
      }),
      this.prisma.vehicleAssignment.findMany({
        where: {
          driverId,
          status: 'ACTIVE',
          startDate: { lte: endDate || new Date('2099-12-31') },
          OR: [
            { endDate: null },
            { endDate: { gte: startDate } },
          ],
        },
        select: {
          id: true,
          startDate: true,
          endDate: true,
        },
      }),
    ]);

    return {
      hasVehicleConflict: vehicleConflicts.length > 0,
      hasDriverConflict: driverConflicts.length > 0,
      vehicleConflicts,
      driverConflicts,
    };
  }
}
