import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { LoggerService } from '../services/logger.service';

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  constructor(private readonly logger: LoggerService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    // Set security headers
    this.setSecurityHeaders(res);
    
    // Log security events
    this.logSecurityEvents(req);
    
    // Validate request
    this.validateRequest(req);

    next();
  }

  private setSecurityHeaders(res: Response): void {
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    // Content Security Policy
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "connect-src 'self'; " +
      "font-src 'self'; " +
      "object-src 'none'; " +
      "media-src 'self'; " +
      "frame-src 'none';"
    );

    // X-Content-Type-Options
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // X-Frame-Options
    res.setHeader('X-Frame-Options', 'DENY');

    // X-XSS-Protection
    res.setHeader('X-XSS-Protection', '1; mode=block');

    // Referrer Policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Permissions Policy
    res.setHeader(
      'Permissions-Policy',
      'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()'
    );

    // Strict Transport Security (HTTPS only)
    if (process.env.NODE_ENV === 'production') {
      res.setHeader(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains; preload'
      );
    }
  }

  private logSecurityEvents(req: Request): void {
    const suspiciousPatterns = [
      /[<>'"]/g, // XSS patterns
      /union.*select/i, // SQL injection patterns
      /script.*src/i, // Script injection
      /javascript:/i, // JavaScript protocol
      /data:.*base64/i, // Base64 data URLs
      /\.\.\/.*\.\./g, // Path traversal
      /(wget|curl|nmap|nikto)/i, // Reconnaissance tools
    ];

    const userAgent = req.headers['user-agent'] || '';
    const url = req.url;
    const method = req.method;
    const ip = req.ip || req.connection.remoteAddress;

    // Check for suspicious patterns in URL
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(url)) {
        this.logger.logSecurityEvent('suspicious_url_pattern', {
          pattern: pattern.toString(),
          url,
          method,
          userAgent,
        }, undefined, ip);
        break;
      }
    }

    // Check for suspicious user agents
    const suspiciousUserAgents = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scanner/i,
      /sqlmap/i,
      /nmap/i,
      /nikto/i,
      /dirb/i,
      /dirbuster/i,
      /gobuster/i,
    ];

    for (const pattern of suspiciousUserAgents) {
      if (pattern.test(userAgent)) {
        this.logger.logSecurityEvent('suspicious_user_agent', {
          userAgent,
          url,
          method,
        }, undefined, ip);
        break;
      }
    }

    // Log admin access attempts
    if (url.includes('/admin') || url.includes('/dashboard')) {
      this.logger.logSecurityEvent('admin_access_attempt', {
        url,
        method,
        userAgent,
      }, undefined, ip);
    }

    // Log authentication endpoints
    if (url.includes('/auth') || url.includes('/login')) {
      this.logger.logSecurityEvent('auth_endpoint_access', {
        url,
        method,
        userAgent,
      }, undefined, ip);
    }
  }

  private validateRequest(req: Request): void {
    // Check request size
    const contentLength = parseInt(req.headers['content-length'] || '0');
    const maxRequestSize = 10 * 1024 * 1024; // 10MB

    if (contentLength > maxRequestSize) {
      this.logger.logSecurityEvent('large_request_size', {
        contentLength,
        maxRequestSize,
        url: req.url,
        method: req.method,
      }, undefined, req.ip);
    }

    // Check for excessive headers
    const headerCount = Object.keys(req.headers).length;
    const maxHeaders = 50;

    if (headerCount > maxHeaders) {
      this.logger.logSecurityEvent('excessive_headers', {
        headerCount,
        maxHeaders,
        url: req.url,
        method: req.method,
      }, undefined, req.ip);
    }

    // Check for null bytes
    const url = req.url;
    if (url.includes('\0')) {
      this.logger.logSecurityEvent('null_byte_injection', {
        url,
        method: req.method,
      }, undefined, req.ip);
    }
  }
}
