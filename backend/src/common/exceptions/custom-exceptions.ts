import { HttpException, HttpStatus } from '@nestjs/common';

export class BusinessLogicException extends HttpException {
  constructor(message: string, statusCode: HttpStatus = HttpStatus.BAD_REQUEST) {
    super(message, statusCode);
  }
}

export class ResourceNotFoundException extends HttpException {
  constructor(resource: string, id?: string) {
    const message = id 
      ? `${resource} with ID '${id}' not found`
      : `${resource} not found`;
    super(message, HttpStatus.NOT_FOUND);
  }
}

export class ValidationException extends HttpException {
  constructor(message: string, details?: any) {
    super({
      message,
      details,
      timestamp: new Date().toISOString(),
    }, HttpStatus.BAD_REQUEST);
  }
}

export class ConflictException extends HttpException {
  constructor(message: string) {
    super(message, HttpStatus.CONFLICT);
  }
}

export class DatabaseException extends HttpException {
  constructor(message: string, originalError?: any) {
    super({
      message: 'Database operation failed',
      details: message,
      originalError: originalError?.message || 'Unknown database error',
      timestamp: new Date().toISOString(),
    }, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
