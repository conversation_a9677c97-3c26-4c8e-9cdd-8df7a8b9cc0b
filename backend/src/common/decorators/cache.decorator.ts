import { SetMetadata } from '@nestjs/common';

export const CACHE_KEY = 'cache';

export interface CacheOptions {
  key?: string;
  ttl?: number; // Time to live in seconds
  keyGenerator?: (...args: any[]) => string;
}

/**
 * Cache decorator for methods
 * @param options Cache configuration options
 */
export const Cache = (options: CacheOptions = {}) => {
  return SetMetadata(CACHE_KEY, options);
};

/**
 * Cache invalidation decorator
 * @param patterns Array of cache key patterns to invalidate
 */
export const CacheEvict = (patterns: string[]) => {
  return SetMetadata('cache-evict', patterns);
};

/**
 * Generate cache key from method arguments
 */
export function generateCacheKey(
  className: string,
  methodName: string,
  args: any[],
  customGenerator?: (...args: any[]) => string
): string {
  if (customGenerator) {
    return customGenerator(...args);
  }

  const argsKey = args.length > 0 ? `:${JSON.stringify(args)}` : '';
  return `${className}:${methodName}${argsKey}`;
}
