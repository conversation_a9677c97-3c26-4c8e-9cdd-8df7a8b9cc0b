"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const prisma_module_1 = require("./prisma/prisma.module");
const vehicles_module_1 = require("./vehicles/vehicles.module");
const trips_module_1 = require("./trips/trips.module");
const users_module_1 = require("./users/users.module");
const business_partners_module_1 = require("./business-partners/business-partners.module");
const distance_module_1 = require("./distance/distance.module");
const fuel_module_1 = require("./fuel/fuel.module");
const common_module_1 = require("./common/common.module");
const health_module_1 = require("./health/health.module");
const app_config_1 = require("./config/app.config");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [app_config_1.default],
            }),
            common_module_1.CommonModule,
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            vehicles_module_1.VehiclesModule,
            trips_module_1.TripsModule,
            users_module_1.UsersModule,
            business_partners_module_1.BusinessPartnersModule,
            distance_module_1.DistanceModule,
            fuel_module_1.FuelModule,
            health_module_1.HealthModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map