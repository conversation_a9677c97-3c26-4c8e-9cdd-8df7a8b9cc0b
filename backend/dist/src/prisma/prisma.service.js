"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrismaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
let PrismaService = PrismaService_1 = class PrismaService extends client_1.PrismaClient {
    logger = new common_1.Logger(PrismaService_1.name);
    constructor() {
        super({
            log: ['query', 'info', 'warn', 'error'],
            datasources: {
                db: {
                    url: process.env.DATABASE_URL,
                },
            },
        });
    }
    async onModuleInit() {
        try {
            await this.$connect();
            this.logger.log('Successfully connected to database');
            await this.$queryRaw `SELECT 1`;
            this.logger.log('Database connection pool warmed up');
            if (process.env.NODE_ENV === 'development') {
                this.logger.log('Database logging enabled for development environment');
            }
        }
        catch (error) {
            this.logger.error('Failed to connect to database:', error);
            throw error;
        }
    }
    async onModuleDestroy() {
        try {
            await this.$disconnect();
            this.logger.log('Disconnected from database');
        }
        catch (error) {
            this.logger.error('Error disconnecting from database:', error);
        }
    }
    async executeTransaction(operations, maxRetries = 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await this.$transaction(operations, {
                    timeout: 10000,
                    isolationLevel: 'ReadCommitted',
                });
            }
            catch (error) {
                lastError = error;
                if (error.code === 'P2034' ||
                    error.code === 'P2028' ||
                    (error.message && error.message.includes('deadlock'))) {
                    if (attempt < maxRetries) {
                        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                        this.logger.warn(`Transaction failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        continue;
                    }
                }
                throw error;
            }
        }
        throw lastError;
    }
    async healthCheck() {
        try {
            await this.$queryRaw `SELECT 1`;
            return {
                status: 'healthy',
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Database health check failed:', error);
            return {
                status: 'unhealthy',
                timestamp: new Date(),
            };
        }
    }
    logQuery(query, duration, params) {
        if (duration > 1000) {
            this.logger.warn(`Slow query detected: ${duration}ms - ${query.substring(0, 100)}...`);
        }
        else if (process.env.NODE_ENV === 'development') {
            this.logger.debug(`Query: ${duration}ms - ${query.substring(0, 100)}...`);
        }
    }
    async monitoredQuery(queryFn, queryName) {
        const start = Date.now();
        try {
            const result = await queryFn();
            const duration = Date.now() - start;
            if (queryName && duration > 100) {
                this.logger.debug(`${queryName} completed in ${duration}ms`);
            }
            return result;
        }
        catch (error) {
            const duration = Date.now() - start;
            this.logger.error(`Query failed after ${duration}ms: ${queryName || 'Unknown'}`, error);
            throw error;
        }
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = PrismaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map