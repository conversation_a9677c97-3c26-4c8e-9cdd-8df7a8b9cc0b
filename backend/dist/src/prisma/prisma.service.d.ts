import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
export declare class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private readonly logger;
    constructor();
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    executeTransaction<T>(operations: (prisma: PrismaClient) => Promise<T>, maxRetries?: number): Promise<T>;
    healthCheck(): Promise<{
        status: string;
        timestamp: Date;
    }>;
    logQuery(query: string, duration: number, params?: any): void;
    monitoredQuery<T>(queryFn: () => Promise<T>, queryName?: string): Promise<T>;
}
