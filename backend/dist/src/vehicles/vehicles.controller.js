"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehiclesController = void 0;
const common_1 = require("@nestjs/common");
const vehicles_service_1 = require("./vehicles.service");
const vehicle_assignment_service_1 = require("./vehicle-assignment.service");
const prisma_service_1 = require("../prisma/prisma.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_vehicle_dto_1 = require("./dto/create-vehicle.dto");
const update_vehicle_dto_1 = require("./dto/update-vehicle.dto");
let VehiclesController = class VehiclesController {
    vehiclesService;
    assignmentService;
    prisma;
    constructor(vehiclesService, assignmentService, prisma) {
        this.vehiclesService = vehiclesService;
        this.assignmentService = assignmentService;
        this.prisma = prisma;
    }
    findAll(type) {
        if (type === 'TRUCK') {
            return this.vehiclesService.findTrucks();
        }
        if (type === 'TRAILER') {
            return this.vehiclesService.findTrailers();
        }
        return this.vehiclesService.findAll();
    }
    findTrucks() {
        return this.vehiclesService.findTrucks();
    }
    findTrailers() {
        return this.vehiclesService.findTrailers();
    }
    findAvailableTrucks() {
        return this.vehiclesService.findAvailableTrucks();
    }
    findAvailableTrailers() {
        return this.vehiclesService.findAvailableTrailers();
    }
    async findAllAssignments() {
        console.log('Backend: Vehicle assignments endpoint called');
        try {
            console.log('Backend: Starting Prisma query for assignments...');
            const rawAssignments = await this.prisma.vehicleAssignment.findMany({
                include: {
                    driver: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    vehicle: {
                        select: {
                            id: true,
                            plateNumber: true,
                            make: true,
                            model: true,
                            year: true,
                            status: true,
                        },
                    },
                },
            });
            console.log(`Backend: Direct query found ${rawAssignments.length} assignments`);
            if (rawAssignments.length > 0) {
                console.log('Backend: Sample assignment:', JSON.stringify(rawAssignments[0], null, 2));
            }
            else {
                console.log('Backend: No assignments found in database');
            }
            console.log('Backend: About to return assignments data');
            return rawAssignments;
        }
        catch (error) {
            console.error('Backend: Error fetching assignments:', error);
            console.error('Backend: Error stack:', error.stack);
            throw error;
        }
    }
    findOne(id) {
        return this.vehiclesService.findOne(id);
    }
    create(createVehicleDto) {
        const formattedDto = {
            ...createVehicleDto,
            purchaseDate: createVehicleDto.purchaseDate
                ? new Date(createVehicleDto.purchaseDate).toISOString()
                : undefined
        };
        return this.vehiclesService.create(formattedDto);
    }
    update(id, updateVehicleDto) {
        const formattedDto = {
            ...updateVehicleDto,
            lastMaintenance: updateVehicleDto.lastMaintenance
                ? new Date(updateVehicleDto.lastMaintenance)
                : undefined
        };
        return this.vehiclesService.update(id, formattedDto);
    }
    fullUpdate(id, updateVehicleDto) {
        const formattedDto = {
            ...updateVehicleDto,
            lastMaintenance: updateVehicleDto.lastMaintenance
                ? new Date(updateVehicleDto.lastMaintenance)
                : undefined
        };
        return this.vehiclesService.update(id, formattedDto);
    }
    remove(id) {
        return this.vehiclesService.delete(id);
    }
    findAssignments(id) {
        return this.assignmentService.findAllByVehicle(id);
    }
    createAssignment(id, createAssignmentDto) {
        return this.assignmentService.create({
            vehicleId: id,
            driverId: createAssignmentDto.driverId,
            startDate: new Date(createAssignmentDto.startDate),
            endDate: createAssignmentDto.endDate ? new Date(createAssignmentDto.endDate) : undefined,
        });
    }
    completeAssignment(id) {
        return this.assignmentService.complete(id);
    }
    cancelAssignment(id) {
        return this.assignmentService.cancel(id);
    }
    async getMaintenanceLogs(id) {
        return this.vehiclesService.findMaintenanceLogs(id);
    }
    async createMaintenanceLog(id, data) {
        return this.vehiclesService.createMaintenanceLog(id, data);
    }
    async updateMaintenanceLog(logId, data) {
        return this.vehiclesService.updateMaintenanceLog(logId, data);
    }
    async deleteMaintenanceLog(logId) {
        return this.vehiclesService.deleteMaintenanceLog(logId);
    }
    async getUpcomingMaintenance() {
        return this.vehiclesService.getUpcomingMaintenance();
    }
};
exports.VehiclesController = VehiclesController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], VehiclesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('trucks'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], VehiclesController.prototype, "findTrucks", null);
__decorate([
    (0, common_1.Get)('trailers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], VehiclesController.prototype, "findTrailers", null);
__decorate([
    (0, common_1.Get)('trucks/available'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], VehiclesController.prototype, "findAvailableTrucks", null);
__decorate([
    (0, common_1.Get)('trailers/available'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], VehiclesController.prototype, "findAvailableTrailers", null);
__decorate([
    (0, common_1.Get)('assignments'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], VehiclesController.prototype, "findAllAssignments", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], VehiclesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_vehicle_dto_1.CreateVehicleDto]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], VehiclesController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_vehicle_dto_1.UpdateVehicleDto]),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], VehiclesController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_vehicle_dto_1.UpdateVehicleDto]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], VehiclesController.prototype, "fullUpdate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], VehiclesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/assignments'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], VehiclesController.prototype, "findAssignments", null);
__decorate([
    (0, common_1.Post)(':id/assignments'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], VehiclesController.prototype, "createAssignment", null);
__decorate([
    (0, common_1.Patch)('assignments/:id/complete'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], VehiclesController.prototype, "completeAssignment", null);
__decorate([
    (0, common_1.Patch)('assignments/:id/cancel'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], VehiclesController.prototype, "cancelAssignment", null);
__decorate([
    (0, common_1.Get)(':id/maintenance-logs'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_r = typeof Promise !== "undefined" && Promise) === "function" ? _r : Object)
], VehiclesController.prototype, "getMaintenanceLogs", null);
__decorate([
    (0, common_1.Post)(':id/maintenance-logs'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], VehiclesController.prototype, "createMaintenanceLog", null);
__decorate([
    (0, common_1.Put)(':id/maintenance-logs/:logId'),
    __param(0, (0, common_1.Param)('logId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], VehiclesController.prototype, "updateMaintenanceLog", null);
__decorate([
    (0, common_1.Delete)(':id/maintenance-logs/:logId'),
    __param(0, (0, common_1.Param)('logId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_u = typeof Promise !== "undefined" && Promise) === "function" ? _u : Object)
], VehiclesController.prototype, "deleteMaintenanceLog", null);
__decorate([
    (0, common_1.Get)('maintenance/upcoming'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_v = typeof Promise !== "undefined" && Promise) === "function" ? _v : Object)
], VehiclesController.prototype, "getUpcomingMaintenance", null);
exports.VehiclesController = VehiclesController = __decorate([
    (0, common_1.Controller)('vehicles'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [vehicles_service_1.VehiclesService,
        vehicle_assignment_service_1.VehicleAssignmentService,
        prisma_service_1.PrismaService])
], VehiclesController);
//# sourceMappingURL=vehicles.controller.js.map