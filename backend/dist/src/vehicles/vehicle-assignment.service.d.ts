import { PrismaService } from '../prisma/prisma.service';
import { VehicleAssignment } from '@prisma/client';
export declare class VehicleAssignmentService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: {
        vehicleId: string;
        driverId: string;
        startDate: Date;
        endDate?: Date;
    }): Promise<VehicleAssignment>;
    findAllByVehicle(vehicleId: string): Promise<VehicleAssignment[]>;
    findAll(): Promise<VehicleAssignment[]>;
    complete(id: string): Promise<VehicleAssignment>;
    cancel(id: string): Promise<VehicleAssignment>;
    private validateDriverAvailability;
    private validateVehicleAvailability;
}
