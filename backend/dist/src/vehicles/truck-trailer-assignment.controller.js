"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TruckTrailerAssignmentController = void 0;
const common_1 = require("@nestjs/common");
const truck_trailer_assignment_service_1 = require("./truck-trailer-assignment.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_truck_trailer_assignment_dto_1 = require("./dto/create-truck-trailer-assignment.dto");
const update_truck_trailer_assignment_dto_1 = require("./dto/update-truck-trailer-assignment.dto");
let TruckTrailerAssignmentController = class TruckTrailerAssignmentController {
    assignmentService;
    constructor(assignmentService) {
        this.assignmentService = assignmentService;
    }
    async findAll(active) {
        if (active === 'true') {
            return this.assignmentService.findActive();
        }
        return this.assignmentService.findAll();
    }
    async findActive() {
        return this.assignmentService.findActive();
    }
    async findOne(id) {
        return this.assignmentService.findOne(id);
    }
    async findByTruck(truckId) {
        return this.assignmentService.findByTruck(truckId);
    }
    async findByTrailer(trailerId) {
        return this.assignmentService.findByTrailer(trailerId);
    }
    async create(createAssignmentDto, req) {
        if (!createAssignmentDto.assignedBy) {
            createAssignmentDto.assignedBy = req.user.id;
        }
        return this.assignmentService.create(createAssignmentDto);
    }
    async update(id, updateAssignmentDto) {
        return this.assignmentService.update(id, updateAssignmentDto);
    }
    async complete(id) {
        return this.assignmentService.complete(id);
    }
    async cancel(id) {
        return this.assignmentService.cancel(id);
    }
    async remove(id) {
        return this.assignmentService.delete(id);
    }
};
exports.TruckTrailerAssignmentController = TruckTrailerAssignmentController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "findActive", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('truck/:truckId'),
    __param(0, (0, common_1.Param)('truckId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "findByTruck", null);
__decorate([
    (0, common_1.Get)('trailer/:trailerId'),
    __param(0, (0, common_1.Param)('trailerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "findByTrailer", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_truck_trailer_assignment_dto_1.CreateTruckTrailerAssignmentDto, Object]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_truck_trailer_assignment_dto_1.UpdateTruckTrailerAssignmentDto]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/complete'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "complete", null);
__decorate([
    (0, common_1.Patch)(':id/cancel'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "cancel", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TruckTrailerAssignmentController.prototype, "remove", null);
exports.TruckTrailerAssignmentController = TruckTrailerAssignmentController = __decorate([
    (0, common_1.Controller)('truck-trailer-assignments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [truck_trailer_assignment_service_1.TruckTrailerAssignmentService])
], TruckTrailerAssignmentController);
//# sourceMappingURL=truck-trailer-assignment.controller.js.map