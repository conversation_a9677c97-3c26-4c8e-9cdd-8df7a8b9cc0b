import { InsuranceService } from './insurance.service';
import { CreateInsuranceDto } from '../insurance/dto/create-insurance.dto';
import { UpdateInsuranceDto } from '../insurance/dto/update-insurance.dto';
import { InsurancePolicy } from '@prisma/client';
export declare class InsuranceController {
    private readonly insuranceService;
    constructor(insuranceService: InsuranceService);
    findAll(vehicleId?: string): Promise<InsurancePolicy[]>;
    findOne(id: string): Promise<InsurancePolicy | null>;
    create(createInsuranceDto: CreateInsuranceDto): Promise<InsurancePolicy>;
    update(id: string, updateInsuranceDto: UpdateInsuranceDto): Promise<InsurancePolicy>;
    remove(id: string): Promise<InsurancePolicy>;
    getUpcomingRenewals(): Promise<InsurancePolicy[]>;
}
