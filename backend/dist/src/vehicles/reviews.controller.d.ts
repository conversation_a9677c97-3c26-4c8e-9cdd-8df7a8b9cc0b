import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from '../reviews/dto/create-review.dto';
import { UpdateReviewDto } from '../reviews/dto/update-review.dto';
import { VehicleReview } from '@prisma/client';
export declare class ReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    findAll(vehicleId?: string): Promise<VehicleReview[]>;
    findOne(id: string): Promise<VehicleReview | null>;
    create(createReviewDto: CreateReviewDto): Promise<VehicleReview>;
    update(id: string, updateReviewDto: UpdateReviewDto): Promise<VehicleReview>;
    remove(id: string): Promise<VehicleReview>;
    getUpcomingInspections(): Promise<VehicleReview[]>;
}
