"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ReviewsService = class ReviewsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll() {
        return this.prisma.vehicleReview.findMany({
            orderBy: { scheduledDate: 'asc' },
        });
    }
    async findOne(id) {
        return this.prisma.vehicleReview.findUnique({
            where: { id },
        });
    }
    async findAllByVehicle(vehicleId) {
        return this.prisma.vehicleReview.findMany({
            where: { vehicleId },
            orderBy: { scheduledDate: 'asc' },
        });
    }
    async create(data) {
        return this.prisma.vehicleReview.create({
            data,
        });
    }
    async update(id, data) {
        return this.prisma.vehicleReview.update({
            where: { id },
            data,
        });
    }
    async delete(id) {
        return this.prisma.vehicleReview.delete({
            where: { id },
        });
    }
    async getUpcomingInspections(days = 14) {
        const today = new Date();
        const threshold = new Date();
        threshold.setDate(today.getDate() + days);
        return this.prisma.vehicleReview.findMany({
            where: {
                scheduledDate: {
                    gte: today,
                    lte: threshold,
                },
                status: {
                    in: ['SCHEDULED', 'IN_PROGRESS'],
                },
            },
            orderBy: {
                scheduledDate: 'asc',
            },
        });
    }
};
exports.ReviewsService = ReviewsService;
exports.ReviewsService = ReviewsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ReviewsService);
//# sourceMappingURL=reviews.service.js.map