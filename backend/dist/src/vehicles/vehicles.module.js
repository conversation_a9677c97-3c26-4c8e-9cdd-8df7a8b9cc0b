"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehiclesModule = void 0;
const common_1 = require("@nestjs/common");
const vehicles_service_1 = require("./vehicles.service");
const vehicle_assignment_service_1 = require("./vehicle-assignment.service");
const truck_trailer_assignment_service_1 = require("./truck-trailer-assignment.service");
const enhanced_maintenance_service_1 = require("./enhanced-maintenance.service");
const insurance_service_1 = require("./insurance.service");
const reviews_service_1 = require("./reviews.service");
const vehicles_controller_1 = require("./vehicles.controller");
const truck_trailer_assignment_controller_1 = require("./truck-trailer-assignment.controller");
const enhanced_maintenance_controller_1 = require("./enhanced-maintenance.controller");
const insurance_controller_1 = require("./insurance.controller");
const reviews_controller_1 = require("./reviews.controller");
const maintenance_controller_1 = require("./maintenance.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let VehiclesModule = class VehiclesModule {
};
exports.VehiclesModule = VehiclesModule;
exports.VehiclesModule = VehiclesModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [
            vehicles_controller_1.VehiclesController,
            truck_trailer_assignment_controller_1.TruckTrailerAssignmentController,
            enhanced_maintenance_controller_1.EnhancedMaintenanceController,
            insurance_controller_1.InsuranceController,
            reviews_controller_1.ReviewsController,
            maintenance_controller_1.MaintenanceController
        ],
        providers: [
            vehicles_service_1.VehiclesService,
            vehicle_assignment_service_1.VehicleAssignmentService,
            truck_trailer_assignment_service_1.TruckTrailerAssignmentService,
            enhanced_maintenance_service_1.EnhancedMaintenanceService,
            insurance_service_1.InsuranceService,
            reviews_service_1.ReviewsService
        ],
        exports: [
            vehicles_service_1.VehiclesService,
            vehicle_assignment_service_1.VehicleAssignmentService,
            truck_trailer_assignment_service_1.TruckTrailerAssignmentService,
            enhanced_maintenance_service_1.EnhancedMaintenanceService,
            insurance_service_1.InsuranceService,
            reviews_service_1.ReviewsService
        ],
    })
], VehiclesModule);
//# sourceMappingURL=vehicles.module.js.map