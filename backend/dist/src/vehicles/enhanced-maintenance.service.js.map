{"version": 3, "file": "enhanced-maintenance.service.js", "sourceRoot": "", "sources": ["../../../src/vehicles/enhanced-maintenance.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAoF;AACpF,6DAAyD;AACzD,2CAOwB;AAoBjB,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACjB;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAG7C,sCAAsC,CAAC,WAAwB;QAC7D,MAAM,gBAAgB,GAA0B;YAC9C,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;SACb,CAAC;QAEF,IAAI,WAAW,KAAK,oBAAW,CAAC,KAAK,EAAE,CAAC;YACtC,OAAO;gBACL,GAAG,gBAAgB;gBACnB,QAAQ;gBACR,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;aACjB,CAAC;QACJ,CAAC;aAAM,IAAI,WAAW,KAAK,oBAAW,CAAC,OAAO,EAAE,CAAC;YAC/C,OAAO;gBACL,GAAG,gBAAgB;gBACnB,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,mBAAmB;gBACnB,iBAAiB;aAClB,CAAC;QACJ,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAGD,KAAK,CAAC,6BAA6B,CAAC,SAAiB;QACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,OAAO,EAAE;gBACP,eAAe,EAAE;oBACf,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;oBACzB,IAAI,EAAE,EAAE;iBACT;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,eAAe,GAAU,EAAE,CAAC;QAClC,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;QAE5C,IAAI,OAAO,CAAC,WAAW,KAAK,oBAAW,CAAC,KAAK,EAAE,CAAC;YAE9C,MAAM,oBAAoB,GAAG,IAAI,CAAC,kCAAkC,CAAC,cAAc,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YAC9G,eAAe,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,KAAK,oBAAW,CAAC,OAAO,EAAE,CAAC;YAEvD,MAAM,sBAAsB,GAAG,IAAI,CAAC,oCAAoC,CAAC,cAAc,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YAClH,eAAe,CAAC,IAAI,CAAC,GAAG,sBAAsB,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,kCAAkC,CAAC,OAAe,EAAE,IAAsB;QAChF,MAAM,eAAe,GAAU,EAAE,CAAC;QAGlC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACpC,GAAG,CAAC,QAAQ,KAAK,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC3E,CAAC;QACF,IAAI,CAAC,aAAa,IAAI,CAAC,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;YACvE,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,wBAAwB;gBAC9B,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,uBAAuB;gBACpC,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,uBAAuB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,cAAc,CAAC,CAAC;QAClF,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,GAAG,CAAC,uBAAuB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;YAC3F,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,wBAAwB;gBAC9B,QAAQ,EAAE,cAAc;gBACxB,WAAW,EAAE,uCAAuC;gBACpD,QAAQ,EAAE,QAAQ;gBAClB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QACxE,IAAI,CAAC,mBAAmB,IAAI,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;YACnF,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,qCAAqC;gBAClD,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,oCAAoC,CAAC,OAAe,EAAE,IAAsB;QAClF,MAAM,eAAe,GAAU,EAAE,CAAC;QAGlC,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QACxE,IAAI,CAAC,mBAAmB,IAAI,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;YACnF,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,iCAAiC;gBAC9C,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,kBAAkB,CAAC,CAAC;QAClF,IAAI,CAAC,mBAAmB,IAAI,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;YACnF,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,kBAAkB;gBAC5B,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,aAAa,EAAE,GAAG;gBAClB,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK;aACjD,CAAC,CAAC;QACL,CAAC;QAID,eAAe,CAAC,IAAI,CAAC;YACnB,IAAI,EAAE,wBAAwB;YAC9B,QAAQ,EAAE,oBAAoB;YAC9B,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,QAAQ;YAClB,aAAa,EAAE,GAAG;YAClB,IAAI,EAAE,2CAA2C;SAClD,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAGD,KAAK,CAAC,2BAA2B,CAAC,WAAwB;QACxD,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACzC,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,WAAW,EAAE,WAAW;iBACzB;aACF;YACD,OAAO,EAAE;gBACP,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,MAAM;aACb;SACF,CAAC,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,gCAAgC;QACpC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;YAC5D,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,WAAW,EAAE,oBAAW,CAAC,KAAK;iBAC/B;aACF;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;aACT;SACF,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;YAC9D,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,WAAW,EAAE,oBAAW,CAAC,OAAO;iBACjC;aACF;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;aACT;SACF,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE;gBACN,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE;gBACtC,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;gBACjC,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI;gBAC/B,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS;gBAC3C,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS;aAC5C;YACD,QAAQ,EAAE;gBACR,gBAAgB,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE;gBACxC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI;gBACnC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI;gBACjC,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,SAAS;gBAC7C,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,SAAS;aAC9C;SACF,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,IAA4B;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACzF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,4BAAmB,CAC3B,YAAY,IAAI,CAAC,QAAQ,qBAAqB,OAAO,CAAC,WAAW,gCAAgC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9H,CAAC;QACJ,CAAC;QAGD,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;QAEhE,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACvC,IAAI,EAAE;gBACJ,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,0BAAiB,CAAC,SAAS;gBAClD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC5C,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC3C,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI;gBACzF,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;aACpD;YACD,OAAO,EAAE;gBACP,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,mCAAmC,CAAC,WAAwB;QAChE,MAAM,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;QACrC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACzC,KAAK,EAAE;gBACL,OAAO,EAAE;oBACP,WAAW,EAAE,WAAW;iBACzB;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,CAAC,0BAAiB,CAAC,SAAS,EAAE,0BAAiB,CAAC,WAAW,CAAC;iBACjE;gBACD,aAAa,EAAE;oBACb,GAAG,EAAE,iBAAiB;iBACvB;aACF;YACD,OAAO,EAAE;gBACP,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,KAAK;aACrB;SACF,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAzUY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,0BAA0B,CAyUtC"}