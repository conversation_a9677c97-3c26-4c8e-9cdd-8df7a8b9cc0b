import { PrismaService } from '../prisma/prisma.service';
import { Vehicle, VehicleStatus, VehicleType, TrailerType, MaintenanceLog, MaintenanceType, MaintenanceCategory, MaintenanceStatus } from '@prisma/client';
export declare class VehiclesService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(): Promise<any[]>;
    findByType(vehicleType: VehicleType): Promise<any[]>;
    findTrucks(): Promise<any[]>;
    findTrailers(): Promise<any[]>;
    findWithPagination(page?: number, limit?: number, filters?: {
        vehicleType?: VehicleType;
        status?: string;
        search?: string;
    }): Promise<{
        vehicles: {
            id: string;
            plateNumber: string;
            make: string;
            model: string;
            year: number;
            status: import(".prisma/client").$Enums.VehicleStatus;
            lastMaintenance: Date | null;
            mileage: number | null;
            vehicleType: import(".prisma/client").$Enums.VehicleType;
            assignments: {
                id: string;
                driver: {
                    firstName: string;
                    lastName: string;
                };
            }[];
        }[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    findAvailableTrucks(): Promise<Vehicle[]>;
    findAvailableTrailers(): Promise<Vehicle[]>;
    findOne(id: string): Promise<any | null>;
    create(data: {
        plateNumber: string;
        make: string;
        model: string;
        year: number;
        vehicleType: VehicleType;
        vin?: string;
        color?: string;
        mileage?: number;
        fuelType?: string;
        purchaseDate?: string;
        engineType?: string;
        transmission?: string;
        fuelCapacity?: number;
        axleConfiguration?: string;
        cabConfiguration?: string;
        trailerType?: TrailerType;
        cargoCapacity?: number;
        maxWeight?: number;
        length?: number;
        width?: number;
        height?: number;
        hasRefrigeration?: boolean;
    }): Promise<Vehicle>;
    update(id: string, data: {
        plateNumber?: string;
        make?: string;
        model?: string;
        year?: number;
        vehicleType?: VehicleType;
        status?: VehicleStatus;
        lastMaintenance?: Date;
        vin?: string;
        color?: string;
        mileage?: number;
        fuelType?: string;
        purchaseDate?: string;
        engineType?: string;
        transmission?: string;
        fuelCapacity?: number;
        axleConfiguration?: string;
        cabConfiguration?: string;
        trailerType?: TrailerType;
        cargoCapacity?: number;
        maxWeight?: number;
        length?: number;
        width?: number;
        height?: number;
        hasRefrigeration?: boolean;
    }): Promise<Vehicle>;
    delete(id: string): Promise<Vehicle>;
    findMaintenanceLogs(vehicleId: string): Promise<MaintenanceLog[]>;
    findAllMaintenanceLogs(): Promise<MaintenanceLog[]>;
    findOneMaintenanceLog(id: string): Promise<MaintenanceLog | null>;
    createMaintenanceLog(vehicleId: string, data: {
        type: MaintenanceType;
        category: MaintenanceCategory;
        description: string;
        status: MaintenanceStatus;
        date?: Date | string;
        scheduledDate: Date | string;
        mileage?: number;
        partsCost?: number;
        laborCost?: number;
        technician?: string;
        notes?: string;
        nextMaintenanceDate?: Date | string;
        nextMaintenanceMileage?: number;
    }): Promise<MaintenanceLog>;
    updateMaintenanceLog(id: string, data: {
        type?: MaintenanceType;
        category?: MaintenanceCategory;
        description?: string;
        status?: MaintenanceStatus;
        date?: Date | string;
        scheduledDate?: Date | string;
        mileage?: number;
        partsCost?: number;
        laborCost?: number;
        technician?: string;
        notes?: string;
        nextMaintenanceDate?: Date | string;
        nextMaintenanceMileage?: number;
    }): Promise<MaintenanceLog>;
    deleteMaintenanceLog(id: string): Promise<MaintenanceLog>;
    getUpcomingMaintenance(): Promise<MaintenanceLog[]>;
}
