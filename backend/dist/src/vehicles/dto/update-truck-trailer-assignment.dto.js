"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTruckTrailerAssignmentDto = void 0;
const mapped_types_1 = require("@nestjs/mapped-types");
const class_validator_1 = require("class-validator");
const create_truck_trailer_assignment_dto_1 = require("./create-truck-trailer-assignment.dto");
const client_1 = require("@prisma/client");
class UpdateTruckTrailerAssignmentDto extends (0, mapped_types_1.PartialType)(create_truck_trailer_assignment_dto_1.CreateTruckTrailerAssignmentDto) {
    status;
}
exports.UpdateTruckTrailerAssignmentDto = UpdateTruckTrailerAssignmentDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.AssignmentStatus, {
        message: 'Status must be one of: ACTIVE, COMPLETED, CANCELLED, PENDING, ON_HOLD, DELAYED'
    }),
    __metadata("design:type", String)
], UpdateTruckTrailerAssignmentDto.prototype, "status", void 0);
//# sourceMappingURL=update-truck-trailer-assignment.dto.js.map