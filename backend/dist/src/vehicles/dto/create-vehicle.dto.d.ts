import { VehicleType, TrailerType } from '@prisma/client';
export declare class CreateVehicleDto {
    plateNumber: string;
    make: string;
    model: string;
    year: number;
    vehicleType: VehicleType;
    vin?: string;
    color?: string;
    mileage?: number;
    fuelType?: string;
    purchaseDate?: string;
    engineType?: string;
    transmission?: string;
    fuelCapacity?: number;
    axleConfiguration?: string;
    cabConfiguration?: string;
    trailerType?: TrailerType;
    cargoCapacity?: number;
    maxWeight?: number;
    length?: number;
    width?: number;
    height?: number;
    hasRefrigeration?: boolean;
}
