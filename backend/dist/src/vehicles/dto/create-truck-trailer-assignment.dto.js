"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTruckTrailerAssignmentDto = void 0;
const class_validator_1 = require("class-validator");
const custom_validators_1 = require("../../common/validators/custom-validators");
const class_transformer_1 = require("class-transformer");
class CreateTruckTrailerAssignmentDto {
    truckId;
    trailerId;
    startDate;
    endDate;
    notes;
    assignedBy;
}
exports.CreateTruckTrailerAssignmentDto = CreateTruckTrailerAssignmentDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Truck ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Truck ID must be a string' }),
    (0, class_validator_1.Length)(25, 25, { message: 'Truck ID must be a valid CUID' }),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "truckId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Trailer ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Trailer ID must be a string' }),
    (0, class_validator_1.Length)(25, 25, { message: 'Trailer ID must be a valid CUID' }),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "trailerId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Start date is required' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Start date must be a valid ISO date string' }),
    (0, custom_validators_1.IsNotPastDate)({ message: 'Start date cannot be in the past' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value).toISOString() : value),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'End date must be a valid ISO date string' }),
    (0, custom_validators_1.IsAfterStartDate)('startDate', { message: 'End date must be after start date' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value).toISOString() : value),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Notes must be a string' }),
    (0, class_validator_1.Length)(0, 1000, { message: 'Notes cannot exceed 1000 characters' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Assigned by must be a string' }),
    (0, class_validator_1.Length)(25, 25, { message: 'Assigned by must be a valid CUID' }),
    __metadata("design:type", String)
], CreateTruckTrailerAssignmentDto.prototype, "assignedBy", void 0);
//# sourceMappingURL=create-truck-trailer-assignment.dto.js.map