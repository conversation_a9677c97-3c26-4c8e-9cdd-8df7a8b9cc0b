"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVehicleDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const client_1 = require("@prisma/client");
const custom_validators_1 = require("../../common/validators/custom-validators");
class CreateVehicleDto {
    plateNumber;
    make;
    model;
    year;
    vehicleType;
    vin;
    color;
    mileage;
    fuelType;
    purchaseDate;
    engineType;
    transmission;
    fuelCapacity;
    axleConfiguration;
    cabConfiguration;
    trailerType;
    cargoCapacity;
    maxWeight;
    length;
    width;
    height;
    hasRefrigeration;
}
exports.CreateVehicleDto = CreateVehicleDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Plate number is required' }),
    (0, class_validator_1.IsString)({ message: 'Plate number must be a string' }),
    (0, class_validator_1.Length)(1, 20, { message: 'Plate number must be between 1 and 20 characters' }),
    (0, custom_validators_1.IsValidPlateNumber)({ message: 'Plate number format is invalid' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim().toUpperCase()),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "plateNumber", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Make is required' }),
    (0, class_validator_1.IsString)({ message: 'Make must be a string' }),
    (0, class_validator_1.Length)(1, 50, { message: 'Make must be between 1 and 50 characters' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "make", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Model is required' }),
    (0, class_validator_1.IsString)({ message: 'Model must be a string' }),
    (0, class_validator_1.Length)(1, 50, { message: 'Model must be between 1 and 50 characters' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "model", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Year is required' }),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Year must be a valid number' }),
    (0, class_validator_1.Min)(1900, { message: 'Year must be 1900 or later' }),
    (0, class_validator_1.Max)(new Date().getFullYear() + 1, { message: 'Year cannot be in the future' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "year", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Vehicle type is required' }),
    (0, class_validator_1.IsEnum)(client_1.VehicleType, {
        message: 'Vehicle type must be either TRUCK or TRAILER'
    }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "vehicleType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'VIN must be a string' }),
    (0, class_validator_1.Length)(17, 17, { message: 'VIN must be exactly 17 characters' }),
    (0, class_validator_1.Matches)(/^[A-HJ-NPR-Z0-9]{17}$/i, { message: 'VIN contains invalid characters (I, O, Q not allowed)' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim().toUpperCase()),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "vin", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Color must be a string' }),
    (0, class_validator_1.Length)(1, 30, { message: 'Color must be between 1 and 30 characters' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "color", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Mileage must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Mileage cannot be negative' }),
    (0, class_validator_1.Max)(9999999, { message: 'Mileage seems unreasonably high' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "mileage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID', 'OTHER'], {
        message: 'Fuel type must be one of: GASOLINE, DIESEL, ELECTRIC, HYBRID, OTHER'
    }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "fuelType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Purchase date must be a valid ISO date string' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value).toISOString() : value),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "purchaseDate", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRUCK'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Engine type must be between 1 and 50 characters' }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "engineType", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRUCK'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Transmission must be between 1 and 50 characters' }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "transmission", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRUCK'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Fuel capacity must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Fuel capacity cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "fuelCapacity", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRUCK'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Axle configuration must be between 1 and 50 characters' }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "axleConfiguration", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRUCK'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Cab configuration must be between 1 and 50 characters' }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "cabConfiguration", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.TrailerType, {
        message: 'Trailer type must be one of: DRY_VAN, REFRIGERATED, FLATBED, TANKER, LOWBOY, STEP_DECK, CONTAINER_CHASSIS'
    }),
    __metadata("design:type", String)
], CreateVehicleDto.prototype, "trailerType", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Cargo capacity must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Cargo capacity cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "cargoCapacity", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Max weight must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Max weight cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "maxWeight", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Length must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Length cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "length", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Width must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Width cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "width", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Height must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Height cannot be negative' }),
    __metadata("design:type", Number)
], CreateVehicleDto.prototype, "height", void 0);
__decorate([
    (0, class_validator_1.ValidateIf)(o => o.vehicleType === 'TRAILER'),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Has refrigeration must be a boolean value' }),
    __metadata("design:type", Boolean)
], CreateVehicleDto.prototype, "hasRefrigeration", void 0);
//# sourceMappingURL=create-vehicle.dto.js.map