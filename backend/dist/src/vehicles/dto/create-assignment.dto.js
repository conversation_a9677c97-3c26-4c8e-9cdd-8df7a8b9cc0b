"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAssignmentDto = void 0;
const class_validator_1 = require("class-validator");
const custom_validators_1 = require("../../common/validators/custom-validators");
const class_transformer_1 = require("class-transformer");
class CreateAssignmentDto {
    driverId;
    vehicleId;
    startDate;
    endDate;
    notes;
    type;
    priority;
    mileage;
}
exports.CreateAssignmentDto = CreateAssignmentDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Driver ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Driver ID must be a string' }),
    (0, class_validator_1.Length)(25, 25, { message: 'Driver ID must be a valid CUID' }),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "driverId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Vehicle ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Vehicle ID must be a string' }),
    (0, class_validator_1.Length)(25, 25, { message: 'Vehicle ID must be a valid CUID' }),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Start date is required' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Start date must be a valid ISO date string' }),
    (0, custom_validators_1.IsNotPastDate)({ message: 'Start date cannot be in the past' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value).toISOString() : value),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'End date must be a valid ISO date string' }),
    (0, custom_validators_1.IsAfterStartDate)('startDate', { message: 'End date must be after start date' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? new Date(value).toISOString() : value),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Notes must be a string' }),
    (0, class_validator_1.Length)(0, 1000, { message: 'Notes cannot exceed 1000 characters' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['REGULAR', 'TEMPORARY', 'EMERGENCY', 'MAINTENANCE', 'TRAINING'], {
        message: 'Type must be one of: REGULAR, TEMPORARY, EMERGENCY, MAINTENANCE, TRAINING'
    }),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['LOW', 'NORMAL', 'HIGH', 'URGENT'], {
        message: 'Priority must be one of: LOW, NORMAL, HIGH, URGENT'
    }),
    __metadata("design:type", String)
], CreateAssignmentDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Mileage must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Mileage cannot be negative' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateAssignmentDto.prototype, "mileage", void 0);
//# sourceMappingURL=create-assignment.dto.js.map