{"version": 3, "file": "create-vehicle.dto.js", "sourceRoot": "", "sources": ["../../../../src/vehicles/dto/create-vehicle.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,qDAAqJ;AACrJ,yDAA8C;AAC9C,2CAA0D;AAC1D,iFAA+E;AAE/E,MAAa,gBAAgB;IAM3B,WAAW,CAAS;IAMpB,IAAI,CAAS;IAMb,KAAK,CAAS;IAOd,IAAI,CAAS;IAOb,WAAW,CAAc;IAOzB,GAAG,CAAU;IAMb,KAAK,CAAU;IAOf,OAAO,CAAU;IAMjB,QAAQ,CAAU;IAKlB,YAAY,CAAU;IAOtB,UAAU,CAAU;IAMpB,YAAY,CAAU;IAMtB,YAAY,CAAU;IAMtB,iBAAiB,CAAU;IAM3B,gBAAgB,CAAU;IAQ1B,WAAW,CAAe;IAM1B,aAAa,CAAU;IAMvB,SAAS,CAAU;IAMnB,MAAM,CAAU;IAMhB,KAAK,CAAU;IAMf,MAAM,CAAU;IAKhB,gBAAgB,CAAW;CAC5B;AA1ID,4CA0IC;AApIC;IALC,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACnD,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IACtD,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC;IAC9E,IAAA,sCAAkB,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IACjE,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;;qDAClC;AAMpB;IAJC,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC3C,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;IAC9C,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACtE,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;;8CAC3B;AAMb;IAJC,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC5C,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IAC/C,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;IACvE,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;;+CAC1B;AAOd;IALC,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC3C,IAAA,0BAAQ,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IAC/F,IAAA,qBAAG,EAAC,IAAI,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACpD,IAAA,qBAAG,EAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IAC9E,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;8CAC3C;AAOb;IAJC,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACnD,IAAA,wBAAM,EAAC,oBAAW,EAAE;QACnB,OAAO,EAAE,8CAA8C;KACxD,CAAC;kDACW,oBAAW,oBAAX,oBAAW;qDAAC;AAOzB;IALC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC7C,IAAA,wBAAM,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAChE,IAAA,yBAAO,EAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,uDAAuD,EAAE,CAAC;IACvG,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;;6CACzC;AAMb;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IAC/C,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;IACvE,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;;+CACzB;AAOf;IALC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAClG,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACjD,IAAA,qBAAG,EAAC,OAAO,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;iDACvC;AAMjB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE;QAC7D,OAAO,EAAE,qEAAqE;KAC/E,CAAC;;kDACgB;AAKlB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,+CAA+C,EAAE,CAAC;IAC9E,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;;sDAClD;AAOtB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;;oDAC1D;AAMpB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC;;sDACzD;AAMtB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IACjE,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;;sDAClC;AAMtB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,wDAAwD,EAAE,CAAC;;2DAC1D;AAM3B;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,uDAAuD,EAAE,CAAC;;0DAC1D;AAQ1B;IALC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,oBAAW,EAAE;QACnB,OAAO,EAAE,2GAA2G;KACrH,CAAC;kDACY,oBAAW,oBAAX,oBAAW;qDAAC;AAM1B;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;IAClE,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;uDAClC;AAMvB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;mDAClC;AAMnB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;;gDACjC;AAMhB;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;+CACjC;AAMf;IAJC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;;gDACjC;AAKhB;IAHC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,EAAC,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;;0DACzC"}