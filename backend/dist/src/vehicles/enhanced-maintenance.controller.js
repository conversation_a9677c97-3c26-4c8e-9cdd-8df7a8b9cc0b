"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedMaintenanceController = void 0;
const common_1 = require("@nestjs/common");
const enhanced_maintenance_service_1 = require("./enhanced-maintenance.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const client_1 = require("@prisma/client");
let EnhancedMaintenanceController = class EnhancedMaintenanceController {
    enhancedMaintenanceService;
    constructor(enhancedMaintenanceService) {
        this.enhancedMaintenanceService = enhancedMaintenanceService;
    }
    getMaintenanceCategoriesForVehicleType(vehicleType) {
        return this.enhancedMaintenanceService.getMaintenanceCategoriesForVehicleType(vehicleType);
    }
    getMaintenanceRecommendations(vehicleId) {
        return this.enhancedMaintenanceService.getMaintenanceRecommendations(vehicleId);
    }
    getMaintenanceByVehicleType(vehicleType) {
        return this.enhancedMaintenanceService.getMaintenanceByVehicleType(vehicleType);
    }
    getMaintenanceStatsByVehicleType() {
        return this.enhancedMaintenanceService.getMaintenanceStatsByVehicleType();
    }
    getUpcomingTruckMaintenance() {
        return this.enhancedMaintenanceService.getUpcomingMaintenanceByVehicleType(client_1.VehicleType.TRUCK);
    }
    getUpcomingTrailerMaintenance() {
        return this.enhancedMaintenanceService.getUpcomingMaintenanceByVehicleType(client_1.VehicleType.TRAILER);
    }
    createMaintenance(createMaintenanceDto) {
        return this.enhancedMaintenanceService.createMaintenance(createMaintenanceDto);
    }
};
exports.EnhancedMaintenanceController = EnhancedMaintenanceController;
__decorate([
    (0, common_1.Get)('categories/:vehicleType'),
    __param(0, (0, common_1.Param)('vehicleType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_a = typeof client_1.VehicleType !== "undefined" && client_1.VehicleType) === "function" ? _a : Object]),
    __metadata("design:returntype", Array)
], EnhancedMaintenanceController.prototype, "getMaintenanceCategoriesForVehicleType", null);
__decorate([
    (0, common_1.Get)('recommendations/:vehicleId'),
    __param(0, (0, common_1.Param)('vehicleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], EnhancedMaintenanceController.prototype, "getMaintenanceRecommendations", null);
__decorate([
    (0, common_1.Get)('by-vehicle-type'),
    __param(0, (0, common_1.Query)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof client_1.VehicleType !== "undefined" && client_1.VehicleType) === "function" ? _c : Object]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], EnhancedMaintenanceController.prototype, "getMaintenanceByVehicleType", null);
__decorate([
    (0, common_1.Get)('stats/by-vehicle-type'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], EnhancedMaintenanceController.prototype, "getMaintenanceStatsByVehicleType", null);
__decorate([
    (0, common_1.Get)('upcoming/trucks'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], EnhancedMaintenanceController.prototype, "getUpcomingTruckMaintenance", null);
__decorate([
    (0, common_1.Get)('upcoming/trailers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], EnhancedMaintenanceController.prototype, "getUpcomingTrailerMaintenance", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], EnhancedMaintenanceController.prototype, "createMaintenance", null);
exports.EnhancedMaintenanceController = EnhancedMaintenanceController = __decorate([
    (0, common_1.Controller)('enhanced-maintenance'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [enhanced_maintenance_service_1.EnhancedMaintenanceService])
], EnhancedMaintenanceController);
//# sourceMappingURL=enhanced-maintenance.controller.js.map