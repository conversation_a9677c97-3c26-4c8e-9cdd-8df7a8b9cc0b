"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsuranceController = void 0;
const common_1 = require("@nestjs/common");
const insurance_service_1 = require("./insurance.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_insurance_dto_1 = require("../insurance/dto/create-insurance.dto");
const update_insurance_dto_1 = require("../insurance/dto/update-insurance.dto");
let InsuranceController = class InsuranceController {
    insuranceService;
    constructor(insuranceService) {
        this.insuranceService = insuranceService;
    }
    findAll(vehicleId) {
        if (vehicleId) {
            return this.insuranceService.findAllByVehicle(vehicleId);
        }
        return this.insuranceService.findAll();
    }
    findOne(id) {
        return this.insuranceService.findOne(id);
    }
    create(createInsuranceDto) {
        const formattedData = {
            vehicle: {
                connect: { id: createInsuranceDto.vehicleId }
            },
            policyNumber: createInsuranceDto.policyNumber,
            provider: createInsuranceDto.provider,
            type: createInsuranceDto.type,
            startDate: new Date(createInsuranceDto.startDate).toISOString(),
            endDate: new Date(createInsuranceDto.endDate).toISOString(),
            premium: createInsuranceDto.premium,
            coverage: createInsuranceDto.coverage,
            deductible: createInsuranceDto.deductible,
            status: createInsuranceDto.status,
            notes: createInsuranceDto.notes,
        };
        return this.insuranceService.create(formattedData);
    }
    update(id, updateInsuranceDto) {
        const formattedData = { ...updateInsuranceDto };
        if (updateInsuranceDto.startDate) {
            formattedData.startDate = new Date(updateInsuranceDto.startDate).toISOString();
        }
        if (updateInsuranceDto.endDate) {
            formattedData.endDate = new Date(updateInsuranceDto.endDate).toISOString();
        }
        return this.insuranceService.update(id, formattedData);
    }
    remove(id) {
        return this.insuranceService.delete(id);
    }
    getUpcomingRenewals() {
        return this.insuranceService.getUpcomingRenewals();
    }
};
exports.InsuranceController = InsuranceController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('vehicleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], InsuranceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], InsuranceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_insurance_dto_1.CreateInsuranceDto]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], InsuranceController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_insurance_dto_1.UpdateInsuranceDto]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], InsuranceController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], InsuranceController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('upcoming/renewals'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], InsuranceController.prototype, "getUpcomingRenewals", null);
exports.InsuranceController = InsuranceController = __decorate([
    (0, common_1.Controller)('insurance'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [insurance_service_1.InsuranceService])
], InsuranceController);
//# sourceMappingURL=insurance.controller.js.map