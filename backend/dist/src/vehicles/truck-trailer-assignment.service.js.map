{"version": 3, "file": "truck-trailer-assignment.service.js", "sourceRoot": "", "sources": ["../../../src/vehicles/truck-trailer-assignment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAuG;AACvG,6DAAyD;AACzD,2CAAuF;AAKhF,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACpB;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC;YACjD,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,IAAI;qBAClB;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC;YACjD,KAAK,EAAE;gBACL,MAAM,EAAE,yBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,IAAI;qBAClB;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC;YACjD,KAAK,EAAE,EAAE,OAAO,EAAE;YAClB,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC;YACjD,KAAK,EAAE,EAAE,SAAS,EAAE;YACpB,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAqC;QAEhD,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAG1D,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAG3E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC/C,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBACnC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;gBACrD,MAAM,EAAE,yBAAgB,CAAC,MAAM;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAAqC;QAC5D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QAGD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CACzB,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,EAClC,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CACvC,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAQ,EAAE,GAAG,IAAI,EAAE,CAAC;QACpC,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,UAAU,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC/C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,yBAAgB,CAAC,MAAM,EAAE,CAAC;YAClD,MAAM,IAAI,4BAAmB,CAAC,0CAA0C,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC/C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE;gBACJ,MAAM,EAAE,yBAAgB,CAAC,SAAS;gBAClC,OAAO,EAAE,IAAI,IAAI,EAAE;aACpB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,yBAAgB,CAAC,SAAS,EAAE,CAAC;YACrD,MAAM,IAAI,4BAAmB,CAAC,2CAA2C,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC/C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE;gBACJ,MAAM,EAAE,yBAAgB,CAAC,SAAS;gBAClC,OAAO,EAAE,IAAI,IAAI,EAAE;aACpB;YACD,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;YAC/C,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,SAAiB;QAE/D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACjD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,OAAO,YAAY,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,KAAK,oBAAW,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,IAAI,4BAAmB,CAAC,WAAW,KAAK,CAAC,WAAW,iBAAiB,CAAC,CAAC;QAC/E,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,SAAS,YAAY,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,KAAK,oBAAW,CAAC,OAAO,EAAE,CAAC;YAChD,MAAM,IAAI,4BAAmB,CAAC,WAAW,OAAO,CAAC,WAAW,mBAAmB,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,SAAiB,EAAE,SAAiB;QACnF,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAG1C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;YACvE,KAAK,EAAE;gBACL,OAAO;gBACP,MAAM,EAAE,yBAAgB,CAAC,MAAM;gBAC/B,EAAE,EAAE;oBACF,EAAE,OAAO,EAAE,IAAI,EAAE;oBACjB,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE,EAAE;iBACpC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,wCAAwC,CAAC,CAAC;QACxE,CAAC;QAGD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;YACzE,KAAK,EAAE;gBACL,SAAS;gBACT,MAAM,EAAE,yBAAgB,CAAC,MAAM;gBAC/B,EAAE,EAAE;oBACF,EAAE,OAAO,EAAE,IAAI,EAAE;oBACjB,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE,EAAE;iBACpC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,0CAA0C,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;CACF,CAAA;AAjSY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,6BAA6B,CAiSzC"}