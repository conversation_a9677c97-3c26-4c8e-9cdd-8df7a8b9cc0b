import { EnhancedMaintenanceService } from './enhanced-maintenance.service';
import { MaintenanceLog, VehicleType, MaintenanceCategory } from '@prisma/client';
export declare class EnhancedMaintenanceController {
    private readonly enhancedMaintenanceService;
    constructor(enhancedMaintenanceService: EnhancedMaintenanceService);
    getMaintenanceCategoriesForVehicleType(vehicleType: VehicleType): MaintenanceCategory[];
    getMaintenanceRecommendations(vehicleId: string): Promise<any[]>;
    getMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]>;
    getMaintenanceStatsByVehicleType(): Promise<any>;
    getUpcomingTruckMaintenance(): Promise<MaintenanceLog[]>;
    getUpcomingTrailerMaintenance(): Promise<MaintenanceLog[]>;
    createMaintenance(createMaintenanceDto: any): Promise<MaintenanceLog>;
}
