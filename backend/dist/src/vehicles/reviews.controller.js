"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsController = void 0;
const common_1 = require("@nestjs/common");
const reviews_service_1 = require("./reviews.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_review_dto_1 = require("../reviews/dto/create-review.dto");
const update_review_dto_1 = require("../reviews/dto/update-review.dto");
let ReviewsController = class ReviewsController {
    reviewsService;
    constructor(reviewsService) {
        this.reviewsService = reviewsService;
    }
    findAll(vehicleId) {
        if (vehicleId) {
            return this.reviewsService.findAllByVehicle(vehicleId);
        }
        return this.reviewsService.findAll();
    }
    findOne(id) {
        return this.reviewsService.findOne(id);
    }
    create(createReviewDto) {
        const formattedData = {
            vehicle: {
                connect: { id: createReviewDto.vehicleId }
            },
            reviewType: createReviewDto.reviewType,
            reviewBy: createReviewDto.reviewBy,
            scheduledDate: new Date(createReviewDto.scheduledDate).toISOString(),
            completedDate: createReviewDto.completedDate ? new Date(createReviewDto.completedDate).toISOString() : undefined,
            location: createReviewDto.location,
            status: createReviewDto.status,
            findings: createReviewDto.findings,
            recommendations: createReviewDto.recommendations,
            nextReviewDate: createReviewDto.nextReviewDate ? new Date(createReviewDto.nextReviewDate).toISOString() : undefined,
            documents: createReviewDto.documents || [],
        };
        return this.reviewsService.create(formattedData);
    }
    update(id, updateReviewDto) {
        const formattedData = { ...updateReviewDto };
        if (updateReviewDto.scheduledDate) {
            formattedData.scheduledDate = new Date(updateReviewDto.scheduledDate).toISOString();
        }
        if (updateReviewDto.completedDate) {
            formattedData.completedDate = new Date(updateReviewDto.completedDate).toISOString();
        }
        if (updateReviewDto.nextReviewDate) {
            formattedData.nextReviewDate = new Date(updateReviewDto.nextReviewDate).toISOString();
        }
        return this.reviewsService.update(id, formattedData);
    }
    remove(id) {
        return this.reviewsService.delete(id);
    }
    getUpcomingInspections() {
        return this.reviewsService.getUpcomingInspections();
    }
};
exports.ReviewsController = ReviewsController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('vehicleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], ReviewsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], ReviewsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_review_dto_1.CreateReviewDto]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], ReviewsController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_review_dto_1.UpdateReviewDto]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], ReviewsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], ReviewsController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('upcoming/inspections'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], ReviewsController.prototype, "getUpcomingInspections", null);
exports.ReviewsController = ReviewsController = __decorate([
    (0, common_1.Controller)('reviews'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [reviews_service_1.ReviewsService])
], ReviewsController);
//# sourceMappingURL=reviews.controller.js.map