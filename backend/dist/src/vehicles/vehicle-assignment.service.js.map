{"version": 3, "file": "vehicle-assignment.service.js", "sourceRoot": "", "sources": ["../../../src/vehicles/vehicle-assignment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6DAAyD;AACzD,2CAA0E;AAC1E,8EAKgD;AAiBzC,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACf;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAgB7C,KAAK,CAAC,MAAM,CAAC,IAKZ;QAEC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACnD,MAAM,IAAI,0CAAsB,CAAC,mCAAmC,CAAC,CAAC;QACxE,CAAC;QAGD,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAGrD,IAAI,CAAC;YACH,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBACnC,IAAI,EAAE;wBACJ,GAAG,IAAI;wBACP,MAAM,EAAE,QAAQ;qBACjB;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;gCACX,MAAM,EAAE,IAAI;6BACb;yBACF;wBACD,OAAO,EAAE;4BACP,MAAM,EAAE;gCACN,WAAW,EAAE,IAAI;gCACjB,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;gCACX,MAAM,EAAE,IAAI;6BACb;yBACF;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBACzB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC7B,IAAI,EAAE,EAAE,MAAM,EAAE,sBAAa,CAAC,QAAQ,EAAE;iBACzC,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACtB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE;oBAC5B,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;iBAC7B,CAAC;aACH,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;gBAE1D,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,OAAO;wBACV,MAAM,IAAI,qCAAiB,CAAC,yDAAyD,CAAC,CAAC;oBACzF,KAAK,OAAO;wBACV,MAAM,IAAI,6CAAyB,CAAC,mBAAmB,EAAE,0BAA0B,CAAC,CAAC;oBACvF;wBACE,MAAM,IAAI,qCAAiB,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE,EAAE,SAAS,EAAE;YACpB,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAE,IAAI;qBACZ;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;qBACZ;iBACF;aACF;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC5C,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAE,IAAI;qBACZ;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;YAChE,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;SAC1D,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,0CAAsB,CAAC,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBACnC,KAAK,EAAE,EAAE,EAAE,EAAE;oBACb,IAAI,EAAE;wBACJ,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,IAAI,IAAI,EAAE;qBACpB;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;6BACZ;yBACF;wBACD,OAAO,EAAE;4BACP,MAAM,EAAE;gCACN,WAAW,EAAE,IAAI;gCACjB,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBACzB,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,SAAS,EAAE;oBACnC,IAAI,EAAE,EAAE,MAAM,EAAE,sBAAa,CAAC,SAAS,EAAE;iBAC1C,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACtB,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,QAAQ,EAAE;oBAClC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;iBAC3B,CAAC;aACH,CAAC,CAAC;YAEH,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;gBAC1D,MAAM,IAAI,qCAAiB,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC;YAChE,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;SAC1D,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,0CAAsB,CAAC,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,iBAAiB,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBACnC,KAAK,EAAE,EAAE,EAAE,EAAE;oBACb,IAAI,EAAE;wBACJ,MAAM,EAAE,WAAW;wBACnB,OAAO,EAAE,IAAI,IAAI,EAAE;qBACpB;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;6BACZ;yBACF;wBACD,OAAO,EAAE;4BACP,MAAM,EAAE;gCACN,WAAW,EAAE,IAAI;gCACjB,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBACzB,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,SAAS,EAAE;oBACnC,IAAI,EAAE,EAAE,MAAM,EAAE,sBAAa,CAAC,SAAS,EAAE;iBAC1C,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACtB,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,QAAQ,EAAE;oBAClC,IAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;iBAC3B,CAAC;aACH,CAAC,CAAC;YAEH,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;gBAC1D,MAAM,IAAI,qCAAiB,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAsBO,KAAK,CAAC,0BAA0B,CAAC,QAAgB;QACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC/C,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;YACvB,OAAO,EAAE;gBACP,WAAW,EAAE;oBACX,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;qBACjB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,6CAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,0CAAsB,CAAC,sBAAsB,CAAC,CAAC;QAC3D,CAAC;QAGD,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,qCAAiB,CAAC,yCAAyC,CAAC,CAAC;QACzE,CAAC;QAGD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACjF,MAAM,IAAI,qCAAiB,CAAC,uBAAuB,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;QAC3G,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAqBO,KAAK,CAAC,2BAA2B,CAAC,SAAiB;QACzD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,OAAO,EAAE;gBACP,WAAW,EAAE;oBACX,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;qBACjB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,6CAAyB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,sBAAa,CAAC,SAAS,EAAE,CAAC;YAC/C,MAAM,IAAI,qCAAiB,CAAC,wBAAwB,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;QAC7G,CAAC;QAGD,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,qCAAiB,CAAC,0CAA0C,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAA;AA1WY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,wBAAwB,CA0WpC"}