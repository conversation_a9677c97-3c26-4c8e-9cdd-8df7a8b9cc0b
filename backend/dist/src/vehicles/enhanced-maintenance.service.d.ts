import { PrismaService } from '../prisma/prisma.service';
import { MaintenanceLog, MaintenanceType, MaintenanceCategory, MaintenanceStatus, VehicleType } from '@prisma/client';
interface CreateMaintenanceInput {
    vehicleId: string;
    type: MaintenanceType;
    category: MaintenanceCategory;
    description: string;
    status?: MaintenanceStatus;
    date?: string;
    scheduledDate: string;
    mileage?: number;
    partsCost?: number;
    laborCost?: number;
    technician?: string;
    notes?: string;
    nextMaintenanceDate?: string;
    nextMaintenanceMileage?: number;
}
export declare class EnhancedMaintenanceService {
    private prisma;
    constructor(prisma: PrismaService);
    getMaintenanceCategoriesForVehicleType(vehicleType: VehicleType): MaintenanceCategory[];
    getMaintenanceRecommendations(vehicleId: string): Promise<any[]>;
    private getTruckMaintenanceRecommendations;
    private getTrailerMaintenanceRecommendations;
    getMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]>;
    getMaintenanceStatsByVehicleType(): Promise<any>;
    createMaintenance(data: CreateMaintenanceInput): Promise<MaintenanceLog>;
    getUpcomingMaintenanceByVehicleType(vehicleType: VehicleType): Promise<MaintenanceLog[]>;
}
export {};
