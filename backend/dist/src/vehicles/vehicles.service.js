"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehiclesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let VehiclesService = class VehiclesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll() {
        return this.prisma.vehicle.findMany({
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                year: true,
                status: true,
                vehicleType: true,
                color: true,
                mileage: true,
                lastMaintenance: true,
                createdAt: true,
                updatedAt: true,
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                        startDate: true,
                        endDate: true,
                        driver: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                    },
                },
                maintenanceLogs: {
                    select: {
                        id: true,
                        date: true,
                        type: true,
                        description: true,
                    },
                    orderBy: {
                        date: 'desc',
                    },
                    take: 1,
                },
                truckAssignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                        startDate: true,
                        endDate: true,
                        trailer: {
                            select: {
                                id: true,
                                plateNumber: true,
                                make: true,
                                model: true,
                                trailerType: true,
                            }
                        }
                    }
                },
                trailerAssignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                        startDate: true,
                        endDate: true,
                        truck: {
                            select: {
                                id: true,
                                plateNumber: true,
                                make: true,
                                model: true,
                            }
                        }
                    }
                },
            },
            orderBy: [
                { status: 'asc' },
                { plateNumber: 'asc' }
            ],
        });
    }
    async findByType(vehicleType) {
        return this.prisma.vehicle.findMany({
            where: { vehicleType },
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                year: true,
                status: true,
                vehicleType: true,
                color: true,
                mileage: true,
                lastMaintenance: true,
                ...(vehicleType === 'TRUCK' ? {
                    engineType: true,
                    transmission: true,
                    fuelCapacity: true,
                    axleConfiguration: true,
                    cabConfiguration: true,
                } : {
                    trailerType: true,
                    cargoCapacity: true,
                    maxWeight: true,
                    length: true,
                    width: true,
                    height: true,
                    hasRefrigeration: true,
                }),
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                        startDate: true,
                        endDate: true,
                        driver: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
                ...(vehicleType === 'TRUCK' ? {
                    truckAssignments: {
                        where: {
                            status: 'ACTIVE',
                        },
                        select: {
                            id: true,
                            status: true,
                            trailer: {
                                select: {
                                    id: true,
                                    plateNumber: true,
                                    trailerType: true,
                                }
                            }
                        }
                    }
                } : {
                    trailerAssignments: {
                        where: {
                            status: 'ACTIVE',
                        },
                        select: {
                            id: true,
                            status: true,
                            truck: {
                                select: {
                                    id: true,
                                    plateNumber: true,
                                    make: true,
                                    model: true,
                                }
                            }
                        }
                    }
                }),
            },
            orderBy: [
                { status: 'asc' },
                { plateNumber: 'asc' }
            ],
        });
    }
    async findTrucks() {
        return this.findByType(client_1.VehicleType.TRUCK);
    }
    async findTrailers() {
        return this.findByType(client_1.VehicleType.TRAILER);
    }
    async findWithPagination(page = 1, limit = 20, filters) {
        const skip = (page - 1) * limit;
        const where = {};
        if (filters?.vehicleType) {
            where.vehicleType = filters.vehicleType;
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        if (filters?.search) {
            where.OR = [
                { plateNumber: { contains: filters.search, mode: 'insensitive' } },
                { make: { contains: filters.search, mode: 'insensitive' } },
                { model: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        const [vehicles, total] = await Promise.all([
            this.prisma.vehicle.findMany({
                where,
                select: {
                    id: true,
                    plateNumber: true,
                    make: true,
                    model: true,
                    year: true,
                    status: true,
                    vehicleType: true,
                    mileage: true,
                    lastMaintenance: true,
                    assignments: {
                        where: {
                            status: 'ACTIVE',
                        },
                        select: {
                            id: true,
                            driver: {
                                select: {
                                    firstName: true,
                                    lastName: true,
                                },
                            },
                        },
                    },
                },
                orderBy: [
                    { status: 'asc' },
                    { plateNumber: 'asc' }
                ],
                skip,
                take: limit,
            }),
            this.prisma.vehicle.count({ where }),
        ]);
        return {
            vehicles,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findAvailableTrucks() {
        return this.prisma.vehicle.findMany({
            where: {
                vehicleType: client_1.VehicleType.TRUCK,
                status: client_1.VehicleStatus.AVAILABLE,
                truckAssignments: {
                    none: {
                        status: 'ACTIVE',
                        endDate: null,
                    }
                }
            },
            orderBy: {
                plateNumber: 'asc',
            },
        });
    }
    async findAvailableTrailers() {
        return this.prisma.vehicle.findMany({
            where: {
                vehicleType: client_1.VehicleType.TRAILER,
                status: client_1.VehicleStatus.AVAILABLE,
                trailerAssignments: {
                    none: {
                        status: 'ACTIVE',
                        endDate: null,
                    }
                }
            },
            orderBy: {
                plateNumber: 'asc',
            },
        });
    }
    async findOne(id) {
        return this.prisma.vehicle.findUnique({
            where: { id },
            include: {
                assignments: {
                    include: {
                        driver: true,
                    },
                },
                maintenanceLogs: {
                    orderBy: {
                        date: 'desc',
                    },
                },
            },
        });
    }
    async create(data) {
        return this.prisma.vehicle.create({
            data: {
                ...data,
                status: client_1.VehicleStatus.AVAILABLE,
                ...(data.purchaseDate && { purchaseDate: new Date(data.purchaseDate) }),
            },
        });
    }
    async update(id, data) {
        const updateData = { ...data };
        if (updateData.purchaseDate) {
            updateData.purchaseDate = new Date(updateData.purchaseDate);
        }
        return this.prisma.vehicle.update({
            where: { id },
            data: updateData,
        });
    }
    async delete(id) {
        return this.prisma.vehicle.delete({
            where: { id },
        });
    }
    async findMaintenanceLogs(vehicleId) {
        return this.prisma.maintenanceLog.findMany({
            where: { vehicleId },
            orderBy: [
                { scheduledDate: 'asc' },
                { date: 'desc' },
            ],
        });
    }
    async findAllMaintenanceLogs() {
        return this.prisma.maintenanceLog.findMany({
            include: {
                vehicle: true,
            },
            orderBy: [
                { scheduledDate: 'asc' },
                { date: 'desc' },
            ],
        });
    }
    async findOneMaintenanceLog(id) {
        return this.prisma.maintenanceLog.findUnique({
            where: { id },
            include: {
                vehicle: true,
            },
        });
    }
    async createMaintenanceLog(vehicleId, data) {
        const cost = (data.partsCost || 0) + (data.laborCost || 0);
        const maintenanceData = {
            type: data.type,
            category: data.category,
            description: data.description,
            status: data.status,
            date: data.date ? new Date(data.date) : new Date(),
            scheduledDate: new Date(data.scheduledDate),
            mileage: data.mileage ?? null,
            cost: cost > 0 ? cost : null,
            partsCost: data.partsCost ?? null,
            laborCost: data.laborCost ?? null,
            technician: data.technician ?? null,
            notes: data.notes ?? null,
            nextMaintenanceDate: data.nextMaintenanceDate ? new Date(data.nextMaintenanceDate) : null,
            nextMaintenanceMileage: data.nextMaintenanceMileage ?? null,
            vehicle: {
                connect: { id: vehicleId }
            }
        };
        const log = await this.prisma.maintenanceLog.create({
            data: maintenanceData,
        });
        if (data.status === client_1.MaintenanceStatus.COMPLETED && data.date) {
            await this.prisma.vehicle.update({
                where: { id: vehicleId },
                data: {
                    lastMaintenance: new Date(data.date),
                    status: client_1.VehicleStatus.AVAILABLE,
                },
            });
        }
        if (data.status === client_1.MaintenanceStatus.SCHEDULED || data.status === client_1.MaintenanceStatus.IN_PROGRESS) {
            await this.prisma.vehicle.update({
                where: { id: vehicleId },
                data: { status: client_1.VehicleStatus.MAINTENANCE },
            });
        }
        return log;
    }
    async updateMaintenanceLog(id, data) {
        const currentLog = await this.prisma.maintenanceLog.findUnique({
            where: { id },
            select: { partsCost: true, laborCost: true },
        });
        const updateData = {
            ...(data.type && { type: data.type }),
            ...(data.category && { category: data.category }),
            ...(data.description && { description: data.description }),
            ...(data.status && { status: data.status }),
            ...(data.date && { date: new Date(data.date) }),
            ...(data.scheduledDate && { scheduledDate: new Date(data.scheduledDate) }),
            ...(data.mileage !== undefined && { mileage: data.mileage }),
            ...(data.technician !== undefined && { technician: data.technician }),
            ...(data.notes !== undefined && { notes: data.notes }),
            ...(data.nextMaintenanceDate && { nextMaintenanceDate: new Date(data.nextMaintenanceDate) }),
            ...(data.nextMaintenanceMileage !== undefined && { nextMaintenanceMileage: data.nextMaintenanceMileage }),
        };
        if (data.partsCost !== undefined || data.laborCost !== undefined) {
            const newPartsCost = data.partsCost ?? currentLog?.partsCost ?? 0;
            const newLaborCost = data.laborCost ?? currentLog?.laborCost ?? 0;
            const totalCost = newPartsCost + newLaborCost;
            updateData.partsCost = data.partsCost;
            updateData.laborCost = data.laborCost;
            updateData.cost = totalCost > 0 ? totalCost : undefined;
        }
        const log = await this.prisma.maintenanceLog.update({
            where: { id },
            data: updateData,
        });
        if (data.status) {
            const vehicle = await this.prisma.maintenanceLog.findUnique({
                where: { id },
                select: { vehicleId: true },
            });
            if (vehicle) {
                if (data.status === client_1.MaintenanceStatus.COMPLETED) {
                    await this.prisma.vehicle.update({
                        where: { id: vehicle.vehicleId },
                        data: {
                            status: client_1.VehicleStatus.AVAILABLE,
                            lastMaintenance: data.date ? new Date(data.date) : new Date(),
                        },
                    });
                }
                else if (data.status === client_1.MaintenanceStatus.SCHEDULED || data.status === client_1.MaintenanceStatus.IN_PROGRESS) {
                    await this.prisma.vehicle.update({
                        where: { id: vehicle.vehicleId },
                        data: { status: client_1.VehicleStatus.MAINTENANCE },
                    });
                }
            }
        }
        return log;
    }
    async deleteMaintenanceLog(id) {
        const log = await this.prisma.maintenanceLog.findUnique({
            where: { id },
            include: { vehicle: true },
        });
        if (log && (log.status === client_1.MaintenanceStatus.SCHEDULED || log.status === client_1.MaintenanceStatus.IN_PROGRESS)) {
            const otherActiveLogs = await this.prisma.maintenanceLog.count({
                where: {
                    vehicleId: log.vehicleId,
                    id: { not: id },
                    status: {
                        in: [client_1.MaintenanceStatus.SCHEDULED, client_1.MaintenanceStatus.IN_PROGRESS],
                    },
                },
            });
            if (otherActiveLogs === 0) {
                await this.prisma.vehicle.update({
                    where: { id: log.vehicleId },
                    data: { status: client_1.VehicleStatus.AVAILABLE },
                });
            }
        }
        return this.prisma.maintenanceLog.delete({
            where: { id },
        });
    }
    async getUpcomingMaintenance() {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        return this.prisma.maintenanceLog.findMany({
            where: {
                OR: [
                    {
                        status: client_1.MaintenanceStatus.SCHEDULED,
                        scheduledDate: {
                            lte: thirtyDaysFromNow,
                        },
                    },
                    {
                        status: client_1.MaintenanceStatus.COMPLETED,
                        nextMaintenanceDate: {
                            lte: thirtyDaysFromNow,
                        },
                    },
                ],
            },
            include: {
                vehicle: true,
            },
            orderBy: [
                { scheduledDate: 'asc' },
                { nextMaintenanceDate: 'asc' },
            ],
        });
    }
};
exports.VehiclesService = VehiclesService;
exports.VehiclesService = VehiclesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VehiclesService);
//# sourceMappingURL=vehicles.service.js.map