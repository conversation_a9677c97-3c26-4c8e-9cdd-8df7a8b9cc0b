import { PrismaService } from '../prisma/prisma.service';
import { TruckTrailerAssignment } from '@prisma/client';
import { CreateTruckTrailerAssignmentDto } from './dto/create-truck-trailer-assignment.dto';
import { UpdateTruckTrailerAssignmentDto } from './dto/update-truck-trailer-assignment.dto';
export declare class TruckTrailerAssignmentService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(): Promise<TruckTrailerAssignment[]>;
    findActive(): Promise<TruckTrailerAssignment[]>;
    findOne(id: string): Promise<TruckTrailerAssignment | null>;
    findByTruck(truckId: string): Promise<TruckTrailerAssignment[]>;
    findByTrailer(trailerId: string): Promise<TruckTrailerAssignment[]>;
    create(data: CreateTruckTrailerAssignmentDto): Promise<TruckTrailerAssignment>;
    update(id: string, data: UpdateTruckTrailerAssignmentDto): Promise<TruckTrailerAssignment>;
    complete(id: string): Promise<TruckTrailerAssignment>;
    cancel(id: string): Promise<TruckTrailerAssignment>;
    delete(id: string): Promise<TruckTrailerAssignment>;
    private validateVehicles;
    private checkForConflicts;
}
