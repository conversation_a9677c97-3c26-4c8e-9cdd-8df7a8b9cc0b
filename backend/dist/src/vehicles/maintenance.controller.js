"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaintenanceController = void 0;
const common_1 = require("@nestjs/common");
const vehicles_service_1 = require("./vehicles.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_maintenance_dto_1 = require("../maintenance/dto/create-maintenance.dto");
const update_maintenance_dto_1 = require("../maintenance/dto/update-maintenance.dto");
let MaintenanceController = class MaintenanceController {
    vehiclesService;
    constructor(vehiclesService) {
        this.vehiclesService = vehiclesService;
    }
    findAll() {
        return this.vehiclesService.findAllMaintenanceLogs();
    }
    findOne(id) {
        return this.vehiclesService.findOneMaintenanceLog(id);
    }
    create(createMaintenanceDto) {
        const formattedData = {
            type: createMaintenanceDto.type,
            category: createMaintenanceDto.category || 'OTHER',
            description: createMaintenanceDto.description,
            status: createMaintenanceDto.status || 'SCHEDULED',
            date: createMaintenanceDto.date ? createMaintenanceDto.date : undefined,
            scheduledDate: createMaintenanceDto.scheduledDate || new Date().toISOString(),
            mileage: createMaintenanceDto.mileage,
            partsCost: createMaintenanceDto.partsCost,
            laborCost: createMaintenanceDto.laborCost,
            technician: createMaintenanceDto.technician,
            notes: createMaintenanceDto.notes,
            nextMaintenanceDate: createMaintenanceDto.nextMaintenanceDate,
            nextMaintenanceMileage: createMaintenanceDto.nextMaintenanceMileage,
        };
        return this.vehiclesService.createMaintenanceLog(createMaintenanceDto.vehicleId, formattedData);
    }
    update(id, updateMaintenanceDto) {
        const updateData = {};
        if (updateMaintenanceDto.type !== undefined)
            updateData.type = updateMaintenanceDto.type;
        if (updateMaintenanceDto.category !== undefined)
            updateData.category = updateMaintenanceDto.category;
        if (updateMaintenanceDto.description !== undefined)
            updateData.description = updateMaintenanceDto.description;
        if (updateMaintenanceDto.status !== undefined)
            updateData.status = updateMaintenanceDto.status;
        if (updateMaintenanceDto.date !== undefined)
            updateData.date = updateMaintenanceDto.date;
        if (updateMaintenanceDto.scheduledDate !== undefined)
            updateData.scheduledDate = updateMaintenanceDto.scheduledDate;
        if (updateMaintenanceDto.mileage !== undefined)
            updateData.mileage = updateMaintenanceDto.mileage;
        if (updateMaintenanceDto.partsCost !== undefined)
            updateData.partsCost = updateMaintenanceDto.partsCost;
        if (updateMaintenanceDto.laborCost !== undefined)
            updateData.laborCost = updateMaintenanceDto.laborCost;
        if (updateMaintenanceDto.technician !== undefined)
            updateData.technician = updateMaintenanceDto.technician;
        if (updateMaintenanceDto.notes !== undefined)
            updateData.notes = updateMaintenanceDto.notes;
        if (updateMaintenanceDto.nextMaintenanceDate !== undefined)
            updateData.nextMaintenanceDate = updateMaintenanceDto.nextMaintenanceDate;
        if (updateMaintenanceDto.nextMaintenanceMileage !== undefined)
            updateData.nextMaintenanceMileage = updateMaintenanceDto.nextMaintenanceMileage;
        return this.vehiclesService.updateMaintenanceLog(id, updateData);
    }
    remove(id) {
        return this.vehiclesService.deleteMaintenanceLog(id);
    }
};
exports.MaintenanceController = MaintenanceController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], MaintenanceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], MaintenanceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_maintenance_dto_1.CreateMaintenanceDto]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], MaintenanceController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_maintenance_dto_1.UpdateMaintenanceDto]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], MaintenanceController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], MaintenanceController.prototype, "remove", null);
exports.MaintenanceController = MaintenanceController = __decorate([
    (0, common_1.Controller)('maintenance'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [vehicles_service_1.VehiclesService])
], MaintenanceController);
//# sourceMappingURL=maintenance.controller.js.map