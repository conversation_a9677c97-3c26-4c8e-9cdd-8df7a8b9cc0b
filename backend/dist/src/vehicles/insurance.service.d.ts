import { InsurancePolicy, Prisma } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';
export declare class InsuranceService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(): Promise<InsurancePolicy[]>;
    findOne(id: string): Promise<InsurancePolicy | null>;
    findAllByVehicle(vehicleId: string): Promise<InsurancePolicy[]>;
    create(data: Prisma.InsurancePolicyCreateInput): Promise<InsurancePolicy>;
    update(id: string, data: Prisma.InsurancePolicyUpdateInput): Promise<InsurancePolicy>;
    delete(id: string): Promise<InsurancePolicy>;
    getUpcomingRenewals(days?: number): Promise<InsurancePolicy[]>;
}
