import { VehiclesService } from './vehicles.service';
import { VehicleAssignmentService } from './vehicle-assignment.service';
import { PrismaService } from '../prisma/prisma.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { Vehicle, MaintenanceLog, MaintenanceType, MaintenanceCategory, MaintenanceStatus, VehicleAssignment } from '@prisma/client';
export declare class VehiclesController {
    private readonly vehiclesService;
    private readonly assignmentService;
    private readonly prisma;
    constructor(vehiclesService: VehiclesService, assignmentService: VehicleAssignmentService, prisma: PrismaService);
    findAll(type?: string): Promise<Vehicle[]>;
    findTrucks(): Promise<Vehicle[]>;
    findTrailers(): Promise<Vehicle[]>;
    findAvailableTrucks(): Promise<Vehicle[]>;
    findAvailableTrailers(): Promise<Vehicle[]>;
    findAllAssignments(): Promise<VehicleAssignment[]>;
    findOne(id: string): Promise<any | null>;
    create(createVehicleDto: CreateVehicleDto): Promise<Vehicle>;
    update(id: string, updateVehicleDto: UpdateVehicleDto): Promise<Vehicle>;
    fullUpdate(id: string, updateVehicleDto: UpdateVehicleDto): Promise<Vehicle>;
    remove(id: string): Promise<Vehicle>;
    findAssignments(id: string): Promise<VehicleAssignment[]>;
    createAssignment(id: string, createAssignmentDto: {
        driverId: string;
        startDate: string;
        endDate?: string;
    }): Promise<VehicleAssignment>;
    completeAssignment(id: string): Promise<VehicleAssignment>;
    cancelAssignment(id: string): Promise<VehicleAssignment>;
    getMaintenanceLogs(id: string): Promise<MaintenanceLog[]>;
    createMaintenanceLog(id: string, data: {
        type: MaintenanceType;
        category: MaintenanceCategory;
        description: string;
        status: MaintenanceStatus;
        date?: string;
        scheduledDate: string;
        mileage?: number;
        partsCost?: number;
        laborCost?: number;
        technician?: string;
        notes?: string;
        nextMaintenanceDate?: string;
        nextMaintenanceMileage?: number;
    }): Promise<MaintenanceLog>;
    updateMaintenanceLog(logId: string, data: {
        type?: MaintenanceType;
        category?: MaintenanceCategory;
        description?: string;
        status?: MaintenanceStatus;
        date?: string;
        scheduledDate?: string;
        mileage?: number;
        partsCost?: number;
        laborCost?: number;
        technician?: string;
        notes?: string;
        nextMaintenanceDate?: string;
        nextMaintenanceMileage?: number;
    }): Promise<MaintenanceLog>;
    deleteMaintenanceLog(logId: string): Promise<MaintenanceLog>;
    getUpcomingMaintenance(): Promise<MaintenanceLog[]>;
}
