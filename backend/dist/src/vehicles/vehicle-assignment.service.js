"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehicleAssignmentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const custom_exceptions_1 = require("../common/exceptions/custom-exceptions");
let VehicleAssignmentService = class VehicleAssignmentService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        if (data.endDate && data.startDate >= data.endDate) {
            throw new custom_exceptions_1.BusinessLogicException('End date must be after start date');
        }
        await this.validateVehicleAvailability(data.vehicleId);
        await this.validateDriverAvailability(data.driverId);
        try {
            const [assignment] = await this.prisma.$transaction([
                this.prisma.vehicleAssignment.create({
                    data: {
                        ...data,
                        status: 'ACTIVE',
                    },
                    include: {
                        driver: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true,
                                status: true,
                            },
                        },
                        vehicle: {
                            select: {
                                plateNumber: true,
                                make: true,
                                model: true,
                                status: true,
                            },
                        },
                    },
                }),
                this.prisma.vehicle.update({
                    where: { id: data.vehicleId },
                    data: { status: client_1.VehicleStatus.ASSIGNED },
                }),
                this.prisma.user.update({
                    where: { id: data.driverId },
                    data: { status: 'Assigned' },
                }),
            ]);
            return assignment;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                switch (error.code) {
                    case 'P2002':
                        throw new custom_exceptions_1.ConflictException('Assignment already exists or violates unique constraint');
                    case 'P2025':
                        throw new custom_exceptions_1.ResourceNotFoundException('Vehicle or Driver', 'referenced in assignment');
                    default:
                        throw new custom_exceptions_1.DatabaseException('Failed to create assignment', error);
                }
            }
            throw error;
        }
    }
    async findAllByVehicle(vehicleId) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: vehicleId },
        });
        if (!vehicle) {
            throw new common_1.NotFoundException('Vehicle not found');
        }
        return this.prisma.vehicleAssignment.findMany({
            where: { vehicleId },
            include: {
                driver: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                vehicle: {
                    select: {
                        plateNumber: true,
                        make: true,
                        model: true,
                    },
                },
            },
            orderBy: { startDate: 'desc' },
        });
    }
    async findAll() {
        return this.prisma.vehicleAssignment.findMany({
            include: {
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                    },
                },
            },
            orderBy: { startDate: 'desc' },
        });
    }
    async complete(id) {
        const assignment = await this.prisma.vehicleAssignment.findUnique({
            where: { id },
            select: { vehicleId: true, driverId: true, status: true },
        });
        if (!assignment) {
            throw new common_1.NotFoundException('Assignment not found');
        }
        if (assignment.status !== 'ACTIVE') {
            throw new custom_exceptions_1.BusinessLogicException('Only active assignments can be completed');
        }
        try {
            const [updatedAssignment] = await this.prisma.$transaction([
                this.prisma.vehicleAssignment.update({
                    where: { id },
                    data: {
                        status: 'COMPLETED',
                        endDate: new Date(),
                    },
                    include: {
                        driver: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                        vehicle: {
                            select: {
                                plateNumber: true,
                                make: true,
                                model: true,
                            },
                        },
                    },
                }),
                this.prisma.vehicle.update({
                    where: { id: assignment.vehicleId },
                    data: { status: client_1.VehicleStatus.AVAILABLE },
                }),
                this.prisma.user.update({
                    where: { id: assignment.driverId },
                    data: { status: 'Active' },
                }),
            ]);
            return updatedAssignment;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                throw new custom_exceptions_1.DatabaseException('Failed to complete assignment', error);
            }
            throw error;
        }
    }
    async cancel(id) {
        const assignment = await this.prisma.vehicleAssignment.findUnique({
            where: { id },
            select: { vehicleId: true, driverId: true, status: true },
        });
        if (!assignment) {
            throw new common_1.NotFoundException('Assignment not found');
        }
        if (assignment.status !== 'ACTIVE') {
            throw new custom_exceptions_1.BusinessLogicException('Only active assignments can be cancelled');
        }
        try {
            const [updatedAssignment] = await this.prisma.$transaction([
                this.prisma.vehicleAssignment.update({
                    where: { id },
                    data: {
                        status: 'CANCELLED',
                        endDate: new Date(),
                    },
                    include: {
                        driver: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                        vehicle: {
                            select: {
                                plateNumber: true,
                                make: true,
                                model: true,
                            },
                        },
                    },
                }),
                this.prisma.vehicle.update({
                    where: { id: assignment.vehicleId },
                    data: { status: client_1.VehicleStatus.AVAILABLE },
                }),
                this.prisma.user.update({
                    where: { id: assignment.driverId },
                    data: { status: 'Active' },
                }),
            ]);
            return updatedAssignment;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                throw new custom_exceptions_1.DatabaseException('Failed to cancel assignment', error);
            }
            throw error;
        }
    }
    async validateDriverAvailability(driverId) {
        const driver = await this.prisma.user.findUnique({
            where: { id: driverId },
            include: {
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                },
            },
        });
        if (!driver) {
            throw new custom_exceptions_1.ResourceNotFoundException('Driver', driverId);
        }
        if (driver.role !== 'DRIVER') {
            throw new custom_exceptions_1.BusinessLogicException('User is not a driver');
        }
        if (driver.assignments.length > 0) {
            throw new custom_exceptions_1.ConflictException('Driver already has an active assignment');
        }
        if (driver.status && driver.status !== 'Active' && driver.status !== 'Available') {
            throw new custom_exceptions_1.ConflictException(`Driver is currently ${driver.status.toLowerCase()} and cannot be assigned`);
        }
        return driver;
    }
    async validateVehicleAvailability(vehicleId) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: vehicleId },
            include: {
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                },
            },
        });
        if (!vehicle) {
            throw new custom_exceptions_1.ResourceNotFoundException('Vehicle', vehicleId);
        }
        if (vehicle.status !== client_1.VehicleStatus.AVAILABLE) {
            throw new custom_exceptions_1.ConflictException(`Vehicle is currently ${vehicle.status.toLowerCase()} and cannot be assigned`);
        }
        if (vehicle.assignments.length > 0) {
            throw new custom_exceptions_1.ConflictException('Vehicle already has an active assignment');
        }
        return vehicle;
    }
};
exports.VehicleAssignmentService = VehicleAssignmentService;
exports.VehicleAssignmentService = VehicleAssignmentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VehicleAssignmentService);
//# sourceMappingURL=vehicle-assignment.service.js.map