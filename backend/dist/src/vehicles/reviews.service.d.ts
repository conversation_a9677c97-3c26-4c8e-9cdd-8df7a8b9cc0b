import { VehicleReview, Prisma } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';
export declare class ReviewsService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(): Promise<VehicleReview[]>;
    findOne(id: string): Promise<VehicleReview | null>;
    findAllByVehicle(vehicleId: string): Promise<VehicleReview[]>;
    create(data: Prisma.VehicleReviewCreateInput): Promise<VehicleReview>;
    update(id: string, data: Prisma.VehicleReviewUpdateInput): Promise<VehicleReview>;
    delete(id: string): Promise<VehicleReview>;
    getUpcomingInspections(days?: number): Promise<VehicleReview[]>;
}
