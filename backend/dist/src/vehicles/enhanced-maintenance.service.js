"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedMaintenanceService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let EnhancedMaintenanceService = class EnhancedMaintenanceService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    getMaintenanceCategoriesForVehicleType(vehicleType) {
        const commonCategories = [
            'OTHER',
            'BRAKES',
            'TIRES',
            'ELECTRICAL'
        ];
        if (vehicleType === client_1.VehicleType.TRUCK) {
            return [
                ...commonCategories,
                'ENGINE',
                'TRANSMISSION',
                'COOLING_SYSTEM',
                'FUEL_SYSTEM',
                'EXHAUST_SYSTEM'
            ];
        }
        else if (vehicleType === client_1.VehicleType.TRAILER) {
            return [
                ...commonCategories,
                'SUSPENSION_AXLES',
                'CARGO_AREA',
                'REFRIGERATION_UNIT',
                'HYDRAULIC_SYSTEMS',
                'LIGHTING_SYSTEM'
            ];
        }
        return commonCategories;
    }
    async getMaintenanceRecommendations(vehicleId) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: vehicleId },
            include: {
                maintenanceLogs: {
                    orderBy: { date: 'desc' },
                    take: 10
                }
            }
        });
        if (!vehicle) {
            throw new common_1.NotFoundException('Vehicle not found');
        }
        const recommendations = [];
        const currentMileage = vehicle.mileage || 0;
        if (vehicle.vehicleType === client_1.VehicleType.TRUCK) {
            const truckRecommendations = this.getTruckMaintenanceRecommendations(currentMileage, vehicle.maintenanceLogs);
            recommendations.push(...truckRecommendations);
        }
        else if (vehicle.vehicleType === client_1.VehicleType.TRAILER) {
            const trailerRecommendations = this.getTrailerMaintenanceRecommendations(currentMileage, vehicle.maintenanceLogs);
            recommendations.push(...trailerRecommendations);
        }
        return recommendations;
    }
    getTruckMaintenanceRecommendations(mileage, logs) {
        const recommendations = [];
        const lastOilChange = logs.find(log => log.category === 'ENGINE' && log.description.toLowerCase().includes('oil'));
        if (!lastOilChange || (mileage - (lastOilChange.mileage || 0)) > 10000) {
            recommendations.push({
                type: 'PREVENTIVE_MAINTENANCE',
                category: 'ENGINE',
                description: 'Oil and filter change',
                priority: 'HIGH',
                estimatedCost: 150,
                dueAtMileage: Math.ceil(mileage / 10000) * 10000
            });
        }
        const lastTransmissionService = logs.find(log => log.category === 'TRANSMISSION');
        if (!lastTransmissionService || (mileage - (lastTransmissionService.mileage || 0)) > 50000) {
            recommendations.push({
                type: 'PREVENTIVE_MAINTENANCE',
                category: 'TRANSMISSION',
                description: 'Transmission fluid and filter service',
                priority: 'MEDIUM',
                estimatedCost: 300,
                dueAtMileage: Math.ceil(mileage / 50000) * 50000
            });
        }
        const lastBrakeInspection = logs.find(log => log.category === 'BRAKES');
        if (!lastBrakeInspection || (mileage - (lastBrakeInspection.mileage || 0)) > 25000) {
            recommendations.push({
                type: 'INSPECTION',
                category: 'BRAKES',
                description: 'Brake system inspection and service',
                priority: 'HIGH',
                estimatedCost: 200,
                dueAtMileage: Math.ceil(mileage / 25000) * 25000
            });
        }
        return recommendations;
    }
    getTrailerMaintenanceRecommendations(mileage, logs) {
        const recommendations = [];
        const lastBrakeInspection = logs.find(log => log.category === 'BRAKES');
        if (!lastBrakeInspection || (mileage - (lastBrakeInspection.mileage || 0)) > 30000) {
            recommendations.push({
                type: 'INSPECTION',
                category: 'BRAKES',
                description: 'Trailer brake system inspection',
                priority: 'HIGH',
                estimatedCost: 150,
                dueAtMileage: Math.ceil(mileage / 30000) * 30000
            });
        }
        const lastSuspensionCheck = logs.find(log => log.category === 'SUSPENSION_AXLES');
        if (!lastSuspensionCheck || (mileage - (lastSuspensionCheck.mileage || 0)) > 40000) {
            recommendations.push({
                type: 'INSPECTION',
                category: 'SUSPENSION_AXLES',
                description: 'Suspension and axle inspection',
                priority: 'MEDIUM',
                estimatedCost: 100,
                dueAtMileage: Math.ceil(mileage / 40000) * 40000
            });
        }
        recommendations.push({
            type: 'PREVENTIVE_MAINTENANCE',
            category: 'REFRIGERATION_UNIT',
            description: 'Refrigeration unit service and inspection',
            priority: 'MEDIUM',
            estimatedCost: 250,
            note: 'Only applicable for refrigerated trailers'
        });
        return recommendations;
    }
    async getMaintenanceByVehicleType(vehicleType) {
        return this.prisma.maintenanceLog.findMany({
            where: {
                vehicle: {
                    vehicleType: vehicleType
                }
            },
            include: {
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                        trailerType: true,
                    }
                }
            },
            orderBy: {
                date: 'desc'
            }
        });
    }
    async getMaintenanceStatsByVehicleType() {
        const truckStats = await this.prisma.maintenanceLog.aggregate({
            where: {
                vehicle: {
                    vehicleType: client_1.VehicleType.TRUCK
                }
            },
            _avg: {
                cost: true,
                partsCost: true,
                laborCost: true,
            },
            _sum: {
                cost: true,
                partsCost: true,
                laborCost: true,
            },
            _count: {
                id: true,
            }
        });
        const trailerStats = await this.prisma.maintenanceLog.aggregate({
            where: {
                vehicle: {
                    vehicleType: client_1.VehicleType.TRAILER
                }
            },
            _avg: {
                cost: true,
                partsCost: true,
                laborCost: true,
            },
            _sum: {
                cost: true,
                partsCost: true,
                laborCost: true,
            },
            _count: {
                id: true,
            }
        });
        return {
            trucks: {
                totalMaintenance: truckStats._count.id,
                averageCost: truckStats._avg.cost,
                totalCost: truckStats._sum.cost,
                averagePartsCost: truckStats._avg.partsCost,
                averageLaborCost: truckStats._avg.laborCost,
            },
            trailers: {
                totalMaintenance: trailerStats._count.id,
                averageCost: trailerStats._avg.cost,
                totalCost: trailerStats._sum.cost,
                averagePartsCost: trailerStats._avg.partsCost,
                averageLaborCost: trailerStats._avg.laborCost,
            }
        };
    }
    async createMaintenance(data) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: data.vehicleId }
        });
        if (!vehicle) {
            throw new common_1.NotFoundException('Vehicle not found');
        }
        const validCategories = this.getMaintenanceCategoriesForVehicleType(vehicle.vehicleType);
        if (!validCategories.includes(data.category)) {
            throw new common_1.BadRequestException(`Category ${data.category} is not valid for ${vehicle.vehicleType} vehicles. Valid categories: ${validCategories.join(', ')}`);
        }
        const totalCost = (data.partsCost || 0) + (data.laborCost || 0);
        return this.prisma.maintenanceLog.create({
            data: {
                vehicleId: data.vehicleId,
                type: data.type,
                category: data.category,
                description: data.description,
                status: data.status || client_1.MaintenanceStatus.SCHEDULED,
                date: data.date ? new Date(data.date) : null,
                scheduledDate: new Date(data.scheduledDate),
                mileage: data.mileage,
                partsCost: data.partsCost,
                laborCost: data.laborCost,
                cost: totalCost,
                technician: data.technician,
                notes: data.notes,
                nextMaintenanceDate: data.nextMaintenanceDate ? new Date(data.nextMaintenanceDate) : null,
                nextMaintenanceMileage: data.nextMaintenanceMileage,
            },
            include: {
                vehicle: {
                    select: {
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                    }
                }
            }
        });
    }
    async getUpcomingMaintenanceByVehicleType(vehicleType) {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        return this.prisma.maintenanceLog.findMany({
            where: {
                vehicle: {
                    vehicleType: vehicleType
                },
                status: {
                    in: [client_1.MaintenanceStatus.SCHEDULED, client_1.MaintenanceStatus.IN_PROGRESS]
                },
                scheduledDate: {
                    lte: thirtyDaysFromNow
                }
            },
            include: {
                vehicle: {
                    select: {
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                    }
                }
            },
            orderBy: {
                scheduledDate: 'asc'
            }
        });
    }
};
exports.EnhancedMaintenanceService = EnhancedMaintenanceService;
exports.EnhancedMaintenanceService = EnhancedMaintenanceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], EnhancedMaintenanceService);
//# sourceMappingURL=enhanced-maintenance.service.js.map