import { TruckTrailerAssignmentService } from './truck-trailer-assignment.service';
import { CreateTruckTrailerAssignmentDto } from './dto/create-truck-trailer-assignment.dto';
import { UpdateTruckTrailerAssignmentDto } from './dto/update-truck-trailer-assignment.dto';
import { TruckTrailerAssignment } from '@prisma/client';
export declare class TruckTrailerAssignmentController {
    private readonly assignmentService;
    constructor(assignmentService: TruckTrailerAssignmentService);
    findAll(active?: string): Promise<TruckTrailerAssignment[]>;
    findActive(): Promise<TruckTrailerAssignment[]>;
    findOne(id: string): Promise<TruckTrailerAssignment | null>;
    findByTruck(truckId: string): Promise<TruckTrailerAssignment[]>;
    findByTrailer(trailerId: string): Promise<TruckTrailerAssignment[]>;
    create(createAssignmentDto: CreateTruckTrailerAssignmentDto, req: any): Promise<TruckTrailerAssignment>;
    update(id: string, updateAssignmentDto: UpdateTruckTrailerAssignmentDto): Promise<TruckTrailerAssignment>;
    complete(id: string): Promise<TruckTrailerAssignment>;
    cancel(id: string): Promise<TruckTrailerAssignment>;
    remove(id: string): Promise<TruckTrailerAssignment>;
}
