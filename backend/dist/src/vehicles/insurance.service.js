"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsuranceService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let InsuranceService = class InsuranceService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll() {
        return this.prisma.insurancePolicy.findMany({
            orderBy: { endDate: 'asc' },
        });
    }
    async findOne(id) {
        return this.prisma.insurancePolicy.findUnique({
            where: { id },
        });
    }
    async findAllByVehicle(vehicleId) {
        return this.prisma.insurancePolicy.findMany({
            where: { vehicleId },
            orderBy: { endDate: 'asc' },
        });
    }
    async create(data) {
        return this.prisma.insurancePolicy.create({
            data,
        });
    }
    async update(id, data) {
        return this.prisma.insurancePolicy.update({
            where: { id },
            data,
        });
    }
    async delete(id) {
        return this.prisma.insurancePolicy.delete({
            where: { id },
        });
    }
    async getUpcomingRenewals(days = 30) {
        const today = new Date();
        const threshold = new Date();
        threshold.setDate(today.getDate() + days);
        return this.prisma.insurancePolicy.findMany({
            where: {
                endDate: {
                    gte: today,
                    lte: threshold,
                },
                status: 'ACTIVE',
            },
            orderBy: {
                endDate: 'asc',
            },
        });
    }
};
exports.InsuranceService = InsuranceService;
exports.InsuranceService = InsuranceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InsuranceService);
//# sourceMappingURL=insurance.service.js.map