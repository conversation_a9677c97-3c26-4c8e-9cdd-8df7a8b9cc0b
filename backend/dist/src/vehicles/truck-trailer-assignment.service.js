"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TruckTrailerAssignmentService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let TruckTrailerAssignmentService = class TruckTrailerAssignmentService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll() {
        return this.prisma.truckTrailerAssignment.findMany({
            include: {
                truck: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                        vehicleType: true,
                    }
                },
                trailer: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                        vehicleType: true,
                        trailerType: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    async findActive() {
        return this.prisma.truckTrailerAssignment.findMany({
            where: {
                status: client_1.AssignmentStatus.ACTIVE,
                endDate: null
            },
            include: {
                truck: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                        vehicleType: true,
                    }
                },
                trailer: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                        vehicleType: true,
                        trailerType: true,
                    }
                }
            }
        });
    }
    async findOne(id) {
        return this.prisma.truckTrailerAssignment.findUnique({
            where: { id },
            include: {
                truck: true,
                trailer: true
            }
        });
    }
    async findByTruck(truckId) {
        return this.prisma.truckTrailerAssignment.findMany({
            where: { truckId },
            include: {
                truck: true,
                trailer: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    async findByTrailer(trailerId) {
        return this.prisma.truckTrailerAssignment.findMany({
            where: { trailerId },
            include: {
                truck: true,
                trailer: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    async create(data) {
        await this.validateVehicles(data.truckId, data.trailerId);
        await this.checkForConflicts(data.truckId, data.trailerId, data.startDate);
        if (!data.assignedBy) {
            throw new common_1.BadRequestException('Assigned by is required');
        }
        return this.prisma.truckTrailerAssignment.create({
            data: {
                truckId: data.truckId,
                trailerId: data.trailerId,
                startDate: new Date(data.startDate),
                endDate: data.endDate ? new Date(data.endDate) : null,
                status: client_1.AssignmentStatus.ACTIVE,
                notes: data.notes,
                assignedBy: data.assignedBy
            },
            include: {
                truck: true,
                trailer: true
            }
        });
    }
    async update(id, data) {
        const assignment = await this.findOne(id);
        if (!assignment) {
            throw new common_1.NotFoundException(`Assignment with ID ${id} not found`);
        }
        if (data.truckId || data.trailerId) {
            await this.validateVehicles(data.truckId || assignment.truckId, data.trailerId || assignment.trailerId);
        }
        const updateData = { ...data };
        if (updateData.startDate) {
            updateData.startDate = new Date(updateData.startDate);
        }
        if (updateData.endDate) {
            updateData.endDate = new Date(updateData.endDate);
        }
        return this.prisma.truckTrailerAssignment.update({
            where: { id },
            data: updateData,
            include: {
                truck: true,
                trailer: true
            }
        });
    }
    async complete(id) {
        const assignment = await this.findOne(id);
        if (!assignment) {
            throw new common_1.NotFoundException(`Assignment with ID ${id} not found`);
        }
        if (assignment.status !== client_1.AssignmentStatus.ACTIVE) {
            throw new common_1.BadRequestException('Only active assignments can be completed');
        }
        return this.prisma.truckTrailerAssignment.update({
            where: { id },
            data: {
                status: client_1.AssignmentStatus.COMPLETED,
                endDate: new Date()
            },
            include: {
                truck: true,
                trailer: true
            }
        });
    }
    async cancel(id) {
        const assignment = await this.findOne(id);
        if (!assignment) {
            throw new common_1.NotFoundException(`Assignment with ID ${id} not found`);
        }
        if (assignment.status === client_1.AssignmentStatus.COMPLETED) {
            throw new common_1.BadRequestException('Completed assignments cannot be cancelled');
        }
        return this.prisma.truckTrailerAssignment.update({
            where: { id },
            data: {
                status: client_1.AssignmentStatus.CANCELLED,
                endDate: new Date()
            },
            include: {
                truck: true,
                trailer: true
            }
        });
    }
    async delete(id) {
        const assignment = await this.findOne(id);
        if (!assignment) {
            throw new common_1.NotFoundException(`Assignment with ID ${id} not found`);
        }
        return this.prisma.truckTrailerAssignment.delete({
            where: { id }
        });
    }
    async validateVehicles(truckId, trailerId) {
        const truck = await this.prisma.vehicle.findUnique({
            where: { id: truckId },
            select: { id: true, vehicleType: true, plateNumber: true }
        });
        if (!truck) {
            throw new common_1.NotFoundException(`Truck with ID ${truckId} not found`);
        }
        if (truck.vehicleType !== client_1.VehicleType.TRUCK) {
            throw new common_1.BadRequestException(`Vehicle ${truck.plateNumber} is not a truck`);
        }
        const trailer = await this.prisma.vehicle.findUnique({
            where: { id: trailerId },
            select: { id: true, vehicleType: true, plateNumber: true }
        });
        if (!trailer) {
            throw new common_1.NotFoundException(`Trailer with ID ${trailerId} not found`);
        }
        if (trailer.vehicleType !== client_1.VehicleType.TRAILER) {
            throw new common_1.BadRequestException(`Vehicle ${trailer.plateNumber} is not a trailer`);
        }
    }
    async checkForConflicts(truckId, trailerId, startDate) {
        const startDateTime = new Date(startDate);
        const truckConflict = await this.prisma.truckTrailerAssignment.findFirst({
            where: {
                truckId,
                status: client_1.AssignmentStatus.ACTIVE,
                OR: [
                    { endDate: null },
                    { endDate: { gte: startDateTime } }
                ]
            }
        });
        if (truckConflict) {
            throw new common_1.ConflictException('Truck already has an active assignment');
        }
        const trailerConflict = await this.prisma.truckTrailerAssignment.findFirst({
            where: {
                trailerId,
                status: client_1.AssignmentStatus.ACTIVE,
                OR: [
                    { endDate: null },
                    { endDate: { gte: startDateTime } }
                ]
            }
        });
        if (trailerConflict) {
            throw new common_1.ConflictException('Trailer already has an active assignment');
        }
    }
};
exports.TruckTrailerAssignmentService = TruckTrailerAssignmentService;
exports.TruckTrailerAssignmentService = TruckTrailerAssignmentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TruckTrailerAssignmentService);
//# sourceMappingURL=truck-trailer-assignment.service.js.map