import { VehiclesService } from './vehicles.service';
import { CreateMaintenanceDto } from '../maintenance/dto/create-maintenance.dto';
import { UpdateMaintenanceDto } from '../maintenance/dto/update-maintenance.dto';
import { MaintenanceLog } from '@prisma/client';
export declare class MaintenanceController {
    private readonly vehiclesService;
    constructor(vehiclesService: VehiclesService);
    findAll(): Promise<MaintenanceLog[]>;
    findOne(id: string): Promise<MaintenanceLog | null>;
    create(createMaintenanceDto: CreateMaintenanceDto): Promise<MaintenanceLog>;
    update(id: string, updateMaintenanceDto: UpdateMaintenanceDto): Promise<MaintenanceLog>;
    remove(id: string): Promise<MaintenanceLog>;
}
