"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateReviewDto = void 0;
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class UpdateReviewDto {
    vehicleId;
    reviewType;
    reviewBy;
    scheduledDate;
    completedDate;
    location;
    status;
    findings;
    recommendations;
    nextReviewDate;
    documents;
}
exports.UpdateReviewDto = UpdateReviewDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100, { message: 'Vehicle ID must not exceed 100 characters' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ReviewType, { message: 'Review type must be a valid review type' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "reviewType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200, { message: 'Reviewer name must not exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "reviewBy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Scheduled date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "scheduledDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Completed date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "completedDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200, { message: 'Location must not exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ReviewStatus, { message: 'Status must be a valid review status' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2000, { message: 'Findings must not exceed 2000 characters' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "findings", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(2000, { message: 'Recommendations must not exceed 2000 characters' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "recommendations", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Next review date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateReviewDto.prototype, "nextReviewDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdateReviewDto.prototype, "documents", void 0);
//# sourceMappingURL=update-review.dto.js.map