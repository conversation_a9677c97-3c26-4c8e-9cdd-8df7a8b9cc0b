import { FuelService } from './fuel.service';
import { CreateFuelRecordDto } from './dto/create-fuel-record.dto';
import { UpdateFuelRecordDto } from './dto/update-fuel-record.dto';
import { FuelRecordFiltersDto } from './dto/fuel-record-filters.dto';
export declare class FuelController {
    private readonly fuelService;
    private readonly logger;
    constructor(fuelService: FuelService);
    createFuelRecord(createFuelRecordDto: CreateFuelRecordDto, receiptFile?: any): unknown;
    getFuelRecords(filters: FuelRecordFiltersDto): unknown;
    getFuelRecordById(id: string): unknown;
    updateFuelRecord(id: string, updateFuelRecordDto: UpdateFuelRecordDto, receiptFile?: any): unknown;
    deleteFuelRecord(id: string): unknown;
    getVehicleFuelSummary(vehicleId: string, startDate?: string, endDate?: string): unknown;
    getVehicleFuelEfficiency(vehicleId: string, startDate?: string, endDate?: string): unknown;
    getDriverFuelSummary(driverId: string, startDate?: string, endDate?: string): unknown;
    getFleetFuelReport(startDate?: string, endDate?: string): unknown;
    getDashboardStats(): unknown;
    getBasicStats(): unknown;
}
