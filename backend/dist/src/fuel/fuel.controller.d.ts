import { FuelService } from './fuel.service';
import { CreateFuelRecordDto } from './dto/create-fuel-record.dto';
import { UpdateFuelRecordDto } from './dto/update-fuel-record.dto';
import { FuelRecordFiltersDto } from './dto/fuel-record-filters.dto';
export declare class FuelController {
    private readonly fuelService;
    private readonly logger;
    constructor(fuelService: FuelService);
    createFuelRecord(createFuelRecordDto: CreateFuelRecordDto, receiptFile?: any): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").FuelRecordWithRelations;
    }>;
    getFuelRecords(filters: FuelRecordFiltersDto): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").FuelRecordWithRelations[];
        pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getFuelRecordById(id: string): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").FuelRecordWithRelations;
    }>;
    updateFuelRecord(id: string, updateFuelRecordDto: UpdateFuelRecordDto, receiptFile?: any): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").FuelRecordWithRelations;
    }>;
    deleteFuelRecord(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getVehicleFuelSummary(vehicleId: string, startDate?: string, endDate?: string): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").VehicleFuelSummary;
    }>;
    getVehicleFuelEfficiency(vehicleId: string, startDate?: string, endDate?: string): Promise<{
        success: boolean;
        message: string;
        data: import("./fuel.service").FuelEfficiencyMetrics | null;
    }>;
    getDriverFuelSummary(driverId: string, startDate?: string, endDate?: string): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
    getFleetFuelReport(startDate?: string, endDate?: string): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
    getDashboardStats(): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
    getBasicStats(): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
}
