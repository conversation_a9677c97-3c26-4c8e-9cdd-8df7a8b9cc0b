"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFuelRecordDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateFuelRecordDto {
    vehicleId;
    driverId;
    quantity;
    totalCost;
    location;
    fuelingDate;
    odometerReading;
    receiptNumber;
    notes;
    enteredBy;
}
exports.CreateFuelRecordDto = CreateFuelRecordDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Vehicle ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Vehicle ID must be a string' }),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Driver ID is required' }),
    (0, class_validator_1.IsString)({ message: 'Driver ID must be a string' }),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "driverId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Quantity is required' }),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Quantity must be a valid number' }),
    (0, class_validator_1.Min)(0.1, { message: 'Quantity must be at least 0.1 liters' }),
    (0, class_validator_1.Max)(1000, { message: 'Quantity cannot exceed 1000 liters' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateFuelRecordDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Total cost is required' }),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Total cost must be a valid number' }),
    (0, class_validator_1.Min)(0.01, { message: 'Total cost must be at least 0.01' }),
    (0, class_validator_1.Max)(10000, { message: 'Total cost cannot exceed 10000' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateFuelRecordDto.prototype, "totalCost", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Location is required' }),
    (0, class_validator_1.IsString)({ message: 'Location must be a string' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "location", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Fueling date is required' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Fueling date must be a valid date string' }),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "fuelingDate", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Odometer reading is required' }),
    (0, class_validator_1.IsInt)({ message: 'Odometer reading must be an integer' }),
    (0, class_validator_1.Min)(0, { message: 'Odometer reading cannot be negative' }),
    (0, class_validator_1.Max)(9999999, { message: 'Odometer reading seems unreasonably high' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], CreateFuelRecordDto.prototype, "odometerReading", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Receipt number must be a string' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "receiptNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Notes must be a string' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Entered by is required' }),
    (0, class_validator_1.IsString)({ message: 'Entered by must be a string' }),
    (0, class_transformer_1.Transform)(({ value }) => value?.trim()),
    __metadata("design:type", String)
], CreateFuelRecordDto.prototype, "enteredBy", void 0);
//# sourceMappingURL=create-fuel-record.dto.js.map