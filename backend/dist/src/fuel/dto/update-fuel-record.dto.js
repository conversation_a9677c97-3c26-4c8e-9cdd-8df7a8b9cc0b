"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateFuelRecordDto = void 0;
const mapped_types_1 = require("@nestjs/mapped-types");
const create_fuel_record_dto_1 = require("./create-fuel-record.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class UpdateFuelRecordDto extends (0, mapped_types_1.PartialType)(create_fuel_record_dto_1.CreateFuelRecordDto) {
    quantity;
    totalCost;
    fuelingDate;
}
exports.UpdateFuelRecordDto = UpdateFuelRecordDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Quantity must be a valid number' }),
    (0, class_validator_1.Min)(0.1, { message: 'Quantity must be at least 0.1 liters' }),
    (0, class_validator_1.Max)(1000, { message: 'Quantity cannot exceed 1000 liters' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], UpdateFuelRecordDto.prototype, "quantity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ allowNaN: false, allowInfinity: false }, { message: 'Total cost must be a valid number' }),
    (0, class_validator_1.Min)(0.01, { message: 'Total cost must be at least 0.01' }),
    (0, class_validator_1.Max)(10000, { message: 'Total cost cannot exceed 10000' }),
    (0, class_transformer_1.Transform)(({ value }) => value ? Number(value) : value),
    __metadata("design:type", Number)
], UpdateFuelRecordDto.prototype, "totalCost", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Fueling date must be a valid date string' }),
    __metadata("design:type", String)
], UpdateFuelRecordDto.prototype, "fuelingDate", void 0);
//# sourceMappingURL=update-fuel-record.dto.js.map