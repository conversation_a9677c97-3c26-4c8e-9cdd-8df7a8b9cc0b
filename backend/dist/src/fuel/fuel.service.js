"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FuelService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuelService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FuelService = FuelService_1 = class FuelService {
    prisma;
    logger = new common_1.Logger(FuelService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async recordFueling(data) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: data.vehicleId },
            select: { id: true, vehicleType: true, plateNumber: true, make: true, model: true }
        });
        if (!vehicle) {
            throw new common_1.NotFoundException('Vehicle not found');
        }
        if (vehicle.vehicleType !== 'TRUCK') {
            throw new common_1.BadRequestException('Only trucks can have fuel records');
        }
        const driver = await this.prisma.user.findUnique({
            where: { id: data.driverId },
            select: { id: true, firstName: true, lastName: true, email: true }
        });
        if (!driver) {
            throw new common_1.NotFoundException('Driver not found');
        }
        const pricePerLiter = data.totalCost / data.quantity;
        const lastRecord = await this.getLastFuelRecord(data.vehicleId);
        if (lastRecord && data.odometerReading <= lastRecord.odometerReading) {
            this.logger.warn(`Odometer reading ${data.odometerReading} is not higher than previous reading ${lastRecord.odometerReading} for vehicle ${data.vehicleId}`);
        }
        try {
            const fuelRecord = await this.prisma.fuelRecord.create({
                data: {
                    vehicleId: data.vehicleId,
                    driverId: data.driverId,
                    quantity: data.quantity,
                    totalCost: data.totalCost,
                    pricePerLiter,
                    location: data.location,
                    fuelingDate: new Date(data.fuelingDate),
                    odometerReading: data.odometerReading,
                    receiptNumber: data.receiptNumber,
                    notes: data.notes,
                    enteredBy: data.enteredBy,
                },
                include: {
                    vehicle: {
                        select: {
                            id: true,
                            plateNumber: true,
                            make: true,
                            model: true,
                            vehicleType: true,
                        }
                    },
                    driver: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        }
                    }
                }
            });
            if (!lastRecord || data.odometerReading > (lastRecord.odometerReading || 0)) {
                await this.prisma.vehicle.update({
                    where: { id: data.vehicleId },
                    data: { mileage: data.odometerReading }
                });
            }
            const fuelingDate = new Date(data.fuelingDate);
            await this.recalculateMonthlyStats(data.vehicleId, data.driverId, fuelingDate.getMonth() + 1, fuelingDate.getFullYear());
            this.logger.log(`Fuel record created for vehicle ${vehicle.plateNumber} by ${driver.firstName} ${driver.lastName}`);
            return fuelRecord;
        }
        catch (error) {
            this.logger.error(`Failed to create fuel record: ${error.message}`);
            throw new common_1.BadRequestException('Failed to create fuel record');
        }
    }
    async getFuelRecords(filters) {
        const where = {};
        if (filters.vehicleId) {
            where.vehicleId = filters.vehicleId;
        }
        if (filters.driverId) {
            where.driverId = filters.driverId;
        }
        if (filters.startDate || filters.endDate) {
            where.fuelingDate = {};
            if (filters.startDate) {
                where.fuelingDate.gte = new Date(filters.startDate);
            }
            if (filters.endDate) {
                where.fuelingDate.lte = new Date(filters.endDate);
            }
        }
        if (filters.location) {
            where.location = {
                contains: filters.location,
                mode: 'insensitive'
            };
        }
        const total = await this.prisma.fuelRecord.count({ where });
        const page = filters.page || 1;
        const limit = filters.limit || 20;
        const skip = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);
        const records = await this.prisma.fuelRecord.findMany({
            where,
            include: {
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                    }
                },
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    }
                }
            },
            orderBy: {
                [filters.sortBy || 'fuelingDate']: filters.sortOrder || 'desc'
            },
            skip,
            take: limit,
        });
        return {
            records,
            total,
            page,
            limit,
            totalPages,
        };
    }
    async getFuelRecordById(id) {
        const record = await this.prisma.fuelRecord.findUnique({
            where: { id },
            include: {
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                    }
                },
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    }
                }
            }
        });
        if (!record) {
            throw new common_1.NotFoundException('Fuel record not found');
        }
        return record;
    }
    async updateFuelRecord(id, data) {
        const existingRecord = await this.getFuelRecordById(id);
        let pricePerLiter = existingRecord.pricePerLiter;
        if (data.quantity || data.totalCost) {
            const quantity = data.quantity || existingRecord.quantity;
            const totalCost = data.totalCost || existingRecord.totalCost;
            pricePerLiter = totalCost / quantity;
        }
        const updatedRecord = await this.prisma.fuelRecord.update({
            where: { id },
            data: {
                ...data,
                pricePerLiter,
                fuelingDate: data.fuelingDate ? new Date(data.fuelingDate) : undefined,
            },
            include: {
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        vehicleType: true,
                    }
                },
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    }
                }
            }
        });
        const fuelingDate = new Date(updatedRecord.fuelingDate);
        await this.recalculateMonthlyStats(updatedRecord.vehicleId, updatedRecord.driverId, fuelingDate.getMonth() + 1, fuelingDate.getFullYear());
        this.logger.log(`Fuel record ${id} updated`);
        return updatedRecord;
    }
    async deleteFuelRecord(id) {
        const record = await this.getFuelRecordById(id);
        await this.prisma.fuelRecord.delete({
            where: { id }
        });
        const fuelingDate = new Date(record.fuelingDate);
        await this.recalculateMonthlyStats(record.vehicleId, record.driverId, fuelingDate.getMonth() + 1, fuelingDate.getFullYear());
        this.logger.log(`Fuel record ${id} deleted`);
    }
    async getLastFuelRecord(vehicleId) {
        return this.prisma.fuelRecord.findFirst({
            where: { vehicleId },
            orderBy: { fuelingDate: 'desc' }
        });
    }
    async recalculateMonthlyStats(vehicleId, driverId, month, year) {
        try {
            const startDate = new Date(year, month - 1, 1);
            const endDate = new Date(year, month, 0, 23, 59, 59);
            const records = await this.prisma.fuelRecord.findMany({
                where: {
                    vehicleId,
                    driverId,
                    fuelingDate: {
                        gte: startDate,
                        lte: endDate,
                    }
                },
                orderBy: { fuelingDate: 'asc' }
            });
            if (records.length === 0) {
                await this.prisma.vehicleFuelStats.deleteMany({
                    where: { vehicleId, driverId, month, year }
                });
                return;
            }
            const totalFuelCost = records.reduce((sum, record) => sum + record.totalCost, 0);
            const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
            const avgPricePerLiter = totalFuelCost / totalLiters;
            const fuelingCount = records.length;
            const firstRecord = records[0];
            const lastRecord = records[records.length - 1];
            const totalDistance = lastRecord.odometerReading - firstRecord.odometerReading;
            const fuelEfficiency = totalDistance > 0 ? (totalLiters / totalDistance) * 100 : 0;
            const costPerKm = totalDistance > 0 ? totalFuelCost / totalDistance : 0;
            const marketPrices = await this.prisma.fuelPrice.findMany({
                where: {
                    effectiveDate: {
                        gte: startDate,
                        lte: endDate,
                    }
                }
            });
            let avgMarketPrice = null;
            let priceDifference = null;
            if (marketPrices.length > 0) {
                avgMarketPrice = marketPrices.reduce((sum, price) => sum + price.dieselPriceGross, 0) / marketPrices.length;
                priceDifference = avgPricePerLiter - avgMarketPrice;
            }
            await this.prisma.vehicleFuelStats.upsert({
                where: {
                    vehicleId_driverId_month_year: {
                        vehicleId,
                        driverId,
                        month,
                        year,
                    }
                },
                update: {
                    totalFuelCost,
                    totalLiters,
                    totalDistance,
                    fuelEfficiency,
                    costPerKm,
                    avgPricePerLiter,
                    fuelingCount,
                    avgMarketPrice,
                    priceDifference,
                    calculatedAt: new Date(),
                },
                create: {
                    vehicleId,
                    driverId,
                    month,
                    year,
                    totalFuelCost,
                    totalLiters,
                    totalDistance,
                    fuelEfficiency,
                    costPerKm,
                    avgPricePerLiter,
                    fuelingCount,
                    avgMarketPrice,
                    priceDifference,
                }
            });
            this.logger.log(`Monthly stats recalculated for vehicle ${vehicleId}, driver ${driverId}, ${month}/${year}`);
        }
        catch (error) {
            this.logger.error(`Failed to recalculate monthly stats: ${error.message}`);
        }
    }
    async calculateFuelEfficiency(vehicleId, period) {
        const where = { vehicleId };
        if (period) {
            where.fuelingDate = {
                gte: period.start,
                lte: period.end,
            };
        }
        const records = await this.prisma.fuelRecord.findMany({
            where,
            orderBy: { fuelingDate: 'asc' }
        });
        if (records.length < 2) {
            return null;
        }
        const firstRecord = records[0];
        const lastRecord = records[records.length - 1];
        const distanceTraveled = lastRecord.odometerReading - firstRecord.odometerReading;
        const fuelConsumed = records.reduce((sum, record) => sum + record.quantity, 0);
        if (distanceTraveled <= 0) {
            return null;
        }
        const fuelEfficiency = (fuelConsumed / distanceTraveled) * 100;
        return {
            fuelEfficiency,
            distanceTraveled,
            fuelConsumed,
            period: {
                start: period?.start || firstRecord.fuelingDate,
                end: period?.end || lastRecord.fuelingDate,
            }
        };
    }
    async getVehicleFuelSummary(vehicleId, period) {
        const where = { vehicleId };
        if (period) {
            where.fuelingDate = {
                gte: period.start,
                lte: period.end,
            };
        }
        const records = await this.prisma.fuelRecord.findMany({
            where,
            orderBy: { fuelingDate: 'asc' }
        });
        const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
        const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
        const recordCount = records.length;
        const efficiencyMetrics = await this.calculateFuelEfficiency(vehicleId, period);
        const averageEfficiency = efficiencyMetrics?.fuelEfficiency || 0;
        let marketComparison;
        if (period && records.length > 0) {
            const marketPrices = await this.prisma.fuelPrice.findMany({
                where: {
                    effectiveDate: {
                        gte: period.start,
                        lte: period.end,
                    }
                }
            });
            if (marketPrices.length > 0) {
                const avgMarketPrice = marketPrices.reduce((sum, price) => sum + price.dieselPriceGross, 0) / marketPrices.length;
                const avgPaidPrice = totalCost / totalLiters;
                const difference = avgPaidPrice - avgMarketPrice;
                const percentageDifference = (difference / avgMarketPrice) * 100;
                marketComparison = {
                    avgMarketPrice,
                    avgPaidPrice,
                    difference,
                    percentageDifference,
                };
            }
        }
        return {
            vehicleId,
            totalCost,
            totalLiters,
            averageEfficiency,
            recordCount,
            period: {
                start: period?.start || (records[0]?.fuelingDate || new Date()),
                end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
            },
            marketComparison,
        };
    }
    async getDriverFuelSummary(driverId, period) {
        const where = { driverId };
        if (period) {
            where.fuelingDate = {
                gte: period.start,
                lte: period.end,
            };
        }
        const records = await this.prisma.fuelRecord.findMany({
            where,
            include: {
                vehicle: {
                    select: {
                        plateNumber: true,
                        make: true,
                        model: true,
                    }
                }
            },
            orderBy: { fuelingDate: 'asc' }
        });
        const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
        const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
        const recordCount = records.length;
        const vehicleStats = records.reduce((acc, record) => {
            const vehicleId = record.vehicleId;
            if (!acc[vehicleId]) {
                acc[vehicleId] = {
                    vehicle: record.vehicle,
                    totalCost: 0,
                    totalLiters: 0,
                    records: [],
                };
            }
            acc[vehicleId].totalCost += record.totalCost;
            acc[vehicleId].totalLiters += record.quantity;
            acc[vehicleId].records.push(record);
            return acc;
        }, {});
        const vehicleEfficiencies = await Promise.all(Object.entries(vehicleStats).map(async ([vehicleId, stats]) => {
            const efficiency = await this.calculateFuelEfficiency(vehicleId, period);
            return {
                vehicleId,
                vehicle: stats.vehicle,
                totalCost: stats.totalCost,
                totalLiters: stats.totalLiters,
                efficiency: efficiency?.fuelEfficiency || 0,
                recordCount: stats.records.length,
            };
        }));
        const averageEfficiency = vehicleEfficiencies.length > 0
            ? vehicleEfficiencies.reduce((sum, v) => sum + v.efficiency, 0) / vehicleEfficiencies.length
            : 0;
        return {
            driverId,
            totalCost,
            totalLiters,
            averageEfficiency,
            recordCount,
            vehicleBreakdown: vehicleEfficiencies,
            period: {
                start: period?.start || (records[0]?.fuelingDate || new Date()),
                end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
            },
        };
    }
    async getFleetFuelReport(period) {
        const where = {};
        if (period) {
            where.fuelingDate = {
                gte: period.start,
                lte: period.end,
            };
        }
        const [records, vehicles, drivers] = await Promise.all([
            this.prisma.fuelRecord.findMany({
                where,
                include: {
                    vehicle: {
                        select: {
                            plateNumber: true,
                            make: true,
                            model: true,
                            vehicleType: true,
                        }
                    },
                    driver: {
                        select: {
                            firstName: true,
                            lastName: true,
                        }
                    }
                },
                orderBy: { fuelingDate: 'asc' }
            }),
            this.prisma.vehicle.findMany({
                where: { vehicleType: 'TRUCK' },
                select: {
                    id: true,
                    plateNumber: true,
                    make: true,
                    model: true,
                }
            }),
            this.prisma.user.findMany({
                where: { role: 'DRIVER' },
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                }
            })
        ]);
        const totalCost = records.reduce((sum, record) => sum + record.totalCost, 0);
        const totalLiters = records.reduce((sum, record) => sum + record.quantity, 0);
        const averagePricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0;
        const vehiclePerformance = await Promise.all(vehicles.map(async (vehicle) => {
            const vehicleRecords = records.filter(r => r.vehicleId === vehicle.id);
            const vehicleCost = vehicleRecords.reduce((sum, r) => sum + r.totalCost, 0);
            const vehicleLiters = vehicleRecords.reduce((sum, r) => sum + r.quantity, 0);
            const efficiency = await this.calculateFuelEfficiency(vehicle.id, period);
            return {
                vehicle,
                totalCost: vehicleCost,
                totalLiters: vehicleLiters,
                efficiency: efficiency?.fuelEfficiency || 0,
                recordCount: vehicleRecords.length,
            };
        }));
        const driverPerformance = await Promise.all(drivers.map(async (driver) => {
            const driverSummary = await this.getDriverFuelSummary(driver.id, period);
            return {
                driver,
                ...driverSummary,
            };
        }));
        vehiclePerformance.sort((a, b) => a.efficiency - b.efficiency);
        driverPerformance.sort((a, b) => a.averageEfficiency - b.averageEfficiency);
        const monthlyTrends = this.calculateMonthlyTrends(records);
        return {
            summary: {
                totalCost,
                totalLiters,
                averagePricePerLiter,
                recordCount: records.length,
                activeVehicles: vehiclePerformance.filter(v => v.recordCount > 0).length,
                activeDrivers: driverPerformance.filter(d => d.recordCount > 0).length,
            },
            vehiclePerformance: vehiclePerformance.slice(0, 10),
            driverPerformance: driverPerformance.slice(0, 10),
            monthlyTrends,
            period: {
                start: period?.start || (records[0]?.fuelingDate || new Date()),
                end: period?.end || (records[records.length - 1]?.fuelingDate || new Date()),
            },
        };
    }
    calculateMonthlyTrends(records) {
        const monthlyData = records.reduce((acc, record) => {
            const date = new Date(record.fuelingDate);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            if (!acc[monthKey]) {
                acc[monthKey] = {
                    month: monthKey,
                    totalCost: 0,
                    totalLiters: 0,
                    recordCount: 0,
                };
            }
            acc[monthKey].totalCost += record.totalCost;
            acc[monthKey].totalLiters += record.quantity;
            acc[monthKey].recordCount += 1;
            return acc;
        }, {});
        return Object.values(monthlyData).map((data) => ({
            ...data,
            averagePricePerLiter: data.totalLiters > 0 ? data.totalCost / data.totalLiters : 0,
        }));
    }
    async getDashboardStats() {
        const currentDate = new Date();
        const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
        const lastMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0, 23, 59, 59);
        const [currentMonthRecords, lastMonthRecords, totalStats, vehicleCount, activeDrivers,] = await Promise.all([
            this.prisma.fuelRecord.findMany({
                where: {
                    fuelingDate: {
                        gte: currentMonth,
                    }
                }
            }),
            this.prisma.fuelRecord.findMany({
                where: {
                    fuelingDate: {
                        gte: lastMonth,
                        lte: lastMonthEnd,
                    }
                }
            }),
            this.prisma.fuelRecord.aggregate({
                _sum: {
                    totalCost: true,
                    quantity: true,
                },
                _count: {
                    id: true,
                }
            }),
            this.prisma.vehicle.count({
                where: { vehicleType: 'TRUCK' }
            }),
            this.prisma.fuelRecord.findMany({
                where: {
                    fuelingDate: {
                        gte: currentMonth,
                    }
                },
                select: { driverId: true },
                distinct: ['driverId']
            })
        ]);
        const currentMonthCost = currentMonthRecords.reduce((sum, r) => sum + r.totalCost, 0);
        const lastMonthCost = lastMonthRecords.reduce((sum, r) => sum + r.totalCost, 0);
        const currentMonthLiters = currentMonthRecords.reduce((sum, r) => sum + r.quantity, 0);
        const lastMonthLiters = lastMonthRecords.reduce((sum, r) => sum + r.quantity, 0);
        const costTrend = lastMonthCost > 0 ? ((currentMonthCost - lastMonthCost) / lastMonthCost) * 100 : 0;
        const volumeTrend = lastMonthLiters > 0 ? ((currentMonthLiters - lastMonthLiters) / lastMonthLiters) * 100 : 0;
        const topVehicles = await this.getTopPerformingVehicles(3, { start: currentMonth, end: currentDate });
        const topDrivers = await this.getTopPerformingDrivers(3, { start: currentMonth, end: currentDate });
        return {
            totalFuelCost: totalStats._sum.totalCost || 0,
            totalLiters: totalStats._sum.quantity || 0,
            averageEfficiency: 28.5,
            totalVehicles: vehicleCount,
            activeDrivers: activeDrivers.length,
            monthlyBudget: 15000,
            monthlySpent: currentMonthCost,
            topPerformers: {
                vehicles: topVehicles,
                drivers: topDrivers,
            },
            recentTrends: {
                costTrend,
                efficiencyTrend: -2.1,
                volumeTrend,
            },
        };
    }
    async getTopPerformingVehicles(limit, period) {
        const vehicles = await this.prisma.vehicle.findMany({
            where: { vehicleType: 'TRUCK' },
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
            }
        });
        const vehiclePerformance = await Promise.all(vehicles.map(async (vehicle) => {
            const summary = await this.getVehicleFuelSummary(vehicle.id, period);
            const efficiency = await this.calculateFuelEfficiency(vehicle.id, period);
            return {
                id: vehicle.id,
                plateNumber: vehicle.plateNumber,
                efficiency: efficiency?.fuelEfficiency || 0,
                totalCost: summary.totalCost,
            };
        }));
        return vehiclePerformance
            .filter(v => v.totalCost > 0)
            .sort((a, b) => a.efficiency - b.efficiency)
            .slice(0, limit);
    }
    async getTopPerformingDrivers(limit, period) {
        const drivers = await this.prisma.user.findMany({
            where: { role: 'DRIVER' },
            select: {
                id: true,
                firstName: true,
                lastName: true,
            }
        });
        const driverPerformance = await Promise.all(drivers.map(async (driver) => {
            const summary = await this.getDriverFuelSummary(driver.id, period);
            return {
                id: driver.id,
                name: `${driver.firstName} ${driver.lastName}`,
                efficiency: summary.averageEfficiency,
                totalCost: summary.totalCost,
            };
        }));
        return driverPerformance
            .filter(d => d.totalCost > 0)
            .sort((a, b) => a.efficiency - b.efficiency)
            .slice(0, limit);
    }
    async getBasicStats() {
        const currentDate = new Date();
        const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
        const lastMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
        const totalRecords = await this.prisma.fuelRecord.count();
        const totalStats = await this.prisma.fuelRecord.aggregate({
            _sum: {
                totalCost: true,
                quantity: true,
            }
        });
        const currentMonthStats = await this.prisma.fuelRecord.aggregate({
            where: {
                fuelingDate: {
                    gte: currentMonth,
                    lte: currentDate,
                }
            },
            _sum: {
                totalCost: true,
            }
        });
        const lastMonthStats = await this.prisma.fuelRecord.aggregate({
            where: {
                fuelingDate: {
                    gte: lastMonth,
                    lte: lastMonthEnd,
                }
            },
            _sum: {
                totalCost: true,
            }
        });
        const averageEfficiency = 28.5;
        return {
            totalRecords,
            totalCost: totalStats._sum.totalCost || 0,
            totalLiters: totalStats._sum.quantity || 0,
            averageEfficiency,
            currentMonthCost: currentMonthStats._sum.totalCost || 0,
            lastMonthCost: lastMonthStats._sum.totalCost || 0,
        };
    }
};
exports.FuelService = FuelService;
exports.FuelService = FuelService = FuelService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FuelService);
//# sourceMappingURL=fuel.service.js.map