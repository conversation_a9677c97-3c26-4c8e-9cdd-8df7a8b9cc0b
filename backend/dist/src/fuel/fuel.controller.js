"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FuelController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuelController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const fuel_service_1 = require("./fuel.service");
const create_fuel_record_dto_1 = require("./dto/create-fuel-record.dto");
const update_fuel_record_dto_1 = require("./dto/update-fuel-record.dto");
const fuel_record_filters_dto_1 = require("./dto/fuel-record-filters.dto");
let FuelController = FuelController_1 = class FuelController {
    fuelService;
    logger = new common_1.Logger(FuelController_1.name);
    constructor(fuelService) {
        this.fuelService = fuelService;
    }
    async createFuelRecord(createFuelRecordDto, receiptFile) {
        try {
            if (receiptFile) {
                this.logger.log(`Receipt file uploaded: ${receiptFile.originalname}, size: ${receiptFile.size} bytes`);
            }
            const fuelRecord = await this.fuelService.recordFueling(createFuelRecordDto);
            return {
                success: true,
                message: 'Fuel record created successfully',
                data: fuelRecord,
            };
        }
        catch (error) {
            this.logger.error(`Failed to create fuel record: ${error.message}`);
            throw error;
        }
    }
    async getFuelRecords(filters) {
        try {
            const result = await this.fuelService.getFuelRecords(filters);
            return {
                success: true,
                message: 'Fuel records retrieved successfully',
                data: result.records,
                pagination: {
                    total: result.total,
                    page: result.page,
                    limit: result.limit,
                    totalPages: result.totalPages,
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to get fuel records: ${error.message}`);
            throw error;
        }
    }
    async getFuelRecordById(id) {
        try {
            const fuelRecord = await this.fuelService.getFuelRecordById(id);
            return {
                success: true,
                message: 'Fuel record retrieved successfully',
                data: fuelRecord,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get fuel record ${id}: ${error.message}`);
            throw error;
        }
    }
    async updateFuelRecord(id, updateFuelRecordDto, receiptFile) {
        try {
            if (receiptFile) {
                this.logger.log(`Receipt file uploaded for update: ${receiptFile.originalname}`);
            }
            const fuelRecord = await this.fuelService.updateFuelRecord(id, updateFuelRecordDto);
            return {
                success: true,
                message: 'Fuel record updated successfully',
                data: fuelRecord,
            };
        }
        catch (error) {
            this.logger.error(`Failed to update fuel record ${id}: ${error.message}`);
            throw error;
        }
    }
    async deleteFuelRecord(id) {
        try {
            await this.fuelService.deleteFuelRecord(id);
            return {
                success: true,
                message: 'Fuel record deleted successfully',
            };
        }
        catch (error) {
            this.logger.error(`Failed to delete fuel record ${id}: ${error.message}`);
            throw error;
        }
    }
    async getVehicleFuelSummary(vehicleId, startDate, endDate) {
        try {
            let period;
            if (startDate && endDate) {
                period = {
                    start: new Date(startDate),
                    end: new Date(endDate),
                };
            }
            const summary = await this.fuelService.getVehicleFuelSummary(vehicleId, period);
            return {
                success: true,
                message: 'Vehicle fuel summary retrieved successfully',
                data: summary,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get vehicle fuel summary for ${vehicleId}: ${error.message}`);
            throw error;
        }
    }
    async getVehicleFuelEfficiency(vehicleId, startDate, endDate) {
        try {
            let period;
            if (startDate && endDate) {
                period = {
                    start: new Date(startDate),
                    end: new Date(endDate),
                };
            }
            const efficiency = await this.fuelService.calculateFuelEfficiency(vehicleId, period);
            return {
                success: true,
                message: 'Vehicle fuel efficiency retrieved successfully',
                data: efficiency,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get vehicle fuel efficiency for ${vehicleId}: ${error.message}`);
            throw error;
        }
    }
    async getDriverFuelSummary(driverId, startDate, endDate) {
        try {
            let period;
            if (startDate && endDate) {
                period = {
                    start: new Date(startDate),
                    end: new Date(endDate),
                };
            }
            const summary = await this.fuelService.getDriverFuelSummary(driverId, period);
            return {
                success: true,
                message: 'Driver fuel summary retrieved successfully',
                data: summary,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get driver fuel summary for ${driverId}: ${error.message}`);
            throw error;
        }
    }
    async getFleetFuelReport(startDate, endDate) {
        try {
            let period;
            if (startDate && endDate) {
                period = {
                    start: new Date(startDate),
                    end: new Date(endDate),
                };
            }
            const report = await this.fuelService.getFleetFuelReport(period);
            return {
                success: true,
                message: 'Fleet fuel report retrieved successfully',
                data: report,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get fleet fuel report: ${error.message}`);
            throw error;
        }
    }
    async getDashboardStats() {
        try {
            const stats = await this.fuelService.getDashboardStats();
            return {
                success: true,
                message: 'Dashboard statistics retrieved successfully',
                data: stats,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get dashboard stats: ${error.message}`);
            throw error;
        }
    }
    async getBasicStats() {
        try {
            const stats = await this.fuelService.getBasicStats();
            return {
                success: true,
                message: 'Basic fuel stats retrieved successfully',
                data: stats,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get basic stats: ${error.message}`);
            throw error;
        }
    }
};
exports.FuelController = FuelController;
__decorate([
    (0, common_1.Post)('records'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('receiptFile')),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_fuel_record_dto_1.CreateFuelRecordDto, Object]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "createFuelRecord", null);
__decorate([
    (0, common_1.Get)('records'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [fuel_record_filters_dto_1.FuelRecordFiltersDto]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getFuelRecords", null);
__decorate([
    (0, common_1.Get)('records/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getFuelRecordById", null);
__decorate([
    (0, common_1.Put)('records/:id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('receiptFile')),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_fuel_record_dto_1.UpdateFuelRecordDto, Object]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "updateFuelRecord", null);
__decorate([
    (0, common_1.Delete)('records/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "deleteFuelRecord", null);
__decorate([
    (0, common_1.Get)('vehicles/:vehicleId/summary'),
    __param(0, (0, common_1.Param)('vehicleId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getVehicleFuelSummary", null);
__decorate([
    (0, common_1.Get)('vehicles/:vehicleId/efficiency'),
    __param(0, (0, common_1.Param)('vehicleId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getVehicleFuelEfficiency", null);
__decorate([
    (0, common_1.Get)('drivers/:driverId/summary'),
    __param(0, (0, common_1.Param)('driverId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getDriverFuelSummary", null);
__decorate([
    (0, common_1.Get)('fleet/report'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getFleetFuelReport", null);
__decorate([
    (0, common_1.Get)('dashboard/stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getDashboardStats", null);
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelController.prototype, "getBasicStats", null);
exports.FuelController = FuelController = FuelController_1 = __decorate([
    (0, common_1.Controller)('fuel'),
    __metadata("design:paramtypes", [fuel_service_1.FuelService])
], FuelController);
//# sourceMappingURL=fuel.controller.js.map