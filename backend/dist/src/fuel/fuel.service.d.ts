import { PrismaService } from '../prisma/prisma.service';
import { FuelRecord } from '@prisma/client';
import { CreateFuelRecordDto } from './dto/create-fuel-record.dto';
import { UpdateFuelRecordDto } from './dto/update-fuel-record.dto';
import { FuelRecordFiltersDto } from './dto/fuel-record-filters.dto';
export interface FuelRecordWithRelations extends FuelRecord {
    vehicle: {
        id: string;
        plateNumber: string;
        make: string;
        model: string;
        vehicleType: string;
    };
    driver: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
    };
}
export interface FuelEfficiencyMetrics {
    fuelEfficiency: number;
    distanceTraveled: number;
    fuelConsumed: number;
    period: {
        start: Date;
        end: Date;
    };
}
export interface VehicleFuelSummary {
    vehicleId: string;
    totalCost: number;
    totalLiters: number;
    averageEfficiency: number;
    recordCount: number;
    period: {
        start: Date;
        end: Date;
    };
    marketComparison?: {
        avgMarketPrice: number;
        avgPaidPrice: number;
        difference: number;
        percentageDifference: number;
    };
}
export declare class FuelService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    recordFueling(data: CreateFuelRecordDto): Promise<FuelRecordWithRelations>;
    getFuelRecords(filters: FuelRecordFiltersDto): Promise<{
        records: FuelRecordWithRelations[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getFuelRecordById(id: string): Promise<FuelRecordWithRelations>;
    updateFuelRecord(id: string, data: UpdateFuelRecordDto): Promise<FuelRecordWithRelations>;
    deleteFuelRecord(id: string): Promise<void>;
    private getLastFuelRecord;
    private recalculateMonthlyStats;
    calculateFuelEfficiency(vehicleId: string, period?: {
        start: Date;
        end: Date;
    }): Promise<FuelEfficiencyMetrics | null>;
    getVehicleFuelSummary(vehicleId: string, period?: {
        start: Date;
        end: Date;
    }): Promise<VehicleFuelSummary>;
    getDriverFuelSummary(driverId: string, period?: {
        start: Date;
        end: Date;
    }): Promise<any>;
    getFleetFuelReport(period?: {
        start: Date;
        end: Date;
    }): Promise<any>;
    private calculateMonthlyTrends;
    getDashboardStats(): Promise<any>;
    private getTopPerformingVehicles;
    private getTopPerformingDrivers;
    getBasicStats(): Promise<any>;
}
