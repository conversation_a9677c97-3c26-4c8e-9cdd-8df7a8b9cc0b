"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuelModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const fuel_controller_1 = require("./fuel.controller");
const fuel_price_controller_1 = require("./fuel-price.controller");
const fuel_service_1 = require("./fuel.service");
const orlen_scraper_service_1 = require("./services/orlen-scraper.service");
const system_alerts_service_1 = require("./services/system-alerts.service");
const prisma_module_1 = require("../prisma/prisma.module");
let FuelModule = class FuelModule {
};
exports.FuelModule = FuelModule;
exports.FuelModule = FuelModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            schedule_1.ScheduleModule.forRoot(),
        ],
        controllers: [
            fuel_controller_1.FuelController,
            fuel_price_controller_1.FuelPriceController,
        ],
        providers: [
            fuel_service_1.FuelService,
            orlen_scraper_service_1.OrlenScraperService,
            system_alerts_service_1.SystemAlertsService,
        ],
        exports: [
            fuel_service_1.FuelService,
            orlen_scraper_service_1.OrlenScraperService,
            system_alerts_service_1.SystemAlertsService,
        ],
    })
], FuelModule);
//# sourceMappingURL=fuel.module.js.map