import { OrlenScraperService } from './services/orlen-scraper.service';
import { SystemAlertsService, AlertFilters } from './services/system-alerts.service';
export declare class FuelPriceController {
    private readonly orlenScraperService;
    private readonly systemAlertsService;
    private readonly logger;
    constructor(orlenScraperService: OrlenScraperService, systemAlertsService: SystemAlertsService);
    getCurrentPrice(): Promise<{
        success: boolean;
        message: string;
        data: {
            priceNet: number;
            priceGross: number;
            effectiveDate: Date;
        } | null;
    }>;
    getPriceHistory(days?: string): Promise<{
        success: boolean;
        message: string;
        data: any[];
    }>;
    manualFetch(): Promise<{
        success: boolean;
        message: string;
        data: import("./services/orlen-scraper.service").ScrapingResult;
    }>;
    getScrapingStatus(): Promise<{
        success: boolean;
        message: string;
        data: {
            success: boolean;
            lastAttempt: string;
            nextRetry?: string;
            errorMessage?: string;
            retryCount: number;
        };
    }>;
    getSystemAlerts(filters: AlertFilters): Promise<{
        success: boolean;
        message: string;
        data: {
            id: string;
            createdAt: Date;
            type: import(".prisma/client").$Enums.AlertType;
            message: string;
            severity: import(".prisma/client").$Enums.AlertSeverity;
            resolved: boolean;
            resolvedAt: Date | null;
            resolvedBy: string | null;
        }[];
    }>;
    getUnresolvedAlerts(): Promise<{
        success: boolean;
        message: string;
        data: {
            id: string;
            createdAt: Date;
            type: import(".prisma/client").$Enums.AlertType;
            message: string;
            severity: import(".prisma/client").$Enums.AlertSeverity;
            resolved: boolean;
            resolvedAt: Date | null;
            resolvedBy: string | null;
        }[];
    }>;
    getAlertStats(): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            unresolved: number;
            byType: Record<string, number>;
            bySeverity: Record<string, number>;
        };
    }>;
    resolveAlert(id: string, resolvedBy: string): Promise<{
        success: boolean;
        message: string;
        data: {
            id: string;
            createdAt: Date;
            type: import(".prisma/client").$Enums.AlertType;
            message: string;
            severity: import(".prisma/client").$Enums.AlertSeverity;
            resolved: boolean;
            resolvedAt: Date | null;
            resolvedBy: string | null;
        };
    }>;
}
