import { OrlenScraperService } from './services/orlen-scraper.service';
import { SystemAlertsService, AlertFilters } from './services/system-alerts.service';
export declare class FuelPriceController {
    private readonly orlenScraperService;
    private readonly systemAlertsService;
    private readonly logger;
    constructor(orlenScraperService: OrlenScraperService, systemAlertsService: SystemAlertsService);
    getCurrentPrice(): unknown;
    getPriceHistory(days?: string): unknown;
    manualFetch(): unknown;
    getScrapingStatus(): unknown;
    getSystemAlerts(filters: AlertFilters): unknown;
    getUnresolvedAlerts(): unknown;
    getAlertStats(): unknown;
    resolveAlert(id: string, resolvedBy: string): unknown;
}
