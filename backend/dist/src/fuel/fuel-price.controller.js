"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FuelPriceController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuelPriceController = void 0;
const common_1 = require("@nestjs/common");
const orlen_scraper_service_1 = require("./services/orlen-scraper.service");
const system_alerts_service_1 = require("./services/system-alerts.service");
let FuelPriceController = FuelPriceController_1 = class FuelPriceController {
    orlenScraperService;
    systemAlertsService;
    logger = new common_1.Logger(FuelPriceController_1.name);
    constructor(orlenScraperService, systemAlertsService) {
        this.orlenScraperService = orlenScraperService;
        this.systemAlertsService = systemAlertsService;
    }
    async getCurrentPrice() {
        try {
            const price = await this.orlenScraperService.getLatestPrice();
            return {
                success: true,
                message: 'Current fuel price retrieved successfully',
                data: price,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get current price: ${error.message}`);
            throw error;
        }
    }
    async getPriceHistory(days) {
        try {
            const daysNumber = days ? parseInt(days) : 30;
            const history = await this.orlenScraperService.getPriceHistory(daysNumber);
            return {
                success: true,
                message: 'Price history retrieved successfully',
                data: history,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get price history: ${error.message}`);
            throw error;
        }
    }
    async manualFetch() {
        try {
            const result = await this.orlenScraperService.manualPriceFetch();
            return {
                success: true,
                message: 'Manual price fetch completed',
                data: result,
            };
        }
        catch (error) {
            this.logger.error(`Manual price fetch failed: ${error.message}`);
            throw error;
        }
    }
    async getScrapingStatus() {
        try {
            const status = await this.orlenScraperService.getScrapingStatus();
            return {
                success: true,
                message: 'Scraping status retrieved successfully',
                data: status,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get scraping status: ${error.message}`);
            throw error;
        }
    }
    async getSystemAlerts(filters) {
        try {
            const alerts = await this.systemAlertsService.getAlerts(filters);
            return {
                success: true,
                message: 'System alerts retrieved successfully',
                data: alerts,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get system alerts: ${error.message}`);
            throw error;
        }
    }
    async getUnresolvedAlerts() {
        try {
            const alerts = await this.systemAlertsService.getUnresolvedAlerts();
            return {
                success: true,
                message: 'Unresolved alerts retrieved successfully',
                data: alerts,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get unresolved alerts: ${error.message}`);
            throw error;
        }
    }
    async getAlertStats() {
        try {
            const stats = await this.systemAlertsService.getAlertStats();
            return {
                success: true,
                message: 'Alert statistics retrieved successfully',
                data: stats,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get alert stats: ${error.message}`);
            throw error;
        }
    }
    async resolveAlert(id, resolvedBy) {
        try {
            const alert = await this.systemAlertsService.resolveAlert(id, resolvedBy);
            return {
                success: true,
                message: 'Alert resolved successfully',
                data: alert,
            };
        }
        catch (error) {
            this.logger.error(`Failed to resolve alert ${id}: ${error.message}`);
            throw error;
        }
    }
};
exports.FuelPriceController = FuelPriceController;
__decorate([
    (0, common_1.Get)('current'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getCurrentPrice", null);
__decorate([
    (0, common_1.Get)('history'),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getPriceHistory", null);
__decorate([
    (0, common_1.Post)('manual-fetch'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "manualFetch", null);
__decorate([
    (0, common_1.Get)('status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getScrapingStatus", null);
__decorate([
    (0, common_1.Get)('alerts'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getSystemAlerts", null);
__decorate([
    (0, common_1.Get)('alerts/unresolved'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getUnresolvedAlerts", null);
__decorate([
    (0, common_1.Get)('alerts/stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "getAlertStats", null);
__decorate([
    (0, common_1.Put)('alerts/:id/resolve'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('resolvedBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FuelPriceController.prototype, "resolveAlert", null);
exports.FuelPriceController = FuelPriceController = FuelPriceController_1 = __decorate([
    (0, common_1.Controller)('fuel-prices'),
    __metadata("design:paramtypes", [orlen_scraper_service_1.OrlenScraperService,
        system_alerts_service_1.SystemAlertsService])
], FuelPriceController);
//# sourceMappingURL=fuel-price.controller.js.map