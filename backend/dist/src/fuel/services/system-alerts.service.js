"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SystemAlertsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemAlertsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let SystemAlertsService = SystemAlertsService_1 = class SystemAlertsService {
    prisma;
    logger = new common_1.Logger(SystemAlertsService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createAlert(data) {
        try {
            const alert = await this.prisma.systemAlert.create({
                data: {
                    type: data.type,
                    message: data.message,
                    severity: data.severity,
                }
            });
            this.logger.log(`🚨 System alert created: ${data.type} - ${data.severity}`);
            return alert;
        }
        catch (error) {
            this.logger.error(`Failed to create system alert: ${error.message}`);
            throw error;
        }
    }
    async getAlerts(filters = {}) {
        const where = {};
        if (filters.type) {
            where.type = filters.type;
        }
        if (filters.severity) {
            where.severity = filters.severity;
        }
        if (filters.resolved !== undefined) {
            where.resolved = filters.resolved;
        }
        return this.prisma.systemAlert.findMany({
            where,
            orderBy: { createdAt: 'desc' },
            take: filters.limit || 50,
        });
    }
    async getUnresolvedAlerts() {
        return this.getAlerts({ resolved: false });
    }
    async getHighPriorityAlerts() {
        return this.getAlerts({
            resolved: false,
            severity: 'HIGH'
        });
    }
    async resolveAlert(id, resolvedBy) {
        const alert = await this.prisma.systemAlert.update({
            where: { id },
            data: {
                resolved: true,
                resolvedAt: new Date(),
                resolvedBy,
            }
        });
        this.logger.log(`✅ System alert resolved: ${id} by ${resolvedBy}`);
        return alert;
    }
    async deleteAlert(id) {
        await this.prisma.systemAlert.delete({
            where: { id }
        });
        this.logger.log(`🗑️ System alert deleted: ${id}`);
    }
    async getAlertStats() {
        const [total, unresolved, byType, bySeverity] = await Promise.all([
            this.prisma.systemAlert.count(),
            this.prisma.systemAlert.count({ where: { resolved: false } }),
            this.prisma.systemAlert.groupBy({
                by: ['type'],
                _count: { type: true },
                where: { resolved: false }
            }),
            this.prisma.systemAlert.groupBy({
                by: ['severity'],
                _count: { severity: true },
                where: { resolved: false }
            })
        ]);
        const typeStats = {};
        byType.forEach(item => {
            typeStats[item.type] = item._count.type;
        });
        const severityStats = {};
        bySeverity.forEach(item => {
            severityStats[item.severity] = item._count.severity;
        });
        return {
            total,
            unresolved,
            byType: typeStats,
            bySeverity: severityStats,
        };
    }
};
exports.SystemAlertsService = SystemAlertsService;
exports.SystemAlertsService = SystemAlertsService = SystemAlertsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SystemAlertsService);
//# sourceMappingURL=system-alerts.service.js.map