import { PrismaService } from '../../prisma/prisma.service';
import { AlertType, AlertSeverity, SystemAlert } from '@prisma/client';
export interface CreateAlertDto {
    type: AlertType;
    message: string;
    severity: AlertSeverity;
}
export interface AlertFilters {
    type?: AlertType;
    severity?: AlertSeverity;
    resolved?: boolean;
    limit?: number;
}
export declare class SystemAlertsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    createAlert(data: CreateAlertDto): Promise<SystemAlert>;
    getAlerts(filters?: AlertFilters): Promise<SystemAlert[]>;
    getUnresolvedAlerts(): Promise<SystemAlert[]>;
    getHighPriorityAlerts(): Promise<SystemAlert[]>;
    resolveAlert(id: string, resolvedBy: string): Promise<SystemAlert>;
    deleteAlert(id: string): Promise<void>;
    getAlertStats(): Promise<{
        total: number;
        unresolved: number;
        byType: Record<string, number>;
        bySeverity: Record<string, number>;
    }>;
}
