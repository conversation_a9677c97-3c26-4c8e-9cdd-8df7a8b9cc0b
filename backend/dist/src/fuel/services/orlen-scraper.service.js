"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OrlenScraperService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrlenScraperService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const prisma_service_1 = require("../../prisma/prisma.service");
const puppeteer = require("puppeteer");
let OrlenScraperService = OrlenScraperService_1 = class OrlenScraperService {
    prisma;
    logger = new common_1.Logger(OrlenScraperService_1.name);
    MAX_RETRIES = 96;
    RETRY_INTERVAL = 15 * 60 * 1000;
    VAT_RATE = 0.23;
    ORLEN_URL = 'https://www.orlen.pl/pl/dla-biznesu/hurtowe-ceny-paliw';
    constructor(prisma) {
        this.prisma = prisma;
    }
    async scheduledPriceFetch() {
        this.logger.log('🕐 Starting scheduled fuel price fetch at 8:00 AM');
        await this.fetchDailyPricesWithRetry();
    }
    async fetchDailyPricesWithRetry(retryCount = 0) {
        try {
            const scrapingResult = await this.scrapeFuelPrice();
            if (scrapingResult.success) {
                await this.saveFuelPrice(scrapingResult);
                await this.logScrapingAttempt(scrapingResult, true, retryCount);
                this.logger.log(`✅ Fuel price scraped successfully: ${scrapingResult.priceNet}zł (net), ${scrapingResult.priceGross}zł (gross)`);
            }
            else {
                await this.handleScrapingFailure(scrapingResult, retryCount);
            }
        }
        catch (error) {
            await this.handleScrapingError(error, retryCount);
        }
    }
    async scrapeFuelPrice() {
        let browser = null;
        try {
            this.logger.log('🌐 Launching browser for Orlen price scraping');
            browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            });
            const page = await browser.newPage();
            await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
            this.logger.log(`📄 Navigating to ${this.ORLEN_URL}`);
            await page.goto(this.ORLEN_URL, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
            const effectiveDate = await this.extractEffectiveDate(page);
            const dieselPriceNet = await this.extractDieselPrice(page);
            const today = new Date();
            const isCurrentDate = this.isSameDate(effectiveDate, today);
            const priceGross = dieselPriceNet * (1 + this.VAT_RATE);
            this.logger.log(`📅 Found effective date: ${effectiveDate.toDateString()}`);
            this.logger.log(`💰 Found diesel price: ${dieselPriceNet}zł (net), ${priceGross.toFixed(2)}zł (gross)`);
            this.logger.log(`🗓️ Expected date: ${today.toDateString()} (today's date for current prices)`);
            this.logger.log(`🗓️ Date match: ${isCurrentDate ? 'YES' : 'NO'} (today: ${today.toDateString()})`);
            return {
                success: isCurrentDate,
                effectiveDate,
                priceNet: dieselPriceNet,
                priceGross,
                isCurrentDate,
                errorMessage: isCurrentDate ? undefined : `Date mismatch: found ${effectiveDate.toDateString()}, expected ${today.toDateString()} (today's date for current prices)`
            };
        }
        catch (error) {
            this.logger.error(`❌ Error during scraping: ${error.message}`);
            throw error;
        }
        finally {
            if (browser) {
                await browser.close();
                this.logger.log('🔒 Browser closed');
            }
        }
    }
    async extractEffectiveDate(page) {
        try {
            const dateText = await page.evaluate(() => {
                const elements = Array.from(document.querySelectorAll('*'));
                for (const element of elements) {
                    const text = element.textContent || '';
                    if (text.includes('obowiązujące od dnia')) {
                        return text;
                    }
                }
                return null;
            });
            if (!dateText) {
                throw new Error('Could not find effective date text on page');
            }
            this.logger.log(`📝 Found date text: "${dateText}"`);
            const dateMatch = dateText.match(/(\d{1,2})[-.](\d{1,2})[-.](\d{4})/);
            if (!dateMatch) {
                throw new Error(`Could not extract date from text: "${dateText}"`);
            }
            const [, day, month, year] = dateMatch;
            const extractedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0);
            this.logger.log(`📅 Extracted date: ${extractedDate.toDateString()}`);
            return extractedDate;
        }
        catch (error) {
            this.logger.error(`❌ Failed to extract effective date: ${error.message}`);
            throw new Error(`Failed to extract effective date: ${error.message}`);
        }
    }
    async extractDieselPrice(page) {
        try {
            const priceData = await page.evaluate(() => {
                const table = document.querySelector('table');
                if (!table) {
                    return null;
                }
                const tableText = table.innerText;
                const lines = tableText.split('\n');
                for (const line of lines) {
                    if (line.includes('Olej Napędowy Ekodiesel')) {
                        const priceMatch = line.match(/(\d+\s+\d+)$/);
                        if (priceMatch) {
                            return priceMatch[1];
                        }
                    }
                }
                for (const line of lines) {
                    if (line.toLowerCase().includes('olej napędowy') && !line.toLowerCase().includes('grzewczy')) {
                        const priceMatch = line.match(/(\d+\s+\d+)$/);
                        if (priceMatch) {
                            return priceMatch[1];
                        }
                    }
                }
                return null;
            });
            if (!priceData) {
                throw new Error('Could not find diesel price on page');
            }
            this.logger.log(`💰 Found price text: "${priceData}"`);
            const priceString = priceData.replace(/\s+/g, '');
            const pricePerM3 = parseFloat(priceString);
            if (isNaN(pricePerM3) || pricePerM3 <= 0) {
                throw new Error(`Invalid price extracted: ${priceData} -> ${pricePerM3}`);
            }
            const pricePerLiter = pricePerM3 / 1000;
            this.logger.log(`💰 Parsed price: ${pricePerM3} PLN/m³ = ${pricePerLiter.toFixed(3)} PLN/L`);
            return pricePerLiter;
        }
        catch (error) {
            this.logger.error(`❌ Failed to extract diesel price: ${error.message}`);
            throw new Error(`Failed to extract diesel price: ${error.message}`);
        }
    }
    isSameDate(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate();
    }
    async saveFuelPrice(result) {
        try {
            await this.prisma.fuelPrice.upsert({
                where: {
                    effectiveDate: result.effectiveDate
                },
                update: {
                    dieselPriceNet: result.priceNet,
                    dieselPriceGross: result.priceGross,
                    vatRate: this.VAT_RATE,
                    scrapedAt: new Date(),
                },
                create: {
                    effectiveDate: result.effectiveDate,
                    dieselPriceNet: result.priceNet,
                    dieselPriceGross: result.priceGross,
                    vatRate: this.VAT_RATE,
                    source: 'orlen.pl',
                }
            });
            this.logger.log(`💾 Fuel price saved to database`);
        }
        catch (error) {
            this.logger.error(`❌ Failed to save fuel price: ${error.message}`);
            throw error;
        }
    }
    async logScrapingAttempt(result, success, retryCount) {
        try {
            await this.prisma.fuelPriceScrapingLog.create({
                data: {
                    success,
                    effectiveDate: result.effectiveDate,
                    priceFound: result.priceNet,
                    errorMessage: result.errorMessage,
                    retryCount,
                }
            });
        }
        catch (error) {
            this.logger.error(`❌ Failed to log scraping attempt: ${error.message}`);
        }
    }
    async handleScrapingFailure(result, retryCount) {
        if (retryCount < this.MAX_RETRIES) {
            const nextRetry = new Date(Date.now() + this.RETRY_INTERVAL);
            await this.logScrapingAttempt(result, false, retryCount + 1);
            this.logger.warn(`⏰ Price date mismatch, scheduling retry ${retryCount + 1}/${this.MAX_RETRIES} at ${nextRetry.toLocaleString()}`);
            setTimeout(() => {
                this.fetchDailyPricesWithRetry(retryCount + 1);
            }, this.RETRY_INTERVAL);
        }
        else {
            await this.sendAdminAlert(`Fuel price scraping failed after 24 hours of retries. Last error: ${result.errorMessage}`);
            this.logger.error('❌ Max retries reached for fuel price scraping');
        }
    }
    async handleScrapingError(error, retryCount) {
        const errorMessage = `Scraping error: ${error.message}`;
        await this.prisma.fuelPriceScrapingLog.create({
            data: {
                success: false,
                errorMessage,
                retryCount,
            }
        });
        if (retryCount < this.MAX_RETRIES) {
            const nextRetry = new Date(Date.now() + this.RETRY_INTERVAL);
            this.logger.warn(`⏰ Scraping error, scheduling retry ${retryCount + 1}/${this.MAX_RETRIES} at ${nextRetry.toLocaleString()}`);
            setTimeout(() => {
                this.fetchDailyPricesWithRetry(retryCount + 1);
            }, this.RETRY_INTERVAL);
        }
        else {
            await this.sendAdminAlert(`Fuel price scraping failed after 24 hours of retries due to errors. Last error: ${errorMessage}`);
            this.logger.error('❌ Max retries reached for fuel price scraping due to errors');
        }
    }
    async sendAdminAlert(message) {
        try {
            this.logger.error(`🚨 ADMIN ALERT: ${message}`);
            await this.prisma.systemAlert.create({
                data: {
                    type: 'FUEL_PRICE_SCRAPING_FAILURE',
                    message,
                    severity: 'HIGH',
                }
            });
        }
        catch (error) {
            this.logger.error(`❌ Failed to send admin alert: ${error.message}`);
        }
    }
    async manualPriceFetch() {
        this.logger.log('🔧 Manual fuel price fetch triggered');
        const result = await this.scrapeFuelPrice();
        if (result.success) {
            await this.saveFuelPrice(result);
        }
        await this.logScrapingAttempt(result, result.success, 0);
        return result;
    }
    async getLatestPrice() {
        const latestPrice = await this.prisma.fuelPrice.findFirst({
            orderBy: { effectiveDate: 'desc' }
        });
        if (!latestPrice) {
            return null;
        }
        return {
            priceNet: latestPrice.dieselPriceNet,
            priceGross: latestPrice.dieselPriceGross,
            effectiveDate: latestPrice.effectiveDate,
        };
    }
    async getScrapingStatus() {
        const latestLog = await this.prisma.fuelPriceScrapingLog.findFirst({
            orderBy: { attemptDate: 'desc' }
        });
        if (!latestLog) {
            return {
                success: false,
                lastAttempt: new Date().toISOString(),
                retryCount: 0,
                errorMessage: 'No scraping attempts found'
            };
        }
        return {
            success: latestLog.success,
            lastAttempt: latestLog.attemptDate.toISOString(),
            retryCount: latestLog.retryCount,
            errorMessage: latestLog.errorMessage || undefined,
        };
    }
    async getPriceHistory(days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return this.prisma.fuelPrice.findMany({
            where: {
                effectiveDate: {
                    gte: startDate
                }
            },
            orderBy: { effectiveDate: 'desc' }
        });
    }
};
exports.OrlenScraperService = OrlenScraperService;
__decorate([
    (0, schedule_1.Cron)('0 8 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], OrlenScraperService.prototype, "scheduledPriceFetch", null);
exports.OrlenScraperService = OrlenScraperService = OrlenScraperService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrlenScraperService);
//# sourceMappingURL=orlen-scraper.service.js.map