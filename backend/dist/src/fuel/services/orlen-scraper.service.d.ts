import { PrismaService } from '../../prisma/prisma.service';
export interface ScrapingResult {
    success: boolean;
    effectiveDate: Date;
    priceNet: number;
    priceGross: number;
    isCurrentDate: boolean;
    errorMessage?: string;
}
export declare class OrlenScraperService {
    private prisma;
    private readonly logger;
    private readonly MAX_RETRIES;
    private readonly RETRY_INTERVAL;
    private readonly VAT_RATE;
    private readonly ORLEN_URL;
    constructor(prisma: PrismaService);
    scheduledPriceFetch(): Promise<void>;
    fetchDailyPricesWithRetry(retryCount?: number): Promise<void>;
    private scrapeFuelPrice;
    private extractEffectiveDate;
    private extractDieselPrice;
    private isSameDate;
    private saveFuelPrice;
    private logScrapingAttempt;
    private handleScrapingFailure;
    private handleScrapingError;
    private sendAdminAlert;
    manualPriceFetch(): Promise<ScrapingResult>;
    getLatestPrice(): Promise<{
        priceNet: number;
        priceGross: number;
        effectiveDate: Date;
    } | null>;
    getScrapingStatus(): Promise<{
        success: boolean;
        lastAttempt: string;
        nextRetry?: string;
        errorMessage?: string;
        retryCount: number;
    }>;
    getPriceHistory(days?: number): Promise<any[]>;
}
