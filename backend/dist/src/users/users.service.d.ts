import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
interface FindAllParams {
    role?: 'ADMIN' | 'MANAGER' | 'DRIVER';
    status?: 'AVAILABLE' | 'ASSIGNED' | 'INACTIVE';
}
interface CreateDriverDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
}
export declare class UsersService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(params?: FindAllParams): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>[]>;
    findAllDrivers(): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role' | 'phone' | 'licenseNumber' | 'status'>[]>;
    findDriverById(id: string): Promise<any>;
    createDriver(data: CreateDriverDto): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>>;
    getDriverAssignments(driverId: string): Promise<any[]>;
    updateDriver(id: string, updateData: any): Promise<any>;
    deleteDriver(id: string): Promise<any>;
}
export {};
