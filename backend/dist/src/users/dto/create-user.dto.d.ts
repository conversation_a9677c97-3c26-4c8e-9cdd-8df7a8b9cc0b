import { UserRole } from '@prisma/client';
export declare class CreateUserDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: UserRole;
    phone?: string;
    licenseNumber?: string;
    licenseType?: string;
    licenseExpiry?: string;
    licenseRestrictions?: string;
    address?: string;
    emergencyContactName?: string;
    emergencyContactPhone?: string;
    hireDate?: string;
    notes?: string;
    status?: string;
}
export declare class CreateDriverDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    licenseNumber?: string;
}
