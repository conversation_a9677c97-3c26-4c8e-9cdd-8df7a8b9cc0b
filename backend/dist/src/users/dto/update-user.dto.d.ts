import { CreateUserDto } from './create-user.dto';
declare const UpdateUserDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateUserDto>>;
export declare class UpdateUserDto extends UpdateUserDto_base {
    password?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
}
export declare class UpdateDriverDto {
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    licenseNumber?: string;
    licenseType?: string;
    licenseExpiry?: string;
    licenseRestrictions?: string;
    address?: string;
    emergencyContactName?: string;
    emergencyContactPhone?: string;
    hireDate?: string;
    notes?: string;
    status?: string;
}
export {};
