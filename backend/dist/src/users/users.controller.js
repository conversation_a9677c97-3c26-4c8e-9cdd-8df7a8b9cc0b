"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let UsersController = class UsersController {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    findAll(role) {
        return this.usersService.findAll({ role });
    }
    findAllDrivers() {
        return this.usersService.findAllDrivers();
    }
    async findOneDriver(id) {
        return this.usersService.findDriverById(id);
    }
    async getDriverAssignments(id) {
        return this.usersService.getDriverAssignments(id);
    }
    async createDriver(createDriverDto) {
        try {
            return await this.usersService.createDriver(createDriverDto);
        }
        catch (error) {
            if (error.message.includes('email already exists')) {
                throw new common_1.BadRequestException('A user with this email already exists');
            }
            throw new common_1.BadRequestException(`Failed to create driver: ${error.message}`);
        }
    }
    async updateDriver(id, updateDriverDto) {
        try {
            console.log(`Updating driver ${id} with data:`, updateDriverDto);
            const result = await this.usersService.updateDriver(id, updateDriverDto);
            console.log(`Driver update successful:`, result);
            return result;
        }
        catch (error) {
            console.error(`Error updating driver ${id}:`, error);
            if (error.message?.includes('not found')) {
                throw new common_1.BadRequestException('Driver not found');
            }
            if (error.message?.includes('email already exists')) {
                throw new common_1.BadRequestException('A user with this email already exists');
            }
            throw new common_1.BadRequestException(`Failed to update driver: ${error.message || 'Unknown error'}`);
        }
    }
    async deleteDriver(id) {
        try {
            return await this.usersService.deleteDriver(id);
        }
        catch (error) {
            console.error(`Error deleting driver ${id}:`, error);
            if (error.message?.includes('not found')) {
                throw new common_1.NotFoundException('Driver not found');
            }
            throw new common_1.BadRequestException(`Failed to delete driver: ${error.message || 'Unknown error'}`);
        }
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('drivers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], UsersController.prototype, "findAllDrivers", null);
__decorate([
    (0, common_1.Get)('drivers/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], UsersController.prototype, "findOneDriver", null);
__decorate([
    (0, common_1.Get)('drivers/:id/assignments'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], UsersController.prototype, "getDriverAssignments", null);
__decorate([
    (0, common_1.Post)('driver'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], UsersController.prototype, "createDriver", null);
__decorate([
    (0, common_1.Patch)('drivers/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], UsersController.prototype, "updateDriver", null);
__decorate([
    (0, common_1.Delete)('drivers/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], UsersController.prototype, "deleteDriver", null);
exports.UsersController = UsersController = __decorate([
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map