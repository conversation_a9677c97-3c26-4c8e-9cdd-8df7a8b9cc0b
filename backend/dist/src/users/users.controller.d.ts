import { UsersService } from './users.service';
import { User } from '@prisma/client';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    findAll(role?: 'ADMIN' | 'MANAGER' | 'DRIVER'): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>[]>;
    findAllDrivers(): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role' | 'phone' | 'licenseNumber' | 'status'>[]>;
    findOneDriver(id: string): Promise<any>;
    getDriverAssignments(id: string): Promise<any[]>;
    createDriver(createDriverDto: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
    }): Promise<Pick<User, 'id' | 'firstName' | 'lastName' | 'email' | 'role'>>;
    updateDriver(id: string, updateDriverDto: any): Promise<any>;
    deleteDriver(id: string): Promise<any>;
}
