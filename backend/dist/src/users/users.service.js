"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcrypt");
let UsersService = class UsersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(params) {
        const { role } = params || {};
        const where = {};
        if (role) {
            where.role = role;
        }
        return this.prisma.user.findMany({
            where,
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
            },
        });
    }
    async findAllDrivers() {
        return this.prisma.user.findMany({
            where: { role: 'DRIVER' },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
                phone: true,
                licenseNumber: true,
                status: true,
            },
        });
    }
    async findDriverById(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user || user.role !== 'DRIVER') {
            throw new Error('Driver not found');
        }
        const { passwordHash, ...driverData } = user;
        return driverData;
    }
    async createDriver(data) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: data.email },
        });
        if (existingUser) {
            throw new Error('A user with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(data.password, 10);
        const user = await this.prisma.user.create({
            data: {
                email: data.email,
                passwordHash: hashedPassword,
                firstName: data.firstName,
                lastName: data.lastName,
                role: 'DRIVER',
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
            },
        });
        return user;
    }
    async getDriverAssignments(driverId) {
        const driver = await this.prisma.user.findUnique({
            where: { id: driverId },
        });
        if (!driver) {
            throw new Error('Driver not found');
        }
        return this.prisma.vehicleAssignment.findMany({
            where: { driverId },
            include: {
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        year: true,
                        status: true,
                    },
                }
            },
            orderBy: {
                startDate: 'desc',
            },
        });
    }
    async updateDriver(id, updateData) {
        console.log('⭐ Update driver called with ID:', id);
        console.log('⭐ Update data received:', JSON.stringify(updateData, null, 2));
        const driver = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!driver) {
            throw new Error('Driver not found');
        }
        if (updateData.email && updateData.email !== driver.email) {
            const existingUser = await this.prisma.user.findUnique({
                where: { email: updateData.email },
            });
            if (existingUser) {
                throw new Error('A user with this email already exists');
            }
        }
        const { passwordHash, role, createdAt, updatedAt, ...rawUpdateData } = updateData;
        const safeUpdateData = { ...rawUpdateData };
        if (safeUpdateData.licenseExpiry) {
            try {
                safeUpdateData.licenseExpiry = new Date(safeUpdateData.licenseExpiry);
            }
            catch (error) {
                console.error('Invalid license expiry date:', safeUpdateData.licenseExpiry);
                delete safeUpdateData.licenseExpiry;
            }
        }
        if (safeUpdateData.hireDate) {
            try {
                safeUpdateData.hireDate = new Date(safeUpdateData.hireDate);
            }
            catch (error) {
                console.error('Invalid hire date:', safeUpdateData.hireDate);
                delete safeUpdateData.hireDate;
            }
        }
        console.log('⭐ Processed update data:', safeUpdateData);
        const updatedDriver = await this.prisma.user.update({
            where: { id },
            data: safeUpdateData,
        });
        const { passwordHash: _, ...driverData } = updatedDriver;
        console.log('⭐ Driver successfully updated:', driverData);
        return driverData;
    }
    async deleteDriver(id) {
        console.log(`Deleting driver with ID: ${id}`);
        const driver = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!driver || driver.role !== 'DRIVER') {
            throw new Error('Driver not found');
        }
        const activeAssignments = await this.prisma.vehicleAssignment.findMany({
            where: {
                driverId: id,
                status: 'ACTIVE',
            },
        });
        if (activeAssignments.length > 0) {
            throw new Error('Cannot delete driver with active vehicle assignments');
        }
        const deletedDriver = await this.prisma.user.delete({
            where: { id },
        });
        const { passwordHash: _, ...driverData } = deletedDriver;
        console.log(`Driver successfully deleted:`, driverData);
        return driverData;
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map