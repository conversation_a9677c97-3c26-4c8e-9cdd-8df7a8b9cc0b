"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const global_exception_filter_1 = require("./common/filters/global-exception.filter");
const security_middleware_1 = require("./common/middleware/security.middleware");
const rate_limit_guard_1 = require("./common/guards/rate-limit.guard");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
const logger_service_1 = require("./common/services/logger.service");
const redis_service_1 = require("./common/services/redis.service");
const core_2 = require("@nestjs/core");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const logger = app.get(logger_service_1.LoggerService);
    app.setGlobalPrefix('api');
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Fleet Fusion API')
        .setDescription('Comprehensive Fleet Management System API')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('auth', 'Authentication endpoints')
        .addTag('vehicles', 'Vehicle management')
        .addTag('maintenance', 'Maintenance management')
        .addTag('insurance', 'Insurance policy management')
        .addTag('reviews', 'Vehicle review management')
        .addTag('trips', 'Trip management')
        .addTag('users', 'User and driver management')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
    });
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const allowedOrigins = configService.get('ALLOWED_ORIGINS')?.split(',') || [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3002',
        'http://************:3000',
        'http://************:3001',
        'http://************:3002',
        'https://fleet-fusion.yourdomain.com'
    ];
    app.enableCors({
        origin: (origin, callback) => {
            if (!origin)
                return callback(null, true);
            if (isDevelopment) {
                if (origin.includes('localhost') ||
                    origin.includes('127.0.0.1') ||
                    origin.includes('************') ||
                    allowedOrigins.includes(origin)) {
                    return callback(null, true);
                }
            }
            else {
                if (allowedOrigins.includes(origin)) {
                    return callback(null, true);
                }
            }
            logger.logSecurityEvent('CORS_VIOLATION', { origin, isDevelopment, timestamp: new Date().toISOString() });
            return callback(new Error('CORS policy violation'), false);
        },
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID', 'X-API-Key'],
        exposedHeaders: ['X-Total-Count', 'X-Request-ID'],
        maxAge: 86400
    });
    const securityMiddleware = new security_middleware_1.SecurityMiddleware(logger);
    app.use(securityMiddleware.use.bind(securityMiddleware));
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: process.env.NODE_ENV === 'production',
        transformOptions: {
            enableImplicitConversion: true,
        },
        exceptionFactory: (errors) => {
            const messages = errors.map(error => Object.values(error.constraints || {}).join(', '));
            logger.logBusinessEvent('VALIDATION_ERROR', { errors: messages });
            return new Error(`Validation failed: ${messages.join('; ')}`);
        }
    }));
    app.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter(logger));
    const reflector = app.get(core_2.Reflector);
    const redisService = app.get(redis_service_1.RedisService);
    app.useGlobalGuards(new rate_limit_guard_1.RateLimitGuard(reflector, logger, redisService));
    app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor(logger));
    if (process.env.NODE_ENV !== 'production') {
        logger.log('API Documentation: Will be available at /api/docs (Swagger pending)');
    }
    app.getHttpAdapter().get('/api/health', (req, res) => {
        const healthcheck = {
            uptime: process.uptime(),
            message: 'Fleet Fusion API is healthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0',
            memory: process.memoryUsage(),
        };
        res.status(200).json(healthcheck);
    });
    process.on('SIGINT', async () => {
        logger.log('Received SIGINT, shutting down gracefully...');
        await app.close();
        process.exit(0);
    });
    process.on('SIGTERM', async () => {
        logger.log('Received SIGTERM, shutting down gracefully...');
        await app.close();
        process.exit(0);
    });
    const port = configService.get('app.port') || 3001;
    await app.listen(port);
    logger.log(`🚀 Fleet Fusion API started successfully`);
    logger.log(`📍 Server running on: http://localhost:${port}`);
    logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`📊 Process ID: ${process.pid}`);
    if (process.env.NODE_ENV !== 'production') {
        logger.log(`📚 API Documentation: Will be available at http://localhost:${port}/api/docs (Swagger pending)`);
        logger.log(`❤️  Health Check: http://localhost:${port}/api/health`);
    }
}
bootstrap().catch((error) => {
    console.error('❌ Failed to start the application:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map