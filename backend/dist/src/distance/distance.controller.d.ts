import { DistanceService, DistanceResult } from './distance.service';
export interface CalculateDistanceDto {
    startLatitude: number;
    startLongitude: number;
    endLatitude: number;
    endLongitude: number;
    profile?: string;
}
export declare class DistanceController {
    private readonly distanceService;
    constructor(distanceService: DistanceService);
    calculateDistance(dto: CalculateDistanceDto): Promise<DistanceResult>;
}
