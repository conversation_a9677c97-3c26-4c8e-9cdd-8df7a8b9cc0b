export interface LocationCoordinates {
    latitude: number;
    longitude: number;
}
export interface DistanceResult {
    distance: number;
    duration: number;
    durationText: string;
    success: boolean;
    error?: string;
}
export declare class DistanceService {
    private readonly ORS_API_KEY;
    private readonly ORS_BASE_URL;
    calculateRoutedDistance(start: LocationCoordinates, end: LocationCoordinates, profile?: string): Promise<DistanceResult>;
    private calculateDirectDistance;
    private toRadians;
    private formatDuration;
}
