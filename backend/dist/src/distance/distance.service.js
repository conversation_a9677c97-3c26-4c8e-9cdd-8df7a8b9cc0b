"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistanceService = void 0;
const common_1 = require("@nestjs/common");
let DistanceService = class DistanceService {
    ORS_API_KEY = process.env.OPENROUTESERVICE_API_KEY;
    ORS_BASE_URL = 'https://api.openrouteservice.org/v2';
    async calculateRoutedDistance(start, end, profile = 'driving-car') {
        if (!this.ORS_API_KEY) {
            throw new common_1.HttpException('OpenRouteService API key not configured', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
        try {
            console.log('🛣️ Backend: Calculating routed distance');
            console.log('📍 Start:', start);
            console.log('📍 End:', end);
            console.log('🚗 Profile:', profile);
            const coordinates = [[start.longitude, start.latitude], [end.longitude, end.latitude]];
            const requestBody = {
                coordinates: coordinates,
                instructions: false,
                geometry: true,
                elevation: false
            };
            if (profile === 'driving-hgv') {
                requestBody['options'] = {
                    vehicle_type: 'hgv',
                    avoid_features: []
                };
            }
            console.log('📦 Request body:', JSON.stringify(requestBody, null, 2));
            const response = await fetch(`${this.ORS_BASE_URL}/directions/${profile}/json`, {
                method: 'POST',
                headers: {
                    'Authorization': this.ORS_API_KEY,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            console.log('📡 Response status:', response.status);
            if (!response.ok) {
                const errorText = await response.text();
                console.error('🛣️ OpenRouteService API error:', errorText);
                throw new common_1.HttpException(`OpenRouteService API error: ${response.status} - ${errorText}`, common_1.HttpStatus.BAD_REQUEST);
            }
            const data = await response.json();
            console.log('📊 Response data keys:', Object.keys(data));
            if (!data.routes || data.routes.length === 0) {
                throw new common_1.HttpException('No routes found in OpenRouteService response', common_1.HttpStatus.BAD_REQUEST);
            }
            const route = data.routes[0];
            const summary = route.summary;
            const distanceKm = summary.distance / 1000;
            const durationHours = summary.duration / 3600;
            console.log('🛣️ Route summary:', {
                distance: distanceKm,
                duration: durationHours
            });
            return {
                distance: Math.round(distanceKm * 10) / 10,
                duration: Math.round(durationHours * 10) / 10,
                durationText: this.formatDuration(durationHours),
                success: true
            };
        }
        catch (error) {
            console.error('🛣️ Distance calculation error:', error);
            console.log('🔄 Falling back to direct distance calculation');
            return this.calculateDirectDistance(start, end);
        }
    }
    calculateDirectDistance(start, end) {
        console.log('📏 Using direct distance calculation (Haversine)');
        const R = 6371;
        const dLat = this.toRadians(end.latitude - start.latitude);
        const dLon = this.toRadians(end.longitude - start.longitude);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(start.latitude)) * Math.cos(this.toRadians(end.latitude)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        const duration = distance / 60;
        console.log('📏 Direct distance result:', {
            distance: Math.round(distance * 10) / 10,
            duration: Math.round(duration * 10) / 10
        });
        return {
            distance: Math.round(distance * 10) / 10,
            duration: Math.round(duration * 10) / 10,
            durationText: this.formatDuration(duration),
            success: true
        };
    }
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    formatDuration(durationHours) {
        const totalMinutes = Math.round(durationHours * 60);
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        if (hours === 0) {
            return `${minutes} minutes`;
        }
        else if (minutes === 0) {
            return `${hours} hour${hours > 1 ? 's' : ''}`;
        }
        else {
            return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minutes`;
        }
    }
};
exports.DistanceService = DistanceService;
exports.DistanceService = DistanceService = __decorate([
    (0, common_1.Injectable)()
], DistanceService);
//# sourceMappingURL=distance.service.js.map