"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function main() {
    try {
        const assignments = await prisma.vehicleAssignment.findMany({
            include: {
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                vehicle: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                    },
                },
            },
        });
        console.log('Total assignments found:', assignments.length);
        if (assignments.length > 0) {
            console.log('Sample assignment:', JSON.stringify(assignments[0], null, 2));
        }
        else {
            console.log('No assignments found, creating a test assignment...');
            const vehicles = await prisma.vehicle.findMany();
            const drivers = await prisma.user.findMany({
                where: {
                    role: 'DRIVER',
                },
            });
            console.log('Available vehicles:', vehicles.length);
            console.log('Available drivers:', drivers.length);
            if (vehicles.length > 0 && drivers.length > 0) {
                const newAssignment = await prisma.vehicleAssignment.create({
                    data: {
                        vehicleId: vehicles[0].id,
                        driverId: drivers[0].id,
                        startDate: new Date(),
                        status: 'ACTIVE',
                    },
                    include: {
                        driver: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                        vehicle: {
                            select: {
                                id: true,
                                plateNumber: true,
                                make: true,
                                model: true,
                            },
                        },
                    },
                });
                console.log('Created new assignment:', JSON.stringify(newAssignment, null, 2));
            }
        }
    }
    catch (error) {
        console.error('Error checking assignments:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
main();
//# sourceMappingURL=check-assignments.js.map