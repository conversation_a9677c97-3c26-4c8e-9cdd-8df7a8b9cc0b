"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcrypt");
const prisma = new client_1.PrismaClient();
async function seedAdmin() {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    try {
        const admin = await prisma.user.upsert({
            where: { email: '<EMAIL>' },
            update: {},
            create: {
                email: '<EMAIL>',
                firstName: 'Admin',
                lastName: 'User',
                passwordHash: hashedPassword,
                role: 'ADMIN',
            },
        });
        console.log('Admin user seeded:', admin);
    }
    catch (error) {
        console.error('Error seeding admin user:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
seedAdmin();
//# sourceMappingURL=seed-admin.js.map