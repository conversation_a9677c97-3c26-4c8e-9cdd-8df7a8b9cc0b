{"version": 3, "file": "check-assignments.js", "sourceRoot": "", "sources": ["../../../src/scripts/check-assignments.ts"], "names": [], "mappings": ";;AAAA,2CAA8C;AAG9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC1D,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;qBACf;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;qBACZ;iBACF;aACF;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;QAE5D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YAGnE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACjD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACzC,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACpD,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAElD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAE9C,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBAC1D,IAAI,EAAE;wBACJ,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;wBACzB,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;wBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,MAAM,EAAE,QAAQ;qBACjB;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;6BACf;yBACF;wBACD,OAAO,EAAE;4BACP,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,WAAW,EAAE,IAAI;gCACjB,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;YAAS,CAAC;QACT,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,IAAI,EAAE,CAAC"}