import { BusinessPartnersService, CreateBusinessPartnerDto, UpdateBusinessPartnerDto, CreatePartnerLocationDto, UpdatePartnerLocationDto } from './business-partners.service';
import { BusinessPartner, PartnerLocation, PartnerType, PartnerStatus } from '@prisma/client';
export declare class BusinessPartnersController {
    private readonly businessPartnersService;
    constructor(businessPartnersService: BusinessPartnersService);
    findAllPartners(type?: PartnerType, status?: PartnerStatus, search?: string): Promise<BusinessPartner[]>;
    findShippers(): Promise<BusinessPartner[]>;
    findLogisticsPartners(): Promise<BusinessPartner[]>;
    findPartnerById(id: string): Promise<BusinessPartner>;
    createPartner(createPartnerDto: CreateBusinessPartnerDto): Promise<BusinessPartner>;
    updatePartner(id: string, updatePartnerDto: UpdateBusinessPartnerDto): Promise<BusinessPartner>;
    deletePartner(id: string): Promise<BusinessPartner>;
    findLocationsByPartner(partnerId: string): Promise<PartnerLocation[]>;
    findLocationById(id: string): Promise<PartnerLocation>;
    createLocation(partnerId: string, createLocationDto: Omit<CreatePartnerLocationDto, 'partnerId'>): Promise<PartnerLocation>;
    updateLocation(id: string, updateLocationDto: UpdatePartnerLocationDto): Promise<PartnerLocation>;
    deleteLocation(id: string): Promise<PartnerLocation>;
    findAllPickupLocations(partnerId?: string): Promise<PartnerLocation[]>;
    findAllDeliveryLocations(partnerId?: string): Promise<PartnerLocation[]>;
}
