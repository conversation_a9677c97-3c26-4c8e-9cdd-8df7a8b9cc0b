"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessPartnersModule = void 0;
const common_1 = require("@nestjs/common");
const business_partners_controller_1 = require("./business-partners.controller");
const business_partners_service_1 = require("./business-partners.service");
const prisma_module_1 = require("../prisma/prisma.module");
let BusinessPartnersModule = class BusinessPartnersModule {
};
exports.BusinessPartnersModule = BusinessPartnersModule;
exports.BusinessPartnersModule = BusinessPartnersModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [business_partners_controller_1.BusinessPartnersController],
        providers: [business_partners_service_1.BusinessPartnersService],
        exports: [business_partners_service_1.BusinessPartnersService],
    })
], BusinessPartnersModule);
//# sourceMappingURL=business-partners.module.js.map