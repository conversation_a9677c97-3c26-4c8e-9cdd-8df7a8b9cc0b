import { PrismaService } from '../prisma/prisma.service';
import { BusinessPartner, PartnerLocation, PartnerType, PartnerStatus, LocationType } from '@prisma/client';
export interface CreateBusinessPartnerDto {
    name: string;
    type: PartnerType;
    status?: PartnerStatus;
    contactPerson?: string;
    email?: string;
    phone?: string;
    website?: string;
    taxId?: string;
    notes?: string;
}
export interface UpdateBusinessPartnerDto {
    name?: string;
    type?: PartnerType;
    status?: PartnerStatus;
    contactPerson?: string;
    email?: string;
    phone?: string;
    website?: string;
    taxId?: string;
    notes?: string;
}
export interface CreatePartnerLocationDto {
    partnerId: string;
    name: string;
    type: LocationType;
    address: string;
    city: string;
    state?: string;
    postalCode?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
    contactPerson?: string;
    phone?: string;
    email?: string;
    operatingHours?: string;
    specialInstructions?: string;
    isActive?: boolean;
    isDefault?: boolean;
}
export interface UpdatePartnerLocationDto {
    name?: string;
    type?: LocationType;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
    contactPerson?: string;
    phone?: string;
    email?: string;
    operatingHours?: string;
    specialInstructions?: string;
    isActive?: boolean;
    isDefault?: boolean;
}
export declare class BusinessPartnersService {
    private prisma;
    constructor(prisma: PrismaService);
    findAllPartners(filters?: {
        type?: PartnerType;
        status?: PartnerStatus;
        search?: string;
    }): Promise<BusinessPartner[]>;
    findPartnerById(id: string): Promise<BusinessPartner>;
    createPartner(data: CreateBusinessPartnerDto): Promise<BusinessPartner>;
    updatePartner(id: string, data: UpdateBusinessPartnerDto): Promise<BusinessPartner>;
    deletePartner(id: string): Promise<BusinessPartner>;
    findLocationsByPartner(partnerId: string): Promise<PartnerLocation[]>;
    findLocationById(id: string): Promise<PartnerLocation>;
    createLocation(data: CreatePartnerLocationDto): Promise<PartnerLocation>;
    updateLocation(id: string, data: UpdatePartnerLocationDto): Promise<PartnerLocation>;
    deleteLocation(id: string): Promise<PartnerLocation>;
    findPickupLocations(partnerId?: string): Promise<PartnerLocation[]>;
    findDeliveryLocations(partnerId?: string): Promise<PartnerLocation[]>;
}
