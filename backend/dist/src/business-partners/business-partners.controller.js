"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessPartnersController = void 0;
const common_1 = require("@nestjs/common");
const business_partners_service_1 = require("./business-partners.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const client_1 = require("@prisma/client");
let BusinessPartnersController = class BusinessPartnersController {
    businessPartnersService;
    constructor(businessPartnersService) {
        this.businessPartnersService = businessPartnersService;
    }
    async findAllPartners(type, status, search) {
        return this.businessPartnersService.findAllPartners({
            type,
            status,
            search
        });
    }
    async findShippers() {
        return this.businessPartnersService.findAllPartners({
            type: client_1.PartnerType.SHIPPER,
            status: client_1.PartnerStatus.ACTIVE
        });
    }
    async findLogisticsPartners() {
        return this.businessPartnersService.findAllPartners({
            type: client_1.PartnerType.LOGISTICS_PARTNER,
            status: client_1.PartnerStatus.ACTIVE
        });
    }
    async findPartnerById(id) {
        return this.businessPartnersService.findPartnerById(id);
    }
    async createPartner(createPartnerDto) {
        return this.businessPartnersService.createPartner(createPartnerDto);
    }
    async updatePartner(id, updatePartnerDto) {
        return this.businessPartnersService.updatePartner(id, updatePartnerDto);
    }
    async deletePartner(id) {
        return this.businessPartnersService.deletePartner(id);
    }
    async findLocationsByPartner(partnerId) {
        return this.businessPartnersService.findLocationsByPartner(partnerId);
    }
    async findLocationById(id) {
        return this.businessPartnersService.findLocationById(id);
    }
    async createLocation(partnerId, createLocationDto) {
        return this.businessPartnersService.createLocation({
            ...createLocationDto,
            partnerId
        });
    }
    async updateLocation(id, updateLocationDto) {
        return this.businessPartnersService.updateLocation(id, updateLocationDto);
    }
    async deleteLocation(id) {
        return this.businessPartnersService.deleteLocation(id);
    }
    async findAllPickupLocations(partnerId) {
        return this.businessPartnersService.findPickupLocations(partnerId);
    }
    async findAllDeliveryLocations(partnerId) {
        return this.businessPartnersService.findDeliveryLocations(partnerId);
    }
};
exports.BusinessPartnersController = BusinessPartnersController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('type')),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findAllPartners", null);
__decorate([
    (0, common_1.Get)('shippers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findShippers", null);
__decorate([
    (0, common_1.Get)('logistics-partners'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findLogisticsPartners", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findPartnerById", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "createPartner", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "updatePartner", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "deletePartner", null);
__decorate([
    (0, common_1.Get)(':partnerId/locations'),
    __param(0, (0, common_1.Param)('partnerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findLocationsByPartner", null);
__decorate([
    (0, common_1.Get)('locations/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findLocationById", null);
__decorate([
    (0, common_1.Post)(':partnerId/locations'),
    __param(0, (0, common_1.Param)('partnerId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "createLocation", null);
__decorate([
    (0, common_1.Put)('locations/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "updateLocation", null);
__decorate([
    (0, common_1.Delete)('locations/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "deleteLocation", null);
__decorate([
    (0, common_1.Get)('locations/pickup/all'),
    __param(0, (0, common_1.Query)('partnerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findAllPickupLocations", null);
__decorate([
    (0, common_1.Get)('locations/delivery/all'),
    __param(0, (0, common_1.Query)('partnerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BusinessPartnersController.prototype, "findAllDeliveryLocations", null);
exports.BusinessPartnersController = BusinessPartnersController = __decorate([
    (0, common_1.Controller)('business-partners'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [business_partners_service_1.BusinessPartnersService])
], BusinessPartnersController);
//# sourceMappingURL=business-partners.controller.js.map