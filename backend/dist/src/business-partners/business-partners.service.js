"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessPartnersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let BusinessPartnersService = class BusinessPartnersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAllPartners(filters) {
        const where = {};
        if (filters?.type) {
            where.type = filters.type;
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        if (filters?.search) {
            where.OR = [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { contactPerson: { contains: filters.search, mode: 'insensitive' } },
                { email: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        return this.prisma.businessPartner.findMany({
            where,
            include: {
                locations: {
                    where: { isActive: true },
                    orderBy: [
                        { isDefault: 'desc' },
                        { name: 'asc' }
                    ]
                },
                _count: {
                    select: {
                        tripPickups: true,
                        tripDeliveries: true,
                    }
                }
            },
            orderBy: [
                { status: 'asc' },
                { name: 'asc' }
            ]
        });
    }
    async findPartnerById(id) {
        const partner = await this.prisma.businessPartner.findUnique({
            where: { id },
            include: {
                locations: {
                    orderBy: [
                        { isDefault: 'desc' },
                        { name: 'asc' }
                    ]
                },
                tripPickups: {
                    take: 10,
                    orderBy: { startTime: 'desc' },
                    include: {
                        driver: { select: { firstName: true, lastName: true } },
                        vehicle: { select: { plateNumber: true, make: true, model: true } }
                    }
                },
                tripDeliveries: {
                    take: 10,
                    orderBy: { startTime: 'desc' },
                    include: {
                        driver: { select: { firstName: true, lastName: true } },
                        vehicle: { select: { plateNumber: true, make: true, model: true } }
                    }
                }
            }
        });
        if (!partner) {
            throw new common_1.NotFoundException('Business partner not found');
        }
        return partner;
    }
    async createPartner(data) {
        try {
            return await this.prisma.businessPartner.create({
                data: {
                    ...data,
                    status: data.status || client_1.PartnerStatus.ACTIVE,
                },
                include: {
                    locations: true
                }
            });
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to create business partner: ' + error.message);
        }
    }
    async updatePartner(id, data) {
        try {
            return await this.prisma.businessPartner.update({
                where: { id },
                data,
                include: {
                    locations: true
                }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException('Business partner not found');
            }
            throw new common_1.BadRequestException('Failed to update business partner: ' + error.message);
        }
    }
    async deletePartner(id) {
        try {
            return await this.prisma.businessPartner.delete({
                where: { id }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException('Business partner not found');
            }
            throw new common_1.BadRequestException('Failed to delete business partner: ' + error.message);
        }
    }
    async findLocationsByPartner(partnerId) {
        return this.prisma.partnerLocation.findMany({
            where: { partnerId },
            orderBy: [
                { isDefault: 'desc' },
                { name: 'asc' }
            ]
        });
    }
    async findLocationById(id) {
        const location = await this.prisma.partnerLocation.findUnique({
            where: { id },
            include: {
                partner: true
            }
        });
        if (!location) {
            throw new common_1.NotFoundException('Partner location not found');
        }
        return location;
    }
    async createLocation(data) {
        try {
            if (data.isDefault) {
                await this.prisma.partnerLocation.updateMany({
                    where: {
                        partnerId: data.partnerId,
                        type: data.type
                    },
                    data: { isDefault: false }
                });
            }
            return await this.prisma.partnerLocation.create({
                data: {
                    ...data,
                    country: data.country || 'Poland',
                    isActive: data.isActive !== undefined ? data.isActive : true,
                    isDefault: data.isDefault || false,
                },
                include: {
                    partner: true
                }
            });
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to create partner location: ' + error.message);
        }
    }
    async updateLocation(id, data) {
        try {
            if (data.isDefault) {
                const location = await this.prisma.partnerLocation.findUnique({
                    where: { id }
                });
                if (location) {
                    await this.prisma.partnerLocation.updateMany({
                        where: {
                            partnerId: location.partnerId,
                            type: data.type || location.type,
                            id: { not: id }
                        },
                        data: { isDefault: false }
                    });
                }
            }
            return await this.prisma.partnerLocation.update({
                where: { id },
                data,
                include: {
                    partner: true
                }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException('Partner location not found');
            }
            throw new common_1.BadRequestException('Failed to update partner location: ' + error.message);
        }
    }
    async deleteLocation(id) {
        try {
            return await this.prisma.partnerLocation.delete({
                where: { id }
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException('Partner location not found');
            }
            throw new common_1.BadRequestException('Failed to delete partner location: ' + error.message);
        }
    }
    async findPickupLocations(partnerId) {
        const where = {
            isActive: true,
            type: { in: ['PICKUP_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER'] }
        };
        if (partnerId) {
            where.partnerId = partnerId;
        }
        return this.prisma.partnerLocation.findMany({
            where,
            include: {
                partner: { select: { name: true, type: true } }
            },
            orderBy: [
                { isDefault: 'desc' },
                { partner: { name: 'asc' } },
                { name: 'asc' }
            ]
        });
    }
    async findDeliveryLocations(partnerId) {
        const where = {
            isActive: true,
            type: { in: ['DELIVERY_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER'] }
        };
        if (partnerId) {
            where.partnerId = partnerId;
        }
        return this.prisma.partnerLocation.findMany({
            where,
            include: {
                partner: { select: { name: true, type: true } }
            },
            orderBy: [
                { isDefault: 'desc' },
                { partner: { name: 'asc' } },
                { name: 'asc' }
            ]
        });
    }
};
exports.BusinessPartnersService = BusinessPartnersService;
exports.BusinessPartnersService = BusinessPartnersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], BusinessPartnersService);
//# sourceMappingURL=business-partners.service.js.map