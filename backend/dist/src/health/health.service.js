"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const redis_service_1 = require("../common/services/redis.service");
const realtime_gateway_1 = require("../common/gateways/realtime.gateway");
let HealthService = class HealthService {
    prisma;
    redisService;
    realtimeGateway;
    startTime = Date.now();
    queryCount = 0;
    cacheHits = 0;
    cacheMisses = 0;
    constructor(prisma, redisService, realtimeGateway) {
        this.prisma = prisma;
        this.redisService = redisService;
        this.realtimeGateway = realtimeGateway;
    }
    async getHealthStatus() {
        const [databaseHealth, redisHealth, websocketHealth] = await Promise.all([
            this.checkDatabaseHealth(),
            this.checkRedisHealth(),
            this.checkWebSocketHealth(),
        ]);
        const overallStatus = this.determineOverallStatus([
            databaseHealth.status,
            redisHealth.status,
            websocketHealth.status,
        ]);
        return {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: Date.now() - this.startTime,
            version: process.env.npm_package_version || '1.0.0',
            services: {
                database: databaseHealth,
                redis: redisHealth,
                websocket: websocketHealth,
            },
        };
    }
    async getDetailedHealthStatus() {
        const basicHealth = await this.getHealthStatus();
        const metrics = await this.getMetrics();
        return {
            ...basicHealth,
            metrics,
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                pid: process.pid,
            },
        };
    }
    async getMetrics() {
        const memoryUsage = process.memoryUsage();
        return {
            memory: {
                used: memoryUsage.heapUsed,
                total: memoryUsage.heapTotal,
                percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
            },
            cpu: {
                usage: process.cpuUsage().user / 1000000,
            },
            database: {
                connections: await this.getDatabaseConnections(),
                queries: this.queryCount,
            },
            websocket: {
                connectedUsers: this.realtimeGateway.getConnectedUsersCount(),
            },
            cache: {
                hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) * 100 || 0,
                missRate: this.cacheMisses / (this.cacheHits + this.cacheMisses) * 100 || 0,
            },
        };
    }
    async checkDatabaseHealth() {
        try {
            const start = Date.now();
            await this.prisma.$queryRaw `SELECT 1`;
            const responseTime = Date.now() - start;
            return {
                status: 'healthy',
                responseTime,
                details: {
                    message: 'Database connection successful',
                },
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                details: {
                    error: error.message,
                },
            };
        }
    }
    async checkRedisHealth() {
        try {
            const start = Date.now();
            const health = await this.redisService.healthCheck();
            const responseTime = Date.now() - start;
            return {
                status: health.connected ? 'healthy' : 'unhealthy',
                responseTime,
                details: health,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                details: {
                    error: error.message,
                },
            };
        }
    }
    async checkWebSocketHealth() {
        try {
            const connectedUsers = this.realtimeGateway.getConnectedUsersCount();
            return {
                status: 'healthy',
                details: {
                    connectedUsers,
                    message: 'WebSocket gateway operational',
                },
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                details: {
                    error: error.message,
                },
            };
        }
    }
    async getDatabaseConnections() {
        try {
            return 1;
        }
        catch (error) {
            return 0;
        }
    }
    determineOverallStatus(statuses) {
        const unhealthyCount = statuses.filter(status => status === 'unhealthy').length;
        if (unhealthyCount === 0) {
            return 'healthy';
        }
        else if (unhealthyCount < statuses.length) {
            return 'degraded';
        }
        else {
            return 'unhealthy';
        }
    }
    incrementQueryCount() {
        this.queryCount++;
    }
    incrementCacheHit() {
        this.cacheHits++;
    }
    incrementCacheMiss() {
        this.cacheMisses++;
    }
    resetMetrics() {
        this.queryCount = 0;
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        redis_service_1.RedisService,
        realtime_gateway_1.RealtimeGateway])
], HealthService);
//# sourceMappingURL=health.service.js.map