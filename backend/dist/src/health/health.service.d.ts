import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../common/services/redis.service';
import { RealtimeGateway } from '../common/gateways/realtime.gateway';
interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    uptime: number;
    version: string;
    services: {
        database: ServiceHealth;
        redis: ServiceHealth;
        websocket: ServiceHealth;
    };
}
interface ServiceHealth {
    status: 'healthy' | 'unhealthy';
    responseTime?: number;
    details?: any;
}
interface ApplicationMetrics {
    memory: {
        used: number;
        total: number;
        percentage: number;
    };
    cpu: {
        usage: number;
    };
    database: {
        connections: number;
        queries: number;
    };
    websocket: {
        connectedUsers: number;
    };
    cache: {
        hitRate: number;
        missRate: number;
    };
}
export declare class HealthService {
    private readonly prisma;
    private readonly redisService;
    private readonly realtimeGateway;
    private startTime;
    private queryCount;
    private cacheHits;
    private cacheMisses;
    constructor(prisma: PrismaService, redisService: RedisService, realtimeGateway: RealtimeGateway);
    getHealthStatus(): Promise<HealthStatus>;
    getDetailedHealthStatus(): Promise<any>;
    getMetrics(): Promise<ApplicationMetrics>;
    private checkDatabaseHealth;
    private checkRedisHealth;
    private checkWebSocketHealth;
    private getDatabaseConnections;
    private determineOverallStatus;
    incrementQueryCount(): void;
    incrementCacheHit(): void;
    incrementCacheMiss(): void;
    resetMetrics(): void;
}
export {};
