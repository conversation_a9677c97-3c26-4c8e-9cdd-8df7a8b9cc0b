"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const optimistic_locking_service_1 = require("../common/services/optimistic-locking.service");
const realtime_gateway_1 = require("../common/gateways/realtime.gateway");
const redis_service_1 = require("../common/services/redis.service");
let TripsService = class TripsService {
    prisma;
    optimisticLocking;
    realtimeGateway;
    redisService;
    constructor(prisma, optimisticLocking, realtimeGateway, redisService) {
        this.prisma = prisma;
        this.optimisticLocking = optimisticLocking;
        this.realtimeGateway = realtimeGateway;
        this.redisService = redisService;
    }
    getTripIncludes() {
        return {
            driver: true,
            vehicle: {
                include: {
                    truckAssignments: {
                        where: {
                            status: 'ACTIVE',
                            endDate: null,
                        },
                        include: {
                            trailer: {
                                select: {
                                    id: true,
                                    plateNumber: true,
                                    make: true,
                                    model: true,
                                    trailerType: true,
                                    cargoCapacity: true,
                                    maxWeight: true,
                                }
                            }
                        }
                    }
                }
            },
            trailer: true,
            assignment: true,
            stops: {
                orderBy: {
                    sequence: 'asc',
                },
            },
            expenses: true,
            pickupPartner: {
                select: {
                    id: true,
                    name: true,
                    type: true,
                    contactPerson: true,
                    phone: true,
                    email: true,
                }
            },
            deliveryPartner: {
                select: {
                    id: true,
                    name: true,
                    type: true,
                    contactPerson: true,
                    phone: true,
                    email: true,
                }
            },
            pickupLocation: {
                select: {
                    id: true,
                    name: true,
                    address: true,
                    city: true,
                    state: true,
                    contactPerson: true,
                    phone: true,
                    operatingHours: true,
                    specialInstructions: true,
                }
            },
            deliveryLocation: {
                select: {
                    id: true,
                    name: true,
                    address: true,
                    city: true,
                    state: true,
                    contactPerson: true,
                    phone: true,
                    operatingHours: true,
                    specialInstructions: true,
                }
            },
        };
    }
    async findAll() {
        const cacheKey = 'trips:all';
        const cached = await this.redisService.get(cacheKey);
        if (cached) {
            return cached;
        }
        const trips = await this.prisma.trip.findMany({
            include: this.getTripIncludes(),
            orderBy: {
                startTime: 'desc',
            },
        });
        await this.redisService.set(cacheKey, trips, 300);
        return trips;
    }
    async findByDriver(driverId) {
        return this.prisma.trip.findMany({
            where: { driverId },
            include: this.getTripIncludes(),
            orderBy: {
                startTime: 'desc',
            },
        });
    }
    async findByVehicle(vehicleId) {
        return this.prisma.trip.findMany({
            where: { vehicleId },
            include: this.getTripIncludes(),
            orderBy: {
                startTime: 'desc',
            },
        });
    }
    async findOne(id) {
        const trip = await this.prisma.trip.findUnique({
            where: { id },
            include: this.getTripIncludes(),
        });
        if (!trip) {
            throw new common_1.BadRequestException('Trip not found');
        }
        return trip;
    }
    async findByTrailer(trailerId) {
        return this.prisma.trip.findMany({
            where: { trailerId },
            include: this.getTripIncludes(),
            orderBy: {
                startTime: 'desc',
            },
        });
    }
    async findActiveTruckTrailerPairs() {
        return this.prisma.truckTrailerAssignment.findMany({
            where: {
                status: 'ACTIVE',
                endDate: null,
            },
            include: {
                truck: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        status: true,
                        vehicleType: true,
                        engineType: true,
                        fuelCapacity: true,
                    }
                },
                trailer: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        status: true,
                        vehicleType: true,
                        trailerType: true,
                        cargoCapacity: true,
                        maxWeight: true,
                    }
                }
            }
        });
    }
    async findAvailableTruckTrailerPairs() {
        const activePairs = await this.prisma.truckTrailerAssignment.findMany({
            where: {
                status: 'ACTIVE',
                endDate: null,
                truck: {
                    status: 'AVAILABLE',
                },
                trailer: {
                    status: 'AVAILABLE',
                },
                AND: [
                    {
                        truck: {
                            trips: {
                                none: {
                                    status: {
                                        in: ['SCHEDULED', 'IN_PROGRESS']
                                    }
                                }
                            }
                        }
                    },
                    {
                        trailer: {
                            trailerTrips: {
                                none: {
                                    status: {
                                        in: ['SCHEDULED', 'IN_PROGRESS']
                                    }
                                }
                            }
                        }
                    }
                ]
            },
            include: {
                truck: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        status: true,
                        vehicleType: true,
                        engineType: true,
                        fuelCapacity: true,
                    }
                },
                trailer: {
                    select: {
                        id: true,
                        plateNumber: true,
                        make: true,
                        model: true,
                        status: true,
                        vehicleType: true,
                        trailerType: true,
                        cargoCapacity: true,
                        maxWeight: true,
                    }
                }
            }
        });
        return activePairs;
    }
    async checkTimeConflicts(vehicleId, trailerId, driverId, startTime, endTime) {
        const start = new Date(startTime);
        const end = endTime ? new Date(endTime) : null;
        const conflicts = [];
        const vehicleConflicts = await this.prisma.trip.findMany({
            where: {
                vehicleId,
                status: {
                    in: ['SCHEDULED', 'IN_PROGRESS']
                },
                OR: [
                    {
                        AND: [
                            { startTime: { lte: start } },
                            end ? { endTime: { gte: start } } : { endTime: null }
                        ]
                    },
                    end ? {
                        AND: [
                            { startTime: { lte: end } },
                            { endTime: { gte: end } }
                        ]
                    } : {},
                    end ? {
                        AND: [
                            { startTime: { gte: start } },
                            { endTime: { lte: end } }
                        ]
                    } : {},
                    {
                        AND: [
                            { startTime: { lte: start } },
                            end ? { endTime: { gte: end } } : { endTime: null }
                        ]
                    }
                ]
            },
            include: {
                vehicle: { select: { plateNumber: true } }
            }
        });
        if (vehicleConflicts.length > 0) {
            conflicts.push(...vehicleConflicts.map(trip => ({
                type: 'vehicle',
                vehicle: trip.vehicle.plateNumber,
                tripId: trip.id,
                startTime: trip.startTime,
                endTime: trip.endTime,
                status: trip.status
            })));
        }
        if (trailerId) {
            const trailerConflicts = await this.prisma.trip.findMany({
                where: {
                    trailerId,
                    status: {
                        in: ['SCHEDULED', 'IN_PROGRESS']
                    },
                    OR: [
                        {
                            AND: [
                                { startTime: { lte: start } },
                                end ? { endTime: { gte: start } } : { endTime: null }
                            ]
                        },
                        end ? {
                            AND: [
                                { startTime: { lte: end } },
                                { endTime: { gte: end } }
                            ]
                        } : {},
                        end ? {
                            AND: [
                                { startTime: { gte: start } },
                                { endTime: { lte: end } }
                            ]
                        } : {},
                        {
                            AND: [
                                { startTime: { lte: start } },
                                end ? { endTime: { gte: end } } : { endTime: null }
                            ]
                        }
                    ]
                },
                include: {
                    trailer: { select: { plateNumber: true } }
                }
            });
            if (trailerConflicts.length > 0) {
                conflicts.push(...trailerConflicts.map(trip => ({
                    type: 'trailer',
                    trailer: trip.trailer?.plateNumber,
                    tripId: trip.id,
                    startTime: trip.startTime,
                    endTime: trip.endTime,
                    status: trip.status
                })));
            }
        }
        const driverConflicts = await this.prisma.trip.findMany({
            where: {
                driverId,
                status: {
                    in: ['SCHEDULED', 'IN_PROGRESS']
                },
                OR: [
                    {
                        AND: [
                            { startTime: { lte: start } },
                            end ? { endTime: { gte: start } } : { endTime: null }
                        ]
                    },
                    end ? {
                        AND: [
                            { startTime: { lte: end } },
                            { endTime: { gte: end } }
                        ]
                    } : {},
                    end ? {
                        AND: [
                            { startTime: { gte: start } },
                            { endTime: { lte: end } }
                        ]
                    } : {},
                    {
                        AND: [
                            { startTime: { lte: start } },
                            end ? { endTime: { gte: end } } : { endTime: null }
                        ]
                    }
                ]
            },
            include: {
                driver: { select: { firstName: true, lastName: true } }
            }
        });
        if (driverConflicts.length > 0) {
            conflicts.push(...driverConflicts.map(trip => ({
                type: 'driver',
                driver: `${trip.driver.firstName} ${trip.driver.lastName}`,
                tripId: trip.id,
                startTime: trip.startTime,
                endTime: trip.endTime,
                status: trip.status
            })));
        }
        return {
            hasConflicts: conflicts.length > 0,
            conflicts
        };
    }
    async create(data) {
        if (data.truckTrailerAssignmentId) {
            const assignment = await this.prisma.truckTrailerAssignment.findUnique({
                where: { id: data.truckTrailerAssignmentId },
                include: {
                    truck: true,
                    trailer: true,
                }
            });
            if (!assignment) {
                throw new common_1.BadRequestException('Truck-trailer assignment not found');
            }
            if (assignment.status !== 'ACTIVE') {
                throw new common_1.BadRequestException('Truck-trailer assignment is not active');
            }
            if (data.vehicleId !== assignment.truckId) {
                throw new common_1.BadRequestException('Vehicle ID must match the truck in the assignment');
            }
            data.trailerId = assignment.trailerId;
        }
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: data.vehicleId },
        });
        if (!vehicle) {
            throw new common_1.BadRequestException('Vehicle not found');
        }
        if (vehicle.status !== 'AVAILABLE' && vehicle.status !== 'ASSIGNED') {
            throw new common_1.BadRequestException('Vehicle is not available');
        }
        if (data.trailerId) {
            const trailer = await this.prisma.vehicle.findUnique({
                where: { id: data.trailerId },
            });
            if (!trailer) {
                throw new common_1.BadRequestException('Trailer not found');
            }
            if (trailer.vehicleType !== 'TRAILER') {
                throw new common_1.BadRequestException('Specified trailer ID is not a trailer');
            }
            if (trailer.status !== 'AVAILABLE' && trailer.status !== 'ASSIGNED') {
                throw new common_1.BadRequestException('Trailer is not available');
            }
        }
        const driver = await this.prisma.user.findUnique({
            where: { id: data.driverId },
        });
        if (!driver) {
            throw new common_1.BadRequestException('Driver not found');
        }
        const conflictCheck = await this.checkTimeConflicts(data.vehicleId, data.trailerId, data.driverId, data.startTime, data.endTime);
        let conflictWarnings = [];
        if (conflictCheck.hasConflicts) {
            conflictWarnings = conflictCheck.conflicts.map(conflict => {
                const timeRange = `${new Date(conflict.startTime).toLocaleString()} - ${conflict.endTime ? new Date(conflict.endTime).toLocaleString() : 'Open-ended'}`;
                switch (conflict.type) {
                    case 'vehicle':
                        return `Vehicle ${conflict.vehicle} has a conflicting trip (${conflict.status}) from ${timeRange}`;
                    case 'trailer':
                        return `Trailer ${conflict.trailer} has a conflicting trip (${conflict.status}) from ${timeRange}`;
                    case 'driver':
                        return `Driver ${conflict.driver} has a conflicting trip (${conflict.status}) from ${timeRange}`;
                    default:
                        return `Conflict detected from ${timeRange}`;
                }
            });
            console.log('⚠️ Time conflicts detected but allowing trip creation:', conflictWarnings);
        }
        try {
            const trip = await this.prisma.trip.create({
                data: {
                    driverId: data.driverId,
                    vehicleId: data.vehicleId,
                    trailerId: data.trailerId,
                    assignmentId: data.assignmentId,
                    type: data.type,
                    priority: data.priority,
                    startLocation: data.startLocation,
                    endLocation: data.endLocation,
                    startTime: data.startTime,
                    endTime: data.endTime,
                    estimatedDuration: data.estimatedDuration,
                    distance: data.distance,
                    notes: data.notes,
                    purpose: data.purpose,
                    cargo: data.cargo,
                    cargoWeight: data.cargoWeight,
                    status: client_1.TripStatus.SCHEDULED,
                    pickupPartnerId: data.pickupPartnerId,
                    deliveryPartnerId: data.deliveryPartnerId,
                    pickupLocationId: data.pickupLocationId,
                    deliveryLocationId: data.deliveryLocationId,
                },
            });
            const createdTrip = await this.findOne(trip.id);
            const result = {
                ...createdTrip,
            };
            if (conflictWarnings.length > 0) {
                result.conflictWarnings = conflictWarnings;
            }
            return result;
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to create trip: ' + error.message);
        }
    }
    async update(id, data, userId) {
        try {
            let updatedTrip;
            if (data.version) {
                const { version, ...updateData } = data;
                updatedTrip = await this.optimisticLocking.updateTrip(id, updateData, version, userId);
            }
            else {
                updatedTrip = await this.prisma.trip.update({
                    where: { id },
                    data,
                    include: this.getTripIncludes(),
                });
            }
            await this.invalidateTripCaches(id);
            this.realtimeGateway.broadcastTripUpdate(id, {
                action: 'updated',
                trip: updatedTrip,
                updatedBy: userId,
            });
            return updatedTrip;
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to update trip: ' + error.message);
        }
    }
    async updateStatus(id, status, version, userId) {
        try {
            let updatedTrip;
            if (version) {
                updatedTrip = await this.optimisticLocking.updateTrip(id, { status }, version, userId);
            }
            else {
                updatedTrip = await this.prisma.trip.update({
                    where: { id },
                    data: { status },
                    include: this.getTripIncludes(),
                });
            }
            await this.invalidateTripCaches(id);
            this.realtimeGateway.broadcastTripUpdate(id, {
                action: 'status_updated',
                status,
                trip: updatedTrip,
                updatedBy: userId,
            });
            return updatedTrip;
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to update trip status: ' + error.message);
        }
    }
    async delete(id, userId) {
        const deletedTrip = await this.prisma.trip.delete({
            where: { id },
            include: this.getTripIncludes(),
        });
        await this.invalidateTripCaches(id);
        this.realtimeGateway.broadcastTripUpdate(id, {
            action: 'deleted',
            deletedBy: userId,
        });
        return deletedTrip;
    }
    async invalidateTripCaches(tripId) {
        try {
            await this.redisService.delPattern('trips:*');
            if (tripId) {
                await this.redisService.del(`trip:${tripId}`);
            }
        }
        catch (error) {
            console.warn('Failed to invalidate trip caches:', error);
        }
    }
};
exports.TripsService = TripsService;
exports.TripsService = TripsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        optimistic_locking_service_1.OptimisticLockingService,
        realtime_gateway_1.RealtimeGateway,
        redis_service_1.RedisService])
], TripsService);
//# sourceMappingURL=trips.service.js.map