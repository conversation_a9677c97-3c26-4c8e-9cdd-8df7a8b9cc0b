import { TripType, TripPriority, TripStatus } from '@prisma/client';
export declare class UpdateTripDto {
    driverId?: string;
    vehicleId?: string;
    trailerId?: string;
    assignmentId?: string;
    type?: TripType;
    priority?: TripPriority;
    startLocation?: string;
    endLocation?: string;
    startTime?: string;
    endTime?: string;
    estimatedDuration?: number;
    distance?: number;
    notes?: string;
    purpose?: string;
    cargo?: string;
    cargoWeight?: number;
    status?: TripStatus;
    pickupPartnerId?: string;
    deliveryPartnerId?: string;
    pickupLocationId?: string;
    deliveryLocationId?: string;
}
