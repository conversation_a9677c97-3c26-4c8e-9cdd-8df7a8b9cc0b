"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTripDto = void 0;
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class UpdateTripDto {
    driverId;
    vehicleId;
    trailerId;
    assignmentId;
    type;
    priority;
    startLocation;
    endLocation;
    startTime;
    endTime;
    estimatedDuration;
    distance;
    notes;
    purpose;
    cargo;
    cargoWeight;
    status;
    pickupPartnerId;
    deliveryPartnerId;
    pickupLocationId;
    deliveryLocationId;
}
exports.UpdateTripDto = UpdateTripDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Driver ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "driverId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Vehicle ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Trailer ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "trailerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Assignment ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "assignmentId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.TripType, { message: 'Trip type must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.TripPriority, { message: 'Trip priority must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200, { message: 'Start location must not exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "startLocation", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200, { message: 'End location must not exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "endLocation", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Start time must be a valid date string' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'End time must be a valid date string' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Estimated duration must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Estimated duration cannot be negative' }),
    __metadata("design:type", Number)
], UpdateTripDto.prototype, "estimatedDuration", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Distance must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Distance cannot be negative' }),
    __metadata("design:type", Number)
], UpdateTripDto.prototype, "distance", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 1000, { message: 'Notes cannot exceed 1000 characters' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 200, { message: 'Purpose cannot exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "purpose", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 200, { message: 'Cargo description cannot exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "cargo", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Cargo weight must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Cargo weight cannot be negative' }),
    __metadata("design:type", Number)
], UpdateTripDto.prototype, "cargoWeight", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.TripStatus, { message: 'Status must be a valid trip status' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Pickup partner ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "pickupPartnerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Delivery partner ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "deliveryPartnerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Pickup location ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "pickupLocationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Delivery location ID must be valid' }),
    __metadata("design:type", String)
], UpdateTripDto.prototype, "deliveryLocationId", void 0);
//# sourceMappingURL=update-trip.dto.js.map