{"version": 3, "file": "create-trip.dto.js", "sourceRoot": "", "sources": ["../../../../src/trips/dto/create-trip.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,qDAAoG;AACpG,2CAAwD;AAExD,MAAa,aAAa;IAGxB,QAAQ,CAAS;IAIjB,SAAS,CAAS;IAKlB,SAAS,CAAU;IAKnB,wBAAwB,CAAU;IAKlC,YAAY,CAAU;IAGtB,IAAI,CAAW;IAGf,QAAQ,CAAe;IAIvB,aAAa,CAAS;IAItB,WAAW,CAAS;IAGpB,SAAS,CAAS;IAIlB,OAAO,CAAU;IAKjB,iBAAiB,CAAU;IAK3B,QAAQ,CAAU;IAKlB,KAAK,CAAU;IAKf,OAAO,CAAU;IAKjB,KAAK,CAAU;IAKf,WAAW,CAAU;IAMrB,eAAe,CAAU;IAKzB,iBAAiB,CAAU;IAK3B,gBAAgB,CAAU;IAK1B,kBAAkB,CAAU;CAC7B;AA/FD,sCA+FC;AA5FC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;+CACnC;AAIjB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;gDACnC;AAKlB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;gDACpC;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;;+DACtC;AAKlC;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;;mDACpC;AAGtB;IADC,IAAA,wBAAM,EAAC,iBAAQ,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;kDACnD,iBAAQ,oBAAR,iBAAQ;2CAAC;AAGf;IADC,IAAA,wBAAM,EAAC,qBAAY,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;kDACvD,qBAAY,oBAAZ,qBAAY;+CAAC;AAIvB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,+DAA+D,EAAE,CAAC;;oDACvE;AAItB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,6DAA6D,EAAE,CAAC;;kDACvE;AAGpB;IADC,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;;gDACtD;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;8CACrD;AAKjB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;IACtE,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;;wDAClC;AAK3B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;;+CACjC;AAKlB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;;4CACrD;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;8CACnD;AAKjB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,gDAAgD,EAAE,CAAC;;4CAC/D;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;;kDAClC;AAMrB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;;sDACrC;AAKzB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;wDACrC;AAK3B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;;uDACrC;AAK1B;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;;yDACrC"}