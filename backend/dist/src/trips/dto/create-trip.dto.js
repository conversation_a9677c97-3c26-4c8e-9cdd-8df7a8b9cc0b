"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTripDto = void 0;
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateTripDto {
    driverId;
    vehicleId;
    trailerId;
    truckTrailerAssignmentId;
    assignmentId;
    type;
    priority;
    startLocation;
    endLocation;
    startTime;
    endTime;
    estimatedDuration;
    distance;
    notes;
    purpose;
    cargo;
    cargoWeight;
    pickupPartnerId;
    deliveryPartnerId;
    pickupLocationId;
    deliveryLocationId;
}
exports.CreateTripDto = CreateTripDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Driver ID is required' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "driverId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Vehicle ID is required' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Trailer ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "trailerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Truck-trailer assignment ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "truckTrailerAssignmentId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Assignment ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "assignmentId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(client_1.TripType, { message: 'Trip type must be valid' }),
    __metadata("design:type", typeof (_a = typeof client_1.TripType !== "undefined" && client_1.TripType) === "function" ? _a : Object)
], CreateTripDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(client_1.TripPriority, { message: 'Trip priority must be valid' }),
    __metadata("design:type", typeof (_b = typeof client_1.TripPriority !== "undefined" && client_1.TripPriority) === "function" ? _b : Object)
], CreateTripDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200, { message: 'Start location is required and must not exceed 200 characters' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "startLocation", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200, { message: 'End location is required and must not exceed 200 characters' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "endLocation", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'Start time must be a valid date string' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'End time must be a valid date string' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Estimated duration must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Estimated duration cannot be negative' }),
    __metadata("design:type", Number)
], CreateTripDto.prototype, "estimatedDuration", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Distance must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Distance cannot be negative' }),
    __metadata("design:type", Number)
], CreateTripDto.prototype, "distance", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 1000, { message: 'Notes cannot exceed 1000 characters' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 200, { message: 'Purpose cannot exceed 200 characters' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "purpose", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 200, { message: 'Cargo description cannot exceed 200 characters' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "cargo", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Cargo weight must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Cargo weight cannot be negative' }),
    __metadata("design:type", Number)
], CreateTripDto.prototype, "cargoWeight", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Pickup partner ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "pickupPartnerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Delivery partner ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "deliveryPartnerId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Pickup location ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "pickupLocationId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 50, { message: 'Delivery location ID must be valid' }),
    __metadata("design:type", String)
], CreateTripDto.prototype, "deliveryLocationId", void 0);
//# sourceMappingURL=create-trip.dto.js.map