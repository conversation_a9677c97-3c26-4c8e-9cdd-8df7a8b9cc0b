import { TripType, TripPriority } from '@prisma/client';
export declare class CreateTripDto {
    driverId: string;
    vehicleId: string;
    trailerId?: string;
    truckTrailerAssignmentId?: string;
    assignmentId?: string;
    type: TripType;
    priority: TripPriority;
    startLocation: string;
    endLocation: string;
    startTime: string;
    endTime?: string;
    estimatedDuration?: number;
    distance?: number;
    notes?: string;
    purpose?: string;
    cargo?: string;
    cargoWeight?: number;
    pickupPartnerId?: string;
    deliveryPartnerId?: string;
    pickupLocationId?: string;
    deliveryLocationId?: string;
}
