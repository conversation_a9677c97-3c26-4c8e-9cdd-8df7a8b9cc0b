import { TripsService } from './trips.service';
import { CreateTripDto } from './dto/create-trip.dto';
import { UpdateTripDto } from './dto/update-trip.dto';
import { Trip, TripStatus, User } from '@prisma/client';
interface RequestWithUser extends Request {
    user: User;
}
export declare class TripsController {
    private readonly tripsService;
    constructor(tripsService: TripsService);
    findAll(req: RequestWithUser): Promise<Trip[]>;
    findActiveTruckTrailerPairs(): Promise<any[]>;
    findAvailableTruckTrailerPairs(): Promise<any[]>;
    findByVehicle(vehicleId: string): Promise<Trip[]>;
    findByTrailer(trailerId: string): Promise<Trip[]>;
    findOne(id: string): Promise<Trip>;
    create(createTripDto: CreateTripDto): Promise<Trip>;
    update(id: string, updateTripDto: UpdateTripDto): Promise<Trip>;
    updateStatus(id: string, status: TripStatus): Promise<Trip>;
    remove(id: string): Promise<Trip>;
}
export {};
