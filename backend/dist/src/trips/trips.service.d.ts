import { PrismaService } from '../prisma/prisma.service';
import { Trip, TripStatus, TripType, TripPriority, Prisma } from '@prisma/client';
import { OptimisticLockingService } from '../common/services/optimistic-locking.service';
import { RealtimeGateway } from '../common/gateways/realtime.gateway';
import { RedisService } from '../common/services/redis.service';
interface CreateTripInput {
    driverId: string;
    vehicleId: string;
    trailerId?: string;
    truckTrailerAssignmentId?: string;
    assignmentId?: string;
    type: TripType;
    priority: TripPriority;
    startLocation: string;
    endLocation: string;
    startTime: Date | string;
    endTime?: Date | string;
    estimatedDuration?: number;
    distance?: number;
    notes?: string;
    purpose?: string;
    cargo?: string;
    cargoWeight?: number;
    pickupPartnerId?: string;
    deliveryPartnerId?: string;
    pickupLocationId?: string;
    deliveryLocationId?: string;
}
export declare class TripsService {
    private prisma;
    private optimisticLocking;
    private realtimeGateway;
    private redisService;
    constructor(prisma: PrismaService, optimisticLocking: OptimisticLockingService, realtimeGateway: RealtimeGateway, redisService: RedisService);
    private getTripIncludes;
    findAll(): Promise<Trip[]>;
    findByDriver(driverId: string): Promise<Trip[]>;
    findByVehicle(vehicleId: string): Promise<Trip[]>;
    findOne(id: string): Promise<Trip>;
    findByTrailer(trailerId: string): Promise<Trip[]>;
    findActiveTruckTrailerPairs(): Promise<any[]>;
    findAvailableTruckTrailerPairs(): Promise<any[]>;
    private checkTimeConflicts;
    create(data: CreateTripInput): Promise<Trip & {
        conflictWarnings?: string[];
    }>;
    update(id: string, data: Prisma.TripUpdateInput & {
        version?: number;
    }, userId?: string): Promise<Trip>;
    updateStatus(id: string, status: TripStatus, version?: number, userId?: string): Promise<Trip>;
    delete(id: string, userId?: string): Promise<Trip>;
    private invalidateTripCaches;
}
export {};
