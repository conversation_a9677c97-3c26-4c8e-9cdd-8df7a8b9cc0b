"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripsController = void 0;
const common_1 = require("@nestjs/common");
const trips_service_1 = require("./trips.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_trip_dto_1 = require("./dto/create-trip.dto");
const update_trip_dto_1 = require("./dto/update-trip.dto");
const client_1 = require("@prisma/client");
let TripsController = class TripsController {
    tripsService;
    constructor(tripsService) {
        this.tripsService = tripsService;
    }
    async findAll(req) {
        if (req.user.role === 'DRIVER') {
            return this.tripsService.findByDriver(req.user.id);
        }
        return this.tripsService.findAll();
    }
    findActiveTruckTrailerPairs() {
        return this.tripsService.findActiveTruckTrailerPairs();
    }
    findAvailableTruckTrailerPairs() {
        return this.tripsService.findAvailableTruckTrailerPairs();
    }
    findByVehicle(vehicleId) {
        return this.tripsService.findByVehicle(vehicleId);
    }
    findByTrailer(trailerId) {
        return this.tripsService.findByTrailer(trailerId);
    }
    findOne(id) {
        return this.tripsService.findOne(id);
    }
    async create(createTripDto) {
        console.log('Creating trip with data:', JSON.stringify(createTripDto, null, 2));
        const formattedData = {
            ...createTripDto,
            startTime: new Date(createTripDto.startTime).toISOString(),
            endTime: createTripDto.endTime ? new Date(createTripDto.endTime).toISOString() : undefined,
        };
        return this.tripsService.create(formattedData);
    }
    update(id, updateTripDto) {
        const formattedData = { ...updateTripDto };
        if (updateTripDto.startTime) {
            formattedData.startTime = new Date(updateTripDto.startTime).toISOString();
        }
        if (updateTripDto.endTime) {
            formattedData.endTime = new Date(updateTripDto.endTime).toISOString();
        }
        return this.tripsService.update(id, formattedData);
    }
    updateStatus(id, status) {
        return this.tripsService.updateStatus(id, status);
    }
    remove(id) {
        return this.tripsService.delete(id);
    }
};
exports.TripsController = TripsController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_a = typeof Promise !== "undefined" && Promise) === "function" ? _a : Object)
], TripsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('truck-trailer-pairs/active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], TripsController.prototype, "findActiveTruckTrailerPairs", null);
__decorate([
    (0, common_1.Get)('truck-trailer-pairs/available'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], TripsController.prototype, "findAvailableTruckTrailerPairs", null);
__decorate([
    (0, common_1.Get)('vehicle/:vehicleId'),
    __param(0, (0, common_1.Param)('vehicleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_d = typeof Promise !== "undefined" && Promise) === "function" ? _d : Object)
], TripsController.prototype, "findByVehicle", null);
__decorate([
    (0, common_1.Get)('trailer/:trailerId'),
    __param(0, (0, common_1.Param)('trailerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], TripsController.prototype, "findByTrailer", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_f = typeof Promise !== "undefined" && Promise) === "function" ? _f : Object)
], TripsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_trip_dto_1.CreateTripDto]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], TripsController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_trip_dto_1.UpdateTripDto]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], TripsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_j = typeof client_1.TripStatus !== "undefined" && client_1.TripStatus) === "function" ? _j : Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], TripsController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], TripsController.prototype, "remove", null);
exports.TripsController = TripsController = __decorate([
    (0, common_1.Controller)('trips'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [trips_service_1.TripsService])
], TripsController);
//# sourceMappingURL=trips.controller.js.map