"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateMaintenanceDto = void 0;
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class UpdateMaintenanceDto {
    vehicleId;
    type;
    category;
    description;
    status;
    date;
    scheduledDate;
    mileage;
    partsCost;
    laborCost;
    technician;
    notes;
    nextMaintenanceDate;
    nextMaintenanceMileage;
}
exports.UpdateMaintenanceDto = UpdateMaintenanceDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100, { message: 'Vehicle ID must not exceed 100 characters' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MaintenanceType, { message: 'Type must be a valid maintenance type' }),
    __metadata("design:type", typeof (_a = typeof client_1.MaintenanceType !== "undefined" && client_1.MaintenanceType) === "function" ? _a : Object)
], UpdateMaintenanceDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MaintenanceCategory, { message: 'Category must be a valid maintenance category' }),
    __metadata("design:type", typeof (_b = typeof client_1.MaintenanceCategory !== "undefined" && client_1.MaintenanceCategory) === "function" ? _b : Object)
], UpdateMaintenanceDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500, { message: 'Description must not exceed 500 characters' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.MaintenanceStatus, { message: 'Status must be a valid maintenance status' }),
    __metadata("design:type", typeof (_c = typeof client_1.MaintenanceStatus !== "undefined" && client_1.MaintenanceStatus) === "function" ? _c : Object)
], UpdateMaintenanceDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Scheduled date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "scheduledDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Mileage must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Mileage must be non-negative' }),
    __metadata("design:type", Number)
], UpdateMaintenanceDto.prototype, "mileage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }, { message: 'Parts cost must be a valid decimal with up to 2 decimal places' }),
    (0, class_validator_1.Min)(0, { message: 'Parts cost must be non-negative' }),
    __metadata("design:type", Number)
], UpdateMaintenanceDto.prototype, "partsCost", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }, { message: 'Labor cost must be a valid decimal with up to 2 decimal places' }),
    (0, class_validator_1.Min)(0, { message: 'Labor cost must be non-negative' }),
    __metadata("design:type", Number)
], UpdateMaintenanceDto.prototype, "laborCost", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200, { message: 'Technician name must not exceed 200 characters' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "technician", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000, { message: 'Notes must not exceed 1000 characters' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Next maintenance date must be a valid ISO date string' }),
    __metadata("design:type", String)
], UpdateMaintenanceDto.prototype, "nextMaintenanceDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Next maintenance mileage must be a valid number' }),
    (0, class_validator_1.Min)(0, { message: 'Next maintenance mileage must be non-negative' }),
    __metadata("design:type", Number)
], UpdateMaintenanceDto.prototype, "nextMaintenanceMileage", void 0);
//# sourceMappingURL=update-maintenance.dto.js.map