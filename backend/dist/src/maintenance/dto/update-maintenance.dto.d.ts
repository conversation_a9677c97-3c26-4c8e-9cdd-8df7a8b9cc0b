import { MaintenanceType, MaintenanceCategory, MaintenanceStatus } from '@prisma/client';
export declare class UpdateMaintenanceDto {
    vehicleId?: string;
    type?: MaintenanceType;
    category?: MaintenanceCategory;
    description?: string;
    status?: MaintenanceStatus;
    date?: string;
    scheduledDate?: string;
    mileage?: number;
    partsCost?: number;
    laborCost?: number;
    technician?: string;
    notes?: string;
    nextMaintenanceDate?: string;
    nextMaintenanceMileage?: number;
}
