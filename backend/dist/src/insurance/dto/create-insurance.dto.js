"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInsuranceDto = void 0;
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateInsuranceDto {
    vehicleId;
    policyNumber;
    provider;
    type;
    startDate;
    endDate;
    premium;
    coverage;
    deductible;
    status = client_1.PolicyStatus.ACTIVE;
    notes;
}
exports.CreateInsuranceDto = CreateInsuranceDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100, { message: 'Vehicle ID must not exceed 100 characters' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "vehicleId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200, { message: 'Policy number must not exceed 200 characters' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "policyNumber", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200, { message: 'Provider name must not exceed 200 characters' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "provider", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(client_1.InsuranceType, { message: 'Type must be a valid insurance type' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'Start date must be a valid ISO date string' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'End date must be a valid ISO date string' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }, { message: 'Premium must be a valid decimal with up to 2 decimal places' }),
    (0, class_validator_1.Min)(0, { message: 'Premium must be non-negative' }),
    __metadata("design:type", Number)
], CreateInsuranceDto.prototype, "premium", void 0);
__decorate([
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }, { message: 'Coverage must be a valid decimal with up to 2 decimal places' }),
    (0, class_validator_1.Min)(0, { message: 'Coverage must be non-negative' }),
    __metadata("design:type", Number)
], CreateInsuranceDto.prototype, "coverage", void 0);
__decorate([
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }, { message: 'Deductible must be a valid decimal with up to 2 decimal places' }),
    (0, class_validator_1.Min)(0, { message: 'Deductible must be non-negative' }),
    __metadata("design:type", Number)
], CreateInsuranceDto.prototype, "deductible", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.PolicyStatus, { message: 'Status must be a valid policy status' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000, { message: 'Notes must not exceed 1000 characters' }),
    __metadata("design:type", String)
], CreateInsuranceDto.prototype, "notes", void 0);
//# sourceMappingURL=create-insurance.dto.js.map