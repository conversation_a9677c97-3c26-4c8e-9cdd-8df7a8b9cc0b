export declare const CACHE_KEY = "cache";
export interface CacheOptions {
    key?: string;
    ttl?: number;
    keyGenerator?: (...args: any[]) => string;
}
export declare const Cache: (options?: CacheOptions) => any;
export declare const CacheEvict: (patterns: string[]) => any;
export declare function generateCacheKey(className: string, methodName: string, args: any[], customGenerator?: (...args: any[]) => string): string;
