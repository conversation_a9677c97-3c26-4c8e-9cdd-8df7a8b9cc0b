"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheEvict = exports.Cache = exports.CACHE_KEY = void 0;
exports.generateCacheKey = generateCacheKey;
const common_1 = require("@nestjs/common");
exports.CACHE_KEY = 'cache';
const Cache = (options = {}) => {
    return (0, common_1.SetMetadata)(exports.CACHE_KEY, options);
};
exports.Cache = Cache;
const CacheEvict = (patterns) => {
    return (0, common_1.SetMetadata)('cache-evict', patterns);
};
exports.CacheEvict = CacheEvict;
function generateCacheKey(className, methodName, args, customGenerator) {
    if (customGenerator) {
        return customGenerator(...args);
    }
    const argsKey = args.length > 0 ? `:${JSON.stringify(args)}` : '';
    return `${className}:${methodName}${argsKey}`;
}
//# sourceMappingURL=cache.decorator.js.map