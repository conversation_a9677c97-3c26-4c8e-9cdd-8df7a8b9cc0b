{"version": 3, "file": "security.middleware.js", "sourceRoot": "", "sources": ["../../../../src/common/middleware/security.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,+DAA2D;AAGpD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IACA;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAEjD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAG7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAG5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAE1B,IAAI,EAAE,CAAC;IACT,CAAC;IAEO,kBAAkB,CAAC,GAAa;QAEtC,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAGjC,GAAG,CAAC,SAAS,CACX,yBAAyB,EACzB,sBAAsB;YACtB,mDAAmD;YACnD,oCAAoC;YACpC,+BAA+B;YAC/B,sBAAsB;YACtB,mBAAmB;YACnB,qBAAqB;YACrB,oBAAoB;YACpB,mBAAmB,CACpB,CAAC;QAGF,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAGnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QAGzC,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QAGnD,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;QAGpE,GAAG,CAAC,SAAS,CACX,oBAAoB,EACpB,+GAA+G,CAChH,CAAC;QAGF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,GAAG,CAAC,SAAS,CACX,2BAA2B,EAC3B,8CAA8C,CAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,GAAY;QACpC,MAAM,kBAAkB,GAAG;YACzB,SAAS;YACT,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,gBAAgB;YAChB,eAAe;YACf,yBAAyB;SAC1B,CAAC;QAEF,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAClD,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QACpB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC;QAGlD,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;YACzC,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;oBACrD,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;oBAC3B,GAAG;oBACH,MAAM;oBACN,SAAS;iBACV,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;gBAClB,MAAM;YACR,CAAC;QACH,CAAC;QAGD,MAAM,oBAAoB,GAAG;YAC3B,MAAM;YACN,UAAU;YACV,SAAS;YACT,UAAU;YACV,SAAS;YACT,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;YACZ,WAAW;SACZ,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,EAAE;oBACpD,SAAS;oBACT,GAAG;oBACH,MAAM;iBACP,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;gBAClB,MAAM;YACR,CAAC;QACH,CAAC;QAGD,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;gBACnD,GAAG;gBACH,MAAM;gBACN,SAAS;aACV,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QACpB,CAAC;QAGD,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;gBACnD,GAAG;gBACH,MAAM;gBACN,SAAS;aACV,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,GAAY;QAElC,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;QACrE,MAAM,cAAc,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QAExC,IAAI,aAAa,GAAG,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE;gBACjD,aAAa;gBACb,cAAc;gBACd,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,EAAE,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACpD,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAI,WAAW,GAAG,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;gBAChD,WAAW;gBACX,UAAU;gBACV,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,EAAE,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC;QAGD,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QACpB,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;gBAClD,GAAG;gBACH,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,EAAE,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;CACF,CAAA;AA1KY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,kBAAkB,CA0K9B"}