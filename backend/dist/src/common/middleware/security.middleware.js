"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityMiddleware = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../services/logger.service");
let SecurityMiddleware = class SecurityMiddleware {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    use(req, res, next) {
        this.setSecurityHeaders(res);
        this.logSecurityEvents(req);
        this.validateRequest(req);
        next();
    }
    setSecurityHeaders(res) {
        res.removeHeader('X-Powered-By');
        res.setHeader('Content-Security-Policy', "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self'; " +
            "font-src 'self'; " +
            "object-src 'none'; " +
            "media-src 'self'; " +
            "frame-src 'none';");
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        res.setHeader('Permissions-Policy', 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()');
        if (process.env.NODE_ENV === 'production') {
            res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
    }
    logSecurityEvents(req) {
        const suspiciousPatterns = [
            /[<>'"]/g,
            /union.*select/i,
            /script.*src/i,
            /javascript:/i,
            /data:.*base64/i,
            /\.\.\/.*\.\./g,
            /(wget|curl|nmap|nikto)/i,
        ];
        const userAgent = req.headers['user-agent'] || '';
        const url = req.url;
        const method = req.method;
        const ip = req.ip || req.connection.remoteAddress;
        for (const pattern of suspiciousPatterns) {
            if (pattern.test(url)) {
                this.logger.logSecurityEvent('suspicious_url_pattern', {
                    pattern: pattern.toString(),
                    url,
                    method,
                    userAgent,
                }, undefined, ip);
                break;
            }
        }
        const suspiciousUserAgents = [
            /bot/i,
            /crawler/i,
            /spider/i,
            /scanner/i,
            /sqlmap/i,
            /nmap/i,
            /nikto/i,
            /dirb/i,
            /dirbuster/i,
            /gobuster/i,
        ];
        for (const pattern of suspiciousUserAgents) {
            if (pattern.test(userAgent)) {
                this.logger.logSecurityEvent('suspicious_user_agent', {
                    userAgent,
                    url,
                    method,
                }, undefined, ip);
                break;
            }
        }
        if (url.includes('/admin') || url.includes('/dashboard')) {
            this.logger.logSecurityEvent('admin_access_attempt', {
                url,
                method,
                userAgent,
            }, undefined, ip);
        }
        if (url.includes('/auth') || url.includes('/login')) {
            this.logger.logSecurityEvent('auth_endpoint_access', {
                url,
                method,
                userAgent,
            }, undefined, ip);
        }
    }
    validateRequest(req) {
        const contentLength = parseInt(req.headers['content-length'] || '0');
        const maxRequestSize = 10 * 1024 * 1024;
        if (contentLength > maxRequestSize) {
            this.logger.logSecurityEvent('large_request_size', {
                contentLength,
                maxRequestSize,
                url: req.url,
                method: req.method,
            }, undefined, req.ip);
        }
        const headerCount = Object.keys(req.headers).length;
        const maxHeaders = 50;
        if (headerCount > maxHeaders) {
            this.logger.logSecurityEvent('excessive_headers', {
                headerCount,
                maxHeaders,
                url: req.url,
                method: req.method,
            }, undefined, req.ip);
        }
        const url = req.url;
        if (url.includes('\0')) {
            this.logger.logSecurityEvent('null_byte_injection', {
                url,
                method: req.method,
            }, undefined, req.ip);
        }
    }
};
exports.SecurityMiddleware = SecurityMiddleware;
exports.SecurityMiddleware = SecurityMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], SecurityMiddleware);
//# sourceMappingURL=security.middleware.js.map