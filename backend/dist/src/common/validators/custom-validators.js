"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseValidatorService = void 0;
exports.IsNotPastDate = IsNotPastDate;
exports.IsAfterStartDate = IsAfterStartDate;
exports.IsValidPlateNumber = IsValidPlateNumber;
exports.IsValidLicenseNumber = IsValidLicenseNumber;
exports.IsValidPhoneNumber = IsValidPhoneNumber;
exports.IsUniqueEmail = IsUniqueEmail;
const class_validator_1 = require("class-validator");
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
function IsNotPastDate(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isNotPastDate',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (!value)
                        return true;
                    const date = new Date(value);
                    const now = new Date();
                    now.setHours(0, 0, 0, 0);
                    return date >= now;
                },
                defaultMessage(args) {
                    return `${args.property} cannot be in the past`;
                },
            },
        });
    };
}
function IsAfterStartDate(startDateProperty, validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isAfterStartDate',
            target: object.constructor,
            propertyName: propertyName,
            constraints: [startDateProperty],
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (!value)
                        return true;
                    const [startDatePropertyName] = args.constraints;
                    const startDate = args.object[startDatePropertyName];
                    if (!startDate)
                        return true;
                    const endDate = new Date(value);
                    const startDateObj = new Date(startDate);
                    return endDate > startDateObj;
                },
                defaultMessage(args) {
                    const [startDatePropertyName] = args.constraints;
                    return `${args.property} must be after ${startDatePropertyName}`;
                },
            },
        });
    };
}
function IsValidPlateNumber(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidPlateNumber',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (!value)
                        return true;
                    const cleanValue = value.replace(/[\s-]/g, '');
                    const plateRegexes = [
                        /^[A-Z]{1,3}\d{3,6}$/i,
                        /^[A-Z]{2,3}\d{3,4}[A-Z]$/i,
                        /^[A-Z]{1,4}\d{1,6}[A-Z]?$/i,
                    ];
                    return plateRegexes.some(regex => regex.test(cleanValue));
                },
                defaultMessage(args) {
                    return `${args.property} must be a valid plate number format`;
                },
            },
        });
    };
}
function IsValidLicenseNumber(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidLicenseNumber',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (!value)
                        return true;
                    const licenseRegex = /^[A-Z0-9]{6,}$/i;
                    return licenseRegex.test(value.replace(/\s/g, ''));
                },
                defaultMessage(args) {
                    return `${args.property} must be a valid license number format`;
                },
            },
        });
    };
}
function IsValidPhoneNumber(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidPhoneNumber',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    if (!value)
                        return true;
                    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                    const cleanPhone = value.replace(/[\s\-\(\)]/g, '');
                    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 7;
                },
                defaultMessage(args) {
                    return `${args.property} must be a valid phone number`;
                },
            },
        });
    };
}
let DatabaseValidatorService = class DatabaseValidatorService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async validateVehicleExists(vehicleId, expectedType) {
        const vehicle = await this.prisma.vehicle.findUnique({
            where: { id: vehicleId },
            select: { id: true, vehicleType: true },
        });
        if (!vehicle)
            return false;
        if (expectedType && vehicle.vehicleType !== expectedType)
            return false;
        return true;
    }
    async validateUserExists(userId, expectedRole) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { id: true, role: true },
        });
        if (!user)
            return false;
        if (expectedRole && user.role !== expectedRole)
            return false;
        return true;
    }
    async validateEmailUnique(email, excludeUserId) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email },
            select: { id: true },
        });
        if (!existingUser)
            return true;
        if (excludeUserId && existingUser.id === excludeUserId)
            return true;
        return false;
    }
    async validatePlateNumberUnique(plateNumber, excludeVehicleId) {
        const existingVehicle = await this.prisma.vehicle.findUnique({
            where: { plateNumber },
            select: { id: true },
        });
        if (!existingVehicle)
            return true;
        if (excludeVehicleId && existingVehicle.id === excludeVehicleId)
            return true;
        return false;
    }
};
exports.DatabaseValidatorService = DatabaseValidatorService;
exports.DatabaseValidatorService = DatabaseValidatorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DatabaseValidatorService);
function IsUniqueEmail(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isUniqueEmail',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            async: true,
            validator: {
                async validate(value, args) {
                    return true;
                },
                defaultMessage(args) {
                    return `${args.property} must be unique`;
                },
            },
        });
    };
}
//# sourceMappingURL=custom-validators.js.map