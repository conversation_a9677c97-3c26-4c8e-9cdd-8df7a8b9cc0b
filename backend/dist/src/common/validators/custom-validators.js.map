{"version": 3, "file": "custom-validators.js", "sourceRoot": "", "sources": ["../../../../src/common/validators/custom-validators.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAOA,sCAqBC;AAKD,4CA0BC;AAKD,gDAmCC;AAKD,oDAqBC;AAKD,gDAqBC;AAqED,sCAoBC;AAhPD,qDAA4F;AAC5F,2CAA4C;AAC5C,gEAA4D;AAK5D,SAAgB,aAAa,CAAC,iBAAqC;IACjE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK;wBAAE,OAAO,IAAI,CAAC;oBACxB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC7B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;oBACvB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzB,OAAO,IAAI,IAAI,GAAG,CAAC;gBACrB,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,wBAAwB,CAAC;gBAClD,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,gBAAgB,CAAC,iBAAyB,EAAE,iBAAqC;IAC/F,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,WAAW,EAAE,CAAC,iBAAiB,CAAC;YAChC,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK;wBAAE,OAAO,IAAI,CAAC;oBACxB,MAAM,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;oBACjD,MAAM,SAAS,GAAI,IAAI,CAAC,MAAc,CAAC,qBAAqB,CAAC,CAAC;oBAC9D,IAAI,CAAC,SAAS;wBAAE,OAAO,IAAI,CAAC;oBAE5B,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;oBAChC,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;oBACzC,OAAO,OAAO,GAAG,YAAY,CAAC;gBAChC,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,MAAM,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;oBACjD,OAAO,GAAG,IAAI,CAAC,QAAQ,kBAAkB,qBAAqB,EAAE,CAAC;gBACnE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,kBAAkB,CAAC,iBAAqC;IACtE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK;wBAAE,OAAO,IAAI,CAAC;oBAMxB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAM/C,MAAM,YAAY,GAAG;wBACnB,sBAAsB;wBACtB,2BAA2B;wBAC3B,4BAA4B;qBAC7B,CAAC;oBAEF,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5D,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,sCAAsC,CAAC;gBAChE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,oBAAoB,CAAC,iBAAqC;IACxE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,sBAAsB;YAC5B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK;wBAAE,OAAO,IAAI,CAAC;oBAGxB,MAAM,YAAY,GAAG,iBAAiB,CAAC;oBACvC,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;gBACrD,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,wCAAwC,CAAC;gBAClE,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,kBAAkB,CAAC,iBAAqC;IACtE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,SAAS,EAAE;gBACT,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAC5C,IAAI,CAAC,KAAK;wBAAE,OAAO,IAAI,CAAC;oBAExB,MAAM,UAAU,GAAG,wBAAwB,CAAC;oBAC5C,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;oBACpD,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;gBAC/D,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,+BAA+B,CAAC;gBACzD,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAMM,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACf;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAK7C,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,YAAqB;QAClE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAC3B,IAAI,YAAY,IAAI,OAAO,CAAC,WAAW,KAAK,YAAY;YAAE,OAAO,KAAK,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,YAAqB;QAC5D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QACxB,IAAI,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY;YAAE,OAAO,KAAK,CAAC;QAC7D,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,KAAa,EAAE,aAAsB;QAC7D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACrD,KAAK,EAAE,EAAE,KAAK,EAAE;YAChB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,aAAa,IAAI,YAAY,CAAC,EAAE,KAAK,aAAa;YAAE,OAAO,IAAI,CAAC;QACpE,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,WAAmB,EAAE,gBAAyB;QAC5E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC3D,KAAK,EAAE,EAAE,WAAW,EAAE;YACtB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe;YAAE,OAAO,IAAI,CAAC;QAClC,IAAI,gBAAgB,IAAI,eAAe,CAAC,EAAE,KAAK,gBAAgB;YAAE,OAAO,IAAI,CAAC;QAC7E,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA1DY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,wBAAwB,CA0DpC;AAKD,SAAgB,aAAa,CAAC,iBAAqC;IACjE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,IAAI,EAAE,eAAe;YACrB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,KAAK,EAAE,IAAI;YACX,SAAS,EAAE;gBACT,KAAK,CAAC,QAAQ,CAAC,KAAU,EAAE,IAAyB;oBAGlD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,cAAc,CAAC,IAAyB;oBACtC,OAAO,GAAG,IAAI,CAAC,QAAQ,iBAAiB,CAAC;gBAC3C,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}