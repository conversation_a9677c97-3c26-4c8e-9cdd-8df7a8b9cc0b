import { ValidationOptions } from 'class-validator';
import { PrismaService } from '../../prisma/prisma.service';
export declare function IsNotPastDate(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
export declare function IsAfterStartDate(startDateProperty: string, validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
export declare function IsValidPlateNumber(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
export declare function IsValidLicenseNumber(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
export declare function IsValidPhoneNumber(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
export declare class DatabaseValidatorService {
    private prisma;
    constructor(prisma: PrismaService);
    validateVehicleExists(vehicleId: string, expectedType?: string): Promise<boolean>;
    validateUserExists(userId: string, expectedRole?: string): Promise<boolean>;
    validateEmailUnique(email: string, excludeUserId?: string): Promise<boolean>;
    validatePlateNumberUnique(plateNumber: string, excludeVehicleId?: string): Promise<boolean>;
}
export declare function IsUniqueEmail(validationOptions?: ValidationOptions): (object: Object, propertyName: string) => void;
