"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RealtimeGateway_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealtimeGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const common_1 = require("@nestjs/common");
const socket_io_1 = require("socket.io");
let RealtimeGateway = RealtimeGateway_1 = class RealtimeGateway {
    server;
    logger = new common_1.Logger(RealtimeGateway_1.name);
    connectedUsers = new Map();
    handleConnection(client) {
        this.logger.log(`Client connected: ${client.id}`);
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
        if (client.user) {
            this.connectedUsers.delete(client.user.id);
            this.logger.log(`User ${client.user.email} disconnected`);
        }
    }
    async handleAuthentication(client, data) {
        try {
            const user = {
                id: 'user-123',
                email: '<EMAIL>',
                role: 'MANAGER',
            };
            client.user = user;
            this.connectedUsers.set(user.id, client);
            client.emit('authenticated', { success: true, user });
            this.logger.log(`User ${user.email} authenticated via WebSocket`);
        }
        catch (error) {
            this.logger.error('WebSocket authentication failed:', error);
            client.emit('authenticated', { success: false, error: 'Authentication failed' });
        }
    }
    handleJoinRoom(client, data) {
        if (!client.user) {
            client.emit('error', { message: 'Not authenticated' });
            return;
        }
        client.join(data.room);
        this.logger.log(`User ${client.user.email} joined room: ${data.room}`);
        client.emit('joined-room', { room: data.room });
    }
    handleLeaveRoom(client, data) {
        client.leave(data.room);
        this.logger.log(`Client ${client.id} left room: ${data.room}`);
        client.emit('left-room', { room: data.room });
    }
    broadcastTripUpdate(tripId, update) {
        this.server.emit('trip-updated', {
            tripId,
            ...update,
            timestamp: new Date().toISOString(),
        });
        this.logger.debug(`Broadcasted trip update for trip ${tripId}`);
    }
    broadcastVehicleUpdate(vehicleId, update) {
        this.server.emit('vehicle-updated', {
            vehicleId,
            ...update,
            timestamp: new Date().toISOString(),
        });
        this.logger.debug(`Broadcasted vehicle update for vehicle ${vehicleId}`);
    }
    broadcastDriverUpdate(driverId, update) {
        this.server.emit('driver-updated', {
            driverId,
            ...update,
            timestamp: new Date().toISOString(),
        });
        this.logger.debug(`Broadcasted driver update for driver ${driverId}`);
    }
    sendNotificationToUser(userId, notification) {
        const userSocket = this.connectedUsers.get(userId);
        if (userSocket) {
            userSocket.emit('notification', {
                ...notification,
                timestamp: new Date().toISOString(),
            });
            this.logger.debug(`Sent notification to user ${userId}`);
        }
    }
    sendNotificationToRole(role, notification) {
        this.connectedUsers.forEach((socket, userId) => {
            if (socket.user && socket.user.role === role) {
                socket.emit('notification', {
                    ...notification,
                    timestamp: new Date().toISOString(),
                });
            }
        });
        this.logger.debug(`Sent notification to users with role ${role}`);
    }
    broadcastSystemAlert(alert) {
        this.server.emit('system-alert', {
            ...alert,
            timestamp: new Date().toISOString(),
        });
        this.logger.debug('Broadcasted system alert');
    }
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }
    getConnectedUsersByRole(role) {
        let count = 0;
        this.connectedUsers.forEach((socket) => {
            if (socket.user && socket.user.role === role) {
                count++;
            }
        });
        return count;
    }
};
exports.RealtimeGateway = RealtimeGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", typeof (_a = typeof socket_io_1.Server !== "undefined" && socket_io_1.Server) === "function" ? _a : Object)
], RealtimeGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('authenticate'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RealtimeGateway.prototype, "handleAuthentication", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('join-room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], RealtimeGateway.prototype, "handleJoinRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave-room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], RealtimeGateway.prototype, "handleLeaveRoom", null);
exports.RealtimeGateway = RealtimeGateway = RealtimeGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true,
        },
        namespace: '/realtime',
    })
], RealtimeGateway);
//# sourceMappingURL=realtime.gateway.js.map