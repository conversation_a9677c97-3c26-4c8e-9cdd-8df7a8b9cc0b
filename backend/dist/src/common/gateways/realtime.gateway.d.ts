import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
interface AuthenticatedSocket extends Socket {
    user?: {
        id: string;
        email: string;
        role: string;
    };
}
export declare class RealtimeGateway implements OnGatewayConnection, OnGatewayDisconnect {
    server: Server;
    private readonly logger;
    private connectedUsers;
    handleConnection(client: AuthenticatedSocket): void;
    handleDisconnect(client: AuthenticatedSocket): void;
    handleAuthentication(client: AuthenticatedSocket, data: {
        token: string;
    }): Promise<void>;
    handleJoinRoom(client: AuthenticatedSocket, data: {
        room: string;
    }): void;
    handleLeaveRoom(client: AuthenticatedSocket, data: {
        room: string;
    }): void;
    broadcastTripUpdate(tripId: string, update: any): void;
    broadcastVehicleUpdate(vehicleId: string, update: any): void;
    broadcastDriverUpdate(driverId: string, update: any): void;
    sendNotificationToUser(userId: string, notification: any): void;
    sendNotificationToRole(role: string, notification: any): void;
    broadcastSystemAlert(alert: any): void;
    getConnectedUsersCount(): number;
    getConnectedUsersByRole(role: string): number;
}
export {};
