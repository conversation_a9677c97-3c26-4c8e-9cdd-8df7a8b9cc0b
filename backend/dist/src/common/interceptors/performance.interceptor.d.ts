import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { HealthService } from '../../health/health.service';
export declare class PerformanceInterceptor implements NestInterceptor {
    private readonly healthService;
    private readonly logger;
    constructor(healthService: HealthService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private logPerformanceMetrics;
    private storeMetrics;
}
