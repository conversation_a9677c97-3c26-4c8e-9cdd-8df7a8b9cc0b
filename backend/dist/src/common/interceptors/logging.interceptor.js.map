{"version": 3, "file": "logging.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/common/interceptors/logging.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AAExB,8CAAqC;AACrC,+DAA2D;AAGpD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IACA;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC;YACF,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;gBACb,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACxC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAGvC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAGtE,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE;wBAC/D,MAAM;wBACN,GAAG;wBACH,MAAM;wBACN,UAAU;qBACX,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACxC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC;gBAGvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,EAAE;oBAC1C,MAAM;oBACN,GAAG;oBACH,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,EAAE;oBACF,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,IAAI,EAAE,WAAW;iBAClB,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AArDY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,kBAAkB,CAqD9B"}