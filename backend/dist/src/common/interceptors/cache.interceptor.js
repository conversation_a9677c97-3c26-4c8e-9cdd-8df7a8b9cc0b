"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheInterceptor_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheInterceptor = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const redis_service_1 = require("../services/redis.service");
const cache_decorator_1 = require("../decorators/cache.decorator");
let CacheInterceptor = CacheInterceptor_1 = class CacheInterceptor {
    redisService;
    reflector;
    logger = new common_1.Logger(CacheInterceptor_1.name);
    constructor(redisService, reflector) {
        this.redisService = redisService;
        this.reflector = reflector;
    }
    async intercept(context, next) {
        const cacheOptions = this.reflector.get(cache_decorator_1.CACHE_KEY, context.getHandler());
        if (!cacheOptions) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const className = context.getClass().name;
        const methodName = context.getHandler().name;
        const args = [request.params, request.query, request.body].filter(Boolean);
        const cacheKey = cacheOptions.key ||
            (0, cache_decorator_1.generateCacheKey)(className, methodName, args, cacheOptions.keyGenerator);
        try {
            const cachedResult = await this.redisService.get(cacheKey);
            if (cachedResult !== null) {
                this.logger.debug(`Cache hit for key: ${cacheKey}`);
                return (0, rxjs_1.of)(cachedResult);
            }
        }
        catch (error) {
            this.logger.warn(`Cache get error for key ${cacheKey}:`, error);
        }
        return next.handle().pipe((0, operators_1.tap)(async (result) => {
            try {
                const ttl = cacheOptions.ttl || 300;
                await this.redisService.set(cacheKey, result, ttl);
                this.logger.debug(`Cached result for key: ${cacheKey} (TTL: ${ttl}s)`);
            }
            catch (error) {
                this.logger.warn(`Cache set error for key ${cacheKey}:`, error);
            }
        }));
    }
};
exports.CacheInterceptor = CacheInterceptor;
exports.CacheInterceptor = CacheInterceptor = CacheInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService, typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], CacheInterceptor);
//# sourceMappingURL=cache.interceptor.js.map