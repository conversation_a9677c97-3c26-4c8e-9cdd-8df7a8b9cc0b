"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PerformanceInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const health_service_1 = require("../../health/health.service");
let PerformanceInterceptor = PerformanceInterceptor_1 = class PerformanceInterceptor {
    healthService;
    logger = new common_1.Logger(PerformanceInterceptor_1.name);
    constructor(healthService) {
        this.healthService = healthService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const method = request.method;
        const url = request.url;
        const userAgent = request.get('User-Agent') || '';
        const ip = request.ip || request.connection.remoteAddress;
        const userId = request.user?.id;
        const startTime = Date.now();
        return next.handle().pipe((0, operators_1.tap)({
            next: (data) => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                const statusCode = response.statusCode;
                this.logPerformanceMetrics({
                    method,
                    url,
                    statusCode,
                    duration,
                    userAgent,
                    ip,
                    userId,
                    success: true,
                });
                this.healthService.incrementQueryCount();
                if (duration > 1000) {
                    this.logger.warn(`Slow request detected: ${method} ${url} took ${duration}ms`, {
                        method,
                        url,
                        duration,
                        userId,
                        ip,
                    });
                }
            },
            error: (error) => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                const statusCode = error.status || 500;
                this.logPerformanceMetrics({
                    method,
                    url,
                    statusCode,
                    duration,
                    userAgent,
                    ip,
                    userId,
                    success: false,
                    error: error.message,
                });
                this.logger.error(`Request failed: ${method} ${url} (${duration}ms)`, {
                    method,
                    url,
                    duration,
                    statusCode,
                    error: error.message,
                    userId,
                    ip,
                });
            },
        }));
    }
    logPerformanceMetrics(metrics) {
        const { method, url, statusCode, duration, userAgent, ip, userId, success, error, } = metrics;
        const logEntry = {
            timestamp: new Date().toISOString(),
            type: 'performance',
            method,
            url,
            statusCode,
            duration,
            userAgent,
            ip,
            userId,
            success,
            error,
        };
        if (duration < 100) {
            this.logger.debug(`Fast request: ${method} ${url} (${duration}ms)`, logEntry);
        }
        else if (duration < 500) {
            this.logger.log(`Normal request: ${method} ${url} (${duration}ms)`, logEntry);
        }
        else if (duration < 1000) {
            this.logger.warn(`Slow request: ${method} ${url} (${duration}ms)`, logEntry);
        }
        else {
            this.logger.error(`Very slow request: ${method} ${url} (${duration}ms)`, logEntry);
        }
        this.storeMetrics(metrics);
    }
    storeMetrics(metrics) {
    }
};
exports.PerformanceInterceptor = PerformanceInterceptor;
exports.PerformanceInterceptor = PerformanceInterceptor = PerformanceInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [health_service_1.HealthService])
], PerformanceInterceptor);
//# sourceMappingURL=performance.interceptor.js.map