{"version": 3, "file": "performance.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/common/interceptors/performance.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAMwB;AAExB,8CAAqC;AACrC,gEAA4D;AAGrD,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAGJ;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAElE,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAE7D,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAClD,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC;QAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC;YACF,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;gBACb,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;gBACrC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAGvC,IAAI,CAAC,qBAAqB,CAAC;oBACzB,MAAM;oBACN,GAAG;oBACH,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,EAAE;oBACF,MAAM;oBACN,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC;gBAGH,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;gBAGzC,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,IAAI,GAAG,SAAS,QAAQ,IAAI,EAAE;wBAC7E,MAAM;wBACN,GAAG;wBACH,QAAQ;wBACR,MAAM;wBACN,EAAE;qBACH,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;gBACrC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC;gBAGvC,IAAI,CAAC,qBAAqB,CAAC;oBACzB,MAAM;oBACN,GAAG;oBACH,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,EAAE;oBACF,MAAM;oBACN,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBAGH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,MAAM,IAAI,GAAG,KAAK,QAAQ,KAAK,EAAE;oBACpE,MAAM;oBACN,GAAG;oBACH,QAAQ;oBACR,UAAU;oBACV,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,MAAM;oBACN,EAAE;iBACH,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,OAU7B;QACC,MAAM,EACJ,MAAM,EACN,GAAG,EACH,UAAU,EACV,QAAQ,EACR,SAAS,EACT,EAAE,EACF,MAAM,EACN,OAAO,EACP,KAAK,GACN,GAAG,OAAO,CAAC;QAGZ,MAAM,QAAQ,GAAG;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,aAAa;YACnB,MAAM;YACN,GAAG;YACH,UAAU;YACV,QAAQ;YACR,SAAS;YACT,EAAE;YACF,MAAM;YACN,OAAO;YACP,KAAK;SACN,CAAC;QAGF,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,MAAM,IAAI,GAAG,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,MAAM,IAAI,GAAG,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,MAAM,IAAI,GAAG,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC/E,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,MAAM,IAAI,GAAG,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrF,CAAC;QAGD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAEO,YAAY,CAAC,OAAY;IAQjC,CAAC;CACF,CAAA;AAhJY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAIiC,8BAAa;GAH9C,sBAAsB,CAgJlC"}