import { HttpException, HttpStatus } from '@nestjs/common';
export declare class BusinessLogicException extends HttpException {
    constructor(message: string, statusCode?: HttpStatus);
}
export declare class ResourceNotFoundException extends HttpException {
    constructor(resource: string, id?: string);
}
export declare class ValidationException extends HttpException {
    constructor(message: string, details?: any);
}
export declare class ConflictException extends HttpException {
    constructor(message: string);
}
export declare class DatabaseException extends HttpException {
    constructor(message: string, originalError?: any);
}
