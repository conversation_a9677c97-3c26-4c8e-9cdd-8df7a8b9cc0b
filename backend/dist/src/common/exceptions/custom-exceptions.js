"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseException = exports.ConflictException = exports.ValidationException = exports.ResourceNotFoundException = exports.BusinessLogicException = void 0;
const common_1 = require("@nestjs/common");
class BusinessLogicException extends common_1.HttpException {
    constructor(message, statusCode = common_1.HttpStatus.BAD_REQUEST) {
        super(message, statusCode);
    }
}
exports.BusinessLogicException = BusinessLogicException;
class ResourceNotFoundException extends common_1.HttpException {
    constructor(resource, id) {
        const message = id
            ? `${resource} with ID '${id}' not found`
            : `${resource} not found`;
        super(message, common_1.HttpStatus.NOT_FOUND);
    }
}
exports.ResourceNotFoundException = ResourceNotFoundException;
class ValidationException extends common_1.HttpException {
    constructor(message, details) {
        super({
            message,
            details,
            timestamp: new Date().toISOString(),
        }, common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.ValidationException = ValidationException;
class ConflictException extends common_1.HttpException {
    constructor(message) {
        super(message, common_1.HttpStatus.CONFLICT);
    }
}
exports.ConflictException = ConflictException;
class DatabaseException extends common_1.HttpException {
    constructor(message, originalError) {
        super({
            message: 'Database operation failed',
            details: message,
            originalError: originalError?.message || 'Unknown database error',
            timestamp: new Date().toISOString(),
        }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.DatabaseException = DatabaseException;
//# sourceMappingURL=custom-exceptions.js.map