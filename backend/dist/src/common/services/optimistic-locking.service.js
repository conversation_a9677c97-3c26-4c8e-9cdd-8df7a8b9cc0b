"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimisticLockingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let OptimisticLockingService = class OptimisticLockingService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async updateTrip(id, updateData, currentVersion, userId) {
        try {
            return await this.prisma.trip.update({
                where: {
                    id,
                    version: currentVersion,
                },
                data: {
                    ...updateData,
                    version: {
                        increment: 1,
                    },
                    lastModifiedBy: userId,
                    updatedAt: new Date(),
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.ConflictException('The record has been modified by another user. Please refresh and try again.');
            }
            throw error;
        }
    }
    async updateVehicle(id, updateData, currentVersion, userId) {
        try {
            return await this.prisma.vehicle.update({
                where: {
                    id,
                },
                data: {
                    ...updateData,
                    updatedAt: new Date(),
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.ConflictException('The vehicle has been modified by another user. Please refresh and try again.');
            }
            throw error;
        }
    }
    async updateWithOptimisticLocking(model, id, updateData, currentVersion, userId) {
        try {
            const modelDelegate = this.prisma[model];
            if (!modelDelegate) {
                throw new Error(`Model ${model} not found`);
            }
            return await modelDelegate.update({
                where: {
                    id,
                    version: currentVersion,
                },
                data: {
                    ...updateData,
                    version: {
                        increment: 1,
                    },
                    lastModifiedBy: userId,
                    updatedAt: new Date(),
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.ConflictException('The record has been modified by another user. Please refresh and try again.');
            }
            throw error;
        }
    }
    async checkVersion(model, id, expectedVersion) {
        try {
            const modelDelegate = this.prisma[model];
            if (!modelDelegate) {
                throw new Error(`Model ${model} not found`);
            }
            const record = await modelDelegate.findUnique({
                where: { id },
                select: { version: true },
            });
            return record && record.version === expectedVersion;
        }
        catch (error) {
            return false;
        }
    }
    async getCurrentVersion(model, id) {
        try {
            const modelDelegate = this.prisma[model];
            if (!modelDelegate) {
                throw new Error(`Model ${model} not found`);
            }
            const record = await modelDelegate.findUnique({
                where: { id },
                select: { version: true },
            });
            return record ? record.version : null;
        }
        catch (error) {
            return null;
        }
    }
    validateOptimisticLockingData(data) {
        if (typeof data.version !== 'number') {
            throw new Error('Version field is required for optimistic locking');
        }
        return {
            version: data.version,
            lastModifiedBy: data.lastModifiedBy,
        };
    }
};
exports.OptimisticLockingService = OptimisticLockingService;
exports.OptimisticLockingService = OptimisticLockingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OptimisticLockingService);
//# sourceMappingURL=optimistic-locking.service.js.map