import { OnModuleInit, OnModuleD<PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisClientType } from 'redis';
export declare class RedisService implements OnModuleInit, OnModuleDestroy {
    private readonly configService;
    private readonly logger;
    private client;
    private isConnected;
    constructor(configService: ConfigService);
    onModuleInit(): any;
    onModuleDestroy(): any;
    private connect;
    private disconnect;
    get<T>(key: string): Promise<T | null>;
    set(key: string, value: any, ttlSeconds?: number): Promise<boolean>;
    del(key: string): Promise<boolean>;
    delPattern(pattern: string): Promise<boolean>;
    exists(key: string): Promise<boolean>;
    expire(key: string, ttlSeconds: number): Promise<boolean>;
    incr(key: string): Promise<number | null>;
    getClient(): RedisClientType | null;
    healthCheck(): Promise<{
        status: string;
        connected: boolean;
    }>;
}
