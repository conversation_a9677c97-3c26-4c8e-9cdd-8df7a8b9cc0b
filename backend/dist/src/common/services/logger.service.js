"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = exports.LogLevel = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["ERROR"] = 0] = "ERROR";
    LogLevel[LogLevel["WARN"] = 1] = "WARN";
    LogLevel[LogLevel["INFO"] = 2] = "INFO";
    LogLevel[LogLevel["DEBUG"] = 3] = "DEBUG";
    LogLevel[LogLevel["VERBOSE"] = 4] = "VERBOSE";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
let LoggerService = class LoggerService {
    configService;
    logLevel;
    serviceName;
    constructor(configService) {
        this.configService = configService;
        this.logLevel = this.getLogLevelFromConfig();
        this.serviceName = this.configService.get('app.serviceName', 'FleetFusion');
    }
    getLogLevelFromConfig() {
        const level = this.configService.get('LOG_LEVEL', 'INFO').toUpperCase();
        return LogLevel[level] ?? LogLevel.INFO;
    }
    shouldLog(level) {
        return level <= this.logLevel;
    }
    formatMessage(level, message, context) {
        const timestamp = new Date().toISOString();
        const pid = process.pid;
        const baseLog = {
            timestamp,
            level,
            service: this.serviceName,
            pid,
            message: typeof message === 'object' ? JSON.stringify(message) : message,
        };
        if (context) {
            Object.assign(baseLog, context);
        }
        return JSON.stringify(baseLog);
    }
    log(message, context) {
        if (this.shouldLog(LogLevel.INFO)) {
            console.log(this.formatMessage('INFO', message, context));
        }
    }
    error(message, trace, context) {
        if (this.shouldLog(LogLevel.ERROR)) {
            const errorLog = {
                ...context,
                trace,
                error: true,
            };
            console.error(this.formatMessage('ERROR', message, errorLog));
        }
    }
    warn(message, context) {
        if (this.shouldLog(LogLevel.WARN)) {
            console.warn(this.formatMessage('WARN', message, context));
        }
    }
    debug(message, context) {
        if (this.shouldLog(LogLevel.DEBUG)) {
            console.debug(this.formatMessage('DEBUG', message, context));
        }
    }
    verbose(message, context) {
        if (this.shouldLog(LogLevel.VERBOSE)) {
            console.log(this.formatMessage('VERBOSE', message, context));
        }
    }
    logApiRequest(method, url, userId, ip, userAgent) {
        this.log('API Request', {
            method,
            url,
            userId,
            ip,
            userAgent,
            type: 'api_request',
        });
    }
    logApiResponse(method, url, statusCode, duration, userId) {
        this.log('API Response', {
            method,
            url,
            statusCode,
            duration,
            userId,
            type: 'api_response',
        });
    }
    logBusinessEvent(event, data, userId) {
        this.log('Business Event', {
            event,
            data,
            userId,
            type: 'business_event',
        });
    }
    logSecurityEvent(event, details, userId, ip) {
        this.warn('Security Event', {
            event,
            details,
            userId,
            ip,
            type: 'security_event',
        });
    }
    logPerformanceMetric(metric, value, unit, context) {
        this.log('Performance Metric', {
            metric,
            value,
            unit,
            ...context,
            type: 'performance_metric',
        });
    }
    logDatabaseQuery(query, duration, affectedRows) {
        if (this.shouldLog(LogLevel.DEBUG)) {
            this.debug('Database Query', {
                query: query.replace(/\s+/g, ' ').trim(),
                duration,
                affectedRows,
                type: 'database_query',
            });
        }
    }
    logVehicleEvent(event, vehicleId, data, userId) {
        this.logBusinessEvent(`vehicle_${event}`, {
            vehicleId,
            ...data,
        }, userId);
    }
    logTripEvent(event, tripId, data, userId) {
        this.logBusinessEvent(`trip_${event}`, {
            tripId,
            ...data,
        }, userId);
    }
    logMaintenanceEvent(event, maintenanceId, data, userId) {
        this.logBusinessEvent(`maintenance_${event}`, {
            maintenanceId,
            ...data,
        }, userId);
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], LoggerService);
//# sourceMappingURL=logger.service.js.map