import { PrismaService } from '../../prisma/prisma.service';
export interface OptimisticLockingData {
    version: number;
    lastModifiedBy?: string;
}
export interface UpdateWithOptimisticLocking<T> extends OptimisticLockingData {
    data: T;
}
export declare class OptimisticLockingService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    updateTrip(id: string, updateData: any, currentVersion: number, userId?: string): Promise<any>;
    updateVehicle(id: string, updateData: any, currentVersion: number, userId?: string): Promise<any>;
    updateWithOptimisticLocking<T>(model: string, id: string, updateData: any, currentVersion: number, userId?: string): Promise<T>;
    checkVersion(model: string, id: string, expectedVersion: number): Promise<boolean>;
    getCurrentVersion(model: string, id: string): Promise<number | null>;
    validateOptimisticLockingData(data: any): OptimisticLockingData;
}
