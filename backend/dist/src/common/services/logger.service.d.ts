import { LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export declare enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3,
    VERBOSE = 4
}
export interface LogContext {
    userId?: string;
    vehicleId?: string;
    tripId?: string;
    requestId?: string;
    ip?: string;
    userAgent?: string;
    method?: string;
    url?: string;
    statusCode?: number;
    duration?: number;
    [key: string]: any;
}
export declare class LoggerService implements NestLoggerService {
    private readonly configService;
    private logLevel;
    private serviceName;
    constructor(configService: ConfigService);
    private getLogLevelFromConfig;
    private shouldLog;
    private formatMessage;
    log(message: any, context?: LogContext): void;
    error(message: any, trace?: string, context?: LogContext): void;
    warn(message: any, context?: LogContext): void;
    debug(message: any, context?: LogContext): void;
    verbose(message: any, context?: LogContext): void;
    logApiRequest(method: string, url: string, userId?: string, ip?: string, userAgent?: string): void;
    logApiResponse(method: string, url: string, statusCode: number, duration: number, userId?: string): void;
    logBusinessEvent(event: string, data: any, userId?: string): void;
    logSecurityEvent(event: string, details: any, userId?: string, ip?: string): void;
    logPerformanceMetric(metric: string, value: number, unit: string, context?: LogContext): void;
    logDatabaseQuery(query: string, duration: number, affectedRows?: number): void;
    logVehicleEvent(event: string, vehicleId: string, data: any, userId?: string): void;
    logTripEvent(event: string, tripId: string, data: any, userId?: string): void;
    logMaintenanceEvent(event: string, maintenanceId: string, data: any, userId?: string): void;
}
