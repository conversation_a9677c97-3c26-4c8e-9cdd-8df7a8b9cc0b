"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseOptimizationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let DatabaseOptimizationService = class DatabaseOptimizationService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findVehiclesMinimal(vehicleType) {
        const where = vehicleType ? { vehicleType } : {};
        return this.prisma.vehicle.findMany({
            where,
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                status: true,
                vehicleType: true,
            },
            orderBy: [
                { status: 'asc' },
                { plateNumber: 'asc' }
            ],
        });
    }
    async findAvailableVehicles(vehicleType) {
        const where = {
            status: client_1.VehicleStatus.AVAILABLE,
        };
        if (vehicleType) {
            where.vehicleType = vehicleType;
        }
        return this.prisma.vehicle.findMany({
            where,
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                year: true,
                status: true,
                vehicleType: true,
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                    },
                },
            },
            orderBy: {
                plateNumber: 'asc',
            },
        });
    }
    async findDriversMinimal() {
        return this.prisma.user.findMany({
            where: {
                role: 'DRIVER',
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                status: true,
                licenseNumber: true,
            },
            orderBy: [
                { status: 'asc' },
                { firstName: 'asc' },
                { lastName: 'asc' }
            ],
        });
    }
    async findAvailableDrivers() {
        return this.prisma.user.findMany({
            where: {
                role: 'DRIVER',
                OR: [
                    { status: 'Active' },
                    { status: 'Available' },
                    { status: null },
                ],
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                status: true,
                licenseNumber: true,
                assignments: {
                    where: {
                        status: 'ACTIVE',
                    },
                    select: {
                        id: true,
                        status: true,
                    },
                },
            },
            orderBy: [
                { firstName: 'asc' },
                { lastName: 'asc' }
            ],
        });
    }
    async batchUpdateVehicleStatus(updates) {
        const operations = updates.map(({ id, status }) => this.prisma.vehicle.update({
            where: { id },
            data: { status },
        }));
        return this.prisma.$transaction(operations);
    }
    async batchUpdateUserStatus(updates) {
        const operations = updates.map(({ id, status }) => this.prisma.user.update({
            where: { id },
            data: { status },
        }));
        return this.prisma.$transaction(operations);
    }
    async findTripsForDashboard(page = 1, limit = 20, filters) {
        const skip = (page - 1) * limit;
        const where = {};
        if (filters?.status) {
            where.status = filters.status;
        }
        if (filters?.driverId) {
            where.driverId = filters.driverId;
        }
        if (filters?.vehicleId) {
            where.vehicleId = filters.vehicleId;
        }
        const [trips, total] = await Promise.all([
            this.prisma.trip.findMany({
                where,
                select: {
                    id: true,
                    startLocation: true,
                    endLocation: true,
                    startTime: true,
                    endTime: true,
                    status: true,
                    distance: true,
                    driver: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    vehicle: {
                        select: {
                            id: true,
                            plateNumber: true,
                            make: true,
                            model: true,
                        },
                    },
                },
                orderBy: {
                    startTime: 'desc',
                },
                skip,
                take: limit,
            }),
            this.prisma.trip.count({ where }),
        ]);
        return {
            trips,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async getAssignmentStatistics() {
        const [totalAssignments, activeAssignments, completedAssignments, vehicleUtilization, driverUtilization,] = await Promise.all([
            this.prisma.vehicleAssignment.count(),
            this.prisma.vehicleAssignment.count({
                where: { status: 'ACTIVE' },
            }),
            this.prisma.vehicleAssignment.count({
                where: { status: 'COMPLETED' },
            }),
            this.prisma.vehicle.count({
                where: { status: 'ASSIGNED' },
            }),
            this.prisma.user.count({
                where: {
                    role: 'DRIVER',
                    status: 'Assigned',
                },
            }),
        ]);
        const totalVehicles = await this.prisma.vehicle.count();
        const totalDrivers = await this.prisma.user.count({
            where: { role: 'DRIVER' },
        });
        return {
            totalAssignments,
            activeAssignments,
            completedAssignments,
            vehicleUtilizationRate: totalVehicles > 0 ? (vehicleUtilization / totalVehicles) * 100 : 0,
            driverUtilizationRate: totalDrivers > 0 ? (driverUtilization / totalDrivers) * 100 : 0,
        };
    }
    async checkResourceConflicts(vehicleId, driverId, startDate, endDate) {
        const endDateFilter = endDate ? { lte: endDate } : undefined;
        const [vehicleConflicts, driverConflicts] = await Promise.all([
            this.prisma.vehicleAssignment.findMany({
                where: {
                    vehicleId,
                    status: 'ACTIVE',
                    startDate: { lte: endDate || new Date('2099-12-31') },
                    OR: [
                        { endDate: null },
                        { endDate: { gte: startDate } },
                    ],
                },
                select: {
                    id: true,
                    startDate: true,
                    endDate: true,
                },
            }),
            this.prisma.vehicleAssignment.findMany({
                where: {
                    driverId,
                    status: 'ACTIVE',
                    startDate: { lte: endDate || new Date('2099-12-31') },
                    OR: [
                        { endDate: null },
                        { endDate: { gte: startDate } },
                    ],
                },
                select: {
                    id: true,
                    startDate: true,
                    endDate: true,
                },
            }),
        ]);
        return {
            hasVehicleConflict: vehicleConflicts.length > 0,
            hasDriverConflict: driverConflicts.length > 0,
            vehicleConflicts,
            driverConflicts,
        };
    }
};
exports.DatabaseOptimizationService = DatabaseOptimizationService;
exports.DatabaseOptimizationService = DatabaseOptimizationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DatabaseOptimizationService);
//# sourceMappingURL=database-optimization.service.js.map