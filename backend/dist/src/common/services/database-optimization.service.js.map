{"version": 3, "file": "database-optimization.service.js", "sourceRoot": "", "sources": ["../../../../src/common/services/database-optimization.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,gEAA4D;AAC5D,2CAA4D;AAMrD,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IAClB;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAK7C,KAAK,CAAC,mBAAmB,CAAC,WAAyB;QACjD,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEjD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClC,KAAK;YACL,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,WAAW,EAAE,IAAI;gBACjB,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE,IAAI;aAClB;YACD,OAAO,EAAE;gBACP,EAAE,MAAM,EAAE,KAAK,EAAE;gBACjB,EAAE,WAAW,EAAE,KAAK,EAAE;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,WAAyB;QACnD,MAAM,KAAK,GAAQ;YACjB,MAAM,EAAE,sBAAa,CAAC,SAAS;SAChC,CAAC;QAEF,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClC,KAAK;YACL,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,WAAW,EAAE,IAAI;gBACjB,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE,IAAI;gBAEjB,WAAW,EAAE;oBACX,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;qBACjB;oBACD,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,KAAK;aACnB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/B,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;aACf;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI;aACpB;YACD,OAAO,EAAE;gBACP,EAAE,MAAM,EAAE,KAAK,EAAE;gBACjB,EAAE,SAAS,EAAE,KAAK,EAAE;gBACpB,EAAE,QAAQ,EAAE,KAAK,EAAE;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,oBAAoB;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/B,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE;oBACF,EAAE,MAAM,EAAE,QAAQ,EAAE;oBACpB,EAAE,MAAM,EAAE,WAAW,EAAE;oBACvB,EAAE,MAAM,EAAE,IAAI,EAAE;iBACjB;aACF;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI;gBAEnB,WAAW,EAAE;oBACX,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;qBACjB;oBACD,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;YACD,OAAO,EAAE;gBACP,EAAE,SAAS,EAAE,KAAK,EAAE;gBACpB,EAAE,QAAQ,EAAE,KAAK,EAAE;aACpB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,OAAqD;QAClF,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAChD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE,EAAE,MAAM,EAAE;SACjB,CAAC,CACH,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,OAA8C;QACxE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACtB,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE,EAAE,MAAM,EAAE;SACjB,CAAC,CACH,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE,OAAa;QAC7E,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAChC,CAAC;QACD,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC;YACtB,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACtC,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxB,KAAK;gBACL,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,aAAa,EAAE,IAAI;oBACnB,WAAW,EAAE,IAAI;oBACjB,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE;wBACN,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,SAAS,EAAE,IAAI;4BACf,QAAQ,EAAE,IAAI;yBACf;qBACF;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,WAAW,EAAE,IAAI;4BACjB,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,IAAI;yBACZ;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;gBACD,IAAI;gBACJ,IAAI,EAAE,KAAK;aACZ,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SAClC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK;YACL,KAAK;YACL,IAAI;YACJ,KAAK;YACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACrC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,uBAAuB;QAC3B,MAAM,CACJ,gBAAgB,EAChB,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,iBAAiB,EAClB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBAClC,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;aAC5B,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBAClC,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;aAC/B,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;aAC9B,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,UAAU;iBACnB;aACF,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACxD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAChD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;SAC1B,CAAC,CAAC;QAEH,OAAO;YACL,gBAAgB;YAChB,iBAAiB;YACjB,oBAAoB;YACpB,sBAAsB,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1F,qBAAqB,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SACvF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,QAAgB,EAAE,SAAe,EAAE,OAAc;QAC/F,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAE7D,MAAM,CAAC,gBAAgB,EAAE,eAAe,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBACrC,KAAK,EAAE;oBACL,SAAS;oBACT,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,EAAE,GAAG,EAAE,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;oBACrD,EAAE,EAAE;wBACF,EAAE,OAAO,EAAE,IAAI,EAAE;wBACjB,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE;qBAChC;iBACF;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;iBACd;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBACrC,KAAK,EAAE;oBACL,QAAQ;oBACR,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,EAAE,GAAG,EAAE,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE;oBACrD,EAAE,EAAE;wBACF,EAAE,OAAO,EAAE,IAAI,EAAE;wBACjB,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE;qBAChC;iBACF;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;iBACd;aACF,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,kBAAkB,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAC/C,iBAAiB,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC;YAC7C,gBAAgB;YAChB,eAAe;SAChB,CAAC;IACJ,CAAC;CACF,CAAA;AApTY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,2BAA2B,CAoTvC"}