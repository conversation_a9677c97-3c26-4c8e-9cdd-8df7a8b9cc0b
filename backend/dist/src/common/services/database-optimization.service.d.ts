import { PrismaService } from '../../prisma/prisma.service';
import { VehicleType, VehicleStatus } from '@prisma/client';
export declare class DatabaseOptimizationService {
    private prisma;
    constructor(prisma: PrismaService);
    findVehiclesMinimal(vehicleType?: VehicleType): unknown;
    findAvailableVehicles(vehicleType?: VehicleType): unknown;
    findDriversMinimal(): unknown;
    findAvailableDrivers(): unknown;
    batchUpdateVehicleStatus(updates: Array<{
        id: string;
        status: VehicleStatus;
    }>): unknown;
    batchUpdateUserStatus(updates: Array<{
        id: string;
        status: string;
    }>): unknown;
    findTripsForDashboard(page?: number, limit?: number, filters?: any): unknown;
    getAssignmentStatistics(): unknown;
    checkResourceConflicts(vehicleId: string, driverId: string, startDate: Date, endDate?: Date): unknown;
}
