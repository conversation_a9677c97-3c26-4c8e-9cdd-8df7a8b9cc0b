import { PrismaService } from '../../prisma/prisma.service';
import { VehicleType, VehicleStatus } from '@prisma/client';
export declare class DatabaseOptimizationService {
    private prisma;
    constructor(prisma: PrismaService);
    findVehiclesMinimal(vehicleType?: VehicleType): Promise<{
        id: string;
        plateNumber: string;
        make: string;
        model: string;
        status: import(".prisma/client").$Enums.VehicleStatus;
        vehicleType: import(".prisma/client").$Enums.VehicleType;
    }[]>;
    findAvailableVehicles(vehicleType?: VehicleType): Promise<{
        id: string;
        plateNumber: string;
        make: string;
        model: string;
        year: number;
        status: import(".prisma/client").$Enums.VehicleStatus;
        vehicleType: import(".prisma/client").$Enums.VehicleType;
        assignments: {
            id: string;
            status: import(".prisma/client").$Enums.AssignmentStatus;
        }[];
    }[]>;
    findDriversMinimal(): Promise<{
        id: string;
        status: string | null;
        email: string;
        firstName: string;
        lastName: string;
        licenseNumber: string | null;
    }[]>;
    findAvailableDrivers(): Promise<{
        id: string;
        status: string | null;
        assignments: {
            id: string;
            status: import(".prisma/client").$Enums.AssignmentStatus;
        }[];
        email: string;
        firstName: string;
        lastName: string;
        licenseNumber: string | null;
    }[]>;
    batchUpdateVehicleStatus(updates: Array<{
        id: string;
        status: VehicleStatus;
    }>): Promise<{
        id: string;
        plateNumber: string;
        make: string;
        model: string;
        year: number;
        status: import(".prisma/client").$Enums.VehicleStatus;
        lastMaintenance: Date | null;
        createdAt: Date;
        updatedAt: Date;
        color: string | null;
        fuelType: string | null;
        mileage: number | null;
        purchaseDate: Date | null;
        vin: string | null;
        axleConfiguration: string | null;
        cabConfiguration: string | null;
        cargoCapacity: number | null;
        engineType: string | null;
        fuelCapacity: number | null;
        hasRefrigeration: boolean;
        height: number | null;
        length: number | null;
        maxWeight: number | null;
        trailerType: import(".prisma/client").$Enums.TrailerType | null;
        transmission: string | null;
        vehicleType: import(".prisma/client").$Enums.VehicleType;
        width: number | null;
    }[]>;
    batchUpdateUserStatus(updates: Array<{
        id: string;
        status: string;
    }>): Promise<{
        id: string;
        status: string | null;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        passwordHash: string;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        phone: string | null;
        licenseNumber: string | null;
        licenseType: string | null;
        licenseExpiry: Date | null;
        licenseRestrictions: string | null;
        address: string | null;
        emergencyContactName: string | null;
        emergencyContactPhone: string | null;
        hireDate: Date | null;
        notes: string | null;
    }[]>;
    findTripsForDashboard(page?: number, limit?: number, filters?: any): Promise<{
        trips: {
            vehicle: {
                id: string;
                plateNumber: string;
                make: string;
                model: string;
            };
            id: string;
            status: import(".prisma/client").$Enums.TripStatus;
            driver: {
                id: string;
                firstName: string;
                lastName: string;
            };
            startLocation: string;
            endLocation: string;
            startTime: Date;
            endTime: Date | null;
            distance: number | null;
        }[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getAssignmentStatistics(): Promise<{
        totalAssignments: number;
        activeAssignments: number;
        completedAssignments: number;
        vehicleUtilizationRate: number;
        driverUtilizationRate: number;
    }>;
    checkResourceConflicts(vehicleId: string, driverId: string, startDate: Date, endDate?: Date): Promise<{
        hasVehicleConflict: boolean;
        hasDriverConflict: boolean;
        vehicleConflicts: {
            id: string;
            startDate: Date;
            endDate: Date | null;
        }[];
        driverConflicts: {
            id: string;
            startDate: Date;
            endDate: Date | null;
        }[];
    }>;
}
