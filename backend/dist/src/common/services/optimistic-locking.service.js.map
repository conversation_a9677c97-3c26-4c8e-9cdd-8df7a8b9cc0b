{"version": 3, "file": "optimistic-locking.service.js", "sourceRoot": "", "sources": ["../../../../src/common/services/optimistic-locking.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA+D;AAC/D,gEAA4D;AAYrD,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACN;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAKtD,KAAK,CAAC,UAAU,CACd,EAAU,EACV,UAAe,EACf,cAAsB,EACtB,MAAe;QAEf,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACnC,KAAK,EAAE;oBACL,EAAE;oBACF,OAAO,EAAE,cAAc;iBACxB;gBACD,IAAI,EAAE;oBACJ,GAAG,UAAU;oBACb,OAAO,EAAE;wBACP,SAAS,EAAE,CAAC;qBACb;oBACD,cAAc,EAAE,MAAM;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAE3B,MAAM,IAAI,0BAAiB,CACzB,6EAA6E,CAC9E,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,EAAU,EACV,UAAe,EACf,cAAsB,EACtB,MAAe;QAEf,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBACtC,KAAK,EAAE;oBACL,EAAE;iBAEH;gBACD,IAAI,EAAE;oBACJ,GAAG,UAAU;oBACb,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CACzB,8EAA8E,CAC/E,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,2BAA2B,CAC/B,KAAa,EACb,EAAU,EACV,UAAe,EACf,cAAsB,EACtB,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,aAAa,GAAI,IAAI,CAAC,MAAc,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,YAAY,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,MAAM,aAAa,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE;oBACL,EAAE;oBACF,OAAO,EAAE,cAAc;iBACxB;gBACD,IAAI,EAAE;oBACJ,GAAG,UAAU;oBACb,OAAO,EAAE;wBACP,SAAS,EAAE,CAAC;qBACb;oBACD,cAAc,EAAE,MAAM;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,0BAAiB,CACzB,6EAA6E,CAC9E,CAAC;YACJ,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,EAAU,EAAE,eAAuB;QACnE,IAAI,CAAC;YACH,MAAM,aAAa,GAAI,IAAI,CAAC,MAAc,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,YAAY,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC;gBAC5C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,eAAe,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,EAAU;QAC/C,IAAI,CAAC;YACH,MAAM,aAAa,GAAI,IAAI,CAAC,MAAc,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,YAAY,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC;gBAC5C,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,6BAA6B,CAAC,IAAS;QACrC,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,cAAc,EAAE,IAAI,CAAC,cAAc;SACpC,CAAC;IACJ,CAAC;CACF,CAAA;AAtKY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,wBAAwB,CAsKpC"}