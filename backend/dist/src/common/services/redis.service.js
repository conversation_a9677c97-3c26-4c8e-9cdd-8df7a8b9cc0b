"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const redis_1 = require("redis");
let RedisService = RedisService_1 = class RedisService {
    configService;
    logger = new common_1.Logger(RedisService_1.name);
    client;
    isConnected = false;
    constructor(configService) {
        this.configService = configService;
    }
    async onModuleInit() {
        await this.connect();
    }
    async onModuleDestroy() {
        await this.disconnect();
    }
    async connect() {
        try {
            const redisUrl = this.configService.get('REDIS_URL', 'redis://localhost:6379');
            this.client = (0, redis_1.createClient)({
                url: redisUrl,
                socket: {
                    connectTimeout: 5000,
                },
            });
            this.client.on('error', (error) => {
                this.logger.error('Redis connection error:', error);
                this.isConnected = false;
            });
            this.client.on('connect', () => {
                this.logger.log('Connected to Redis');
                this.isConnected = true;
            });
            this.client.on('disconnect', () => {
                this.logger.warn('Disconnected from Redis');
                this.isConnected = false;
            });
            await this.client.connect();
        }
        catch (error) {
            this.logger.error('Failed to connect to Redis:', error);
            this.isConnected = false;
        }
    }
    async disconnect() {
        if (this.client && this.isConnected) {
            await this.client.disconnect();
            this.isConnected = false;
        }
    }
    async get(key) {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping cache get');
            return null;
        }
        try {
            const value = await this.client.get(key);
            return value ? JSON.parse(value) : null;
        }
        catch (error) {
            this.logger.error(`Error getting cache key ${key}:`, error);
            return null;
        }
    }
    async set(key, value, ttlSeconds) {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping cache set');
            return false;
        }
        try {
            const serializedValue = JSON.stringify(value);
            if (ttlSeconds) {
                await this.client.setEx(key, ttlSeconds, serializedValue);
            }
            else {
                await this.client.set(key, serializedValue);
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Error setting cache key ${key}:`, error);
            return false;
        }
    }
    async del(key) {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping cache delete');
            return false;
        }
        try {
            await this.client.del(key);
            return true;
        }
        catch (error) {
            this.logger.error(`Error deleting cache key ${key}:`, error);
            return false;
        }
    }
    async delPattern(pattern) {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping pattern delete');
            return false;
        }
        try {
            const keys = await this.client.keys(pattern);
            if (keys.length > 0) {
                await this.client.del(keys);
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Error deleting cache pattern ${pattern}:`, error);
            return false;
        }
    }
    async exists(key) {
        if (!this.isConnected) {
            return false;
        }
        try {
            const result = await this.client.exists(key);
            return result === 1;
        }
        catch (error) {
            this.logger.error(`Error checking cache key existence ${key}:`, error);
            return false;
        }
    }
    async expire(key, ttlSeconds) {
        if (!this.isConnected) {
            return false;
        }
        try {
            await this.client.expire(key, ttlSeconds);
            return true;
        }
        catch (error) {
            this.logger.error(`Error setting TTL for key ${key}:`, error);
            return false;
        }
    }
    async incr(key) {
        if (!this.isConnected) {
            return null;
        }
        try {
            return await this.client.incr(key);
        }
        catch (error) {
            this.logger.error(`Error incrementing key ${key}:`, error);
            return null;
        }
    }
    getClient() {
        return this.isConnected ? this.client : null;
    }
    async healthCheck() {
        try {
            if (!this.isConnected) {
                return { status: 'disconnected', connected: false };
            }
            await this.client.ping();
            return { status: 'healthy', connected: true };
        }
        catch (error) {
            this.logger.error('Redis health check failed:', error);
            return { status: 'unhealthy', connected: false };
        }
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
//# sourceMappingURL=redis.service.js.map