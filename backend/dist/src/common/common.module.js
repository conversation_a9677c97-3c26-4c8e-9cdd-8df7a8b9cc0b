"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const health_module_1 = require("../health/health.module");
const logger_service_1 = require("./services/logger.service");
const redis_service_1 = require("./services/redis.service");
const optimistic_locking_service_1 = require("./services/optimistic-locking.service");
const rate_limit_guard_1 = require("./guards/rate-limit.guard");
const logging_interceptor_1 = require("./interceptors/logging.interceptor");
const cache_interceptor_1 = require("./interceptors/cache.interceptor");
const performance_interceptor_1 = require("./interceptors/performance.interceptor");
const realtime_gateway_1 = require("./gateways/realtime.gateway");
const global_exception_filter_1 = require("./filters/global-exception.filter");
let CommonModule = class CommonModule {
};
exports.CommonModule = CommonModule;
exports.CommonModule = CommonModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [config_1.ConfigModule, health_module_1.HealthModule],
        providers: [
            logger_service_1.LoggerService,
            redis_service_1.RedisService,
            optimistic_locking_service_1.OptimisticLockingService,
            rate_limit_guard_1.RateLimitGuard,
            logging_interceptor_1.LoggingInterceptor,
            cache_interceptor_1.CacheInterceptor,
            performance_interceptor_1.PerformanceInterceptor,
            realtime_gateway_1.RealtimeGateway,
            global_exception_filter_1.GlobalExceptionFilter,
        ],
        exports: [
            logger_service_1.LoggerService,
            redis_service_1.RedisService,
            optimistic_locking_service_1.OptimisticLockingService,
            rate_limit_guard_1.RateLimitGuard,
            logging_interceptor_1.LoggingInterceptor,
            cache_interceptor_1.CacheInterceptor,
            performance_interceptor_1.PerformanceInterceptor,
            realtime_gateway_1.RealtimeGateway,
            global_exception_filter_1.GlobalExceptionFilter,
        ],
    })
], CommonModule);
//# sourceMappingURL=common.module.js.map