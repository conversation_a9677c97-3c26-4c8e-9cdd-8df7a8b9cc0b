"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseFormatter = void 0;
class ResponseFormatter {
    static success(data, message, meta, requestId) {
        return {
            success: true,
            data,
            message,
            meta,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static error(message, errors, requestId) {
        return {
            success: false,
            data: null,
            message,
            errors,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static paginated(data, page, limit, total, message, requestId) {
        const totalPages = Math.ceil(total / limit);
        const hasNext = page < totalPages;
        const hasPrev = page > 1;
        return {
            success: true,
            data,
            message,
            meta: {
                page,
                limit,
                total,
                totalPages,
                hasNext,
                hasPrev,
            },
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static validationError(errors, requestId) {
        return {
            success: false,
            data: null,
            message: 'Validation failed',
            errors,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static notFound(resource, requestId) {
        return {
            success: false,
            data: null,
            message: `${resource} not found`,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static unauthorized(message = 'Unauthorized access', requestId) {
        return {
            success: false,
            data: null,
            message,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static forbidden(message = 'Access forbidden', requestId) {
        return {
            success: false,
            data: null,
            message,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static created(data, message = 'Resource created successfully', requestId) {
        return {
            success: true,
            data,
            message,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static updated(data, message = 'Resource updated successfully', requestId) {
        return {
            success: true,
            data,
            message,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
    static deleted(message = 'Resource deleted successfully', requestId) {
        return {
            success: true,
            data: null,
            message,
            timestamp: new Date().toISOString(),
            requestId,
        };
    }
}
exports.ResponseFormatter = ResponseFormatter;
//# sourceMappingURL=response-formatter.util.js.map