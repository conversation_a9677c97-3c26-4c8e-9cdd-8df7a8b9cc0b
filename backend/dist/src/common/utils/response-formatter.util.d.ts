export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: string[];
    meta?: {
        page?: number;
        limit?: number;
        total?: number;
        totalPages?: number;
        hasNext?: boolean;
        hasPrev?: boolean;
    };
    timestamp: string;
    requestId?: string;
}
export declare class ResponseFormatter {
    static success<T>(data: T, message?: string, meta?: ApiResponse<T>['meta'], requestId?: string): ApiResponse<T>;
    static error(message: string, errors?: string[], requestId?: string): ApiResponse<null>;
    static paginated<T>(data: T[], page: number, limit: number, total: number, message?: string, requestId?: string): ApiResponse<T[]>;
    static validationError(errors: string[], requestId?: string): ApiResponse<null>;
    static notFound(resource: string, requestId?: string): ApiResponse<null>;
    static unauthorized(message?: string, requestId?: string): ApiResponse<null>;
    static forbidden(message?: string, requestId?: string): ApiResponse<null>;
    static created<T>(data: T, message?: string, requestId?: string): ApiResponse<T>;
    static updated<T>(data: T, message?: string, requestId?: string): ApiResponse<T>;
    static deleted(message?: string, requestId?: string): ApiResponse<null>;
}
