{"version": 3, "file": "rate-limit.guard.js", "sourceRoot": "", "sources": ["../../../../src/common/guards/rate-limit.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAOwB;AACxB,uCAAyC;AACzC,+DAA2D;AAC3D,6DAAyD;AAgBzD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAyB,CAAC;AAE1C,QAAA,cAAc,GAAG,WAAW,CAAC;AAGnC,MAAM,SAAS,GAAG,CAAC,OAAyB,EAAE,EAAE,CACrD,IAAA,oBAAW,EAAC,sBAAc,EAAE,OAAO,CAAC,CAAC;AAD1B,QAAA,SAAS,aACiB;AAGhC,IAAM,cAAc,GAApB,MAAM,cAAc;IAEN;IACA;IACA;IAHnB,YACmB,SAAoB,EACpB,MAAqB,EACrB,YAA0B;QAF1B,cAAS,GAAT,SAAS,CAAW;QACpB,WAAM,GAAN,MAAM,CAAe;QACrB,iBAAY,GAAZ,YAAY,CAAc;IAC1C,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACzC,sBAAc,EACd,OAAO,CACR,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACxD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAC3C,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC;QAEjD,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEjE,IAAI,aAAa,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;gBACvC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;gBAGxE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;oBAClD,GAAG;oBACH,KAAK,EAAE,aAAa,CAAC,KAAK;oBAC1B,WAAW;oBACX,aAAa;oBACb,QAAQ;iBACT,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;gBAEjC,MAAM,IAAI,sBAAa,CACrB;oBACE,UAAU,EAAE,mBAAU,CAAC,iBAAiB;oBACxC,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,mBAAmB;oBAC1B,UAAU,EAAE,aAAa;iBAC1B,EACD,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;YACJ,CAAC;YAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAW,EAAE,QAAgB;QAC1D,MAAM,QAAQ,GAAG,cAAc,GAAG,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAgB,QAAQ,CAAC,CAAC;YAElE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;gBAEnC,MAAM,OAAO,GAAkB;oBAC7B,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;iBAC1B,CAAC;gBACF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;gBAC3E,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,GAAW,EAAE,QAAgB;QAC5D,MAAM,QAAQ,GAAG,cAAc,GAAG,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAgB,QAAQ,CAAC,CAAC;YAElE,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;gBACjC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,OAAY,EAAE,gBAAkC;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QACxD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAGpD,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;QAExC,IAAI,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;YACrD,aAAa,GAAG;gBACd,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,QAAQ;aAC3C,CAAC;YACF,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,aAAa,CAAC,KAAK,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAExE,MAAM,IAAI,sBAAa,CACrB;gBACE,UAAU,EAAE,mBAAU,CAAC,iBAAiB;gBACxC,OAAO,EAAE,qBAAqB;gBAC9B,KAAK,EAAE,mBAAmB;gBAC1B,UAAU,EAAE,aAAa;aAC1B,EACD,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;QACJ,CAAC;QAED,aAAa,CAAC,KAAK,EAAE,CAAC;QACtB,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,WAAW,CAAC,OAAY,EAAE,OAAyB;QACzD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAGD,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC;QAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,WAAW,CAAC;QAC/C,OAAO,GAAG,EAAE,IAAI,MAAM,EAAE,CAAC;IAC3B,CAAC;IAEO,qBAAqB,CAAC,WAAmB;QAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,SAAS,IAAI,GAAG,EAAE,CAAC;gBAC1B,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,oBAAoB,CAAC,GAAW,EAAE,WAAmB;QAC1D,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC1C,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAGD,MAAM,CAAC,YAAY,CAAC,GAAW;QAC7B,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF,CAAA;AA/KY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;yDAGmB,gBAAS,oBAAT,gBAAS,gCACZ,8BAAa;QACP,4BAAY;GAJlC,cAAc,CA+K1B"}