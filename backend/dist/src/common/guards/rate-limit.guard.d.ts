import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { LoggerService } from '../services/logger.service';
import { RedisService } from '../services/redis.service';
interface RateLimitOptions {
    windowMs: number;
    maxRequests: number;
    keyGenerator?: (request: any) => string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
}
export declare const RATE_LIMIT_KEY = "rateLimit";
export declare const RateLimit: (options: RateLimitOptions) => any;
export declare class RateLimitGuard implements CanActivate {
    private readonly reflector;
    private readonly logger;
    private readonly redisService;
    constructor(reflector: Reflector, logger: LoggerService, redisService: RedisService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private getRateLimitData;
    private incrementRateLimit;
    private fallbackRateLimit;
    private generateKey;
    private cleanupExpiredEntries;
    static getRemainingRequests(key: string, maxRequests: number): number;
    static getResetTime(key: string): number | null;
}
export {};
