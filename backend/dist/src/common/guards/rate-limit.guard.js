"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitGuard = exports.RateLimit = exports.RATE_LIMIT_KEY = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const logger_service_1 = require("../services/logger.service");
const redis_service_1 = require("../services/redis.service");
const requestCounts = new Map();
exports.RATE_LIMIT_KEY = 'rateLimit';
const RateLimit = (options) => (0, common_1.SetMetadata)(exports.RATE_LIMIT_KEY, options);
exports.RateLimit = RateLimit;
let RateLimitGuard = class RateLimitGuard {
    reflector;
    logger;
    redisService;
    constructor(reflector, logger, redisService) {
        this.reflector = reflector;
        this.logger = logger;
        this.redisService = redisService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const handler = context.getHandler();
        const rateLimitOptions = this.reflector.get(exports.RATE_LIMIT_KEY, handler);
        if (!rateLimitOptions) {
            return true;
        }
        const key = this.generateKey(request, rateLimitOptions);
        const now = Date.now();
        const windowMs = rateLimitOptions.windowMs;
        const maxRequests = rateLimitOptions.maxRequests;
        try {
            const rateLimitData = await this.getRateLimitData(key, windowMs);
            if (rateLimitData.count >= maxRequests) {
                const remainingTime = Math.ceil((rateLimitData.resetTime - now) / 1000);
                this.logger.logSecurityEvent('rate_limit_exceeded', {
                    key,
                    count: rateLimitData.count,
                    maxRequests,
                    remainingTime,
                    windowMs,
                }, request.user?.id, request.ip);
                throw new common_1.HttpException({
                    statusCode: common_1.HttpStatus.TOO_MANY_REQUESTS,
                    message: 'Rate limit exceeded',
                    error: 'Too Many Requests',
                    retryAfter: remainingTime,
                }, common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
            await this.incrementRateLimit(key, windowMs);
            return true;
        }
        catch (error) {
            this.logger.warn('Redis rate limiting failed, falling back to in-memory', error);
            return this.fallbackRateLimit(request, rateLimitOptions);
        }
    }
    async getRateLimitData(key, windowMs) {
        const redisKey = `rate_limit:${key}`;
        const now = Date.now();
        try {
            const data = await this.redisService.get(redisKey);
            if (!data || data.resetTime <= now) {
                const newData = {
                    count: 0,
                    resetTime: now + windowMs,
                };
                await this.redisService.set(redisKey, newData, Math.ceil(windowMs / 1000));
                return newData;
            }
            return data;
        }
        catch (error) {
            throw error;
        }
    }
    async incrementRateLimit(key, windowMs) {
        const redisKey = `rate_limit:${key}`;
        const now = Date.now();
        try {
            const data = await this.redisService.get(redisKey);
            if (data && data.resetTime > now) {
                data.count++;
                await this.redisService.set(redisKey, data, Math.ceil((data.resetTime - now) / 1000));
            }
        }
        catch (error) {
            throw error;
        }
    }
    fallbackRateLimit(request, rateLimitOptions) {
        const key = this.generateKey(request, rateLimitOptions);
        const now = Date.now();
        const windowStart = now - rateLimitOptions.windowMs;
        this.cleanupExpiredEntries(windowStart);
        let rateLimitData = requestCounts.get(key);
        if (!rateLimitData || rateLimitData.resetTime <= now) {
            rateLimitData = {
                count: 1,
                resetTime: now + rateLimitOptions.windowMs,
            };
            requestCounts.set(key, rateLimitData);
            return true;
        }
        if (rateLimitData.count >= rateLimitOptions.maxRequests) {
            const remainingTime = Math.ceil((rateLimitData.resetTime - now) / 1000);
            throw new common_1.HttpException({
                statusCode: common_1.HttpStatus.TOO_MANY_REQUESTS,
                message: 'Rate limit exceeded',
                error: 'Too Many Requests',
                retryAfter: remainingTime,
            }, common_1.HttpStatus.TOO_MANY_REQUESTS);
        }
        rateLimitData.count++;
        requestCounts.set(key, rateLimitData);
        return true;
    }
    generateKey(request, options) {
        if (options.keyGenerator) {
            return options.keyGenerator(request);
        }
        const ip = request.ip || request.connection.remoteAddress;
        const userId = request.user?.id || 'anonymous';
        return `${ip}:${userId}`;
    }
    cleanupExpiredEntries(windowStart) {
        const now = Date.now();
        for (const [key, data] of requestCounts.entries()) {
            if (data.resetTime <= now) {
                requestCounts.delete(key);
            }
        }
    }
    static getRemainingRequests(key, maxRequests) {
        const data = requestCounts.get(key);
        if (!data || data.resetTime <= Date.now()) {
            return maxRequests;
        }
        return Math.max(0, maxRequests - data.count);
    }
    static getResetTime(key) {
        const data = requestCounts.get(key);
        if (!data || data.resetTime <= Date.now()) {
            return null;
        }
        return data.resetTime;
    }
};
exports.RateLimitGuard = RateLimitGuard;
exports.RateLimitGuard = RateLimitGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object, logger_service_1.LoggerService,
        redis_service_1.RedisService])
], RateLimitGuard);
//# sourceMappingURL=rate-limit.guard.js.map