{"version": 3, "file": "global-exception.filter.js", "sourceRoot": "", "sources": ["../../../../src/common/filters/global-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA8G;AAE9G,2CAAwC;AACxC,+DAA2D;AAC3D,uEAMyC;AASlC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IACH;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAE1C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,IAAI,MAAM,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC9C,IAAI,OAAO,GAAG,uBAAuB,CAAC;QACtC,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,OAA2B,CAAC;QAGhC,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC1C,OAAO,GAAG,iBAAiB,CAAC;YAC9B,CAAC;iBAAM,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBACjD,MAAM,IAAI,GAAG,iBAAwB,CAAC;gBACtC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC;gBAClC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YAC1E,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;YACrE,MAAM,GAAG,mBAAU,CAAC,WAAW,CAAC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACjD,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YACzB,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QAC/B,CAAC;aAAM,IAAI,SAAS,YAAY,eAAM,CAAC,2BAA2B,EAAE,CAAC;YACnE,MAAM,GAAG,mBAAU,CAAC,WAAW,CAAC;YAChC,OAAO,GAAG,uBAAuB,CAAC;YAClC,MAAM,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,CAAC;aAAM,IAAI,SAAS,YAAY,0CAAsB,EAAE,CAAC;YACvD,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAC9B,CAAC;aAAM,IAAI,SAAS,YAAY,uCAAmB,EAAE,CAAC;YACpD,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAS,CAAC;YAChD,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;YAChD,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAC7B,CAAC;aAAM,IAAI,SAAS,YAAY,qCAAiB,EAAE,CAAC;YAClD,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAC9B,CAAC;aAAM,IAAI,SAAS,YAAY,6CAAyB,EAAE,CAAC;YAC1D,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAC9B,CAAC;aAAM,IAAI,SAAS,YAAY,qCAAiB,EAAE,CAAC;YAClD,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAS,CAAC;YAChD,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,2BAA2B,CAAC;YAC1D,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;QACjF,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YACtC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC;YAC9F,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,wBAAwB,CAAC;YACnC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,QAAQ,MAAM,WAAW,OAAO,EAAE,EAClC,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,EAC9D;YACE,SAAS;YACT,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CACF,CAAC;QAGF,IAAI,MAAM,KAAK,mBAAU,CAAC,YAAY,IAAI,MAAM,KAAK,mBAAU,CAAC,SAAS,EAAE,CAAC;YAC1E,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;gBACrD,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;gBACpC,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,MAAM;YAClB,OAAO;YACP,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;YAC3B,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YACpC,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC;QAGF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,OAAO,aAAa,CAAC,OAAO,CAAC;QAC/B,CAAC;QAED,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEO,iBAAiB,CAAC,SAA+C;QACvE,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,+CAA+C;oBACxD,MAAM,EAAE,CAAC,yCAAyC,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;iBAC5E,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,yEAAyE;oBAClF,MAAM,EAAE,CAAC,kCAAkC,CAAC;iBAC7C,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,+BAA+B;oBACxC,MAAM,EAAE,CAAC,2CAA2C,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;iBAClF,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,kBAAkB;oBAC3B,MAAM,EAAE,CAAC,qCAAqC,CAAC;iBAChD,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,2BAA2B;oBACpC,MAAM,EAAE,CAAC,0CAA0C,SAAS,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;iBAClF,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,uBAAuB;oBAChC,MAAM,EAAE,CAAC,wCAAwC,CAAC;iBACnD,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,mBAAmB;oBAC5B,MAAM,EAAE,CAAC,wCAAwC,SAAS,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC;iBACnF,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,0BAA0B;oBACnC,MAAM,EAAE,CAAC,qCAAqC,CAAC;iBAChD,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,4BAA4B;oBACrC,MAAM,EAAE,CAAC,2CAA2C,CAAC;iBACtD,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,uBAAuB;oBAChC,MAAM,EAAE,CAAC,4CAA4C,CAAC;iBACvD,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,sCAAsC;oBAC/C,MAAM,EAAE,CAAC,+CAA+C,CAAC;iBAC1D,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,aAAa;oBACtB,MAAM,EAAE,CAAC,kCAAkC,CAAC;iBAC7C,CAAC;YACJ,KAAK,OAAO;gBACV,OAAO;oBACL,OAAO,EAAE,oBAAoB;oBAC7B,MAAM,EAAE,CAAC,iCAAiC,CAAC;iBAC5C,CAAC;YACJ;gBACE,OAAO;oBACL,OAAO,EAAE,2BAA2B;oBACpC,MAAM,EAAE,CAAC,mBAAmB,SAAS,CAAC,IAAI,EAAE,CAAC;iBAC9C,CAAC;QACN,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;CACF,CAAA;AA3LY,sDAAqB;gCAArB,qBAAqB;IAFjC,IAAA,mBAAU,GAAE;IACZ,IAAA,cAAK,GAAE;qCAE+B,8BAAa;GADvC,qBAAqB,CA2LjC"}