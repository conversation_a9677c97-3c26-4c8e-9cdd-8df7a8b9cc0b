"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const logger_service_1 = require("../services/logger.service");
const custom_exceptions_1 = require("../exceptions/custom-exceptions");
let GlobalExceptionFilter = class GlobalExceptionFilter {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = this.generateRequestId();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let errors = [];
        let details;
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else if (typeof exceptionResponse === 'object') {
                const resp = exceptionResponse;
                message = resp.message || message;
                errors = Array.isArray(resp.message) ? resp.message : resp.errors || [];
            }
        }
        else if (exception instanceof client_1.Prisma.PrismaClientKnownRequestError) {
            status = common_1.HttpStatus.BAD_REQUEST;
            const result = this.handlePrismaError(exception);
            message = result.message;
            errors = result.errors || [];
        }
        else if (exception instanceof client_1.Prisma.PrismaClientValidationError) {
            status = common_1.HttpStatus.BAD_REQUEST;
            message = 'Invalid data provided';
            errors = ['Validation failed for the provided data'];
        }
        else if (exception instanceof custom_exceptions_1.BusinessLogicException) {
            status = exception.getStatus();
            message = exception.message;
        }
        else if (exception instanceof custom_exceptions_1.ValidationException) {
            status = exception.getStatus();
            const response = exception.getResponse();
            message = response.message || exception.message;
            details = response.details;
        }
        else if (exception instanceof custom_exceptions_1.ConflictException) {
            status = exception.getStatus();
            message = exception.message;
        }
        else if (exception instanceof custom_exceptions_1.ResourceNotFoundException) {
            status = exception.getStatus();
            message = exception.message;
        }
        else if (exception instanceof custom_exceptions_1.DatabaseException) {
            status = exception.getStatus();
            const response = exception.getResponse();
            message = response.message || 'Database operation failed';
            details = process.env.NODE_ENV === 'production' ? undefined : response.details;
        }
        else if (exception instanceof Error) {
            message = process.env.NODE_ENV === 'production' ? 'Internal server error' : exception.message;
            details = exception.message;
        }
        else {
            message = 'Unknown error occurred';
            details = String(exception);
        }
        this.logger.error(`HTTP ${status} Error: ${message}`, exception instanceof Error ? exception.stack : 'Unknown error', {
            requestId,
            method: request.method,
            url: request.url,
            userAgent: request.get('user-agent'),
            ip: request.ip,
            body: request.body,
            query: request.query,
            params: request.params
        });
        if (status === common_1.HttpStatus.UNAUTHORIZED || status === common_1.HttpStatus.FORBIDDEN) {
            this.logger.logSecurityEvent('AUTHENTICATION_FAILURE', {
                ip: request.ip,
                userAgent: request.get('user-agent'),
                url: request.url,
                method: request.method
            });
        }
        const errorResponse = {
            success: false,
            statusCode: status,
            message,
            ...(details && { details }),
            ...(errors.length > 0 && { errors }),
            requestId,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method
        };
        if (process.env.NODE_ENV === 'production') {
            delete errorResponse.details;
        }
        response.status(status).json(errorResponse);
    }
    handlePrismaError(exception) {
        switch (exception.code) {
            case 'P2002':
                return {
                    message: 'A record with this information already exists',
                    errors: [`Unique constraint violation on field: ${exception.meta?.target}`]
                };
            case 'P2014':
                return {
                    message: 'The change you are trying to make would violate a required relationship',
                    errors: ['Foreign key constraint violation']
                };
            case 'P2003':
                return {
                    message: 'Foreign key constraint failed',
                    errors: [`Foreign key constraint failed on field: ${exception.meta?.field_name}`]
                };
            case 'P2025':
                return {
                    message: 'Record not found',
                    errors: ['The requested record does not exist']
                };
            case 'P2000':
                return {
                    message: 'Value too long for column',
                    errors: [`Value provided is too long for column: ${exception.meta?.column_name}`]
                };
            case 'P2001':
                return {
                    message: 'Record does not exist',
                    errors: ['The record searched for does not exist']
                };
            case 'P2004':
                return {
                    message: 'Constraint failed',
                    errors: [`A constraint failed on the database: ${exception.meta?.database_error}`]
                };
            case 'P2015':
                return {
                    message: 'Related record not found',
                    errors: ['A related record could not be found']
                };
            case 'P2016':
                return {
                    message: 'Query interpretation error',
                    errors: ['There was an error interpreting the query']
                };
            case 'P2017':
                return {
                    message: 'Records not connected',
                    errors: ['The records for relation are not connected']
                };
            case 'P2018':
                return {
                    message: 'Required connected records not found',
                    errors: ['The required connected records were not found']
                };
            case 'P2019':
                return {
                    message: 'Input error',
                    errors: ['Input error in the provided data']
                };
            case 'P2020':
                return {
                    message: 'Value out of range',
                    errors: ['Value out of range for the type']
                };
            default:
                return {
                    message: 'Database operation failed',
                    errors: [`Database error: ${exception.code}`]
                };
        }
    }
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = __decorate([
    (0, common_1.Injectable)(),
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map