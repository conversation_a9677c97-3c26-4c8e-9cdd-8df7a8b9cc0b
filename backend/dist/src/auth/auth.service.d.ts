import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
export declare class AuthService {
    private readonly prisma;
    private readonly jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    validateUser(email: string, password: string): unknown;
    login(email: string, password: string): unknown;
    register(email: string, password: string, firstName: string, lastName: string): unknown;
}
