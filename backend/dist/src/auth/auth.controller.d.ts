import { AuthService } from './auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: {
        email: string;
        password: string;
    }): unknown;
    register(registerDto: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
    }): unknown;
    getProfile(req: any): unknown;
}
