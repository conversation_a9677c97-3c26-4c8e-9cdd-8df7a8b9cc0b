import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
declare const JwtStrategy_base: new (...args: any) => any;
export declare class JwtStrategy extends JwtStrategy_base {
    private readonly configService;
    private readonly prisma;
    constructor(configService: ConfigService, prisma: PrismaService);
    validate(payload: {
        sub: string;
        email: string;
    }): Promise<{
        id: string;
        status: string | null;
        createdAt: Date;
        updatedAt: Date;
        email: string;
        passwordHash: string;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        phone: string | null;
        licenseNumber: string | null;
        licenseType: string | null;
        licenseExpiry: Date | null;
        licenseRestrictions: string | null;
        address: string | null;
        emergencyContactName: string | null;
        emergencyContactPhone: string | null;
        hireDate: Date | null;
        notes: string | null;
    }>;
}
export {};
