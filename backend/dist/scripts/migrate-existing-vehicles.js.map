{"version": 3, "file": "migrate-existing-vehicles.js", "sourceRoot": "", "sources": ["../../scripts/migrate-existing-vehicles.ts"], "names": [], "mappings": ";;AAkGS,0DAAuB;AAlGhC,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC,KAAK,UAAU,uBAAuB;IACpC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAChD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,WAAW,EAAE,IAAI;gBACjB,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;gBACX,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,WAAW,CAAC,MAAM,+BAA+B,CAAC,CAAC;QAIxE,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACzD,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC;YAC9C,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YACvD,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,KAAK,KAAK,WAAW,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAGH,MAAM,qBAAqB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAEtE,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,iBAAiB,qBAAqB,CAAC,MAAM,4BAA4B,CAAC,CAAC;YAEvF,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACnD,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,EAAE,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;qBACzC;iBACF;gBACD,IAAI,EAAE;oBACJ,WAAW,EAAE,OAAO;iBACrB;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,2BAA2B,YAAY,CAAC,KAAK,yBAAyB,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAC7E,CAAC;QAGD,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YACvC,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,KAAK,YAAY,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;YAC/C,EAAE,EAAE,CAAC,aAAa,CAAC;YACnB,MAAM,EAAE;gBACN,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW,WAAW,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,KAAK,CAAC;IACd,CAAC;YAAS,CAAC;QACT,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,uBAAuB,EAAE;SACtB,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}