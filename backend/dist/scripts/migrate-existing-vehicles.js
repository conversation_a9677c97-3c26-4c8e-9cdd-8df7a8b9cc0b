"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateExistingVehicles = migrateExistingVehicles;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function migrateExistingVehicles() {
    console.log('🚛 Starting vehicle type migration...');
    try {
        const allVehicles = await prisma.vehicle.findMany({
            select: {
                id: true,
                plateNumber: true,
                make: true,
                model: true,
                vehicleType: true,
            }
        });
        console.log(`Found ${allVehicles.length} total vehicles in the system`);
        const vehiclesByType = allVehicles.reduce((acc, vehicle) => {
            const type = vehicle.vehicleType || 'UNKNOWN';
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {});
        console.log('\n📊 Current vehicle type distribution:');
        Object.entries(vehiclesByType).forEach(([type, count]) => {
            console.log(`  - ${type}: ${count} vehicles`);
        });
        const vehiclesNeedingUpdate = allVehicles.filter(v => !v.vehicleType);
        if (vehiclesNeedingUpdate.length > 0) {
            console.log(`\n🔄 Updating ${vehiclesNeedingUpdate.length} vehicles to TRUCK type...`);
            const updateResult = await prisma.vehicle.updateMany({
                where: {
                    id: {
                        in: vehiclesNeedingUpdate.map(v => v.id)
                    }
                },
                data: {
                    vehicleType: 'TRUCK'
                }
            });
            console.log(`✅ Successfully migrated ${updateResult.count} vehicles to TRUCK type`);
        }
        else {
            console.log('\n✅ All vehicles already have proper vehicle types assigned');
        }
        if (vehiclesNeedingUpdate.length > 0) {
            console.log('\n📋 Migrated vehicles:');
            vehiclesNeedingUpdate.forEach(vehicle => {
                console.log(`  - ${vehicle.plateNumber} (${vehicle.make} ${vehicle.model}) -> TRUCK`);
            });
        }
        const finalCounts = await prisma.vehicle.groupBy({
            by: ['vehicleType'],
            _count: {
                vehicleType: true
            }
        });
        console.log('\n📊 Final vehicle type distribution:');
        finalCounts.forEach(count => {
            console.log(`  - ${count.vehicleType}: ${count._count.vehicleType} vehicles`);
        });
    }
    catch (error) {
        console.error('❌ Error during migration:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
if (require.main === module) {
    migrateExistingVehicles()
        .then(() => {
        console.log('🎉 Migration completed successfully!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Migration failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=migrate-existing-vehicles.js.map