const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    // Check vehicle assignments
    const assignments = await prisma.vehicleAssignment.findMany()
    console.log(`Found ${assignments.length} vehicle assignments:`)
    console.log(JSON.stringify(assignments, null, 2))
    
    // Check vehicles
    const vehicles = await prisma.vehicle.findMany()
    console.log(`Found ${vehicles.length} vehicles:`)
    console.log(JSON.stringify(vehicles.slice(0, 2), null, 2))
    
    // Check drivers
    const drivers = await prisma.user.findMany({
      where: { role: 'DRIVER' }
    })
    console.log(`Found ${drivers.length} drivers:`)
    console.log(JSON.stringify(drivers.slice(0, 2), null, 2))

    // Create a test assignment if none exist
    if (assignments.length === 0 && vehicles.length > 0 && drivers.length > 0) {
      const newAssignment = await prisma.vehicleAssignment.create({
        data: {
          vehicleId: vehicles[0].id,
          driverId: drivers[0].id,
          startDate: new Date(),
          status: 'ACTIVE',
        }
      })
      console.log('Created test assignment:', newAssignment)
    }
  } catch (err) {
    console.error('Error:', err)
  } finally {
    await prisma.$disconnect()
  }
}

main()
