generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String              @id @default(cuid())
  email                 String              @unique
  passwordHash          String
  firstName             String
  lastName              String
  role                  UserRole            @default(DRIVER)
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  phone                 String?
  licenseNumber         String?
  licenseType           String?
  licenseExpiry         DateTime?
  licenseRestrictions   String?
  address               String?
  emergencyContactName  String?
  emergencyContactPhone String?
  hireDate              DateTime?
  notes                 String?
  status                String?             @default("Active")
  trips                 Trip[]
  assignments           VehicleAssignment[]
  fuelRecords           FuelRecord[]
  fuelStats             VehicleFuelStats[]
}

model Vehicle {
  id                 String                   @id @default(cuid())
  plateNumber        String                   @unique
  make               String
  model              String
  year               Int
  status             VehicleStatus            @default(AVAILABLE)
  lastMaintenance    DateTime?
  createdAt          DateTime                 @default(now())
  updatedAt          DateTime                 @updatedAt
  color              String?
  fuelType           String?
  mileage            Int?
  purchaseDate       DateTime?
  vin                String?
  axleConfiguration  String?
  cabConfiguration   String?
  cargoCapacity      Float?
  engineType         String?
  fuelCapacity       Float?
  hasRefrigeration   Boolean                  @default(false)
  height             Float?
  length             Float?
  maxWeight          Float?
  trailerType        TrailerType?
  transmission       String?
  vehicleType        VehicleType              @default(TRUCK)
  width              Float?
  insurancePolicies  InsurancePolicy[]
  maintenanceLogs    MaintenanceLog[]
  trips              Trip[]
  trailerTrips       Trip[]                   @relation("TripTrailer")
  trailerAssignments TruckTrailerAssignment[] @relation("TrailerAssignments")
  truckAssignments   TruckTrailerAssignment[] @relation("TruckAssignments")
  assignments        VehicleAssignment[]
  reviews            VehicleReview[]
  fuelRecords        FuelRecord[]
  fuelStats          VehicleFuelStats[]
}

model VehicleAssignment {
  id            String             @id @default(cuid())
  vehicleId     String
  driverId      String
  startDate     DateTime
  endDate       DateTime?
  status        AssignmentStatus   @default(ACTIVE)
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  approvedAt    DateTime?
  approvedBy    String?
  locationEnd   String?
  locationStart String?
  mileage       Float?
  notes         String?
  priority      AssignmentPriority @default(NORMAL)
  reason        String?
  type          AssignmentType     @default(REGULAR)
  trips         Trip[]             @relation("AssignmentTrips")
  driver        User               @relation(fields: [driverId], references: [id])
  vehicle       Vehicle            @relation(fields: [vehicleId], references: [id])

  @@index([driverId, startDate, endDate])
  @@index([vehicleId, startDate, endDate])
}

model TruckTrailerAssignment {
  id         String           @id @default(cuid())
  truckId    String
  trailerId  String
  startDate  DateTime
  endDate    DateTime?
  status     AssignmentStatus @default(ACTIVE)
  notes      String?
  assignedBy String
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  trailer    Vehicle          @relation("TrailerAssignments", fields: [trailerId], references: [id])
  truck      Vehicle          @relation("TruckAssignments", fields: [truckId], references: [id])

  @@unique([truckId, trailerId, startDate])
  @@index([truckId, status])
  @@index([trailerId, status])
}

model Trip {
  id                String             @id @default(cuid())
  driverId          String
  startLocation     String
  endLocation       String
  startTime         DateTime
  endTime           DateTime?
  status            TripStatus         @default(SCHEDULED)
  distance          Float?
  notes             String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  version           Int                @default(1) // Optimistic locking
  lastModifiedBy    String?            // User who last modified
  actualDuration    Int?
  assignmentId      String?
  cargo             String?
  cargoWeight       Float?
  estimatedDuration Int?
  priority          TripPriority       @default(NORMAL)
  purpose           String?
  type              TripType           @default(DELIVERY)
  vehicleId         String
  trailerId         String?
  // Business Partners Integration
  pickupPartnerId   String?
  deliveryPartnerId String?
  pickupLocationId  String?
  deliveryLocationId String?
  assignment        VehicleAssignment? @relation("AssignmentTrips", fields: [assignmentId], references: [id])
  driver            User               @relation(fields: [driverId], references: [id])
  vehicle           Vehicle            @relation(fields: [vehicleId], references: [id])
  trailer           Vehicle?           @relation("TripTrailer", fields: [trailerId], references: [id])
  // Business Partners Relationships
  pickupPartner     BusinessPartner?   @relation("PickupPartner", fields: [pickupPartnerId], references: [id])
  deliveryPartner   BusinessPartner?   @relation("DeliveryPartner", fields: [deliveryPartnerId], references: [id])
  pickupLocation    PartnerLocation?   @relation("PickupLocation", fields: [pickupLocationId], references: [id])
  deliveryLocation  PartnerLocation?   @relation("DeliveryLocation", fields: [deliveryLocationId], references: [id])
  expenses          TripExpense[]
  stops             TripStop[]
}

model TripStop {
  id            String         @id @default(cuid())
  tripId        String
  location      String
  arrivalTime   DateTime?
  departureTime DateTime?
  purpose       String?
  status        TripStopStatus @default(PENDING)
  notes         String?
  sequence      Int
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  trip          Trip           @relation(fields: [tripId], references: [id])
}

model TripExpense {
  id          String      @id @default(cuid())
  tripId      String
  type        ExpenseType
  amount      Float
  description String?
  receiptUrl  String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  trip        Trip        @relation(fields: [tripId], references: [id])
}

model MaintenanceLog {
  id                     String              @id @default(cuid())
  vehicleId              String
  type                   MaintenanceType
  description            String
  cost                   Float?
  date                   DateTime?
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt
  category               MaintenanceCategory
  laborCost              Float?
  mileage                Int?
  nextMaintenanceDate    DateTime?
  nextMaintenanceMileage Int?
  notes                  String?
  partsCost              Float?
  scheduledDate          DateTime
  status                 MaintenanceStatus   @default(SCHEDULED)
  technician             String?
  vehicle                Vehicle             @relation(fields: [vehicleId], references: [id])
}

model InsurancePolicy {
  id           String        @id @default(cuid())
  vehicleId    String
  policyNumber String        @unique
  provider     String
  type         InsuranceType
  startDate    DateTime
  endDate      DateTime
  premium      Float
  coverage     Float
  deductible   Float
  status       PolicyStatus  @default(ACTIVE)
  notes        String?
  documents    String[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  vehicle      Vehicle       @relation(fields: [vehicleId], references: [id])
}

model VehicleReview {
  id              String       @id @default(cuid())
  vehicleId       String
  reviewType      ReviewType
  reviewBy        String
  scheduledDate   DateTime
  completedDate   DateTime?
  location        String?
  status          ReviewStatus @default(SCHEDULED)
  findings        String?
  recommendations String?
  nextReviewDate  DateTime?
  documents       String[]
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  vehicle         Vehicle      @relation(fields: [vehicleId], references: [id])
}

enum UserRole {
  ADMIN
  MANAGER
  DRIVER
}

enum VehicleType {
  TRUCK
  TRAILER
}

enum TrailerType {
  DRY_VAN
  REFRIGERATED
  FLATBED
  TANKER
  LOWBOY
  STEP_DECK
  CONTAINER_CHASSIS
}

enum VehicleStatus {
  AVAILABLE
  ASSIGNED
  MAINTENANCE
  OUT_OF_SERVICE
}

enum AssignmentStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  PENDING
  ON_HOLD
  DELAYED
}

enum AssignmentType {
  REGULAR
  TEMPORARY
  EMERGENCY
  MAINTENANCE
  TRAINING
}

enum AssignmentPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum TripStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum MaintenanceType {
  PREVENTIVE
  REPAIR
  INSPECTION
}

enum MaintenanceCategory {
  ENGINE
  TRANSMISSION
  BRAKES
  ELECTRICAL
  TIRES
  OTHER
  COOLING_SYSTEM
  FUEL_SYSTEM
  EXHAUST_SYSTEM
  SUSPENSION_AXLES
  CARGO_AREA
  REFRIGERATION_UNIT
  HYDRAULIC_SYSTEMS
  LIGHTING_SYSTEM
}

enum MaintenanceStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TripType {
  DELIVERY
  PICKUP
  TRANSFER
  MAINTENANCE
  PASSENGER
  OTHER
}

enum TripPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum TripStopStatus {
  PENDING
  ARRIVED
  COMPLETED
  SKIPPED
  CANCELLED
}

enum ExpenseType {
  FUEL
  TOLL
  PARKING
  MAINTENANCE
  FOOD
  OTHER
}

enum InsuranceType {
  COMPREHENSIVE
  THIRD_PARTY
  FIRE_THEFT
  LIABILITY
}

enum PolicyStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  RENEWAL_DUE
}

enum ReviewType {
  ANNUAL_INSPECTION
  SAFETY_CHECK
  EMISSIONS_TEST
  QUALITY_CONTROL
}

enum ReviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

// Business Partners Management Models
model BusinessPartner {
  id                String                @id @default(cuid())
  name              String
  type              PartnerType           // Only SHIPPER or LOGISTICS_PARTNER
  status            PartnerStatus         @default(ACTIVE)
  contactPerson     String?
  email             String?
  phone             String?
  website           String?
  taxId             String?
  notes             String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  // Relationships
  locations         PartnerLocation[]
  tripPickups       Trip[]                @relation("PickupPartner")
  tripDeliveries    Trip[]                @relation("DeliveryPartner")

  @@index([type, status])
  @@index([name])
}

model PartnerLocation {
  id                String              @id @default(cuid())
  partnerId         String
  name              String              // e.g., "Main Warehouse", "Loading Dock A"
  type              LocationType
  address           String
  city              String
  state             String?
  postalCode        String?
  country           String              @default("USA")
  latitude          Float?
  longitude         Float?
  contactPerson     String?
  phone             String?
  email             String?
  operatingHours    String?             // e.g., "Mon-Fri 8:00-17:00"
  specialInstructions String?           // Loading instructions, access codes
  isActive          Boolean             @default(true)
  isDefault         Boolean             @default(false) // Default pickup/delivery location
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relationships
  partner           BusinessPartner     @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  pickupTrips       Trip[]              @relation("PickupLocation")
  deliveryTrips     Trip[]              @relation("DeliveryLocation")

  @@index([partnerId, type])
  @@index([city, state])
  @@index([isActive, isDefault])
}

// Business Partners Enums
enum PartnerType {
  SHIPPER           // Companies that need goods transported (your clients)
  LOGISTICS_PARTNER // Spedition companies you work with
}

enum PartnerStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum LocationType {
  PICKUP_POINT      // Where you pick up loads
  DELIVERY_POINT    // Where you deliver loads
  WAREHOUSE         // General warehouse/storage
  DISTRIBUTION_CENTER
}

// Fuel Management Models
model FuelRecord {
  id              String      @id @default(cuid())
  vehicleId       String
  driverId        String

  // Fuel Transaction Details
  quantity        Float       // Liters
  totalCost       Float       // Total fuel cost (gross)
  pricePerLiter   Float       // Calculated: totalCost / quantity

  // Location & Time
  location        String      // Gas station location
  fuelingDate     DateTime    // Date of fueling

  // Odometer Reading
  odometerReading Int         // Current odometer reading

  // Receipt Documentation (Optional)
  receiptUrl      String?     // Receipt photo/scan
  receiptNumber   String?     // Receipt reference

  // Metadata
  notes           String?
  enteredBy       String      // Staff member who entered data
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relationships
  vehicle         Vehicle     @relation(fields: [vehicleId], references: [id])
  driver          User        @relation(fields: [driverId], references: [id])

  @@index([vehicleId, fuelingDate])
  @@index([driverId, fuelingDate])
  @@index([fuelingDate])
}

model FuelPrice {
  id              String    @id @default(cuid())
  effectiveDate   DateTime  @unique // Date from "obowiązujące od dnia"
  dieselPriceNet  Float     // Net price from Orlen (without VAT)
  dieselPriceGross Float    // Gross price (net + 23% VAT)
  vatRate         Float     @default(0.23) // 23% Polish VAT
  source          String    @default("orlen.pl")
  scrapedAt       DateTime  @default(now())
  createdAt       DateTime  @default(now())

  @@index([effectiveDate])
}

model FuelPriceScrapingLog {
  id              String    @id @default(cuid())
  attemptDate     DateTime  @default(now())
  success         Boolean
  effectiveDate   DateTime? // Date found on webpage
  priceFound      Float?    // Net price scraped
  errorMessage    String?   // Error details if failed
  retryCount      Int       @default(0)
  nextRetryAt     DateTime?

  @@index([attemptDate, success])
}

model VehicleFuelStats {
  id                String    @id @default(cuid())
  vehicleId         String
  driverId          String
  month             Int       // 1-12
  year              Int

  // Calculated Metrics
  totalFuelCost     Float     // Gross cost
  totalLiters       Float
  totalDistance     Float     // km driven this month
  fuelEfficiency    Float     // liters per 100km
  costPerKm         Float

  // Performance Indicators
  avgPricePerLiter  Float     // Average gross price paid
  fuelingCount      Int       // Number of fuel-ups

  // Market Comparison
  avgMarketPrice    Float?    // Average Orlen gross price for the month
  priceDifference   Float?    // Difference vs market price

  // Metadata
  calculatedAt      DateTime  @default(now())

  // Relationships
  vehicle           Vehicle   @relation(fields: [vehicleId], references: [id])
  driver            User      @relation(fields: [driverId], references: [id])

  @@unique([vehicleId, driverId, month, year])
  @@index([vehicleId, year, month])
  @@index([driverId, year, month])
}

model SystemAlert {
  id          String        @id @default(cuid())
  type        AlertType
  message     String
  severity    AlertSeverity
  resolved    Boolean       @default(false)
  resolvedAt  DateTime?
  resolvedBy  String?
  createdAt   DateTime      @default(now())

  @@index([type, resolved])
  @@index([severity, resolved])
  @@index([createdAt])
}

// Fuel Management Enums
enum FuelType {
  GASOLINE
  DIESEL
  ELECTRIC
  HYBRID
  LPG
  CNG
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  FUEL_CARD
  COMPANY_ACCOUNT
}

enum AlertType {
  FUEL_PRICE_SCRAPING_FAILURE
  SYSTEM_ERROR
  DATA_VALIDATION_ERROR
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
