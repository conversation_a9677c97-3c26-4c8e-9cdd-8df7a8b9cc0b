-- Create<PERSON><PERSON>
CREATE TYPE "PartnerType" AS ENUM ('SHIPPER', 'LOGISTICS_PARTNER');

-- CreateEnum
CREATE TYPE "PartnerStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "LocationType" AS ENUM ('PICKUP_POINT', 'DELIVERY_POINT', 'WAREHOUSE', 'DISTRIBUTION_CENTER');

-- AlterTable
ALTER TABLE "Trip" ADD COLUMN     "deliveryLocationId" TEXT,
ADD COLUMN     "deliveryPartnerId" TEXT,
ADD COLUMN     "pickupLocationId" TEXT,
ADD COLUMN     "pickupPartnerId" TEXT;

-- CreateTable
CREATE TABLE "BusinessPartner" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "PartnerType" NOT NULL,
    "status" "PartnerStatus" NOT NULL DEFAULT 'ACTIVE',
    "contactPerson" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "website" TEXT,
    "taxId" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BusinessPartner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerLocation" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "LocationType" NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT,
    "postalCode" TEXT,
    "country" TEXT NOT NULL DEFAULT 'USA',
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "contactPerson" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "operatingHours" TEXT,
    "specialInstructions" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerLocation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BusinessPartner_type_status_idx" ON "BusinessPartner"("type", "status");

-- CreateIndex
CREATE INDEX "BusinessPartner_name_idx" ON "BusinessPartner"("name");

-- CreateIndex
CREATE INDEX "PartnerLocation_partnerId_type_idx" ON "PartnerLocation"("partnerId", "type");

-- CreateIndex
CREATE INDEX "PartnerLocation_city_state_idx" ON "PartnerLocation"("city", "state");

-- CreateIndex
CREATE INDEX "PartnerLocation_isActive_isDefault_idx" ON "PartnerLocation"("isActive", "isDefault");

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_trailerId_fkey" FOREIGN KEY ("trailerId") REFERENCES "Vehicle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_pickupPartnerId_fkey" FOREIGN KEY ("pickupPartnerId") REFERENCES "BusinessPartner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_deliveryPartnerId_fkey" FOREIGN KEY ("deliveryPartnerId") REFERENCES "BusinessPartner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_pickupLocationId_fkey" FOREIGN KEY ("pickupLocationId") REFERENCES "PartnerLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trip" ADD CONSTRAINT "Trip_deliveryLocationId_fkey" FOREIGN KEY ("deliveryLocationId") REFERENCES "PartnerLocation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerLocation" ADD CONSTRAINT "PartnerLocation_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "BusinessPartner"("id") ON DELETE CASCADE ON UPDATE CASCADE;
