-- Create<PERSON>num
CREATE TYPE "FuelType" AS ENUM ('GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID', 'LPG', 'CNG');

-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('CASH', 'CREDIT_CARD', 'FUEL_CARD', 'COMPANY_ACCOUNT');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "AlertType" AS ENUM ('FUEL_PRICE_SCRAPING_FAILURE', 'SYSTEM_ERROR', 'DATA_VALIDATION_ERROR');

-- CreateEnum
CREATE TYPE "AlertSeverity" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- DropIndex
DROP INDEX "InsurancePolicy_endDate_status_idx";

-- DropIndex
DROP INDEX "InsurancePolicy_provider_idx";

-- DropIndex
DROP INDEX "InsurancePolicy_vehicleId_status_idx";

-- DropIndex
DROP INDEX "InsurancePolicy_vehicle_fk_idx";

-- DropIndex
DROP INDEX "MaintenanceLog_status_idx";

-- DropIndex
DROP INDEX "MaintenanceLog_type_date_idx";

-- DropIndex
DROP INDEX "MaintenanceLog_vehicleId_date_idx";

-- DropIndex
DROP INDEX "MaintenanceLog_vehicle_fk_idx";

-- DropIndex
DROP INDEX "PartnerLocation_partnerId_isActive_idx";

-- DropIndex
DROP INDEX "Trip_assignmentId_idx";

-- DropIndex
DROP INDEX "Trip_created_idx";

-- DropIndex
DROP INDEX "Trip_date_range_idx";

-- DropIndex
DROP INDEX "Trip_driverId_status_idx";

-- DropIndex
DROP INDEX "Trip_driver_fk_idx";

-- DropIndex
DROP INDEX "Trip_priority_status_idx";

-- DropIndex
DROP INDEX "Trip_startTime_endTime_idx";

-- DropIndex
DROP INDEX "Trip_status_startTime_idx";

-- DropIndex
DROP INDEX "Trip_trailerId_idx";

-- DropIndex
DROP INDEX "Trip_trailer_fk_idx";

-- DropIndex
DROP INDEX "Trip_vehicleId_status_idx";

-- DropIndex
DROP INDEX "Trip_vehicle_fk_idx";

-- DropIndex
DROP INDEX "TruckTrailerAssignment_assignedBy_idx";

-- DropIndex
DROP INDEX "TruckTrailerAssignment_startDate_endDate_idx";

-- DropIndex
DROP INDEX "User_created_updated_idx";

-- DropIndex
DROP INDEX "User_driver_lookup_idx";

-- DropIndex
DROP INDEX "User_email_role_idx";

-- DropIndex
DROP INDEX "User_firstName_lastName_idx";

-- DropIndex
DROP INDEX "User_role_status_idx";

-- DropIndex
DROP INDEX "Vehicle_assignment_lookup_idx";

-- DropIndex
DROP INDEX "Vehicle_created_updated_idx";

-- DropIndex
DROP INDEX "Vehicle_lastMaintenance_idx";

-- DropIndex
DROP INDEX "Vehicle_make_model_idx";

-- DropIndex
DROP INDEX "Vehicle_plateNumber_status_idx";

-- DropIndex
DROP INDEX "Vehicle_status_vehicleType_idx";

-- DropIndex
DROP INDEX "Vehicle_vehicleType_status_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_created_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_date_range_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_driverId_status_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_driver_fk_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_startDate_endDate_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_status_endDate_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_status_startDate_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_vehicleId_status_idx";

-- DropIndex
DROP INDEX "VehicleAssignment_vehicle_fk_idx";

-- DropIndex
DROP INDEX "VehicleReview_reviewType_status_idx";

-- DropIndex
DROP INDEX "VehicleReview_scheduledDate_idx";

-- DropIndex
DROP INDEX "VehicleReview_vehicleId_status_idx";

-- DropIndex
DROP INDEX "VehicleReview_vehicle_fk_idx";

-- CreateTable
CREATE TABLE "FuelRecord" (
    "id" TEXT NOT NULL,
    "vehicleId" TEXT NOT NULL,
    "driverId" TEXT NOT NULL,
    "quantity" DOUBLE PRECISION NOT NULL,
    "totalCost" DOUBLE PRECISION NOT NULL,
    "pricePerLiter" DOUBLE PRECISION NOT NULL,
    "location" TEXT NOT NULL,
    "fuelingDate" TIMESTAMP(3) NOT NULL,
    "odometerReading" INTEGER NOT NULL,
    "receiptUrl" TEXT,
    "receiptNumber" TEXT,
    "notes" TEXT,
    "enteredBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FuelRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FuelPrice" (
    "id" TEXT NOT NULL,
    "effectiveDate" TIMESTAMP(3) NOT NULL,
    "dieselPriceNet" DOUBLE PRECISION NOT NULL,
    "dieselPriceGross" DOUBLE PRECISION NOT NULL,
    "vatRate" DOUBLE PRECISION NOT NULL DEFAULT 0.23,
    "source" TEXT NOT NULL DEFAULT 'orlen.pl',
    "scrapedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FuelPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FuelPriceScrapingLog" (
    "id" TEXT NOT NULL,
    "attemptDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "success" BOOLEAN NOT NULL,
    "effectiveDate" TIMESTAMP(3),
    "priceFound" DOUBLE PRECISION,
    "errorMessage" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "nextRetryAt" TIMESTAMP(3),

    CONSTRAINT "FuelPriceScrapingLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VehicleFuelStats" (
    "id" TEXT NOT NULL,
    "vehicleId" TEXT NOT NULL,
    "driverId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "totalFuelCost" DOUBLE PRECISION NOT NULL,
    "totalLiters" DOUBLE PRECISION NOT NULL,
    "totalDistance" DOUBLE PRECISION NOT NULL,
    "fuelEfficiency" DOUBLE PRECISION NOT NULL,
    "costPerKm" DOUBLE PRECISION NOT NULL,
    "avgPricePerLiter" DOUBLE PRECISION NOT NULL,
    "fuelingCount" INTEGER NOT NULL,
    "avgMarketPrice" DOUBLE PRECISION,
    "priceDifference" DOUBLE PRECISION,
    "calculatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VehicleFuelStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SystemAlert" (
    "id" TEXT NOT NULL,
    "type" "AlertType" NOT NULL,
    "message" TEXT NOT NULL,
    "severity" "AlertSeverity" NOT NULL,
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedAt" TIMESTAMP(3),
    "resolvedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SystemAlert_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "FuelRecord_vehicleId_fuelingDate_idx" ON "FuelRecord"("vehicleId", "fuelingDate");

-- CreateIndex
CREATE INDEX "FuelRecord_driverId_fuelingDate_idx" ON "FuelRecord"("driverId", "fuelingDate");

-- CreateIndex
CREATE INDEX "FuelRecord_fuelingDate_idx" ON "FuelRecord"("fuelingDate");

-- CreateIndex
CREATE UNIQUE INDEX "FuelPrice_effectiveDate_key" ON "FuelPrice"("effectiveDate");

-- CreateIndex
CREATE INDEX "FuelPrice_effectiveDate_idx" ON "FuelPrice"("effectiveDate");

-- CreateIndex
CREATE INDEX "FuelPriceScrapingLog_attemptDate_success_idx" ON "FuelPriceScrapingLog"("attemptDate", "success");

-- CreateIndex
CREATE INDEX "VehicleFuelStats_vehicleId_year_month_idx" ON "VehicleFuelStats"("vehicleId", "year", "month");

-- CreateIndex
CREATE INDEX "VehicleFuelStats_driverId_year_month_idx" ON "VehicleFuelStats"("driverId", "year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "VehicleFuelStats_vehicleId_driverId_month_year_key" ON "VehicleFuelStats"("vehicleId", "driverId", "month", "year");

-- CreateIndex
CREATE INDEX "SystemAlert_type_resolved_idx" ON "SystemAlert"("type", "resolved");

-- CreateIndex
CREATE INDEX "SystemAlert_severity_resolved_idx" ON "SystemAlert"("severity", "resolved");

-- CreateIndex
CREATE INDEX "SystemAlert_createdAt_idx" ON "SystemAlert"("createdAt");

-- AddForeignKey
ALTER TABLE "FuelRecord" ADD CONSTRAINT "FuelRecord_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "Vehicle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FuelRecord" ADD CONSTRAINT "FuelRecord_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VehicleFuelStats" ADD CONSTRAINT "VehicleFuelStats_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "Vehicle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VehicleFuelStats" ADD CONSTRAINT "VehicleFuelStats_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
