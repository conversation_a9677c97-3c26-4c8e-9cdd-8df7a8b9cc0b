-- Performance optimization indexes for Fleet Fusion

-- User table indexes
CREATE INDEX IF NOT EXISTS "User_role_status_idx" ON "User"("role", "status");
CREATE INDEX IF NOT EXISTS "User_email_role_idx" ON "User"("email", "role");
CREATE INDEX IF NOT EXISTS "User_firstName_lastName_idx" ON "User"("firstName", "lastName");

-- Vehicle table indexes
CREATE INDEX IF NOT EXISTS "Vehicle_status_vehicleType_idx" ON "Vehicle"("status", "vehicleType");
CREATE INDEX IF NOT EXISTS "Vehicle_plateNumber_status_idx" ON "Vehicle"("plateNumber", "status");
CREATE INDEX IF NOT EXISTS "Vehicle_vehicleType_status_idx" ON "Vehicle"("vehicleType", "status");
CREATE INDEX IF NOT EXISTS "Vehicle_make_model_idx" ON "Vehicle"("make", "model");
CREATE INDEX IF NOT EXISTS "Vehicle_lastMaintenance_idx" ON "Vehicle"("lastMaintenance");

-- VehicleAssignment table indexes
CREATE INDEX IF NOT EXISTS "VehicleAssignment_status_startDate_idx" ON "VehicleAssignment"("status", "startDate");
CREATE INDEX IF NOT EXISTS "VehicleAssignment_vehicleId_status_idx" ON "VehicleAssignment"("vehicleId", "status");
CREATE INDEX IF NOT EXISTS "VehicleAssignment_driverId_status_idx" ON "VehicleAssignment"("driverId", "status");
CREATE INDEX IF NOT EXISTS "VehicleAssignment_startDate_endDate_idx" ON "VehicleAssignment"("startDate", "endDate");
CREATE INDEX IF NOT EXISTS "VehicleAssignment_status_endDate_idx" ON "VehicleAssignment"("status", "endDate");

-- TruckTrailerAssignment table indexes (already has some indexes, adding more)
CREATE INDEX IF NOT EXISTS "TruckTrailerAssignment_startDate_endDate_idx" ON "TruckTrailerAssignment"("startDate", "endDate");
CREATE INDEX IF NOT EXISTS "TruckTrailerAssignment_assignedBy_idx" ON "TruckTrailerAssignment"("assignedBy");

-- Trip table indexes
CREATE INDEX IF NOT EXISTS "Trip_status_startTime_idx" ON "Trip"("status", "startTime");
CREATE INDEX IF NOT EXISTS "Trip_driverId_status_idx" ON "Trip"("driverId", "status");
CREATE INDEX IF NOT EXISTS "Trip_vehicleId_status_idx" ON "Trip"("vehicleId", "status");
CREATE INDEX IF NOT EXISTS "Trip_startTime_endTime_idx" ON "Trip"("startTime", "endTime");
CREATE INDEX IF NOT EXISTS "Trip_assignmentId_idx" ON "Trip"("assignmentId");
CREATE INDEX IF NOT EXISTS "Trip_trailerId_idx" ON "Trip"("trailerId");
CREATE INDEX IF NOT EXISTS "Trip_priority_status_idx" ON "Trip"("priority", "status");

-- MaintenanceLog table indexes
CREATE INDEX IF NOT EXISTS "MaintenanceLog_vehicleId_date_idx" ON "MaintenanceLog"("vehicleId", "date");
CREATE INDEX IF NOT EXISTS "MaintenanceLog_type_date_idx" ON "MaintenanceLog"("type", "date");
CREATE INDEX IF NOT EXISTS "MaintenanceLog_status_idx" ON "MaintenanceLog"("status");

-- InsurancePolicy table indexes
CREATE INDEX IF NOT EXISTS "InsurancePolicy_vehicleId_status_idx" ON "InsurancePolicy"("vehicleId", "status");
CREATE INDEX IF NOT EXISTS "InsurancePolicy_endDate_status_idx" ON "InsurancePolicy"("endDate", "status");
CREATE INDEX IF NOT EXISTS "InsurancePolicy_provider_idx" ON "InsurancePolicy"("provider");

-- VehicleReview table indexes
CREATE INDEX IF NOT EXISTS "VehicleReview_vehicleId_status_idx" ON "VehicleReview"("vehicleId", "status");
CREATE INDEX IF NOT EXISTS "VehicleReview_scheduledDate_idx" ON "VehicleReview"("scheduledDate");
CREATE INDEX IF NOT EXISTS "VehicleReview_reviewType_status_idx" ON "VehicleReview"("reviewType", "status");

-- BusinessPartner table indexes (if exists)
CREATE INDEX IF NOT EXISTS "BusinessPartner_type_status_idx" ON "BusinessPartner"("type", "status");
CREATE INDEX IF NOT EXISTS "BusinessPartner_name_idx" ON "BusinessPartner"("name");

-- PartnerLocation table indexes (if exists)
CREATE INDEX IF NOT EXISTS "PartnerLocation_isActive_isDefault_idx" ON "PartnerLocation"("isActive", "isDefault");
CREATE INDEX IF NOT EXISTS "PartnerLocation_partnerId_isActive_idx" ON "PartnerLocation"("partnerId", "isActive");

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS "Vehicle_assignment_lookup_idx" ON "Vehicle"("id", "status", "vehicleType");
CREATE INDEX IF NOT EXISTS "User_driver_lookup_idx" ON "User"("id", "role", "status");

-- Indexes for date range queries
CREATE INDEX IF NOT EXISTS "VehicleAssignment_date_range_idx" ON "VehicleAssignment"("startDate", "endDate", "status");
CREATE INDEX IF NOT EXISTS "Trip_date_range_idx" ON "Trip"("startTime", "endTime", "status");

-- Indexes for search functionality
CREATE INDEX IF NOT EXISTS "Vehicle_search_idx" ON "Vehicle" USING gin(to_tsvector('english', "plateNumber" || ' ' || "make" || ' ' || "model"));
CREATE INDEX IF NOT EXISTS "User_search_idx" ON "User" USING gin(to_tsvector('english', "firstName" || ' ' || "lastName" || ' ' || "email"));

-- Partial indexes for active records (more efficient for common queries)
CREATE INDEX IF NOT EXISTS "VehicleAssignment_active_idx" ON "VehicleAssignment"("vehicleId", "driverId", "startDate") WHERE "status" = 'ACTIVE';
CREATE INDEX IF NOT EXISTS "Trip_active_idx" ON "Trip"("driverId", "vehicleId", "startTime") WHERE "status" IN ('SCHEDULED', 'IN_PROGRESS');
CREATE INDEX IF NOT EXISTS "Vehicle_available_idx" ON "Vehicle"("vehicleType", "plateNumber") WHERE "status" = 'AVAILABLE';
CREATE INDEX IF NOT EXISTS "User_active_drivers_idx" ON "User"("firstName", "lastName") WHERE "role" = 'DRIVER' AND "status" IN ('Active', 'Available');

-- Indexes for foreign key relationships to improve join performance
CREATE INDEX IF NOT EXISTS "VehicleAssignment_driver_fk_idx" ON "VehicleAssignment"("driverId");
CREATE INDEX IF NOT EXISTS "VehicleAssignment_vehicle_fk_idx" ON "VehicleAssignment"("vehicleId");
CREATE INDEX IF NOT EXISTS "Trip_driver_fk_idx" ON "Trip"("driverId");
CREATE INDEX IF NOT EXISTS "Trip_vehicle_fk_idx" ON "Trip"("vehicleId");
CREATE INDEX IF NOT EXISTS "Trip_trailer_fk_idx" ON "Trip"("trailerId");
CREATE INDEX IF NOT EXISTS "MaintenanceLog_vehicle_fk_idx" ON "MaintenanceLog"("vehicleId");
CREATE INDEX IF NOT EXISTS "InsurancePolicy_vehicle_fk_idx" ON "InsurancePolicy"("vehicleId");
CREATE INDEX IF NOT EXISTS "VehicleReview_vehicle_fk_idx" ON "VehicleReview"("vehicleId");

-- Indexes for audit and tracking
CREATE INDEX IF NOT EXISTS "VehicleAssignment_created_idx" ON "VehicleAssignment"("createdAt");
CREATE INDEX IF NOT EXISTS "Trip_created_idx" ON "Trip"("createdAt");
CREATE INDEX IF NOT EXISTS "Vehicle_created_updated_idx" ON "Vehicle"("createdAt", "updatedAt");
CREATE INDEX IF NOT EXISTS "User_created_updated_idx" ON "User"("createdAt", "updatedAt");
