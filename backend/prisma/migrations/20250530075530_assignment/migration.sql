-- CreateEnum
CREATE TYPE "AssignmentType" AS ENUM ('REGULAR', 'TEMPORARY', 'EMERGENCY', 'MAINTENANCE', 'TRAINING');

-- CreateEnum
CREATE TYPE "AssignmentPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AssignmentStatus" ADD VALUE 'PENDING';
ALTER TYPE "AssignmentStatus" ADD VALUE 'ON_HOLD';
ALTER TYPE "AssignmentStatus" ADD VALUE 'DELAYED';

-- AlterTable
ALTER TABLE "VehicleAssignment" ADD COLUMN     "approvedAt" TIMESTAMP(3),
ADD COLUMN     "approvedBy" TEXT,
ADD COLUMN     "locationEnd" TEXT,
ADD COLUMN     "locationStart" TEXT,
ADD COLUMN     "mileage" DOUBLE PRECISION,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "priority" "AssignmentPriority" NOT NULL DEFAULT 'NORMAL',
ADD COLUMN     "reason" TEXT,
ADD COLUMN     "type" "AssignmentType" NOT NULL DEFAULT 'REGULAR';

-- CreateIndex
CREATE INDEX "VehicleAssignment_driverId_startDate_endDate_idx" ON "VehicleAssignment"("driverId", "startDate", "endDate");

-- CreateIndex
CREATE INDEX "VehicleAssignment_vehicleId_startDate_endDate_idx" ON "VehicleAssignment"("vehicleId", "startDate", "endDate");
