-- CreateEnum
CREATE TYPE "VehicleType" AS ENUM ('TRUCK', 'TRAILER');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TrailerType" AS ENUM ('DRY_VAN', 'REFRIGERATED', 'FLATBED', 'TANKER', 'LOWBOY', 'STEP_DECK', 'CONTAINER_CHASSIS');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "MaintenanceCategory" ADD VALUE 'COOLING_SYSTEM';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'FUEL_SYSTEM';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'EXHAUST_SYSTEM';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'SUSPENSION_AXLES';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'CARGO_AREA';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'REFRIGERATION_UNIT';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'HYDRAULIC_SYSTEMS';
ALTER TYPE "MaintenanceCategory" ADD VALUE 'LIGHTING_SYSTEM';

-- AlterTable
ALTER TABLE "Trip" ADD COLUMN     "trailerId" TEXT;

-- AlterTable
ALTER TABLE "Vehicle" ADD COLUMN     "axleConfiguration" TEXT,
ADD COLUMN     "cabConfiguration" TEXT,
ADD COLUMN     "cargoCapacity" DOUBLE PRECISION,
ADD COLUMN     "engineType" TEXT,
ADD COLUMN     "fuelCapacity" DOUBLE PRECISION,
ADD COLUMN     "hasRefrigeration" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "height" DOUBLE PRECISION,
ADD COLUMN     "length" DOUBLE PRECISION,
ADD COLUMN     "maxWeight" DOUBLE PRECISION,
ADD COLUMN     "trailerType" "TrailerType",
ADD COLUMN     "transmission" TEXT,
ADD COLUMN     "vehicleType" "VehicleType" NOT NULL DEFAULT 'TRUCK',
ADD COLUMN     "width" DOUBLE PRECISION;

-- CreateTable
CREATE TABLE "TruckTrailerAssignment" (
    "id" TEXT NOT NULL,
    "truckId" TEXT NOT NULL,
    "trailerId" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "status" "AssignmentStatus" NOT NULL DEFAULT 'ACTIVE',
    "notes" TEXT,
    "assignedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TruckTrailerAssignment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TruckTrailerAssignment_truckId_status_idx" ON "TruckTrailerAssignment"("truckId", "status");

-- CreateIndex
CREATE INDEX "TruckTrailerAssignment_trailerId_status_idx" ON "TruckTrailerAssignment"("trailerId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "TruckTrailerAssignment_truckId_trailerId_startDate_key" ON "TruckTrailerAssignment"("truckId", "trailerId", "startDate");

-- AddForeignKey
ALTER TABLE "TruckTrailerAssignment" ADD CONSTRAINT "TruckTrailerAssignment_truckId_fkey" FOREIGN KEY ("truckId") REFERENCES "Vehicle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TruckTrailerAssignment" ADD CONSTRAINT "TruckTrailerAssignment_trailerId_fkey" FOREIGN KEY ("trailerId") REFERENCES "Vehicle"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
