-- Add driver-specific fields to User model
ALTER TABLE "User" ADD COLUMN "phone" TEXT;
ALTER TABLE "User" ADD COLUMN "licenseNumber" TEXT;
ALTER TABLE "User" ADD COLUMN "licenseType" TEXT;
ALTER TABLE "User" ADD COLUMN "licenseExpiry" TIMESTAMP(3);
ALTER TABLE "User" ADD COLUMN "licenseRestrictions" TEXT;
ALTER TABLE "User" ADD COLUMN "address" TEXT;
ALTER TABLE "User" ADD COLUMN "emergencyContactName" TEXT;
ALTER TABLE "User" ADD COLUMN "emergencyContactPhone" TEXT;
ALTER TABLE "User" ADD COLUMN "hireDate" TIMESTAMP(3);
ALTER TABLE "User" ADD COLUMN "notes" TEXT;
ALTER TABLE "User" ADD COLUMN "status" TEXT DEFAULT 'Active';
