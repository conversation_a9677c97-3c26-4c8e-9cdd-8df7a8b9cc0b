/*
  Warnings:

  - The values [ROUTIN<PERSON>,EMER<PERSON>NCY] on the enum `MaintenanceType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `nextDueDate` on the `MaintenanceLog` table. All the data in the column will be lost.
  - Added the required column `category` to the `MaintenanceLog` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MaintenanceCategory" AS ENUM ('ENGINE', 'TRANSMISSION', 'BRAKES', 'ELECTRICAL', 'TIRES', 'OTHER');

-- CreateEnum
CREATE TYPE "MaintenanceStatus" AS ENUM ('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');

-- AlterEnum
BEGIN;
CREATE TYPE "MaintenanceType_new" AS ENUM ('PREVENTIVE', 'REPAIR', 'INSPECTION');
ALTER TABLE "MaintenanceLog" ALTER COLUMN "type" TYPE "MaintenanceType_new" USING ("type"::text::"MaintenanceType_new");
ALTER TYPE "MaintenanceType" RENAME TO "MaintenanceType_old";
ALTER TYPE "MaintenanceType_new" RENAME TO "MaintenanceType";
DROP TYPE "MaintenanceType_old";
COMMIT;

-- AlterTable
ALTER TABLE "MaintenanceLog" DROP COLUMN "nextDueDate",
ADD COLUMN     "category" "MaintenanceCategory" NOT NULL,
ADD COLUMN     "laborCost" DOUBLE PRECISION,
ADD COLUMN     "mileage" INTEGER,
ADD COLUMN     "nextMaintenanceDate" TIMESTAMP(3),
ADD COLUMN     "nextMaintenanceMileage" INTEGER,
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "partsCost" DOUBLE PRECISION,
ADD COLUMN     "scheduledDate" TIMESTAMP(3),
ADD COLUMN     "status" "MaintenanceStatus" NOT NULL DEFAULT 'SCHEDULED',
ADD COLUMN     "technician" TEXT,
ALTER COLUMN "cost" DROP NOT NULL;
