// Business Partners Management Schema Extension
// Add this to the main schema.prisma file

model BusinessPartner {
  id                String                @id @default(cuid())
  name              String
  type              PartnerType           // Only SHIPPER or LOGISTICS_PARTNER
  status            PartnerStatus         @default(ACTIVE)
  contactPerson     String?
  email             String?
  phone             String?
  website           String?
  taxId             String?
  notes             String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  // Relationships
  locations         PartnerLocation[]
  tripPickups       Trip[]                @relation("PickupPartner")
  tripDeliveries    Trip[]                @relation("DeliveryPartner")

  @@index([type, status])
  @@index([name])
}

model PartnerLocation {
  id                String              @id @default(cuid())
  partnerId         String
  name              String              // e.g., "Main Warehouse", "Loading Dock A"
  type              LocationType
  address           String
  city              String
  state             String?
  postalCode        String?
  country           String              @default("USA")
  latitude          Float?
  longitude         Float?
  contactPerson     String?
  phone             String?
  email             String?
  operatingHours    String?             // e.g., "Mon-Fri 8:00-17:00"
  specialInstructions String?           // Loading instructions, access codes
  isActive          Boolean             @default(true)
  isDefault         Boolean             @default(false) // Default pickup/delivery location
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relationships
  partner           BusinessPartner     @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  pickupTrips       Trip[]              @relation("PickupLocation")
  deliveryTrips     Trip[]              @relation("DeliveryLocation")

  @@index([partnerId, type])
  @@index([city, state])
  @@index([isActive, isDefault])
}

// Simplified Enums
enum PartnerType {
  SHIPPER           // Companies that need goods transported (your clients)
  LOGISTICS_PARTNER // Spedition companies you work with
}

enum PartnerStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum LocationType {
  PICKUP_POINT      // Where you pick up loads
  DELIVERY_POINT    // Where you deliver loads
  WAREHOUSE         // General warehouse/storage
  DISTRIBUTION_CENTER
}

// Enhanced Trip model (modifications to existing Trip model)
// Add these fields to the existing Trip model:
/*
  pickupPartnerId     String?
  deliveryPartnerId   String?
  pickupLocationId    String?
  deliveryLocationId  String?
  
  // Enhanced relationships
  pickupPartner       BusinessPartner?   @relation("PickupPartner", fields: [pickupPartnerId], references: [id])
  deliveryPartner     BusinessPartner?   @relation("DeliveryPartner", fields: [deliveryPartnerId], references: [id])
  pickupLocation      PartnerLocation?   @relation("PickupLocation", fields: [pickupLocationId], references: [id])
  deliveryLocation    PartnerLocation?   @relation("DeliveryLocation", fields: [deliveryLocationId], references: [id])
*/
