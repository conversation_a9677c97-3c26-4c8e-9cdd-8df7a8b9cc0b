enum UserStatus {
  <PERSON><PERSON><PERSON><PERSON>LE
  BUSY
  INACTIVE
}

model User {
  id            String     @id @default(cuid())
  email         String     @unique
  passwordHash  String
  firstName     String
  lastName      String
  role          UserRole   @default(DRIVER)
  status        UserStatus @default(AVAILABLE)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  assignments   VehicleAssignment[]
  trips         Trip[]
}
