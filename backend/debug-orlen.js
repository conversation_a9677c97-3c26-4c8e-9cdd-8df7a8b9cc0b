const puppeteer = require('puppeteer');

async function debugOrlenPage() {
  console.log('🚀 Starting Orlen page debug...');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process',
      '--disable-gpu'
    ]
  });
  
  const page = await browser.newPage();
  
  try {
    console.log('📄 Navigating to Orlen page...');
    await page.goto('https://www.orlen.pl/pl/dla-biznesu/hurtowe-ceny-paliw', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    console.log('🔍 Looking for price-related content...');
    
    // Get all text content and look for price patterns
    const allText = await page.evaluate(() => {
      return document.body.innerText;
    });
    
    console.log('📝 Page text content (first 2000 chars):');
    console.log(allText.substring(0, 2000));
    console.log('...\n');
    
    // Look for numbers that could be prices
    const pricePatterns = allText.match(/\d{1,2}[,.]?\d{3}/g);
    console.log('💰 Found potential price patterns:', pricePatterns);
    
    // Look for diesel-related text
    const dieselMatches = allText.match(/.{0,50}(diesel|olej napędowy|ON).{0,50}/gi);
    console.log('⛽ Found diesel-related text:', dieselMatches);
    
    // Look for date patterns
    const dateMatches = allText.match(/.{0,50}(obowiązujące|od dnia).{0,50}/gi);
    console.log('📅 Found date-related text:', dateMatches);
    
    // Get all table content
    const tables = await page.evaluate(() => {
      const tables = Array.from(document.querySelectorAll('table'));
      return tables.map(table => table.innerText);
    });
    
    console.log('📊 Found tables:', tables.length);
    tables.forEach((table, index) => {
      console.log(`Table ${index + 1}:`, table.substring(0, 500));
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await browser.close();
    console.log('🔒 Browser closed');
  }
}

debugOrlenPage();
