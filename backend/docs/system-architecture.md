# Fleet Fusion System Architecture

## Overview

Fleet Fusion is a modern fleet management system built with a microservices-inspired architecture using NestJS for the backend and Next.js for the frontend. The system is designed for scalability, maintainability, and performance.

## Architecture Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (Next.js)     │◄──►│   (NestJS)      │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ - React         │    │ - Controllers   │    │ - Prisma ORM    │
│ - TypeScript    │    │ - Services      │    │ - Migrations    │
│ - Tailwind CSS  │    │ - Guards        │    │ - Indexes       │
│ - Shadcn/ui     │    │ - Middleware    │    │ - Constraints   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Backend Architecture

### Layer Structure

```
┌─────────────────────────────────────────────────────────────┐
│                     Presentation Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Controllers │  │   Guards    │  │ Middleware  │        │
│  │             │  │             │  │             │        │
│  │ - HTTP      │  │ - Auth      │  │ - Logging   │        │
│  │ - Routing   │  │ - Roles     │  │ - CORS      │        │
│  │ - Validation│  │ - Rate Limit│  │ - Error     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Business Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Services   │  │ Validators  │  │ Exceptions  │        │
│  │             │  │             │  │             │        │
│  │ - Business  │  │ - Custom    │  │ - Custom    │        │
│  │   Logic     │  │ - DTOs      │  │ - HTTP      │        │
│  │ - Workflows │  │ - Rules     │  │ - Business  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Data Access Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Prisma    │  │ Repositories│  │ Optimization│        │
│  │             │  │             │  │             │        │
│  │ - ORM       │  │ - Patterns  │  │ - Caching   │        │
│  │ - Migrations│  │ - Queries   │  │ - Indexing  │        │
│  │ - Schema    │  │ - Transactions│ │ - Pooling   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### Module Structure

#### Core Modules
- **Users Module**: User management, authentication, driver profiles
- **Vehicles Module**: Vehicle management, assignments, maintenance
- **Trips Module**: Trip planning, execution, tracking
- **Business Partners Module**: Partner management, locations

#### Common Modules
- **Prisma Module**: Database connection and ORM
- **Auth Module**: Authentication and authorization
- **Common Module**: Shared utilities, exceptions, validators

### Key Design Patterns

#### 1. Dependency Injection
```typescript
@Injectable()
export class VehicleAssignmentService {
  constructor(
    private prisma: PrismaService,
    private optimizationService: DatabaseOptimizationService
  ) {}
}
```

#### 2. Repository Pattern (via Prisma)
```typescript
// Abstracted through Prisma service
async findAvailableVehicles() {
  return this.prisma.vehicle.findMany({
    where: { status: 'AVAILABLE' },
    select: { /* optimized fields */ }
  });
}
```

#### 3. Transaction Pattern
```typescript
async createAssignment(data) {
  return this.prisma.$transaction([
    this.prisma.vehicleAssignment.create(data),
    this.prisma.vehicle.update(/* update status */),
    this.prisma.user.update(/* update driver status */)
  ]);
}
```

#### 4. Exception Handling Pattern
```typescript
// Custom exceptions for business logic
throw new ConflictException('Driver already has an active assignment');
throw new ResourceNotFoundException('Vehicle', vehicleId);
```

## Database Design

### Entity Relationship Overview

```
User (Driver) ──┐
                ├── VehicleAssignment ──── Vehicle
                │
                └── Trip ──── Vehicle
                              │
                              └── Trailer (Vehicle)
```

### Key Entities

#### User
- Primary entity for drivers, managers, and admins
- Includes driver-specific fields (license, contact info)
- Status tracking for availability

#### Vehicle
- Supports both trucks and trailers via discriminator
- Status management (AVAILABLE, ASSIGNED, MAINTENANCE)
- Maintenance history tracking

#### VehicleAssignment
- Links drivers to vehicles with time periods
- Status lifecycle (ACTIVE, COMPLETED, CANCELLED)
- Atomic updates with vehicle/driver status

#### Trip
- Represents actual work assignments
- Links to assignments and vehicles
- Business partner integration

### Performance Optimizations

#### Indexing Strategy
```sql
-- Composite indexes for common queries
CREATE INDEX "Vehicle_status_vehicleType_idx" ON "Vehicle"("status", "vehicleType");
CREATE INDEX "VehicleAssignment_status_startDate_idx" ON "VehicleAssignment"("status", "startDate");

-- Partial indexes for active records
CREATE INDEX "VehicleAssignment_active_idx" ON "VehicleAssignment"("vehicleId", "driverId") 
WHERE "status" = 'ACTIVE';
```

#### Query Optimization
- Selective field retrieval
- Pagination for large datasets
- Connection pooling
- Slow query monitoring

## Security Architecture

### Authentication Flow
```
Client Request → JWT Validation → Role Check → Route Access
```

### Authorization Levels
- **Admin**: Full system access
- **Manager**: Fleet management, reporting
- **Driver**: Limited to own assignments and trips

### Data Protection
- Password hashing with bcrypt
- Input validation and sanitization
- SQL injection prevention via Prisma
- XSS protection through input encoding

## Error Handling Strategy

### Exception Hierarchy
```
HttpException
├── BusinessLogicException (400)
├── ResourceNotFoundException (404)
├── ConflictException (409)
├── ValidationException (422)
└── DatabaseException (500)
```

### Error Response Format
```typescript
{
  message: string,
  statusCode: number,
  details?: any,
  timestamp: string,
  context?: string
}
```

## Performance Monitoring

### Metrics Collection
- Database query performance
- API response times
- Error rates and types
- Resource utilization

### Logging Strategy
- Structured logging with context
- Different log levels (debug, info, warn, error)
- Slow query detection
- Business event tracking

## Scalability Considerations

### Horizontal Scaling
- Stateless service design
- Database connection pooling
- Load balancer ready

### Vertical Scaling
- Efficient query patterns
- Memory optimization
- CPU-intensive task optimization

### Future Enhancements
- Redis caching layer
- Message queue for async operations
- Microservices decomposition
- Event-driven architecture

## Development Workflow

### Code Organization
```
src/
├── common/           # Shared utilities
│   ├── exceptions/   # Custom exceptions
│   ├── validators/   # Custom validators
│   └── services/     # Shared services
├── modules/          # Feature modules
│   ├── users/
│   ├── vehicles/
│   └── trips/
├── prisma/           # Database layer
└── docs/             # Documentation
```

### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Database transaction testing
- Error scenario testing

### Deployment
- Docker containerization
- Environment-based configuration
- Database migration automation
- Health check endpoints

## Monitoring and Maintenance

### Health Checks
- Database connectivity
- External service availability
- Memory and CPU usage
- Disk space monitoring

### Backup Strategy
- Automated database backups
- Point-in-time recovery
- Configuration backup
- Disaster recovery procedures
