# Fleet Fusion API Documentation

## Overview

Fleet Fusion is a comprehensive fleet management system that provides APIs for managing vehicles, drivers, assignments, and trips. This documentation covers the main API endpoints, their usage, and business logic.

## Base URL

```
http://localhost:3001/api
```

## Authentication

All API endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### Vehicle Management

#### GET /vehicles
Retrieve all vehicles with their current assignments and maintenance status.

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 20)
- `vehicleType` (optional): Filter by vehicle type (TRUCK, TRAILER)
- `status` (optional): Filter by status (AVAILABLE, ASSIGNED, MAINTENANCE, OUT_OF_SERVICE)
- `search` (optional): Search by plate number, make, or model

**Response:**
```json
{
  "vehicles": [
    {
      "id": "string",
      "plateNumber": "string",
      "make": "string",
      "model": "string",
      "year": number,
      "status": "AVAILABLE|ASSIGNED|MAINTENANCE|OUT_OF_SERVICE",
      "vehicleType": "TRUCK|TRAILER",
      "assignments": [...],
      "maintenanceLogs": [...]
    }
  ],
  "total": number,
  "page": number,
  "totalPages": number
}
```

#### POST /vehicles
Create a new vehicle.

**Request Body:**
```json
{
  "plateNumber": "string (required)",
  "make": "string (required)",
  "model": "string (required)",
  "year": number (required),
  "vehicleType": "TRUCK|TRAILER (required)",
  "vin": "string (optional, 17 characters)",
  "color": "string (optional)",
  "mileage": number (optional),
  "fuelType": "GASOLINE|DIESEL|ELECTRIC|HYBRID|OTHER (optional)"
}
```

**Validation Rules:**
- Plate number must be unique and follow valid format
- Year must be between 1900 and current year + 1
- VIN must be exactly 17 characters if provided
- Mileage cannot be negative

#### GET /vehicles/:id
Retrieve a specific vehicle by ID.

#### PUT /vehicles/:id
Update a vehicle.

#### DELETE /vehicles/:id
Delete a vehicle (only if no active assignments).

### Vehicle Assignments

#### GET /vehicle-assignments
Retrieve all vehicle assignments.

**Query Parameters:**
- `status` (optional): Filter by assignment status
- `vehicleId` (optional): Filter by vehicle ID
- `driverId` (optional): Filter by driver ID

#### POST /vehicle-assignments
Create a new vehicle assignment.

**Request Body:**
```json
{
  "vehicleId": "string (required, UUID)",
  "driverId": "string (required, UUID)",
  "startDate": "string (required, ISO date)",
  "endDate": "string (optional, ISO date)",
  "notes": "string (optional, max 1000 chars)",
  "type": "REGULAR|TEMPORARY|EMERGENCY|MAINTENANCE|TRAINING (optional)",
  "priority": "LOW|NORMAL|HIGH|URGENT (optional)"
}
```

**Business Logic:**
- Vehicle must be available (status: AVAILABLE)
- Driver must be available (no active assignments)
- Driver must have DRIVER role
- End date must be after start date
- Creates assignment and updates both vehicle and driver status atomically

**Error Responses:**
- `400`: Validation error or invalid date range
- `404`: Vehicle or driver not found
- `409`: Vehicle or driver already assigned

#### PUT /vehicle-assignments/:id/complete
Complete an active assignment.

#### PUT /vehicle-assignments/:id/cancel
Cancel an active assignment.

### User Management

#### GET /users
Retrieve all users.

**Query Parameters:**
- `role` (optional): Filter by user role (ADMIN, MANAGER, DRIVER)

#### GET /users/drivers
Retrieve all drivers with their assignment status.

#### POST /users/driver
Create a new driver.

**Request Body:**
```json
{
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars, complex)",
  "firstName": "string (required, max 50 chars)",
  "lastName": "string (required, max 50 chars)",
  "phone": "string (optional, valid phone format)",
  "licenseNumber": "string (optional, valid license format)"
}
```

**Password Requirements:**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

#### PUT /users/drivers/:id
Update driver information.

### Trip Management

#### GET /trips
Retrieve all trips with pagination.

#### POST /trips
Create a new trip.

#### PUT /trips/:id
Update a trip.

#### DELETE /trips/:id
Delete a trip.

## Error Handling

### Standard Error Response Format

```json
{
  "message": "string",
  "statusCode": number,
  "details": "object (optional)",
  "timestamp": "string (ISO date)",
  "path": "string (optional)",
  "method": "string (optional)"
}
```

### Common Error Codes

- `400 Bad Request`: Invalid input data or validation errors
- `401 Unauthorized`: Authentication required or invalid token
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (e.g., already assigned)
- `422 Unprocessable Entity`: Validation errors with details
- `500 Internal Server Error`: Server error

### Business Logic Errors

#### Vehicle Assignment Conflicts
```json
{
  "message": "Driver already has an active assignment",
  "statusCode": 409,
  "details": {
    "driverId": "string",
    "existingAssignmentId": "string"
  }
}
```

#### Validation Errors
```json
{
  "message": "Validation failed",
  "statusCode": 422,
  "details": [
    {
      "field": "plateNumber",
      "message": "Plate number format is invalid"
    }
  ]
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- 100 requests per minute per IP address
- 1000 requests per hour per authenticated user

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

Response includes pagination metadata:
```json
{
  "data": [...],
  "total": number,
  "page": number,
  "limit": number,
  "totalPages": number
}
```

## Data Validation

### Input Sanitization
- All string inputs are trimmed
- Email addresses are converted to lowercase
- Plate numbers are converted to uppercase
- Phone numbers are normalized

### Custom Validators
- `IsValidPlateNumber`: Validates plate number format
- `IsValidLicenseNumber`: Validates license number format
- `IsValidPhoneNumber`: Validates phone number format
- `IsNotPastDate`: Ensures dates are not in the past
- `IsAfterStartDate`: Ensures end date is after start date

## Performance Considerations

### Database Optimization
- Comprehensive indexing for frequently queried fields
- Connection pooling for better performance
- Query optimization with selective field retrieval
- Pagination to limit result sets

### Caching
- Response caching for frequently accessed data
- Database query result caching
- Static asset caching

### Monitoring
- Slow query logging (queries > 1 second)
- Error tracking and alerting
- Performance metrics collection
