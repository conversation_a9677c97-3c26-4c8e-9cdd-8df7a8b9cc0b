const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestTruckTrailerAssignments() {
  console.log('🚛🚚 Creating test truck-trailer assignments...');
  
  try {
    // First, get all trucks and trailers
    const trucks = await prisma.vehicle.findMany({
      where: { vehicleType: 'TRUCK' }
    });
    
    const trailers = await prisma.vehicle.findMany({
      where: { vehicleType: 'TRAILER' }
    });

    console.log(`Found ${trucks.length} trucks and ${trailers.length} trailers`);

    if (trucks.length === 0 || trailers.length === 0) {
      console.log('❌ Need both trucks and trailers to create assignments');
      return;
    }

    // Create some test assignments
    const assignments = [];

    // Assignment 1: First truck with first trailer
    if (trucks[0] && trailers[0]) {
      const assignment1 = await prisma.truckTrailerAssignment.upsert({
        where: {
          truckId_trailerId_startDate: {
            truckId: trucks[0].id,
            trailerId: trailers[0].id,
            startDate: new Date('2025-01-01')
          }
        },
        update: {},
        create: {
          truckId: trucks[0].id,
          trailerId: trailers[0].id,
          startDate: new Date('2025-01-01'),
          status: 'ACTIVE',
          assignedBy: 'admin',
          notes: 'Test assignment 1 - Long haul configuration'
        },
        include: {
          truck: true,
          trailer: true
        }
      });
      assignments.push(assignment1);
    }

    // Assignment 2: Second truck with second trailer (if available)
    if (trucks[1] && trailers[1]) {
      const assignment2 = await prisma.truckTrailerAssignment.upsert({
        where: {
          truckId_trailerId_startDate: {
            truckId: trucks[1].id,
            trailerId: trailers[1].id,
            startDate: new Date('2025-01-01')
          }
        },
        update: {},
        create: {
          truckId: trucks[1].id,
          trailerId: trailers[1].id,
          startDate: new Date('2025-01-01'),
          status: 'ACTIVE',
          assignedBy: 'admin',
          notes: 'Test assignment 2 - Regional delivery setup'
        },
        include: {
          truck: true,
          trailer: true
        }
      });
      assignments.push(assignment2);
    }

    // Assignment 3: Third truck with third trailer (if available)
    if (trucks[2] && trailers[2]) {
      const assignment3 = await prisma.truckTrailerAssignment.upsert({
        where: {
          truckId_trailerId_startDate: {
            truckId: trucks[2].id,
            trailerId: trailers[2].id,
            startDate: new Date('2025-01-01')
          }
        },
        update: {},
        create: {
          truckId: trucks[2].id,
          trailerId: trailers[2].id,
          startDate: new Date('2025-01-01'),
          status: 'ACTIVE',
          assignedBy: 'admin',
          notes: 'Test assignment 3 - Specialized transport'
        },
        include: {
          truck: true,
          trailer: true
        }
      });
      assignments.push(assignment3);
    }

    console.log('\n✅ Test truck-trailer assignments created:');
    assignments.forEach((assignment, index) => {
      console.log(`${index + 1}. 🚛 ${assignment.truck.plateNumber} (${assignment.truck.make} ${assignment.truck.model}) + 🚚 ${assignment.trailer.plateNumber} (${assignment.trailer.make} ${assignment.trailer.model})`);
    });

    // Check total assignments
    const totalAssignments = await prisma.truckTrailerAssignment.count({
      where: { status: 'ACTIVE' }
    });

    console.log(`\n📊 Total active truck-trailer assignments: ${totalAssignments}`);

  } catch (error) {
    console.error('❌ Error creating test truck-trailer assignments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestTruckTrailerAssignments();
