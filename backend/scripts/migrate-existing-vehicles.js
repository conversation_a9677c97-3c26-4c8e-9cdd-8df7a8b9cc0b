const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateExistingVehicles() {
  console.log('🚛 Starting vehicle type migration...');
  
  try {
    // Get all vehicles to check their current state
    const allVehicles = await prisma.vehicle.findMany({
      select: {
        id: true,
        plateNumber: true,
        make: true,
        model: true,
        vehicleType: true,
      }
    });

    console.log(`Found ${allVehicles.length} total vehicles in the system`);

    // Since we have a default value, all vehicles should already be TRUCK type
    // Let's verify this and show the current distribution
    const vehiclesByType = allVehicles.reduce((acc, vehicle) => {
      const type = vehicle.vehicleType || 'UNKNOWN';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    console.log('\n📊 Current vehicle type distribution:');
    Object.entries(vehiclesByType).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} vehicles`);
    });

    // Check if any vehicles need to be explicitly set to TRUCK
    const vehiclesNeedingUpdate = allVehicles.filter(v => !v.vehicleType);
    
    if (vehiclesNeedingUpdate.length > 0) {
      console.log(`\n🔄 Updating ${vehiclesNeedingUpdate.length} vehicles to TRUCK type...`);
      
      const updateResult = await prisma.vehicle.updateMany({
        where: {
          id: {
            in: vehiclesNeedingUpdate.map(v => v.id)
          }
        },
        data: {
          vehicleType: 'TRUCK'
        }
      });

      console.log(`✅ Successfully migrated ${updateResult.count} vehicles to TRUCK type`);
      
      // Display migrated vehicles
      console.log('\n📋 Migrated vehicles:');
      vehiclesNeedingUpdate.forEach(vehicle => {
        console.log(`  - ${vehicle.plateNumber} (${vehicle.make} ${vehicle.model}) -> TRUCK`);
      });
    } else {
      console.log('\n✅ All vehicles already have proper vehicle types assigned');
    }

    // Show final count
    const finalCounts = await prisma.vehicle.groupBy({
      by: ['vehicleType'],
      _count: {
        vehicleType: true
      }
    });

    console.log('\n📊 Final vehicle type distribution:');
    finalCounts.forEach(count => {
      console.log(`  - ${count.vehicleType}: ${count._count.vehicleType} vehicles`);
    });

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateExistingVehicles()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
