const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTruckTrailerFunctionality() {
  console.log('🧪 Testing Truck-Trailer Functionality...\n');
  
  try {
    // Test 1: Create a truck
    console.log('1️⃣ Creating a test truck...');
    const truck = await prisma.vehicle.create({
      data: {
        plateNumber: 'TRK-001',
        make: 'Volvo',
        model: 'VNL 860',
        year: 2023,
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        engineType: 'D13TC',
        transmission: 'I-Shift',
        fuelCapacity: 150.0,
        axleConfiguration: '6x4',
        cabConfiguration: 'Sleeper',
        fuelType: 'DIESEL',
      }
    });
    console.log(`✅ Created truck: ${truck.plateNumber} (ID: ${truck.id})`);

    // Test 2: Create a trailer
    console.log('\n2️⃣ Creating a test trailer...');
    const trailer = await prisma.vehicle.create({
      data: {
        plateNumber: 'TRL-001',
        make: 'Great Dane',
        model: 'Everest',
        year: 2023,
        vehicleType: 'TRAILER',
        status: 'AVAILABLE',
        trailerType: 'DRY_VAN',
        cargoCapacity: 3000.0,
        maxWeight: 80000,
        length: 53.0,
        width: 8.5,
        height: 13.6,
        hasRefrigeration: false,
      }
    });
    console.log(`✅ Created trailer: ${trailer.plateNumber} (ID: ${trailer.id})`);

    // Test 3: Create truck-trailer assignment
    console.log('\n3️⃣ Creating truck-trailer assignment...');
    const assignment = await prisma.truckTrailerAssignment.create({
      data: {
        truckId: truck.id,
        trailerId: trailer.id,
        startDate: new Date(),
        status: 'ACTIVE',
        assignedBy: 'Test Manager',
        notes: 'Test assignment for functionality verification',
      },
      include: {
        truck: true,
        trailer: true,
      }
    });
    console.log(`✅ Created assignment: ${assignment.truck.plateNumber} ↔ ${assignment.trailer.plateNumber}`);

    // Test 4: Query vehicles by type
    console.log('\n4️⃣ Testing vehicle type queries...');
    const trucks = await prisma.vehicle.findMany({
      where: { vehicleType: 'TRUCK' }
    });
    const trailers = await prisma.vehicle.findMany({
      where: { vehicleType: 'TRAILER' }
    });
    console.log(`✅ Found ${trucks.length} trucks and ${trailers.length} trailers`);

    // Test 5: Query assignments with relationships
    console.log('\n5️⃣ Testing assignment queries...');
    const assignments = await prisma.truckTrailerAssignment.findMany({
      include: {
        truck: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
          }
        },
        trailer: {
          select: {
            plateNumber: true,
            make: true,
            model: true,
            vehicleType: true,
            trailerType: true,
          }
        }
      }
    });
    console.log(`✅ Found ${assignments.length} assignments:`);
    assignments.forEach(a => {
      console.log(`   - ${a.truck.plateNumber} (${a.truck.make} ${a.truck.model}) ↔ ${a.trailer.plateNumber} (${a.trailer.make} ${a.trailer.model}, ${a.trailer.trailerType})`);
    });

    // Test 6: Test vehicle filtering with assignments
    console.log('\n6️⃣ Testing vehicle queries with assignment relationships...');
    const vehiclesWithAssignments = await prisma.vehicle.findMany({
      include: {
        truckAssignments: {
          where: {
            status: 'ACTIVE',
            endDate: null,
          },
          include: {
            trailer: {
              select: {
                plateNumber: true,
                trailerType: true,
              }
            }
          }
        },
        trailerAssignments: {
          where: {
            status: 'ACTIVE',
            endDate: null,
          },
          include: {
            truck: {
              select: {
                plateNumber: true,
              }
            }
          }
        },
      }
    });

    console.log(`✅ Vehicle assignment relationships:`);
    vehiclesWithAssignments.forEach(v => {
      if (v.vehicleType === 'TRUCK' && v.truckAssignments.length > 0) {
        const assignedTrailer = v.truckAssignments[0].trailer;
        console.log(`   - Truck ${v.plateNumber} → Trailer ${assignedTrailer.plateNumber} (${assignedTrailer.trailerType})`);
      } else if (v.vehicleType === 'TRAILER' && v.trailerAssignments.length > 0) {
        const assignedTruck = v.trailerAssignments[0].truck;
        console.log(`   - Trailer ${v.plateNumber} ← Truck ${assignedTruck.plateNumber}`);
      }
    });

    // Test 7: Test available vehicles (not assigned)
    console.log('\n7️⃣ Testing available vehicle queries...');
    const availableTrucks = await prisma.vehicle.findMany({
      where: {
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        truckAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      }
    });

    const availableTrailers = await prisma.vehicle.findMany({
      where: {
        vehicleType: 'TRAILER',
        status: 'AVAILABLE',
        trailerAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      }
    });

    console.log(`✅ Available vehicles: ${availableTrucks.length} trucks, ${availableTrailers.length} trailers`);

    // Test 8: Complete the assignment
    console.log('\n8️⃣ Testing assignment completion...');
    const completedAssignment = await prisma.truckTrailerAssignment.update({
      where: { id: assignment.id },
      data: {
        status: 'COMPLETED',
        endDate: new Date(),
      }
    });
    console.log(`✅ Completed assignment: ${completedAssignment.status}`);

    // Test 9: Verify vehicles are now available again
    console.log('\n9️⃣ Verifying vehicles are available after assignment completion...');
    const nowAvailableTrucks = await prisma.vehicle.findMany({
      where: {
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        truckAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      }
    });

    const nowAvailableTrailers = await prisma.vehicle.findMany({
      where: {
        vehicleType: 'TRAILER',
        status: 'AVAILABLE',
        trailerAssignments: {
          none: {
            status: 'ACTIVE',
            endDate: null,
          }
        }
      }
    });

    console.log(`✅ Now available: ${nowAvailableTrucks.length} trucks, ${nowAvailableTrailers.length} trailers`);

    console.log('\n🎉 All tests passed! Truck-trailer functionality is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testTruckTrailerFunctionality()
  .then(() => {
    console.log('\n✅ Test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
