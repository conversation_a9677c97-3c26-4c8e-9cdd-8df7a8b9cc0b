const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestDriver() {
  console.log('🚗 Creating test driver...');
  
  try {
    const hashedPassword = await bcrypt.hash('driver123', 10);
    
    const driver = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Driver',
        passwordHash: hashedPassword,
        role: 'DRIVER',
        phone: '******-0123',
        licenseNumber: 'DL123456789',
        licenseType: 'CDL-A',
        status: 'AVAILABLE',
      },
    });

    console.log('✅ Test driver created:', {
      id: driver.id,
      email: driver.email,
      name: `${driver.firstName} ${driver.lastName}`,
      role: driver.role,
    });

    // Create another driver for testing
    const driver2 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: '<PERSON>',
        passwordHash: hashedPassword,
        role: 'DRIVER',
        phone: '******-0124',
        licenseNumber: 'DL987654321',
        licenseType: 'CDL-B',
        status: 'AVAILABLE',
      },
    });

    console.log('✅ Second test driver created:', {
      id: driver2.id,
      email: driver2.email,
      name: `${driver2.firstName} ${driver2.lastName}`,
      role: driver2.role,
    });

    // Check total drivers
    const totalDrivers = await prisma.user.count({
      where: { role: 'DRIVER' }
    });

    console.log(`\n📊 Total drivers in database: ${totalDrivers}`);

  } catch (error) {
    console.error('❌ Error creating test driver:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestDriver();
