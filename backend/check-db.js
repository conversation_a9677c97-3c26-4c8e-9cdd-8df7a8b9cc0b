const { PrismaClient } = require('@prisma/client');

async function checkDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Checking VehicleAssignment table...');
    const assignments = await prisma.vehicleAssignment.findMany({
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        vehicle: {
          select: {
            id: true,
            plateNumber: true,
            make: true,
            model: true,
          },
        },
      },
    });
    
    console.log(`Found ${assignments.length} assignments`);
    if (assignments.length > 0) {
      console.log('Sample assignment:', JSON.stringify(assignments[0], null, 2));
    }
    
    console.log('\nChecking Users (drivers)...');
    const drivers = await prisma.user.findMany({
      where: { role: 'DRIVER' },
      select: { id: true, firstName: true, lastName: true }
    });
    console.log(`Found ${drivers.length} drivers`);
    
    console.log('\nChecking Vehicles...');
    const vehicles = await prisma.vehicle.findMany({
      select: { id: true, plateNumber: true, make: true, model: true, status: true }
    });
    console.log(`Found ${vehicles.length} vehicles`);
    
    // If we have drivers and vehicles but no assignments, create a test assignment
    if (drivers.length > 0 && vehicles.length > 0 && assignments.length === 0) {
      console.log('\nCreating test assignment...');
      const testAssignment = await prisma.vehicleAssignment.create({
        data: {
          driverId: drivers[0].id,
          vehicleId: vehicles[0].id,
          startDate: new Date(),
          status: 'ACTIVE',
          type: 'REGULAR',
          priority: 'NORMAL',
          notes: 'Test assignment created automatically'
        },
        include: {
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          vehicle: {
            select: {
              id: true,
              plateNumber: true,
              make: true,
              model: true,
            },
          },
        },
      });
      
      console.log('Test assignment created:', JSON.stringify(testAssignment, null, 2));
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
