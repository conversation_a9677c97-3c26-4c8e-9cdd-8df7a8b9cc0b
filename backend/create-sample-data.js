const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createSampleData() {
  try {
    console.log('🚀 Creating sample data for fuel management testing...');

    // Create sample drivers
    const passwordHash = await bcrypt.hash('driver123', 10);

    const drivers = [
      {
        email: 'jan.<PERSON><PERSON><PERSON><PERSON>@fleetfusion.com',
        firstName: 'Jan',
        lastName: '<PERSON><PERSON><PERSON>',
        phone: '+48123456001',
        licenseNumber: 'DRV001',
        licenseType: 'C+E',
        licenseExpiry: new Date('2026-12-31'),
      },
      {
        email: '<EMAIL>',
        firstName: 'Anna',
        lastName: 'Nowak',
        phone: '+48123456002',
        licenseNumber: 'DRV002',
        licenseType: 'C+E',
        licenseExpiry: new Date('2027-06-30'),
      },
      {
        email: 'piotr.wisnie<PERSON>@fleetfusion.com',
        firstName: 'Piotr',
        lastName: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        phone: '+48123456003',
        licenseNumber: 'DRV003',
        licenseType: 'C+E',
        licenseExpiry: new Date('2026-03-15'),
      }
    ];

    for (const driverData of drivers) {
      const existingDriver = await prisma.user.findUnique({
        where: { email: driverData.email }
      });

      if (!existingDriver) {
        await prisma.user.create({
          data: {
            ...driverData,
            passwordHash,
            role: 'DRIVER',
            status: 'Active',
            address: 'Warsaw, Poland',
            hireDate: new Date('2024-01-01'),
          }
        });
        console.log(`✅ Created driver: ${driverData.firstName} ${driverData.lastName}`);
      }
    }

    // Create sample vehicles (trucks)
    const vehicles = [
      {
        plateNumber: 'ABC-123',
        make: 'Volvo',
        model: 'FH16',
        year: 2022,
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        mileage: 125000,
        fuelCapacity: 400,
        loadCapacity: 25000,
      },
      {
        plateNumber: 'DEF-456',
        make: 'Scania',
        model: 'R450',
        year: 2023,
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        mileage: 98000,
        fuelCapacity: 380,
        loadCapacity: 24000,
      },
      {
        plateNumber: 'GHI-789',
        make: 'MAN',
        model: 'TGX',
        year: 2021,
        vehicleType: 'TRUCK',
        status: 'AVAILABLE',
        mileage: 156000,
        fuelCapacity: 420,
        loadCapacity: 26000,
      }
    ];

    for (const vehicleData of vehicles) {
      const existingVehicle = await prisma.vehicle.findUnique({
        where: { plateNumber: vehicleData.plateNumber }
      });

      if (!existingVehicle) {
        await prisma.vehicle.create({
          data: vehicleData
        });
        console.log(`✅ Created vehicle: ${vehicleData.plateNumber} (${vehicleData.make} ${vehicleData.model})`);
      }
    }

    // Create a manager user
    const managerExists = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!managerExists) {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: await bcrypt.hash('manager123', 10),
          firstName: 'Maria',
          lastName: 'Kowalczyk',
          role: 'MANAGER',
          status: 'Active',
          phone: '+***********',
          address: 'Warsaw, Poland',
          hireDate: new Date('2023-01-01'),
        }
      });
      console.log('✅ Created manager: Maria Kowalczyk');
    }

    console.log('\n🎉 Sample data creation completed!');
    console.log('\n📋 Login credentials:');
    console.log('👤 Admin: <EMAIL> / admin123');
    console.log('👤 Manager: <EMAIL> / manager123');
    console.log('👤 Driver: <EMAIL> / driver123');
    console.log('👤 Driver: <EMAIL> / driver123');
    console.log('👤 Driver: <EMAIL> / driver123');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleData();
