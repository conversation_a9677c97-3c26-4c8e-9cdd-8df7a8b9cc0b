{"name": "fleet-fusion", "version": "1.0.0", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "npm-run-all --parallel dev:*", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run start:dev", "build": "npm-run-all --parallel build:*", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build"}, "devDependencies": {"npm-run-all": "^4.1.5", "ts-node": "^10.9.2"}}